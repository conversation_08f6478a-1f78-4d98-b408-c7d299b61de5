# Marketing Agent Workflow Template
# Optimized for marketing strategy, content creation, and campaign workflows

template_id: "marketing_workflow"
template_name: "Marketing Agent Workflow"
version: "1.0.0"
description: "Comprehensive workflow template for marketing agent with content creation and strategy capabilities"
agent_type: "marketing"

# Template metadata
metadata:
  author: "Datagenius Team"
  created_date: "2024-01-26"
  last_modified: "2024-01-26"
  tags: ["marketing", "content", "strategy", "campaigns", "creative"]
  complexity: "medium"
  estimated_duration: "20-60 seconds"

# Workflow configuration
workflow_config:
  # Execution settings
  max_execution_steps: 8  # Moderate complexity for marketing tasks
  execution_timeout: 90   # 1.5 minutes for content creation
  enable_parallel_execution: true  # Enable parallel content generation
  
  # Loop prevention
  max_agent_executions: 4  # Allow iterations for refinement
  min_execution_interval: 1.5  # 1.5 seconds between executions
  enable_infinite_loop_detection: true
  
  # Termination conditions
  auto_terminate_on_response: false  # May need refinement iterations
  require_explicit_continuation: true
  enable_workflow_completion_signals: true

# Node configuration
nodes:
  # Entry point - marketing agent
  entry:
    type: "agent"
    agent_id: "marketing"
    config:
      max_retries: 2
      timeout: 45
      enable_fallback: true
      enable_creativity_mode: true
    
  # Strategy planning node
  strategy_planning:
    type: "tool_execution"
    tools: ["market_analyzer", "competitor_analyzer", "strategy_planner"]
    config:
      parallel_execution: false  # Sequential for strategy coherence
      timeout: 30
      enable_research: true
    
  # Content creation node
  content_creation:
    type: "tool_execution"
    tools: ["content_generator", "copywriter", "social_media_creator"]
    config:
      parallel_execution: true
      timeout: 25
      enable_creativity_boost: true
    
  # Content optimization node
  content_optimization:
    type: "tool_execution"
    tools: ["seo_optimizer", "readability_checker", "brand_aligner"]
    config:
      parallel_execution: true
      timeout: 20
      optional: true
    
  # Campaign planning node
  campaign_planning:
    type: "tool_execution"
    tools: ["campaign_planner", "budget_optimizer", "timeline_creator"]
    config:
      parallel_execution: false
      timeout: 25
      enable_collaboration: true
    
  # Quality review node
  quality_review:
    type: "validation"
    config:
      validate_brand_consistency: true
      check_content_quality: true
      verify_strategy_alignment: true
    
  # Routing node
  routing:
    type: "routing"
    config:
      enable_intelligent_routing: true
      enable_collaboration: true
      max_routing_attempts: 2

# Edge configuration (routing patterns)
edges:
  # Start -> Marketing Agent
  - from: "START"
    to: "marketing"
    condition: "always"
    priority: 1
    
  # Marketing -> Strategy Planning
  - from: "marketing"
    to: "strategy_planning"
    condition: "strategy_required OR campaign_planning"
    priority: 1
    
  # Marketing -> Content Creation (direct)
  - from: "marketing"
    to: "content_creation"
    condition: "content_request AND NOT strategy_required"
    priority: 1
    
  # Strategy Planning -> Content Creation
  - from: "strategy_planning"
    to: "content_creation"
    condition: "strategy_complete AND content_needed"
    priority: 1
    
  # Strategy Planning -> Campaign Planning
  - from: "strategy_planning"
    to: "campaign_planning"
    condition: "strategy_complete AND campaign_needed"
    priority: 1
    
  # Content Creation -> Content Optimization
  - from: "content_creation"
    to: "content_optimization"
    condition: "content_created AND optimization_requested"
    priority: 1
    
  # Content Creation -> Quality Review
  - from: "content_creation"
    to: "quality_review"
    condition: "content_created AND NOT optimization_requested"
    priority: 1
    
  # Content Optimization -> Quality Review
  - from: "content_optimization"
    to: "quality_review"
    condition: "optimization_complete"
    priority: 1
    
  # Campaign Planning -> Quality Review
  - from: "campaign_planning"
    to: "quality_review"
    condition: "campaign_plan_complete"
    priority: 1
    
  # Quality Review -> Marketing Agent (for response)
  - from: "quality_review"
    to: "marketing"
    condition: "quality_approved"
    priority: 1
    
  # Marketing Agent -> END
  - from: "marketing"
    to: "END"
    condition: "workflow_complete OR final_deliverable_ready"
    priority: 1
    
  # Quality Review -> Content Creation (for revisions)
  - from: "quality_review"
    to: "content_creation"
    condition: "quality_failed AND revision_needed AND retry_count < max_retries"
    priority: 2
    
  # Emergency termination
  - from: "*"
    to: "END"
    condition: "critical_error OR timeout OR max_executions_reached"
    priority: 3

# Termination conditions
termination_conditions:
  # Successful completion
  - condition: "deliverable_complete AND quality_approved AND response_generated"
    action: "END"
    priority: 1
    
  # Workflow completion signal
  - condition: "workflow_complete"
    action: "END"
    priority: 1
    
  # Content-related termination
  - condition: "content_approved OR campaign_finalized"
    action: "END"
    priority: 1
    
  # Strategy completion
  - condition: "strategy_delivered AND no_content_needed"
    action: "END"
    priority: 1
    
  # Error conditions
  - condition: "creative_block OR content_generation_failed"
    action: "END"
    priority: 2
    
  # Resource limits
  - condition: "creativity_timeout OR processing_limit_reached"
    action: "END"
    priority: 3
    
  # Loop prevention
  - condition: "infinite_loop_detected OR max_revisions_reached"
    action: "END"
    priority: 3

# State management
state_management:
  # Required state fields
  required_fields:
    - "user_id"
    - "conversation_id"
    - "messages"
    - "marketing_context"
    
  # Marketing-specific state
  marketing_state:
    - "brand_guidelines"
    - "target_audience"
    - "campaign_objectives"
    - "content_requirements"
    - "deliverables"
    
  # State validation rules
  validation_rules:
    - field: "marketing_context"
      rule: "not_empty"
      error_action: "request_context"
      
    - field: "target_audience"
      rule: "defined"
      error_action: "use_general_audience"
  
  # State persistence
  persist_creative_assets: true
  enable_version_control: true
  cleanup_on_completion: false  # Keep assets for future use

# Performance optimization
performance:
  # Caching
  enable_content_caching: true
  cache_duration: 3600  # 1 hour
  cache_creative_assets: true
  
  # Resource limits
  memory_limit_mb: 200  # Moderate for content creation
  cpu_time_limit_seconds: 60
  max_content_size_mb: 50
  
  # Parallel processing
  max_parallel_tools: 3
  enable_async_content_generation: true
  
  # Creativity settings
  enable_creative_mode: true
  creativity_level: "high"
  
  # Monitoring
  enable_performance_tracking: true
  track_content_quality: true
  track_creativity_metrics: true
  track_brand_consistency: true

# Error handling
error_handling:
  # Retry configuration
  max_retries: 2
  retry_delay_seconds: 1.5
  exponential_backoff: false
  
  # Fallback strategies
  fallback_strategies:
    - trigger: "content_generation_failed"
      action: "use_template_content"
      
    - trigger: "strategy_planning_error"
      action: "use_best_practices"
      
    - trigger: "brand_guideline_missing"
      action: "use_generic_guidelines"
      
    - trigger: "timeout"
      action: "return_draft_content"
  
  # Error responses
  content_error_response: "I had trouble creating the content. Let me provide a draft version for your review."
  strategy_error_response: "I couldn't complete the full strategy analysis. Here are some general recommendations."
  creative_block_response: "I'm experiencing some creative challenges. Let me try a different approach."

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable: true
    required: true
    use_brand_context: true
    
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable: true
    coordinate_with: ["analysis", "concierge"]
    
  # Brand management
  brand_management:
    enable: true
    enforce_guidelines: true
    check_consistency: true
    
  # Social media integration
  social_media:
    enable: true
    platforms: ["linkedin", "twitter", "facebook", "instagram"]
    
  # MCP tools
  mcp_tools:
    enable: true
    max_tools: 4
    timeout: 30

# Monitoring and metrics
monitoring:
  # Metrics to collect
  metrics:
    - "content_quality_score"
    - "brand_consistency_score"
    - "creativity_rating"
    - "strategy_effectiveness"
    - "user_satisfaction"
    - "content_engagement_potential"
    
  # Performance thresholds
  thresholds:
    content_quality: 0.8
    brand_consistency: 0.9
    processing_time: 60.0
    
  # Alerts
  alerts:
    - metric: "brand_consistency_score"
      threshold: 0.8
      action: "review_brand_guidelines"
      
    - metric: "content_quality_score"
      threshold: 0.7
      action: "enhance_content_quality"

# Template validation
validation:
  # Required components
  required_components:
    - "marketing_agent"
    - "content_creation_tools"
    - "brand_management"
    
  # Optional components
  optional_components:
    - "strategy_planning_tools"
    - "campaign_management_tools"
    - "social_media_tools"
    
  # Validation rules
  rules:
    - rule: "max_execution_steps >= 4"
      error: "Marketing workflows need sufficient steps for creative processes"
      
    - rule: "execution_timeout >= 45"
      error: "Marketing workflows need adequate time for content creation"
