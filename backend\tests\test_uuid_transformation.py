#!/usr/bin/env python3
"""
Test script to demonstrate UUID label transformation for better readability
"""

import sys
import os
import pandas as pd
import uuid
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.tools.mcp.interactive_chart_data import InteractiveChartDataTool

def create_test_data():
    """Create test data with UUIDs and other problematic labels."""
    data = []
    
    # Generate sample data with UUIDs
    for i in range(10):
        data.append({
            'id': str(uuid.uuid4()),
            'long_description': f'This is a very long description that should be truncated for better readability in charts - item {i}',
            'email': f'user{i}@example.com',
            'file_path': f'/very/long/path/to/some/file/document_{i}.pdf',
            'url': f'https://example.com/very/long/path/to/resource/{i}',
            'value': (i + 1) * 100 + (i * 15),
            'category': f'Category {chr(65 + i % 5)}'  # A, B, C, D, E
        })
    
    return pd.DataFrame(data)

def test_label_transformation():
    """Test the label transformation functionality."""
    print("🧪 Testing UUID Label Transformation")
    print("=" * 50)
    
    # Create test data
    df = create_test_data()
    print(f"📊 Created test data with {len(df)} rows")
    print(f"📋 Columns: {list(df.columns)}")
    print()
    
    # Initialize the tool
    tool = InteractiveChartDataTool()
    
    # Test different types of labels
    test_labels = [
        str(uuid.uuid4()),  # UUID
        '<EMAIL>',  # Email
        '/very/long/path/to/some/file/document.pdf',  # File path
        'https://example.com/very/long/path/to/resource',  # URL
        'This is a very long description that should be truncated',  # Long text
        'Short',  # Short text (should remain unchanged)
    ]
    
    print("🔄 Testing label transformations:")
    print("-" * 30)
    
    for original in test_labels:
        transformed = tool._transform_label_for_readability(original)
        print(f"Original:    {original}")
        print(f"Transformed: {transformed}")
        print()
    
    # Test with actual chart data extraction
    print("📈 Testing with pie chart data extraction:")
    print("-" * 40)
    
    # Create chart analysis config for pie chart using UUIDs
    chart_analysis = {
        "chart_type": "pie",
        "title": "Test Pie Chart with UUIDs",
        "description": "Testing UUID transformation in pie chart",
        "config": {
            "axes": {
                "x_axis": "id",  # UUID column
                "y_axes": ["value"]
            }
        }
    }
    
    # Extract chart data
    chart_data = tool._extract_chart_data(df, chart_analysis)
    
    print(f"📊 Extracted {len(chart_data['chart_data'])} data points")
    print("Sample transformed data:")
    
    for i, item in enumerate(chart_data['chart_data'][:3]):  # Show first 3 items
        print(f"  {i+1}. Name: '{item['name']}'")
        print(f"     Original: '{item.get('original_name', 'N/A')}'")
        print(f"     Value: {item['value']}")
        print()
    
    # Test with different column types
    print("📊 Testing with different column types:")
    print("-" * 35)
    
    test_configs = [
        ("email", "Email addresses"),
        ("file_path", "File paths"),
        ("url", "URLs"),
        ("long_description", "Long descriptions")
    ]
    
    for col, desc in test_configs:
        chart_analysis["config"]["axes"]["x_axis"] = col
        chart_data = tool._extract_chart_data(df, chart_analysis)
        
        print(f"{desc}:")
        sample_item = chart_data['chart_data'][0]
        print(f"  Original: '{sample_item.get('original_name', 'N/A')}'")
        print(f"  Readable: '{sample_item['name']}'")
        print()

if __name__ == "__main__":
    test_label_transformation()
