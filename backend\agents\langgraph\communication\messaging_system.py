"""
Inter-Agent Messaging System for LangGraph Network Architecture.

This module provides direct messaging capabilities between agents in the network,
enabling peer-to-peer communication without going through a central router.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json
from collections import defaultdict, deque

from ..states.network_state import NetworkCommunicationType, NetworkMessagePriority, NetworkDatageniusState
from ..core.agent_registry import NetworkAgentRegistry, network_registry

logger = logging.getLogger(__name__)


class MessageStatus(str, Enum):
    """Status of network messages."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    PROCESSED = "processed"
    FAILED = "failed"
    EXPIRED = "expired"


class MessageDeliveryMode(str, Enum):
    """Message delivery modes."""
    IMMEDIATE = "immediate"
    QUEUED = "queued"
    SCHEDULED = "scheduled"
    BROADCAST = "broadcast"


@dataclass
class NetworkMessage:
    """Represents a message in the network."""
    id: str
    sender_agent: str
    recipient_agent: str
    message_type: NetworkCommunicationType
    content: Dict[str, Any]
    priority: NetworkMessagePriority = NetworkMessagePriority.NORMAL
    delivery_mode: MessageDeliveryMode = MessageDeliveryMode.IMMEDIATE
    status: MessageStatus = MessageStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    metadata: Dict[str, Any] = field(default_factory=dict)
    response_expected: bool = False
    correlation_id: Optional[str] = None
    conversation_thread: Optional[str] = None

    def is_expired(self) -> bool:
        """Check if message has expired."""
        return self.expires_at is not None and datetime.now() > self.expires_at

    def can_retry(self) -> bool:
        """Check if message can be retried."""
        return self.retry_count < self.max_retries and not self.is_expired()


class InterAgentMessagingSystem:
    """
    System for direct messaging between agents in the network.
    
    Features:
    - Direct peer-to-peer messaging
    - Message queuing and delivery
    - Priority-based message handling
    - Message persistence and reliability
    - Broadcast and multicast capabilities
    - Message threading and correlation
    """

    def __init__(self, registry: NetworkAgentRegistry = None):
        """Initialize the messaging system."""
        self.registry = registry or network_registry
        
        # Message storage
        self.messages: Dict[str, NetworkMessage] = {}
        self.agent_inboxes: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.agent_outboxes: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.pending_messages: Dict[str, NetworkMessage] = {}
        
        # Message routing
        self.message_handlers: Dict[str, Dict[NetworkCommunicationType, Callable]] = defaultdict(dict)
        self.message_filters: Dict[str, List[Callable]] = defaultdict(list)
        self.delivery_queues: Dict[NetworkMessagePriority, deque] = {
            priority: deque() for priority in NetworkMessagePriority
        }
        
        # System state
        self.active_conversations: Dict[str, Dict[str, Any]] = {}
        self.message_threads: Dict[str, List[str]] = defaultdict(list)
        self.delivery_stats: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        
        # Configuration
        self.max_message_age = timedelta(hours=24)
        self.delivery_timeout = timedelta(minutes=5)
        self.batch_size = 10
        self.enable_persistence = True
        
        # Background tasks
        self._delivery_task = None
        self._cleanup_task = None
        self._start_background_tasks()
        
        logger.info("InterAgentMessagingSystem initialized")

    def _start_background_tasks(self):
        """Start background tasks for message delivery and cleanup."""
        self._delivery_task = asyncio.create_task(self._message_delivery_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())

    async def _message_delivery_loop(self):
        """Background loop for message delivery."""
        while True:
            try:
                await self._process_delivery_queues()
                await asyncio.sleep(0.1)  # Process messages every 100ms
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in message delivery loop: {e}")
                await asyncio.sleep(1)

    async def _cleanup_loop(self):
        """Background loop for message cleanup."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._cleanup_expired_messages()
                await self._cleanup_old_messages()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")

    async def send_message(
        self,
        sender_agent: str,
        recipient_agent: str,
        message_type: NetworkCommunicationType,
        content: Dict[str, Any],
        priority: NetworkMessagePriority = NetworkMessagePriority.NORMAL,
        delivery_mode: MessageDeliveryMode = MessageDeliveryMode.IMMEDIATE,
        response_expected: bool = False,
        expires_in: Optional[timedelta] = None,
        correlation_id: Optional[str] = None,
        conversation_thread: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Send a message from one agent to another.
        
        Args:
            sender_agent: ID of the sending agent
            recipient_agent: ID of the receiving agent
            message_type: Type of message being sent
            content: Message content
            priority: Message priority level
            delivery_mode: How the message should be delivered
            response_expected: Whether a response is expected
            expires_in: When the message expires
            correlation_id: ID to correlate with other messages
            conversation_thread: Thread ID for conversation grouping
            metadata: Additional metadata
            
        Returns:
            Message ID
        """
        try:
            # Validate agents exist
            if not self.registry.get_agent(sender_agent):
                raise ValueError(f"Sender agent {sender_agent} not found")
            if not self.registry.get_agent(recipient_agent):
                raise ValueError(f"Recipient agent {recipient_agent} not found")

            # Create message
            message = NetworkMessage(
                id=str(uuid.uuid4()),
                sender_agent=sender_agent,
                recipient_agent=recipient_agent,
                message_type=message_type,
                content=content,
                priority=priority,
                delivery_mode=delivery_mode,
                response_expected=response_expected,
                correlation_id=correlation_id,
                conversation_thread=conversation_thread or str(uuid.uuid4()),
                metadata=metadata or {}
            )

            # Set expiration
            if expires_in:
                message.expires_at = datetime.now() + expires_in

            # Store message
            self.messages[message.id] = message
            self.agent_outboxes[sender_agent].append(message.id)

            # Add to delivery queue based on mode
            if delivery_mode == MessageDeliveryMode.IMMEDIATE:
                self.delivery_queues[priority].append(message.id)
            elif delivery_mode == MessageDeliveryMode.QUEUED:
                self.pending_messages[message.id] = message
            elif delivery_mode == MessageDeliveryMode.BROADCAST:
                await self._handle_broadcast_message(message)
            
            # Update message threading
            if conversation_thread:
                self.message_threads[conversation_thread].append(message.id)

            # Update delivery stats
            self.delivery_stats[sender_agent]["sent"] += 1

            logger.debug(f"Message {message.id} queued from {sender_agent} to {recipient_agent}")
            return message.id

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise

    async def _process_delivery_queues(self):
        """Process messages in delivery queues."""
        # Process in priority order
        for priority in [NetworkMessagePriority.CRITICAL, NetworkMessagePriority.HIGH, 
                        NetworkMessagePriority.NORMAL, NetworkMessagePriority.LOW]:
            queue = self.delivery_queues[priority]
            
            # Process batch of messages
            for _ in range(min(self.batch_size, len(queue))):
                if not queue:
                    break
                    
                message_id = queue.popleft()
                message = self.messages.get(message_id)
                
                if not message:
                    continue
                    
                if message.is_expired():
                    message.status = MessageStatus.EXPIRED
                    continue
                
                await self._deliver_message(message)

    async def _deliver_message(self, message: NetworkMessage):
        """Deliver a message to its recipient."""
        try:
            # Check if recipient is available
            recipient_agent = self.registry.get_agent(message.recipient_agent)
            if not recipient_agent:
                message.status = MessageStatus.FAILED
                logger.warning(f"Recipient agent {message.recipient_agent} not found")
                return

            # Add to recipient's inbox
            self.agent_inboxes[message.recipient_agent].append(message.id)
            
            # Update message status
            message.status = MessageStatus.DELIVERED
            message.delivered_at = datetime.now()
            
            # Call message handler if registered
            await self._call_message_handler(message)
            
            # Update delivery stats
            self.delivery_stats[message.recipient_agent]["received"] += 1
            
            logger.debug(f"Message {message.id} delivered to {message.recipient_agent}")

        except Exception as e:
            logger.error(f"Error delivering message {message.id}: {e}")
            message.retry_count += 1
            
            if message.can_retry():
                # Re-queue for retry
                self.delivery_queues[message.priority].append(message.id)
                message.status = MessageStatus.PENDING
            else:
                message.status = MessageStatus.FAILED

    async def _call_message_handler(self, message: NetworkMessage):
        """Call registered message handler for the message type."""
        handlers = self.message_handlers.get(message.recipient_agent, {})
        handler = handlers.get(message.message_type)
        
        if handler:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(message)
                else:
                    handler(message)
                message.status = MessageStatus.PROCESSED
                message.processed_at = datetime.now()
            except Exception as e:
                logger.error(f"Error in message handler for {message.message_type}: {e}")

    async def _handle_broadcast_message(self, message: NetworkMessage):
        """Handle broadcast message delivery."""
        # Get all agents except sender
        all_agents = self.registry.get_all_agents()
        recipients = [agent.agent_id for agent in all_agents if agent.agent_id != message.sender_agent]
        
        # Create individual messages for each recipient
        for recipient in recipients:
            individual_message = NetworkMessage(
                id=str(uuid.uuid4()),
                sender_agent=message.sender_agent,
                recipient_agent=recipient,
                message_type=message.message_type,
                content=message.content,
                priority=message.priority,
                delivery_mode=MessageDeliveryMode.IMMEDIATE,
                correlation_id=message.id,  # Link to original broadcast
                metadata=message.metadata
            )
            
            self.messages[individual_message.id] = individual_message
            self.delivery_queues[message.priority].append(individual_message.id)

    def register_message_handler(
        self,
        agent_id: str,
        message_type: NetworkCommunicationType,
        handler: Callable
    ):
        """Register a message handler for an agent and message type."""
        self.message_handlers[agent_id][message_type] = handler
        logger.debug(f"Registered handler for {agent_id} - {message_type}")

    def unregister_message_handler(
        self,
        agent_id: str,
        message_type: NetworkCommunicationType
    ):
        """Unregister a message handler."""
        if agent_id in self.message_handlers:
            self.message_handlers[agent_id].pop(message_type, None)

    async def get_messages_for_agent(
        self,
        agent_id: str,
        message_types: Optional[List[NetworkCommunicationType]] = None,
        limit: int = 50,
        mark_as_read: bool = True
    ) -> List[NetworkMessage]:
        """
        Get messages for an agent.
        
        Args:
            agent_id: ID of the agent
            message_types: Filter by message types
            limit: Maximum number of messages to return
            mark_as_read: Whether to mark messages as read
            
        Returns:
            List of messages
        """
        inbox = self.agent_inboxes[agent_id]
        messages = []
        
        # Get recent message IDs
        message_ids = list(inbox)[-limit:] if limit else list(inbox)
        
        for message_id in reversed(message_ids):  # Most recent first
            message = self.messages.get(message_id)
            if not message:
                continue
                
            # Filter by message type if specified
            if message_types and message.message_type not in message_types:
                continue
                
            messages.append(message)
            
            # Mark as read if requested
            if mark_as_read and message.status == MessageStatus.DELIVERED:
                message.status = MessageStatus.READ

        return messages

    async def send_response(
        self,
        original_message: NetworkMessage,
        response_content: Dict[str, Any],
        response_type: NetworkCommunicationType = NetworkCommunicationType.DIRECT_MESSAGE
    ) -> str:
        """Send a response to a message."""
        return await self.send_message(
            sender_agent=original_message.recipient_agent,
            recipient_agent=original_message.sender_agent,
            message_type=response_type,
            content=response_content,
            priority=original_message.priority,
            correlation_id=original_message.id,
            conversation_thread=original_message.conversation_thread
        )

    async def get_conversation_thread(self, thread_id: str) -> List[NetworkMessage]:
        """Get all messages in a conversation thread."""
        message_ids = self.message_threads.get(thread_id, [])
        messages = []
        
        for message_id in message_ids:
            message = self.messages.get(message_id)
            if message:
                messages.append(message)
        
        # Sort by creation time
        messages.sort(key=lambda m: m.created_at)
        return messages

    async def _cleanup_expired_messages(self):
        """Clean up expired messages."""
        current_time = datetime.now()
        expired_messages = []
        
        for message_id, message in self.messages.items():
            if message.is_expired():
                expired_messages.append(message_id)
                message.status = MessageStatus.EXPIRED
        
        for message_id in expired_messages:
            await self._remove_message(message_id)
        
        if expired_messages:
            logger.info(f"Cleaned up {len(expired_messages)} expired messages")

    async def _cleanup_old_messages(self):
        """Clean up old messages beyond retention period."""
        cutoff_time = datetime.now() - self.max_message_age
        old_messages = []
        
        for message_id, message in self.messages.items():
            if message.created_at < cutoff_time and message.status in [MessageStatus.PROCESSED, MessageStatus.READ]:
                old_messages.append(message_id)
        
        for message_id in old_messages:
            await self._remove_message(message_id)
        
        if old_messages:
            logger.info(f"Cleaned up {len(old_messages)} old messages")

    async def _remove_message(self, message_id: str):
        """Remove a message from all storage locations."""
        message = self.messages.pop(message_id, None)
        if not message:
            return
        
        # Remove from inboxes and outboxes
        for inbox in self.agent_inboxes.values():
            if message_id in inbox:
                inbox.remove(message_id)
        
        for outbox in self.agent_outboxes.values():
            if message_id in outbox:
                outbox.remove(message_id)
        
        # Remove from pending messages
        self.pending_messages.pop(message_id, None)
        
        # Remove from delivery queues
        for queue in self.delivery_queues.values():
            if message_id in queue:
                queue.remove(message_id)

    def get_messaging_stats(self) -> Dict[str, Any]:
        """Get messaging system statistics."""
        total_messages = len(self.messages)
        status_counts = defaultdict(int)
        
        for message in self.messages.values():
            status_counts[message.status.value] += 1
        
        return {
            "total_messages": total_messages,
            "status_counts": dict(status_counts),
            "active_conversations": len(self.active_conversations),
            "message_threads": len(self.message_threads),
            "delivery_stats": dict(self.delivery_stats),
            "queue_sizes": {
                priority.value: len(queue) 
                for priority, queue in self.delivery_queues.items()
            }
        }

    async def shutdown(self):
        """Shutdown the messaging system."""
        if self._delivery_task:
            self._delivery_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        logger.info("InterAgentMessagingSystem shutdown")


# Global messaging system instance
messaging_system = InterAgentMessagingSystem()
