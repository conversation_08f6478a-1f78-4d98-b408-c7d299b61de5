"""
Capability Registry

Registry system for managing and tracking agent capabilities in the marketplace.
Provides capability discovery, validation, and metadata management.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import json

from ..events.event_bus import event_bus, LangGraphEvent
from ..monitoring.metrics import MetricsCollector

logger = logging.getLogger(__name__)


class CapabilityType(Enum):
    """Types of capabilities."""
    DATA_ANALYSIS = "data_analysis"
    CONTENT_GENERATION = "content_generation"
    DECISION_MAKING = "decision_making"
    AUTOMATION = "automation"
    INTEGRATION = "integration"
    MONITORING = "monitoring"
    OPTIMIZATION = "optimization"
    COMMUNICATION = "communication"


@dataclass
class CapabilityMetadata:
    """Metadata for a capability."""
    capability_id: str
    name: str
    description: str
    capability_type: CapabilityType
    version: str
    agent_id: str
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # Technical specifications
    input_schema: Dict[str, Any] = field(default_factory=dict)
    output_schema: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    resource_requirements: Dict[str, Any] = field(default_factory=dict)
    
    # Performance characteristics
    average_execution_time: float = 0.0
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    success_rate: float = 0.0
    
    # Business information
    tags: List[str] = field(default_factory=list)
    categories: List[str] = field(default_factory=list)
    use_cases: List[str] = field(default_factory=list)
    
    # Compatibility and constraints
    compatible_agents: List[str] = field(default_factory=list)
    incompatible_agents: List[str] = field(default_factory=list)
    constraints: Dict[str, Any] = field(default_factory=dict)
    
    # Marketplace information
    is_public: bool = True
    is_certified: bool = False
    certification_level: str = "basic"
    pricing_model: str = "free"


class CapabilityRegistry:
    """
    Registry for managing agent capabilities in the marketplace.
    
    Features:
    - Capability registration and discovery
    - Metadata management and validation
    - Compatibility checking
    - Performance tracking
    - Version management
    """
    
    def __init__(self):
        self.capabilities: Dict[str, CapabilityMetadata] = {}
        self.agent_capabilities: Dict[str, Set[str]] = {}  # agent_id -> capability_ids
        self.type_index: Dict[CapabilityType, Set[str]] = {}  # type -> capability_ids
        self.tag_index: Dict[str, Set[str]] = {}  # tag -> capability_ids
        self.metrics = MetricsCollector("capability_registry")
        
        # Initialize type index
        for cap_type in CapabilityType:
            self.type_index[cap_type] = set()
    
    async def initialize(self):
        """Initialize the capability registry."""
        try:
            # Load existing capabilities from storage
            await self._load_capabilities()
            
            # Setup event handlers
            self._setup_event_handlers()
            
            # Start background tasks
            asyncio.create_task(self._registry_maintenance_loop())
            
            logger.info("Capability registry initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize capability registry: {e}")
            raise
    
    def _setup_event_handlers(self):
        """Setup event handlers for registry operations."""
        event_bus.subscribe("agent.registered", self._handle_agent_registration)
        event_bus.subscribe("agent.capability_added", self._handle_capability_addition)
        event_bus.subscribe("agent.capability_removed", self._handle_capability_removal)
        event_bus.subscribe("workflow.capability_used", self._handle_capability_usage)
    
    async def register_capability(self, capability_data: Any) -> str:
        """
        Register a new capability in the registry.
        
        Args:
            capability_data: Capability data (can be CapabilityListing or dict)
            
        Returns:
            Capability ID
        """
        try:
            # Convert capability_data to metadata
            if hasattr(capability_data, 'capability_id'):
                # It's a CapabilityListing
                metadata = CapabilityMetadata(
                    capability_id=capability_data.capability_id,
                    name=capability_data.name,
                    description=capability_data.description,
                    capability_type=CapabilityType(capability_data.category.lower().replace(" ", "_")),
                    version=capability_data.version,
                    agent_id=capability_data.agent_id,
                    tags=capability_data.tags,
                    is_certified=capability_data.certification_level != "basic",
                    certification_level=capability_data.certification_level,
                    pricing_model=capability_data.trading_mode.value if hasattr(capability_data.trading_mode, 'value') else "free"
                )
            else:
                # It's a dictionary
                metadata = CapabilityMetadata(
                    capability_id=capability_data["capability_id"],
                    name=capability_data["name"],
                    description=capability_data.get("description", ""),
                    capability_type=CapabilityType(capability_data.get("type", "automation")),
                    version=capability_data.get("version", "1.0.0"),
                    agent_id=capability_data["agent_id"],
                    tags=capability_data.get("tags", []),
                    input_schema=capability_data.get("input_schema", {}),
                    output_schema=capability_data.get("output_schema", {}),
                    dependencies=capability_data.get("dependencies", []),
                    resource_requirements=capability_data.get("resource_requirements", {}),
                    use_cases=capability_data.get("use_cases", []),
                    compatible_agents=capability_data.get("compatible_agents", []),
                    incompatible_agents=capability_data.get("incompatible_agents", []),
                    constraints=capability_data.get("constraints", {}),
                    is_public=capability_data.get("is_public", True),
                    is_certified=capability_data.get("is_certified", False),
                    certification_level=capability_data.get("certification_level", "basic"),
                    pricing_model=capability_data.get("pricing_model", "free")
                )
            
            # Validate capability
            if not self._validate_capability(metadata):
                raise ValueError("Invalid capability data")
            
            # Store capability
            self.capabilities[metadata.capability_id] = metadata
            
            # Update indexes
            self._update_indexes(metadata)
            
            # Publish registration event
            await event_bus.publish(LangGraphEvent(
                event_type="registry.capability_registered",
                timestamp=datetime.now(),
                source="capability_registry",
                data={
                    "capability_id": metadata.capability_id,
                    "agent_id": metadata.agent_id,
                    "name": metadata.name,
                    "type": metadata.capability_type.value,
                    "version": metadata.version
                }
            ))
            
            # Update metrics
            self.metrics.increment("capabilities_registered")
            
            logger.info(f"Registered capability {metadata.name} for agent {metadata.agent_id}")
            return metadata.capability_id
            
        except Exception as e:
            logger.error(f"Failed to register capability: {e}")
            raise
    
    async def discover_capabilities(self, 
                                  filters: Optional[Dict[str, Any]] = None,
                                  include_private: bool = False) -> List[CapabilityMetadata]:
        """
        Discover capabilities based on filters.
        
        Args:
            filters: Optional filters for discovery
            include_private: Whether to include private capabilities
            
        Returns:
            List of matching capabilities
        """
        try:
            # Start with all public capabilities
            candidates = [
                cap for cap in self.capabilities.values()
                if include_private or cap.is_public
            ]
            
            # Apply filters
            if filters:
                candidates = self._apply_discovery_filters(candidates, filters)
            
            # Update metrics
            self.metrics.increment("capability_discoveries")
            
            logger.info(f"Discovered {len(candidates)} capabilities with filters: {filters}")
            return candidates
            
        except Exception as e:
            logger.error(f"Failed to discover capabilities: {e}")
            raise
    
    async def get_capability(self, capability_id: str) -> Optional[CapabilityMetadata]:
        """Get a specific capability by ID."""
        return self.capabilities.get(capability_id)
    
    async def get_agent_capabilities(self, agent_id: str) -> List[CapabilityMetadata]:
        """Get all capabilities for a specific agent."""
        capability_ids = self.agent_capabilities.get(agent_id, set())
        return [self.capabilities[cap_id] for cap_id in capability_ids if cap_id in self.capabilities]
    
    async def check_compatibility(self, capability_id: str, agent_id: str) -> bool:
        """Check if a capability is compatible with an agent."""
        if capability_id not in self.capabilities:
            return False
        
        capability = self.capabilities[capability_id]
        
        # Check explicit compatibility lists
        if capability.compatible_agents and agent_id not in capability.compatible_agents:
            return False
        
        if agent_id in capability.incompatible_agents:
            return False
        
        return True
    
    async def update_capability_performance(self, capability_id: str, performance_data: Dict[str, float]):
        """Update performance metrics for a capability."""
        if capability_id not in self.capabilities:
            return
        
        capability = self.capabilities[capability_id]
        capability.average_execution_time = performance_data.get("execution_time", capability.average_execution_time)
        capability.memory_usage = performance_data.get("memory_usage", capability.memory_usage)
        capability.cpu_usage = performance_data.get("cpu_usage", capability.cpu_usage)
        capability.success_rate = performance_data.get("success_rate", capability.success_rate)
        capability.updated_at = datetime.now()
        
        logger.info(f"Updated performance metrics for capability {capability_id}")
    
    def _validate_capability(self, metadata: CapabilityMetadata) -> bool:
        """Validate capability metadata."""
        # Check required fields
        if not all([metadata.capability_id, metadata.name, metadata.agent_id]):
            return False
        
        # Check for duplicate capability ID
        if metadata.capability_id in self.capabilities:
            return False
        
        # Validate schemas if provided
        if metadata.input_schema and not isinstance(metadata.input_schema, dict):
            return False
        
        if metadata.output_schema and not isinstance(metadata.output_schema, dict):
            return False
        
        return True
    
    def _update_indexes(self, metadata: CapabilityMetadata):
        """Update registry indexes."""
        # Update agent capabilities index
        if metadata.agent_id not in self.agent_capabilities:
            self.agent_capabilities[metadata.agent_id] = set()
        self.agent_capabilities[metadata.agent_id].add(metadata.capability_id)
        
        # Update type index
        self.type_index[metadata.capability_type].add(metadata.capability_id)
        
        # Update tag index
        for tag in metadata.tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = set()
            self.tag_index[tag].add(metadata.capability_id)
    
    def _apply_discovery_filters(self, capabilities: List[CapabilityMetadata], 
                               filters: Dict[str, Any]) -> List[CapabilityMetadata]:
        """Apply filters to capability discovery."""
        filtered = capabilities
        
        if "type" in filters:
            cap_type = CapabilityType(filters["type"])
            filtered = [c for c in filtered if c.capability_type == cap_type]
        
        if "agent_id" in filters:
            filtered = [c for c in filtered if c.agent_id == filters["agent_id"]]
        
        if "tags" in filters:
            required_tags = set(filters["tags"])
            filtered = [c for c in filtered if required_tags.issubset(set(c.tags))]
        
        if "certified_only" in filters and filters["certified_only"]:
            filtered = [c for c in filtered if c.is_certified]
        
        if "min_success_rate" in filters:
            filtered = [c for c in filtered if c.success_rate >= filters["min_success_rate"]]
        
        if "max_execution_time" in filters:
            filtered = [c for c in filtered if c.average_execution_time <= filters["max_execution_time"]]
        
        return filtered

    # Event Handlers
    async def _handle_agent_registration(self, event: LangGraphEvent):
        """Handle agent registration events."""
        try:
            agent_id = event.data.get("agent_id")
            if agent_id and agent_id not in self.agent_capabilities:
                self.agent_capabilities[agent_id] = set()
                logger.info(f"Initialized capability tracking for agent {agent_id}")
        except Exception as e:
            logger.error(f"Error handling agent registration: {e}")

    async def _handle_capability_addition(self, event: LangGraphEvent):
        """Handle capability addition events."""
        try:
            capability_data = event.data
            await self.register_capability(capability_data)
        except Exception as e:
            logger.error(f"Error handling capability addition: {e}")

    async def _handle_capability_removal(self, event: LangGraphEvent):
        """Handle capability removal events."""
        try:
            capability_id = event.data.get("capability_id")
            if capability_id in self.capabilities:
                await self._remove_capability(capability_id)
        except Exception as e:
            logger.error(f"Error handling capability removal: {e}")

    async def _handle_capability_usage(self, event: LangGraphEvent):
        """Handle capability usage events for tracking."""
        try:
            capability_id = event.data.get("capability_id")
            performance_data = event.data.get("performance", {})

            if capability_id and performance_data:
                await self.update_capability_performance(capability_id, performance_data)
        except Exception as e:
            logger.error(f"Error handling capability usage: {e}")

    # Background Tasks
    async def _registry_maintenance_loop(self):
        """Background task for registry maintenance."""
        while True:
            try:
                await asyncio.sleep(600)  # Run every 10 minutes

                # Clean up orphaned capabilities
                await self._cleanup_orphaned_capabilities()

                # Update capability statistics
                await self._update_capability_statistics()

                # Validate capability health
                await self._validate_capability_health()

            except Exception as e:
                logger.error(f"Error in registry maintenance: {e}")

    async def _cleanup_orphaned_capabilities(self):
        """Clean up capabilities for non-existent agents."""
        # This would check against the agent registry
        # For now, we'll just log the check
        logger.debug("Checking for orphaned capabilities")

    async def _update_capability_statistics(self):
        """Update capability statistics and metrics."""
        total_capabilities = len(self.capabilities)
        certified_capabilities = len([c for c in self.capabilities.values() if c.is_certified])
        public_capabilities = len([c for c in self.capabilities.values() if c.is_public])

        self.metrics.set_gauge("total_capabilities", total_capabilities)
        self.metrics.set_gauge("certified_capabilities", certified_capabilities)
        self.metrics.set_gauge("public_capabilities", public_capabilities)

        # Update type distribution
        for cap_type in CapabilityType:
            count = len(self.type_index[cap_type])
            self.metrics.set_gauge(f"capabilities_by_type_{cap_type.value}", count)

    async def _validate_capability_health(self):
        """Validate health of registered capabilities."""
        current_time = datetime.now()
        stale_threshold = timedelta(days=30)

        for capability in self.capabilities.values():
            time_since_update = current_time - capability.updated_at
            if time_since_update > stale_threshold:
                logger.warning(f"Capability {capability.capability_id} hasn't been updated in {time_since_update.days} days")

    async def _remove_capability(self, capability_id: str):
        """Remove a capability from the registry."""
        if capability_id not in self.capabilities:
            return

        capability = self.capabilities[capability_id]

        # Remove from main storage
        del self.capabilities[capability_id]

        # Update indexes
        self.agent_capabilities[capability.agent_id].discard(capability_id)
        self.type_index[capability.capability_type].discard(capability_id)

        for tag in capability.tags:
            if tag in self.tag_index:
                self.tag_index[tag].discard(capability_id)
                if not self.tag_index[tag]:  # Remove empty tag index
                    del self.tag_index[tag]

        # Publish removal event
        await event_bus.publish(LangGraphEvent(
            event_type="registry.capability_removed",
            timestamp=datetime.now(),
            source="capability_registry",
            data={
                "capability_id": capability_id,
                "agent_id": capability.agent_id,
                "name": capability.name
            }
        ))

        logger.info(f"Removed capability {capability_id}")

    async def _load_capabilities(self):
        """Load existing capabilities from storage."""
        # This would load from database in production
        # For now, we'll start with empty state
        logger.info("Capability registry data loaded")

    # Utility Methods
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        return {
            "total_capabilities": len(self.capabilities),
            "total_agents": len(self.agent_capabilities),
            "certified_capabilities": len([c for c in self.capabilities.values() if c.is_certified]),
            "public_capabilities": len([c for c in self.capabilities.values() if c.is_public]),
            "capabilities_by_type": {
                cap_type.value: len(capability_ids)
                for cap_type, capability_ids in self.type_index.items()
            },
            "top_tags": self._get_top_tags(),
            "average_success_rate": self._calculate_average_success_rate(),
            "most_active_agents": self._get_most_active_agents()
        }

    def _get_top_tags(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most popular tags."""
        tag_counts = [(tag, len(capability_ids)) for tag, capability_ids in self.tag_index.items()]
        tag_counts.sort(key=lambda x: x[1], reverse=True)
        return [{"tag": tag, "count": count} for tag, count in tag_counts[:limit]]

    def _calculate_average_success_rate(self) -> float:
        """Calculate average success rate across all capabilities."""
        if not self.capabilities:
            return 0.0

        total_rate = sum(cap.success_rate for cap in self.capabilities.values())
        return total_rate / len(self.capabilities)

    def _get_most_active_agents(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get agents with most capabilities."""
        agent_counts = [(agent_id, len(capability_ids))
                       for agent_id, capability_ids in self.agent_capabilities.items()]
        agent_counts.sort(key=lambda x: x[1], reverse=True)
        return [{"agent_id": agent_id, "capability_count": count}
                for agent_id, count in agent_counts[:limit]]
