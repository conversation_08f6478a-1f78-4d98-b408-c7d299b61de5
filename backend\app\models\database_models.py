"""
Database Models for Repository Pattern.

Re-exports SQLAlchemy models from database.py for use with repositories.
This module provides a clean interface for importing database models.
"""

# Import SQLAlchemy models from database.py
from ..database import (
    # Core models
    User,
    Conversation,
    Message,
    BusinessProfile,
    
    # File and task models
    File,
    Task,
    
    # Persona and marketplace models
    Persona,
    PersonaVersion,
    Purchase,
    CartItem,
    
    # Data source models
    DataSource,
    BusinessProfileDataSource,
    
    # Provider models
    ProviderApiKey,
    
    # Admin models
    AdminActivityLog,
    
    # Dashboard models (if they exist)
    # Dashboard,
    # DashboardSection,
    # DashboardWidget,
    
    # Search models
    # UserSearchActivity,
    
    # MCP models
    MCPServer,
    MCPTool,
    MCPResource,
    MCPPrompt,
    MCPInputVariable,
    
    # Subscription models
    # Subscription,
    # PricingTier,
    
    # Base class
    Base
)

# Export all models for easy importing
__all__ = [
    # Core models
    "User",
    "Conversation", 
    "Message",
    "BusinessProfile",
    
    # File and task models
    "File",
    "Task",
    
    # Persona and marketplace models
    "Persona",
    "PersonaVersion", 
    "Purchase",
    "CartItem",
    
    # Data source models
    "DataSource",
    "BusinessProfileDataSource",
    
    # Provider models
    "ProviderApiKey",
    
    # Admin models
    "AdminActivityLog",
    
    # MCP models
    "MCPServer",
    "MCPTool",
    "MCPResource", 
    "MCPPrompt",
    "MCPInputVariable",
    
    # Base class
    "Base"
]
