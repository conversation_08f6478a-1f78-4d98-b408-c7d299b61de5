"""
Privacy-Compliant Logging Service for Phase 4 Privacy & Compliance.

This module provides privacy-compliant logging with user consent tracking,
data retention policies, and automatic PII sanitization.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from enum import Enum
from dataclasses import dataclass, field
from app.security.business_profile_security import sanitize_log_data
from app.utils.ip_utils import anonymize_ip_for_logging

logger = logging.getLogger(__name__)


class ConsentType(str, Enum):
    """Types of user consent."""
    ANALYTICS = "analytics"
    MARKETING = "marketing"
    FUNCTIONAL = "functional"
    PERFORMANCE = "performance"
    CONVERSATION_LOGGING = "conversation_logging"
    DATA_PROCESSING = "data_processing"
    CROSS_AGENT_SHARING = "cross_agent_sharing"


class LogLevel(str, Enum):
    """Privacy-aware log levels."""
    PUBLIC = "public"  # No PII, safe to log
    INTERNAL = "internal"  # May contain business data, sanitize
    SENSITIVE = "sensitive"  # Contains PII, requires consent
    RESTRICTED = "restricted"  # Highly sensitive, minimal logging


@dataclass
class UserConsent:
    """User consent record."""
    user_id: str
    consent_types: Set[ConsentType] = field(default_factory=set)
    granted_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    consent_version: str = "1.0"
    
    def is_valid(self) -> bool:
        """Check if consent is still valid."""
        if self.expires_at and datetime.now() > self.expires_at:
            return False
        return True
    
    def has_consent(self, consent_type: ConsentType) -> bool:
        """Check if user has given specific consent."""
        return self.is_valid() and consent_type in self.consent_types


@dataclass
class RetentionPolicy:
    """Data retention policy configuration."""
    log_type: str
    retention_days: int
    auto_delete: bool = True
    anonymize_after_days: Optional[int] = None
    requires_consent: bool = False
    consent_types: Set[ConsentType] = field(default_factory=set)


class PrivacyCompliantLogger:
    """Privacy-compliant logging service."""
    
    def __init__(self):
        """Initialize the privacy-compliant logger."""
        self.user_consents: Dict[str, UserConsent] = {}
        self.retention_policies = self._initialize_retention_policies()
        self.log_buffer: List[Dict[str, Any]] = []
        self.max_buffer_size = 1000
        
    def _initialize_retention_policies(self) -> Dict[str, RetentionPolicy]:
        """Initialize default retention policies."""
        return {
            "conversation": RetentionPolicy(
                log_type="conversation",
                retention_days=365,  # 1 year
                anonymize_after_days=90,
                requires_consent=True,
                consent_types={ConsentType.CONVERSATION_LOGGING}
            ),
            "user_activity": RetentionPolicy(
                log_type="user_activity",
                retention_days=90,
                anonymize_after_days=30,
                requires_consent=True,
                consent_types={ConsentType.ANALYTICS}
            ),
            "security": RetentionPolicy(
                log_type="security",
                retention_days=2555,  # 7 years for compliance
                anonymize_after_days=365,
                requires_consent=False
            ),
            "performance": RetentionPolicy(
                log_type="performance",
                retention_days=30,
                requires_consent=True,
                consent_types={ConsentType.PERFORMANCE}
            ),
            "error": RetentionPolicy(
                log_type="error",
                retention_days=90,
                anonymize_after_days=30,
                requires_consent=False
            ),
            "audit": RetentionPolicy(
                log_type="audit",
                retention_days=2555,  # 7 years for compliance
                requires_consent=False
            )
        }
    
    def update_user_consent(
        self,
        user_id: str,
        consent_types: Set[ConsentType],
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        expires_at: Optional[datetime] = None
    ) -> None:
        """
        Update user consent preferences.
        
        Args:
            user_id: User identifier
            consent_types: Set of consent types granted
            ip_address: User's IP address
            user_agent: User's user agent
            expires_at: Optional expiration date
        """
        self.user_consents[user_id] = UserConsent(
            user_id=user_id,
            consent_types=consent_types,
            ip_address=anonymize_ip_for_logging(ip_address) if ip_address else None,
            user_agent=user_agent,
            expires_at=expires_at
        )
        
        self.log_privacy_event(
            event_type="consent_updated",
            user_id=user_id,
            data={
                "consent_types": [ct.value for ct in consent_types],
                "expires_at": expires_at.isoformat() if expires_at else None
            },
            log_level=LogLevel.INTERNAL
        )
    
    def revoke_user_consent(self, user_id: str, consent_types: Set[ConsentType]) -> None:
        """
        Revoke specific user consent types.
        
        Args:
            user_id: User identifier
            consent_types: Set of consent types to revoke
        """
        if user_id in self.user_consents:
            self.user_consents[user_id].consent_types -= consent_types
            
            self.log_privacy_event(
                event_type="consent_revoked",
                user_id=user_id,
                data={
                    "revoked_consent_types": [ct.value for ct in consent_types]
                },
                log_level=LogLevel.INTERNAL
            )
    
    def has_user_consent(self, user_id: str, consent_type: ConsentType) -> bool:
        """
        Check if user has given specific consent.
        
        Args:
            user_id: User identifier
            consent_type: Type of consent to check
            
        Returns:
            True if user has given consent
        """
        if user_id not in self.user_consents:
            return False
        
        return self.user_consents[user_id].has_consent(consent_type)
    
    def log_privacy_event(
        self,
        event_type: str,
        user_id: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        log_level: LogLevel = LogLevel.INTERNAL,
        log_type: str = "general"
    ) -> None:
        """
        Log an event with privacy compliance.
        
        Args:
            event_type: Type of event
            user_id: Optional user identifier
            data: Optional event data
            log_level: Privacy level of the log
            log_type: Type of log for retention policy
        """
        # Check if logging requires consent
        policy = self.retention_policies.get(log_type)
        if policy and policy.requires_consent and user_id:
            # Check if user has given required consent
            has_consent = any(
                self.has_user_consent(user_id, consent_type)
                for consent_type in policy.consent_types
            )
            
            if not has_consent:
                # Log without user data if no consent
                self._log_without_user_data(event_type, log_type)
                return
        
        # Sanitize data based on log level
        sanitized_data = self._sanitize_log_data(data, log_level)
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "user_id": user_id if log_level != LogLevel.PUBLIC else None,
            "data": sanitized_data,
            "log_level": log_level.value,
            "log_type": log_type
        }
        
        # Add to buffer
        self.log_buffer.append(log_entry)
        
        # Trim buffer if needed
        if len(self.log_buffer) > self.max_buffer_size:
            self.log_buffer = self.log_buffer[-self.max_buffer_size:]
        
        # Log to standard logger
        logger.info(f"PRIVACY_LOG: {json.dumps(log_entry)}")
    
    def log_conversation_event(
        self,
        user_id: str,
        conversation_id: str,
        event_type: str,
        data: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log conversation-related events with consent checking.
        
        Args:
            user_id: User identifier
            conversation_id: Conversation identifier
            event_type: Type of conversation event
            data: Optional event data
        """
        if not self.has_user_consent(user_id, ConsentType.CONVERSATION_LOGGING):
            # Log anonymized version without user data
            self.log_privacy_event(
                event_type=f"conversation_{event_type}_anonymous",
                data={"conversation_count": 1},
                log_level=LogLevel.PUBLIC,
                log_type="conversation"
            )
            return
        
        conversation_data = {
            "conversation_id": conversation_id,
            **(data or {})
        }
        
        self.log_privacy_event(
            event_type=f"conversation_{event_type}",
            user_id=user_id,
            data=conversation_data,
            log_level=LogLevel.SENSITIVE,
            log_type="conversation"
        )
    
    def log_cross_agent_sharing(
        self,
        user_id: str,
        source_agent: str,
        target_agent: str,
        data_type: str,
        data_summary: Optional[str] = None
    ) -> None:
        """
        Log cross-agent data sharing events.
        
        Args:
            user_id: User identifier
            source_agent: Source agent identifier
            target_agent: Target agent identifier
            data_type: Type of data being shared
            data_summary: Optional summary of shared data
        """
        if not self.has_user_consent(user_id, ConsentType.CROSS_AGENT_SHARING):
            # Log without user identification
            self.log_privacy_event(
                event_type="cross_agent_sharing_no_consent",
                data={
                    "source_agent": source_agent,
                    "target_agent": target_agent,
                    "data_type": data_type
                },
                log_level=LogLevel.INTERNAL,
                log_type="user_activity"
            )
            return
        
        self.log_privacy_event(
            event_type="cross_agent_sharing",
            user_id=user_id,
            data={
                "source_agent": source_agent,
                "target_agent": target_agent,
                "data_type": data_type,
                "data_summary": data_summary
            },
            log_level=LogLevel.SENSITIVE,
            log_type="user_activity"
        )
    
    def _sanitize_log_data(self, data: Optional[Dict[str, Any]], log_level: LogLevel) -> Optional[Dict[str, Any]]:
        """
        Sanitize log data based on privacy level.
        
        Args:
            data: Data to sanitize
            log_level: Privacy level
            
        Returns:
            Sanitized data
        """
        if not data:
            return data
        
        if log_level == LogLevel.PUBLIC:
            # Remove all potentially sensitive data
            return {k: v for k, v in data.items() if k in ['count', 'type', 'status']}
        elif log_level == LogLevel.INTERNAL:
            # Use existing sanitization
            return sanitize_log_data(data)
        elif log_level == LogLevel.SENSITIVE:
            # Light sanitization, preserve more data
            return {k: sanitize_log_data(v) if isinstance(v, (str, dict)) else v for k, v in data.items()}
        else:  # RESTRICTED
            # Minimal logging
            return {"event_occurred": True}
    
    def _log_without_user_data(self, event_type: str, log_type: str) -> None:
        """Log event without user data when consent is not given."""
        self.log_privacy_event(
            event_type=f"{event_type}_no_consent",
            data={"event_occurred": True},
            log_level=LogLevel.PUBLIC,
            log_type=log_type
        )
    
    def get_user_data_summary(self, user_id: str) -> Dict[str, Any]:
        """
        Get summary of logged data for a user (for GDPR compliance).
        
        Args:
            user_id: User identifier
            
        Returns:
            Summary of user's logged data
        """
        user_logs = [log for log in self.log_buffer if log.get("user_id") == user_id]
        
        return {
            "user_id": user_id,
            "total_log_entries": len(user_logs),
            "log_types": list(set(log.get("log_type", "unknown") for log in user_logs)),
            "date_range": {
                "earliest": min((log["timestamp"] for log in user_logs), default=None),
                "latest": max((log["timestamp"] for log in user_logs), default=None)
            },
            "consent_status": self.user_consents.get(user_id).__dict__ if user_id in self.user_consents else None
        }
    
    def delete_user_data(self, user_id: str) -> Dict[str, Any]:
        """
        Delete all logged data for a user (for GDPR compliance).
        
        Args:
            user_id: User identifier
            
        Returns:
            Summary of deleted data
        """
        # Count logs before deletion
        user_logs_count = len([log for log in self.log_buffer if log.get("user_id") == user_id])
        
        # Remove user logs from buffer
        self.log_buffer = [log for log in self.log_buffer if log.get("user_id") != user_id]
        
        # Remove user consent
        if user_id in self.user_consents:
            del self.user_consents[user_id]
        
        # Log the deletion
        self.log_privacy_event(
            event_type="user_data_deleted",
            data={
                "deleted_user_id": user_id,
                "deleted_log_count": user_logs_count
            },
            log_level=LogLevel.INTERNAL,
            log_type="audit"
        )
        
        return {
            "user_id": user_id,
            "deleted_log_entries": user_logs_count,
            "deletion_timestamp": datetime.now().isoformat()
        }


# Global privacy-compliant logger instance
privacy_logger = PrivacyCompliantLogger()
