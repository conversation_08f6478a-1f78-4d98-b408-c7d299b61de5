"""
Test script for dynamic agent discovery system.

This script tests the automatic agent discovery without hardcoded patterns.
"""

import logging
import sys
from pathlib import Path

# Add the backend directory to the path
backend_dir = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(backend_dir))

from agents.langgraph.core.dynamic_agent_discovery import DynamicAgentDiscovery

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def test_dynamic_discovery():
    """Test the dynamic agent discovery system."""
    try:
        logger.info("🔍 Testing Dynamic Agent Discovery System")
        logger.info("=" * 50)
        
        # Initialize discovery system
        discovery = DynamicAgentDiscovery()
        
        # Test configuration loading
        logger.info(f"📋 Configuration loaded: {discovery.config is not None}")
        logger.info(f"📁 Agents directory: {discovery.agents_directory}")
        logger.info(f"⚙️ Config path: {discovery.config_path}")
        
        # Discover agents
        logger.info("\n🔎 Starting agent discovery...")
        discovered_agents = discovery.discover_agents()
        
        # Report results
        logger.info(f"\n✅ Discovery completed!")
        logger.info(f"📊 Total agents discovered: {len(discovered_agents)}")
        
        if discovered_agents:
            logger.info("\n📋 Discovered Agents:")
            logger.info("-" * 30)
            
            for agent_id, config in discovered_agents.items():
                logger.info(f"🤖 Agent ID: {agent_id}")
                logger.info(f"   Type: {config.get('agent_type', 'unknown')}")
                logger.info(f"   Name: {config.get('name', 'Unknown')}")
                logger.info(f"   Class: {config.get('class_name', 'Unknown')}")
                logger.info(f"   Module: {config.get('module_path', 'Unknown')}")
                logger.info(f"   Discovery: {config.get('discovery_method', 'Unknown')}")
                logger.info("")
        else:
            logger.warning("⚠️ No agents were discovered!")
        
        # Test agent class retrieval
        logger.info("🧪 Testing agent class retrieval...")
        available_agents = discovery.get_available_agents()
        logger.info(f"📝 Available agent IDs: {available_agents}")
        
        for agent_id in available_agents:
            agent_class = discovery.get_agent_class(agent_id)
            if agent_class:
                logger.info(f"✅ {agent_id}: {agent_class.__name__} loaded successfully")
            else:
                logger.error(f"❌ {agent_id}: Failed to load agent class")
        
        # Test automatic ID generation
        logger.info("\n🏷️ Testing automatic ID generation...")
        test_class_names = [
            "MarketingPersona",
            "ConciergePersona",
            "AnalysisPersona",
            "VisualizationPersona",
            "ClassificationPersona"
        ]
        
        for class_name in test_class_names:
            agent_id = discovery._class_name_to_agent_id(class_name)
            logger.info(f"   {class_name} -> {agent_id}")
        
        logger.info("\n🎉 Dynamic discovery test completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during discovery test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_loading():
    """Test configuration loading."""
    try:
        logger.info("\n📋 Testing configuration loading...")
        
        discovery = DynamicAgentDiscovery()
        config = discovery.config
        
        # Check key configuration sections
        sections = ["discovery", "agent_selection", "id_generation", "config"]
        for section in sections:
            if section in config:
                logger.info(f"✅ {section}: Found")
            else:
                logger.warning(f"⚠️ {section}: Missing")
        
        # Check discovery settings
        discovery_config = config.get("discovery", {})
        logger.info(f"🔍 Auto-discover enabled: {discovery_config.get('auto_discover', False)}")
        logger.info(f"🔍 Discovery method: {discovery_config.get('discovery_method', 'unknown')}")
        
        # Check selection strategy
        selection_config = config.get("agent_selection", {})
        logger.info(f"🎯 Selection strategy: {selection_config.get('strategy', 'unknown')}")
        
        # Check ID generation
        id_config = config.get("id_generation", {})
        logger.info(f"🏷️ ID generation strategy: {id_config.get('strategy', 'unknown')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing config: {e}")
        return False


if __name__ == "__main__":
    logger.info("🚀 Starting Dynamic Agent Discovery Tests")
    logger.info("=" * 60)
    
    # Test configuration loading
    config_success = test_config_loading()
    
    # Test dynamic discovery
    discovery_success = test_dynamic_discovery()
    
    # Summary
    logger.info("\n📊 Test Summary")
    logger.info("=" * 20)
    logger.info(f"Configuration Loading: {'✅ PASS' if config_success else '❌ FAIL'}")
    logger.info(f"Dynamic Discovery: {'✅ PASS' if discovery_success else '❌ FAIL'}")
    
    if config_success and discovery_success:
        logger.info("\n🎉 All tests passed! Dynamic discovery is working correctly.")
        sys.exit(0)
    else:
        logger.error("\n❌ Some tests failed. Check the logs above for details.")
        sys.exit(1)
