"""
Tool Execution Event Handler for LangGraph System.

This handler processes tool execution events and converts them to WebSocket messages
for real-time tool usage indicators in the chat interface.
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

from ..event_bus import LangGraphEvent
from ..types import (
    ToolExecutionStartedEvent,
    ToolExecutionProgressEvent,
    ToolExecutionCompletedEvent,
    ToolExecutionFailedEvent
)

logger = logging.getLogger(__name__)


class ToolExecutionEventHandler:
    """
    Handler for tool execution events that converts them to WebSocket messages
    for real-time tool usage indicators in the chat interface.
    """
    
    def __init__(self):
        self.active_tool_executions: Dict[str, Dict[str, Any]] = {}
        self.chat_manager = None
        
    async def initialize(self):
        """Initialize the handler and get chat manager reference."""
        try:
            # Import here to avoid circular imports
            from app.api.chat import manager as chat_manager
            self.chat_manager = chat_manager
            logger.info("ToolExecutionEventHandler initialized successfully")
        except ImportError as e:
            logger.warning(f"Could not import chat manager: {e}")
            self.chat_manager = None
    
    async def handle_tool_execution_started(self, event: ToolExecutionStartedEvent):
        """Handle tool execution started events."""
        try:
            data = event.data
            execution_key = f"{data['conversation_id']}:{data['message_id']}:{data['tool_name']}"
            
            # Track active execution
            self.active_tool_executions[execution_key] = {
                "tool_name": data["tool_name"],
                "agent_type": data["agent_type"],
                "start_time": datetime.now(),
                "status": "starting"
            }
            
            # Send WebSocket message to chat interface
            if self.chat_manager:
                ws_message = {
                    "type": "tool_execution_start",
                    "message_id": data["message_id"],
                    "conversation_id": data["conversation_id"],
                    "tool_name": data["tool_name"],
                    "agent_type": data["agent_type"],
                    "status": "starting",
                    "timestamp": data["start_time"]
                }
                
                await self.chat_manager.broadcast(ws_message, data["conversation_id"])
                logger.debug(f"Sent tool execution start notification for {data['tool_name']}")
            
        except Exception as e:
            logger.error(f"Error handling tool execution started event: {e}")
    
    async def handle_tool_execution_progress(self, event: ToolExecutionProgressEvent):
        """Handle tool execution progress events."""
        try:
            data = event.data
            execution_key = f"{data['conversation_id']}:{data['message_id']}:{data['tool_name']}"
            
            # Update active execution
            if execution_key in self.active_tool_executions:
                self.active_tool_executions[execution_key].update({
                    "status": "running",
                    "progress": data.get("progress"),
                    "status_message": data.get("status_message")
                })
            
            # Send WebSocket message to chat interface
            if self.chat_manager:
                ws_message = {
                    "type": "tool_execution_progress",
                    "message_id": data["message_id"],
                    "conversation_id": data["conversation_id"],
                    "tool_name": data["tool_name"],
                    "agent_type": data["agent_type"],
                    "status": "running",
                    "progress": data.get("progress"),
                    "message": data.get("status_message"),
                    "timestamp": data["progress_time"]
                }
                
                await self.chat_manager.broadcast(ws_message, data["conversation_id"])
                logger.debug(f"Sent tool execution progress notification for {data['tool_name']}")
            
        except Exception as e:
            logger.error(f"Error handling tool execution progress event: {e}")
    
    async def handle_tool_execution_completed(self, event: ToolExecutionCompletedEvent):
        """Handle tool execution completed events."""
        try:
            data = event.data
            execution_key = f"{data['conversation_id']}:{data['message_id']}:{data['tool_name']}"
            
            # Update and clean up active execution
            if execution_key in self.active_tool_executions:
                self.active_tool_executions[execution_key].update({
                    "status": "completed",
                    "execution_time": data["execution_time"],
                    "end_time": datetime.now()
                })
            
            # Send WebSocket message to chat interface
            if self.chat_manager:
                ws_message = {
                    "type": "tool_execution_complete",
                    "message_id": data["message_id"],
                    "conversation_id": data["conversation_id"],
                    "tool_name": data["tool_name"],
                    "agent_type": data["agent_type"],
                    "status": "completed",
                    "execution_time": data["execution_time"],
                    "timestamp": data["completion_time"]
                }
                
                await self.chat_manager.broadcast(ws_message, data["conversation_id"])
                logger.debug(f"Sent tool execution completion notification for {data['tool_name']}")
            
            # Clean up after a delay
            asyncio.create_task(self._cleanup_execution(execution_key, delay=3.0))
            
        except Exception as e:
            logger.error(f"Error handling tool execution completed event: {e}")
    
    async def handle_tool_execution_failed(self, event: ToolExecutionFailedEvent):
        """Handle tool execution failed events."""
        try:
            data = event.data
            execution_key = f"{data['conversation_id']}:{data['message_id']}:{data['tool_name']}"
            
            # Update active execution
            if execution_key in self.active_tool_executions:
                self.active_tool_executions[execution_key].update({
                    "status": "error",
                    "error_message": data["error_message"],
                    "execution_time": data.get("execution_time"),
                    "end_time": datetime.now()
                })
            
            # Send WebSocket message to chat interface
            if self.chat_manager:
                ws_message = {
                    "type": "tool_execution_error",
                    "message_id": data["message_id"],
                    "conversation_id": data["conversation_id"],
                    "tool_name": data["tool_name"],
                    "agent_type": data["agent_type"],
                    "status": "error",
                    "error": data["error_message"],
                    "execution_time": data.get("execution_time"),
                    "timestamp": data["failure_time"]
                }
                
                await self.chat_manager.broadcast(ws_message, data["conversation_id"])
                logger.debug(f"Sent tool execution error notification for {data['tool_name']}")
            
            # Clean up after a longer delay for errors
            asyncio.create_task(self._cleanup_execution(execution_key, delay=5.0))
            
        except Exception as e:
            logger.error(f"Error handling tool execution failed event: {e}")
    
    async def _cleanup_execution(self, execution_key: str, delay: float = 3.0):
        """Clean up completed/failed executions after a delay."""
        await asyncio.sleep(delay)
        if execution_key in self.active_tool_executions:
            del self.active_tool_executions[execution_key]
            logger.debug(f"Cleaned up execution tracking for {execution_key}")
    
    def get_active_executions(self) -> Dict[str, Dict[str, Any]]:
        """Get currently active tool executions."""
        return self.active_tool_executions.copy()
    
    async def handle_event(self, event: LangGraphEvent):
        """Main event handler that routes to specific handlers based on event type."""
        try:
            if isinstance(event, ToolExecutionStartedEvent):
                await self.handle_tool_execution_started(event)
            elif isinstance(event, ToolExecutionProgressEvent):
                await self.handle_tool_execution_progress(event)
            elif isinstance(event, ToolExecutionCompletedEvent):
                await self.handle_tool_execution_completed(event)
            elif isinstance(event, ToolExecutionFailedEvent):
                await self.handle_tool_execution_failed(event)
            else:
                logger.debug(f"Unhandled event type: {event.event_type}")
                
        except Exception as e:
            logger.error(f"Error in tool execution event handler: {e}")


# Global handler instance
tool_execution_handler = ToolExecutionEventHandler()
