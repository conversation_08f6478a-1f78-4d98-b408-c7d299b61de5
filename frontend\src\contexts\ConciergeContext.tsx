import React, { createContext, useContext, useState, useEffect, useCallback, useReducer } from 'react';
import { WorkflowStage } from '@/components/chat/WorkflowStageIndicator';
import { Message, Persona, personaApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { createReactSyncApp } from '@/utils/createReactSyncApp';

// Chat message type
interface ChatMessage {
  id: string;
  sender: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ConciergeState {
  stage: WorkflowStage;
  recommendedPersonas: string[];
  currentPersona: Persona | null;
  availablePersonas: Persona[];
  dataAttached: boolean;
  attachedFiles: any[];
  handoffCompleted: boolean;
  followupNeeded: boolean;
  highlights: any[];
  messages: ChatMessage[];
  isTyping: boolean;

  // Phase 2 coordination features
  coordinationChain: string[];
  activeCollaborations: {
    personas: string[];
    task: string;
    status: 'pending' | 'active' | 'completed';
  }[];
  pendingCallbacks: {
    source: string;
    target: string;
    reason: string;
    status: 'pending' | 'seen' | 'responded';
  }[];
  handoffs: {
    source: string;
    target: string;
    reason: string;
    status: 'pending' | 'completed' | 'rejected';
  }[];

  // Phase 3 team and role features
  teamId: string | null;
  teamMembers: {
    persona_id: string;
    role: string;
    joined_at: number;
  }[];
  teamHierarchy: Record<string, string[]>;
  assignedRole: string | null;
  roleCapabilities: string[];
  activeTasks: {
    task_id: string;
    description: string;
    assignee: string;
    status: 'assigned' | 'in_progress' | 'completed' | 'failed';
    created_at: number;
  }[];
  routingStrategy: string | null;
  fallbackChain: string[];
}

interface ConciergeContextType {
  isConciergeActive: boolean;
  conciergeState: ConciergeState | null;
  messages: ChatMessage[];
  sendMessage: (content: string) => void;
  currentPersona: Persona | null;
  switchPersona: (personaId: string) => void;
  isTyping: boolean;
  setConciergeActive: (active: boolean) => void;
  updateConciergeState: (state: Partial<ConciergeState>) => void;
  resetConciergeState: () => void;
  returnToConcierge: () => void;
  onReturnToConcierge?: () => void;
  setOnReturnToConcierge: (callback: () => void) => void;
}

const defaultConciergeState: ConciergeState = {
  stage: 'initial',
  recommendedPersonas: [],
  currentPersona: null,
  availablePersonas: [],
  messages: [],
  isTyping: false,
  dataAttached: false,
  attachedFiles: [],
  handoffCompleted: false,
  followupNeeded: false,
  highlights: [],
  coordinationChain: [],
  activeCollaborations: [],
  pendingCallbacks: [],
  handoffs: [],
  teamId: null,
  teamMembers: [],
  teamHierarchy: {},
  assignedRole: null,
  roleCapabilities: [],
  activeTasks: [],
  routingStrategy: null,
  fallbackChain: []
};

const ConciergeContext = createContext<ConciergeContextType>({
  isConciergeActive: false,
  conciergeState: null,
  messages: [],
  sendMessage: () => {},
  currentPersona: null,
  switchPersona: () => {},
  isTyping: false,
  setConciergeActive: () => {},
  updateConciergeState: () => {},
  resetConciergeState: () => {},
  returnToConcierge: () => {},
  setOnReturnToConcierge: () => {}
});

export const useConcierge = () => useContext(ConciergeContext);

interface ConciergeProviderProps {
  children: React.ReactNode;
}

// Message reducer to handle chat state
function messageReducer(state: ChatMessage[], action: any) {
  switch (action.type) {
    case 'ADD_MESSAGE':
      return [...state, action.payload];
    case 'SET_MESSAGES':
      return action.payload;
    default:
      return state;
  }
}

export const ConciergeProvider: React.FC<ConciergeProviderProps> = ({ children }) => {
  const [isConciergeActive, setIsConciergeActive] = useState<boolean>(false);
  const [conciergeState, setConciergeState] = useState<ConciergeState | null>(null);
  const [messages, dispatchMessages] = useReducer(messageReducer, []);
  const [currentPersona, setCurrentPersona] = useState<Persona | null>(null);
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const [onReturnToConcierge, setOnReturnToConciergeCallback] = useState<(() => void) | undefined>(undefined);
  const { toast } = useToast();

  // Initialize concierge state when activated
  useEffect(() => {
    if (isConciergeActive && !conciergeState) {
      setConciergeState(defaultConciergeState);
    }
  }, [isConciergeActive, conciergeState]);

  const updateConciergeState = (state: Partial<ConciergeState>) => {
    setConciergeState(prev => {
      if (!prev) return { ...defaultConciergeState, ...state };
      return { ...prev, ...state };
    });
  };

  const resetConciergeState = () => {
    setConciergeState(defaultConciergeState);
  };

  const returnToConcierge = useCallback(() => {
    if (onReturnToConcierge) {
      // Schedule the callback for next render cycle to avoid state updates during render
      setTimeout(() => {
        onReturnToConcierge();
      }, 0);
    }
  }, [onReturnToConcierge]);

  // Update concierge state based on messages - using useCallback to prevent unnecessary re-renders
  const updateStateFromMessages = useCallback((messages: Message[]) => {
    if (!isConciergeActive || !conciergeState) return;

    // Check for concierge_state in message metadata
    for (const message of messages) {
      if (message.sender === 'ai' && message.metadata?.concierge_state) {
        // Use a functional update to avoid stale state issues
        setConciergeState(prevState => {
          if (!prevState) return { ...defaultConciergeState, ...message.metadata.concierge_state };
          return { ...prevState, ...message.metadata.concierge_state };
        });
        break;
      }
    }
  }, [isConciergeActive, conciergeState]);

  // Fetch available personas on mount
  useEffect(() => {
    const fetchPersonas = async () => {
      try {
        const response = await personaApi.getPersonas();
        if (response.personas && response.personas.length > 0) {
          setConciergeState(prev => ({
            ...(prev || defaultConciergeState),
            availablePersonas: response.personas,
            currentPersona: response.personas[0]
          }));
          setCurrentPersona(response.personas[0]);
        }
      } catch (error) {
        console.error('Failed to fetch personas for concierge:', error);
        toast({
          title: 'Failed to load personas',
          description: 'Could not fetch available personas',
          variant: 'destructive'
        });
      }
    };

    fetchPersonas();
  }, []);

  // Send message function
  const sendMessage = async (content: string) => {
    if (!content.trim()) return;
    
    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: 'user',
      content,
      timestamp: new Date()
    };
    dispatchMessages({ type: 'ADD_MESSAGE', payload: userMessage });
    
    // Set typing indicator
    setIsTyping(true);
    
    try {
      // Simulate API call to backend
      setTimeout(() => {
        const aiMessage: ChatMessage = {
          id: Date.now().toString(),
          sender: 'assistant',
          content: `This is a response to: "${content}"`,
          timestamp: new Date()
        };
        dispatchMessages({ type: 'ADD_MESSAGE', payload: aiMessage });
        setIsTyping(false);
      }, 1000);
    } catch (error) {
      toast({
        title: 'Message Failed',
        description: 'Could not send message to server',
        variant: 'destructive'
      });
      setIsTyping(false);
    }
  };

  // Switch persona
  const switchPersona = (personaId: string) => {
    if (!conciergeState?.availablePersonas) return;
    
    const persona = conciergeState.availablePersonas.find(p => p.id === personaId);
    if (persona) {
      setCurrentPersona(persona);
      setConciergeState(prev => ({
        ...(prev || defaultConciergeState),
        currentPersona: persona
      }));
    }
  };

  // Other existing functions...

  return (
    <ConciergeContext.Provider
      value={{
        isConciergeActive,
        conciergeState,
        messages,
        sendMessage,
        currentPersona,
        switchPersona,
        isTyping,
        setConciergeActive: setIsConciergeActive,
        updateConciergeState,
        resetConciergeState,
        returnToConcierge,
        onReturnToConcierge,
        setOnReturnToConcierge: setOnReturnToConciergeCallback
      }}
    >
      {children}
    </ConciergeContext.Provider>
  );
};

// Helper function to extract concierge state from messages
export const extractConciergeState = (messages: Message[]): ConciergeState | null => {
  let conciergeState: ConciergeState | null = null;

  // First, look for explicit concierge state
  for (const message of messages) {
    if (message.sender === 'ai' && message.metadata?.concierge_state) {
      conciergeState = message.metadata.concierge_state as ConciergeState;
      break;
    }
  }

  if (!conciergeState) return null;

  // Then, look for coordination information
  const coordinationInfo = extractCoordinationInfo(messages);
  if (coordinationInfo) {
    conciergeState = {
      ...conciergeState,
      ...coordinationInfo
    };
  }

  // Then, look for team and role information
  const teamAndRoleInfo = extractTeamAndRoleInfo(messages);
  if (teamAndRoleInfo) {
    conciergeState = {
      ...conciergeState,
      ...teamAndRoleInfo
    };
  }

  // Then, look for routing information
  const routingInfo = extractRoutingInfo(messages);
  if (routingInfo) {
    conciergeState = {
      ...conciergeState,
      ...routingInfo
    };
  }

  return conciergeState;
};

// Helper function to extract routing information from messages
const extractRoutingInfo = (messages: Message[]) => {
  const routingInfo: Partial<ConciergeState> = {
    routingStrategy: null,
    fallbackChain: []
  };

  // Process messages in reverse order (newest first)
  for (const message of [...messages].reverse()) {
    if (message.sender !== 'ai') continue;

    // Extract routing strategy
    if (message.metadata?.routing_result?.strategy) {
      routingInfo.routingStrategy = message.metadata.routing_result.strategy;
    }

    // Extract fallback chain
    if (message.metadata?.routing_result?.fallback_chain) {
      routingInfo.fallbackChain = message.metadata.routing_result.fallback_chain;
    }

    // If we found routing information, break
    if (routingInfo.routingStrategy || routingInfo.fallbackChain.length) {
      break;
    }
  }

  return routingInfo;
};

// Helper function to extract coordination information from messages
const extractCoordinationInfo = (messages: Message[]) => {
  const coordinationInfo: Partial<ConciergeState> = {
    coordinationChain: [],
    activeCollaborations: [],
    pendingCallbacks: [],
    handoffs: []
  };

  // Process messages in reverse order (newest first)
  for (const message of [...messages].reverse()) {
    if (message.sender !== 'ai') continue;

    // Extract coordination chain
    if (message.metadata?.coordination?.chain) {
      coordinationInfo.coordinationChain = message.metadata.coordination.chain;
    }

    // Extract active collaborations
    if (message.metadata?.coordination?.collaborations) {
      coordinationInfo.activeCollaborations = message.metadata.coordination.collaborations;
    }

    // Extract pending callbacks
    if (message.metadata?.coordination?.callbacks) {
      coordinationInfo.pendingCallbacks = message.metadata.coordination.callbacks;
    }

    // Extract handoffs
    if (message.metadata?.coordination?.handoffs) {
      coordinationInfo.handoffs = message.metadata.coordination.handoffs;
    }

    // If we found all coordination information, break
    if (
      coordinationInfo.coordinationChain?.length ||
      coordinationInfo.activeCollaborations?.length ||
      coordinationInfo.pendingCallbacks?.length ||
      coordinationInfo.handoffs?.length
    ) {
      break;
    }
  }

  return coordinationInfo;
};

// Helper function to extract team and role information from messages
const extractTeamAndRoleInfo = (messages: Message[]) => {
  const teamInfo: Partial<ConciergeState> = {
    teamId: null,
    teamMembers: [],
    teamHierarchy: {},
    assignedRole: null,
    roleCapabilities: [],
    activeTasks: []
  };

  // Process messages in reverse order (newest first)
  for (const message of [...messages].reverse()) {
    if (message.sender !== 'ai') continue;

    // Extract team information
    if (message.metadata?.team) {
      if (message.metadata.team.team_id) {
        teamInfo.teamId = message.metadata.team.team_id;
      }

      if (message.metadata.team.team_members) {
        teamInfo.teamMembers = message.metadata.team.team_members;
      }

      if (message.metadata.team.hierarchy) {
        teamInfo.teamHierarchy = message.metadata.team.hierarchy;
      }
    }

    // Extract role information
    if (message.metadata?.role) {
      if (message.metadata.role.assigned_role) {
        teamInfo.assignedRole = message.metadata.role.assigned_role;
      }

      if (message.metadata.role.capabilities) {
        teamInfo.roleCapabilities = message.metadata.role.capabilities;
      }
    }

    // Extract task information
    if (message.metadata?.tasks) {
      teamInfo.activeTasks = message.metadata.tasks;
    }

    // If we found all team and role information, break
    if (
      teamInfo.teamId ||
      teamInfo.teamMembers?.length ||
      Object.keys(teamInfo.teamHierarchy || {}).length ||
      teamInfo.assignedRole ||
      teamInfo.roleCapabilities?.length ||
      teamInfo.activeTasks?.length
    ) {
      break;
    }
  }

  return teamInfo;
};
