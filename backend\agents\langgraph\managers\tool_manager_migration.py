"""
Tool Manager Migration Utilities.

This module provides utilities to migrate from legacy tool managers
(MCPToolRegistry, UnifiedToolManager, MCPToolManager) to the new
ConsolidatedToolManager.
"""

import logging
import warnings
from typing import Dict, Any, List, Optional, Union

from .consolidated_tool_manager import ConsolidatedToolManager, consolidated_tool_manager
from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class LegacyToolManagerWrapper:
    """
    Wrapper class that provides backward compatibility for legacy tool managers.
    
    This allows existing code to continue working while gradually migrating
    to the new ConsolidatedToolManager.
    """
    
    def __init__(self, manager_type: str = "unified"):
        """
        Initialize the legacy wrapper.
        
        Args:
            manager_type: Type of legacy manager to emulate
        """
        self.manager_type = manager_type
        self.consolidated_manager = consolidated_tool_manager
        self.logger = logging.getLogger(__name__)
        
        # Issue deprecation warning
        warnings.warn(
            f"Legacy {manager_type} tool manager is deprecated. "
            "Please migrate to ConsolidatedToolManager.",
            DeprecationWarning,
            stacklevel=2
        )
    
    async def execute_tool(self, tool_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a tool using legacy interface.
        
        Args:
            tool_name: Name of the tool to execute
            context: Tool execution context
            
        Returns:
            Tool execution result
        """
        try:
            # Convert legacy context to unified state
            state = self._convert_context_to_state(context)
            
            # Execute using consolidated manager
            result = await self.consolidated_manager.execute_tool(
                tool_name=tool_name,
                state=state,
                **context.get('parameters', {})
            )
            
            # Convert result to legacy format
            return self._convert_result_to_legacy_format(result)
            
        except Exception as e:
            self.logger.error(f"Error executing tool {tool_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": None
            }
    
    def get_tools_for_persona(self, persona_type: str) -> List[str]:
        """Get tools for a specific persona (legacy interface)."""
        return self.consolidated_manager.get_tools_for_persona(persona_type)
    
    def get_all_tools(self) -> Dict[str, Any]:
        """Get all tools (legacy interface)."""
        tool_definitions = self.consolidated_manager.get_all_tools()
        
        # Convert to legacy format
        legacy_tools = {}
        for tool_name, tool_def in tool_definitions.items():
            legacy_tools[tool_name] = {
                "name": tool_def.name,
                "description": tool_def.description,
                "input_schema": tool_def.input_schema,
                "output_schema": tool_def.output_schema,
                "category": tool_def.category.value,
                "enabled": tool_def.is_enabled
            }
        
        return legacy_tools
    
    async def register_tool(self, tool_name: str, tool_config: Dict[str, Any]) -> None:
        """Register a tool (legacy interface)."""
        # This would need to create a BaseMCPTool instance from config
        # For now, log that this should be migrated
        self.logger.warning(
            f"Legacy tool registration for {tool_name} should be migrated "
            "to use ConsolidatedToolManager.register_tool() directly"
        )
    
    def _convert_context_to_state(self, context: Dict[str, Any]) -> UnifiedDatageniusState:
        """Convert legacy context to UnifiedDatageniusState."""
        try:
            # Create a basic state from context
            state = UnifiedDatageniusState()
            
            # Map common context fields
            if 'user_id' in context:
                state.user_id = context['user_id']
            if 'session_id' in context:
                state.session_id = context['session_id']
            if 'conversation_id' in context:
                state.conversation_id = context['conversation_id']
            if 'persona_type' in context:
                state.persona_type = context['persona_type']
            
            # Add any additional context as metadata
            state.metadata = context.get('metadata', {})
            
            return state
            
        except Exception as e:
            self.logger.warning(f"Error converting context to state: {e}")
            return UnifiedDatageniusState()
    
    def _convert_result_to_legacy_format(self, result) -> Dict[str, Any]:
        """Convert ConsolidatedToolManager result to legacy format."""
        try:
            return {
                "success": result.status.value == "completed",
                "result": result.result,
                "error": result.error_message,
                "execution_time": result.execution_time,
                "execution_id": result.execution_id,
                "metadata": result.metadata
            }
        except Exception as e:
            self.logger.warning(f"Error converting result to legacy format: {e}")
            return {
                "success": False,
                "result": None,
                "error": str(e)
            }


# Legacy compatibility classes
class UnifiedToolManager(LegacyToolManagerWrapper):
    """Legacy UnifiedToolManager compatibility wrapper."""
    
    def __init__(self):
        super().__init__("unified")


class MCPToolManager(LegacyToolManagerWrapper):
    """Legacy MCPToolManager compatibility wrapper."""
    
    def __init__(self):
        super().__init__("mcp")


class MCPToolRegistry(LegacyToolManagerWrapper):
    """Legacy MCPToolRegistry compatibility wrapper."""
    
    def __init__(self):
        super().__init__("registry")
    
    def discover_tools(self) -> Dict[str, Any]:
        """Discover tools (legacy interface)."""
        return self.get_all_tools()
    
    def register_tool(self, tool_name: str, tool_instance: Any) -> None:
        """Register a tool instance (legacy interface)."""
        # Log that this should be migrated
        self.logger.warning(
            f"Legacy tool registration for {tool_name} should be migrated "
            "to use ConsolidatedToolManager.register_tool() directly"
        )
    
    def get_tool(self, tool_name: str) -> Optional[Any]:
        """Get a specific tool (legacy interface)."""
        tools = self.get_all_tools()
        return tools.get(tool_name)


def migrate_tool_manager_imports():
    """
    Utility function to help with import migration.
    
    This function can be called to ensure the consolidated tool manager
    is properly initialized and available.
    """
    try:
        # Initialize the consolidated manager if not already done
        if not hasattr(consolidated_tool_manager, '_initialized'):
            import asyncio
            
            # Run initialization if we're in an async context
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Schedule initialization
                    asyncio.create_task(consolidated_tool_manager.initialize())
                else:
                    # Run initialization synchronously
                    loop.run_until_complete(consolidated_tool_manager.initialize())
            except RuntimeError:
                # No event loop, create one
                asyncio.run(consolidated_tool_manager.initialize())
            
            consolidated_tool_manager._initialized = True
        
        logger.info("Tool manager migration utilities loaded successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error during tool manager migration: {e}")
        return False


# Auto-initialize when module is imported
migrate_tool_manager_imports()
