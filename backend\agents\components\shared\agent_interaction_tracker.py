"""
Agent Interaction Tracking System for Cross-Agent Intelligence.

This component tracks all agent interactions, responses, and generated content
to build comprehensive context history for cross-agent sharing.
"""

import logging
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional, Set
from ..base_component import BaseAgentComponent, AgentContext
from app.services.shared_context_repository import SharedContextRepository
from app.models.shared_context import (
    AgentInteractionCreate,
    AgentInsightCreate,
    InsightType,
    InteractionOutcome
)

logger = logging.getLogger(__name__)


class AgentInteractionTracker(BaseAgentComponent):
    """
    Component that tracks all agent interactions and generates insights
    for cross-agent intelligence sharing.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("agent_interaction_tracker", config)
        
        # Configuration
        self.enable_tracking = config.get("enable_tracking", True)
        self.auto_generate_insights = config.get("auto_generate_insights", True)
        self.track_tool_usage = config.get("track_tool_usage", True)
        self.track_context_usage = config.get("track_context_usage", True)
        self.min_response_length_for_insight = config.get("min_response_length_for_insight", 100)
        
        # Agent-specific insight generation rules
        self.insight_generation_rules = {
            "concierge": {
                "types": [InsightType.USER_INTENT, InsightType.WORKFLOW, InsightType.COORDINATION],
                "triggers": ["user_guidance", "workflow_coordination", "agent_routing"]
            },
            "composable-marketing-ai": {
                "types": [InsightType.MARKETING_STRATEGY, InsightType.CAMPAIGN_RESULTS, InsightType.AUDIENCE_INSIGHTS],
                "triggers": ["strategy_creation", "campaign_analysis", "audience_research"]
            },
            "composable-analysis-ai": {
                "types": [InsightType.DATA_INSIGHTS, InsightType.TRENDS, InsightType.PATTERNS, InsightType.RECOMMENDATIONS],
                "triggers": ["data_analysis", "trend_identification", "pattern_recognition"]
            },
            "composable-classifier-ai": {
                "types": [InsightType.CATEGORIZATION, InsightType.ORGANIZATION],
                "triggers": ["data_classification", "content_organization"]
            },
            "data-assistant": {
                "types": [InsightType.DATA_QUALITY, InsightType.PROCESSING_RESULTS],
                "triggers": ["data_processing", "quality_assessment"]
            },
            "text-processor": {
                "types": [InsightType.TEXT_INSIGHTS, InsightType.SUMMARIES, InsightType.ENTITIES, InsightType.SENTIMENT],
                "triggers": ["text_analysis", "summarization", "entity_extraction"]
            }
        }
        
        # Repository for persistent storage
        self.repository: Optional[SharedContextRepository] = None
    
    async def _initialize_component(self) -> None:
        """Initialize the agent interaction tracker."""
        self.logger.info("Initializing AgentInteractionTracker")
        
        try:
            # Initialize repository will be done per request with DB session
            self.logger.info("Agent interaction tracker initialized")
        except Exception as e:
            self.logger.warning(f"Failed to initialize tracker: {e}")
    
    def get_required_fields(self) -> List[str]:
        """Return list of required context fields."""
        return ["user_id", "business_profile_id", "agent_id"]
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process agent interaction tracking.
        
        Args:
            context: AgentContext containing interaction information
            
        Returns:
            Updated AgentContext with tracking results
        """
        if not self.enable_tracking:
            return context
        
        operation = context.get_field("tracking_operation", "track_interaction")
        business_profile_id = context.get_field("business_profile_id")
        agent_id = context.get_field("agent_id", "unknown")
        
        self.logger.info(f"Tracking operation {operation} for agent {agent_id}")
        
        try:
            if operation == "track_interaction":
                return await self._track_interaction(context, business_profile_id, agent_id)
            elif operation == "generate_insight":
                return await self._generate_insight(context, business_profile_id, agent_id)
            elif operation == "track_tool_usage":
                return await self._track_tool_usage(context, business_profile_id, agent_id)
            elif operation == "analyze_response":
                return await self._analyze_response(context, business_profile_id, agent_id)
            else:
                context.add_error(self.name, f"Unknown tracking operation: {operation}")
                context.set_status("error")
                return context
                
        except Exception as e:
            self.logger.error(f"Error in interaction tracking: {e}")
            context.add_error(self.name, f"Tracking error: {str(e)}")
            context.set_field("tracking_status", "error")
        
        return context
    
    async def _track_interaction(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Track a complete agent interaction."""
        interaction_data = context.get_field("interaction_data", {})
        
        # Extract interaction details
        user_message = interaction_data.get("user_message", "")
        agent_response = interaction_data.get("agent_response", "")
        tools_used = interaction_data.get("tools_used", [])
        context_used = interaction_data.get("context_used", [])
        outcome = interaction_data.get("outcome", InteractionOutcome.UNKNOWN)
        
        # Create interaction record
        interaction_create = AgentInteractionCreate(
            agent_id=agent_id,
            user_message=user_message,
            agent_response=agent_response,
            context_used=context_used,
            tools_used=tools_used,
            outcome=outcome,
            interaction_metadata={
                "response_length": len(agent_response),
                "tools_count": len(tools_used),
                "context_items_count": len(context_used),
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
        # Store interaction using repository
        try:
            from app.database import get_db
            db = next(get_db())
            repository = SharedContextRepository(db)
            
            interaction_response = await repository.record_interaction(
                business_profile_id, interaction_create
            )
            
            context.set_field("interaction_id", interaction_response.id)
            context.set_field("tracking_status", "recorded")
            
            # Auto-generate insights if enabled
            if self.auto_generate_insights and len(agent_response) >= self.min_response_length_for_insight:
                insights = await self._auto_generate_insights(
                    agent_id, user_message, agent_response, tools_used, context_used
                )
                
                # Store generated insights
                for insight_data in insights:
                    insight_create = AgentInsightCreate(**insight_data)
                    await repository.create_insight(business_profile_id, insight_create)
                
                context.set_field("insights_generated", len(insights))
            
            self.logger.info(f"Tracked interaction {interaction_response.id} for agent {agent_id}")
            
        except Exception as e:
            self.logger.error(f"Error storing interaction: {e}")
            context.add_error(self.name, f"Failed to store interaction: {str(e)}")
        
        return context
    
    async def _generate_insight(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Generate a specific insight from agent output."""
        insight_data = context.get_field("insight_data", {})
        
        if not insight_data:
            context.add_error(self.name, "No insight data provided")
            return context
        
        # Create insight
        insight_create = AgentInsightCreate(
            source_agent_id=agent_id,
            insight_type=insight_data.get("type", InsightType.GENERAL),
            content=insight_data.get("content", ""),
            insight_metadata=insight_data.get("metadata", {}),
            relevance_tags=insight_data.get("tags", []),
            confidence_score=insight_data.get("confidence", 1.0)
        )
        
        try:
            from app.database import get_db
            db = next(get_db())
            repository = SharedContextRepository(db)
            
            insight_response = await repository.create_insight(business_profile_id, insight_create)
            
            context.set_field("insight_id", insight_response.id)
            context.set_field("insight_generated", True)
            
            self.logger.info(f"Generated insight {insight_response.id} from agent {agent_id}")
            
        except Exception as e:
            self.logger.error(f"Error generating insight: {e}")
            context.add_error(self.name, f"Failed to generate insight: {str(e)}")
        
        return context
    
    async def _track_tool_usage(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Track tool usage patterns for cross-agent learning."""
        if not self.track_tool_usage:
            return context
        
        tool_usage_data = context.get_field("tool_usage_data", {})
        
        # Analyze tool usage patterns
        tools_used = tool_usage_data.get("tools_used", [])
        tool_outcomes = tool_usage_data.get("tool_outcomes", {})
        tool_performance = tool_usage_data.get("tool_performance", {})
        
        # Generate tool usage insights
        if tools_used:
            tool_insight = {
                "type": InsightType.GENERAL,
                "content": f"Agent {agent_id} successfully used tools: {', '.join(tools_used)}",
                "metadata": {
                    "tools_used": tools_used,
                    "tool_outcomes": tool_outcomes,
                    "tool_performance": tool_performance,
                    "insight_category": "tool_usage"
                },
                "tags": ["tool_usage", "performance"] + tools_used,
                "confidence": 0.8
            }
            
            # Store tool usage insight
            context.set_field("insight_data", tool_insight)
            await self._generate_insight(context, business_profile_id, agent_id)
        
        context.set_field("tool_tracking_complete", True)
        return context
    
    async def _analyze_response(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Analyze agent response for quality and insights."""
        response_data = context.get_field("response_data", {})
        
        agent_response = response_data.get("response", "")
        user_message = response_data.get("user_message", "")
        
        if not agent_response:
            return context
        
        # Analyze response characteristics
        analysis = {
            "response_length": len(agent_response),
            "response_complexity": self._calculate_response_complexity(agent_response),
            "response_sentiment": self._analyze_sentiment(agent_response),
            "key_topics": self._extract_key_topics(agent_response),
            "actionable_items": self._extract_actionable_items(agent_response)
        }
        
        # Generate response analysis insight
        if analysis["response_length"] > 50:  # Only for substantial responses
            analysis_insight = {
                "type": InsightType.GENERAL,
                "content": f"Response analysis for {agent_id}: {analysis['response_complexity']} complexity, {len(analysis['key_topics'])} key topics identified",
                "metadata": {
                    **analysis,
                    "insight_category": "response_analysis",
                    "user_message_preview": user_message[:100] + "..." if len(user_message) > 100 else user_message
                },
                "tags": ["response_analysis", "quality"] + analysis["key_topics"][:3],
                "confidence": 0.7
            }
            
            context.set_field("insight_data", analysis_insight)
            await self._generate_insight(context, business_profile_id, agent_id)
        
        context.set_field("response_analysis", analysis)
        return context
    
    async def _auto_generate_insights(self, agent_id: str, user_message: str, 
                                    agent_response: str, tools_used: List[str], 
                                    context_used: List[str]) -> List[Dict[str, Any]]:
        """Auto-generate insights based on agent interaction."""
        insights = []
        
        # Get agent-specific rules
        agent_rules = self.insight_generation_rules.get(agent_id, {})
        insight_types = agent_rules.get("types", [InsightType.GENERAL])
        triggers = agent_rules.get("triggers", [])
        
        # Check for trigger keywords in user message or response
        combined_text = f"{user_message} {agent_response}".lower()
        triggered_types = []
        
        for trigger in triggers:
            if trigger.replace("_", " ") in combined_text:
                triggered_types.extend(insight_types)
        
        # Generate insights based on triggers
        if triggered_types:
            primary_insight = {
                "source_agent_id": agent_id,
                "insight_type": triggered_types[0],
                "content": self._generate_insight_content(agent_id, user_message, agent_response),
                "metadata": {
                    "tools_used": tools_used,
                    "context_used": context_used,
                    "auto_generated": True,
                    "trigger_keywords": [t for t in triggers if t.replace("_", " ") in combined_text]
                },
                "relevance_tags": self._generate_relevance_tags(agent_id, user_message, agent_response),
                "confidence_score": 0.8
            }
            insights.append(primary_insight)
        
        # Generate tool usage insight if tools were used
        if tools_used:
            tool_insight = {
                "source_agent_id": agent_id,
                "insight_type": InsightType.GENERAL,
                "content": f"Successfully utilized {len(tools_used)} tools: {', '.join(tools_used)}",
                "metadata": {
                    "tools_used": tools_used,
                    "tool_effectiveness": "high" if len(agent_response) > 200 else "medium",
                    "auto_generated": True
                },
                "relevance_tags": ["tool_usage"] + tools_used,
                "confidence_score": 0.7
            }
            insights.append(tool_insight)
        
        return insights
    
    def _generate_insight_content(self, agent_id: str, user_message: str, agent_response: str) -> str:
        """Generate insight content based on interaction."""
        # Extract key information from the response
        response_preview = agent_response[:200] + "..." if len(agent_response) > 200 else agent_response
        
        return f"Agent {agent_id} provided comprehensive response addressing user query. Key insights: {response_preview}"
    
    def _generate_relevance_tags(self, agent_id: str, user_message: str, agent_response: str) -> List[str]:
        """Generate relevance tags for cross-agent matching."""
        tags = [agent_id.replace("-", "_")]
        
        # Extract keywords from user message and response
        combined_text = f"{user_message} {agent_response}".lower()
        
        # Common business keywords
        business_keywords = [
            "marketing", "analysis", "data", "strategy", "campaign", "customer",
            "sales", "revenue", "growth", "insights", "trends", "performance",
            "optimization", "conversion", "engagement", "roi", "kpi"
        ]
        
        for keyword in business_keywords:
            if keyword in combined_text:
                tags.append(keyword)
        
        return list(set(tags))[:10]  # Limit to 10 unique tags
    
    def _calculate_response_complexity(self, response: str) -> str:
        """Calculate response complexity level."""
        if len(response) < 100:
            return "simple"
        elif len(response) < 500:
            return "moderate"
        else:
            return "complex"
    
    def _analyze_sentiment(self, text: str) -> str:
        """Basic sentiment analysis."""
        positive_words = ["good", "great", "excellent", "success", "effective", "helpful"]
        negative_words = ["bad", "poor", "failed", "error", "problem", "issue"]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
    
    def _extract_key_topics(self, text: str) -> List[str]:
        """Extract key topics from text."""
        # Simple keyword extraction
        topics = []
        business_topics = [
            "marketing", "analysis", "data", "strategy", "campaign", "customer",
            "sales", "revenue", "growth", "insights", "trends", "performance"
        ]
        
        text_lower = text.lower()
        for topic in business_topics:
            if topic in text_lower:
                topics.append(topic)
        
        return topics[:5]  # Limit to top 5 topics
    
    def _extract_actionable_items(self, text: str) -> List[str]:
        """Extract actionable items from response."""
        # Look for action-oriented phrases
        action_indicators = [
            "should", "need to", "recommend", "suggest", "consider",
            "implement", "create", "develop", "analyze", "review"
        ]
        
        actionable_items = []
        sentences = text.split('.')
        
        for sentence in sentences:
            sentence_lower = sentence.lower().strip()
            if any(indicator in sentence_lower for indicator in action_indicators):
                actionable_items.append(sentence.strip())
        
        return actionable_items[:3]  # Limit to top 3 actionable items
