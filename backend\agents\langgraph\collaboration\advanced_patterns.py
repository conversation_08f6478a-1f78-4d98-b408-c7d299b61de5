"""
Advanced Multi-Agent Collaboration Patterns for LangGraph.

This module implements sophisticated collaboration mechanisms including
dynamic team formation, skill-based matching, collaborative learning,
and consensus building for complex multi-agent workflows.

Key Features:
- Dynamic team formation based on task requirements and agent capabilities
- Skill-based agent matching with performance optimization
- Collaborative learning system for knowledge sharing
- Consensus building mechanisms for complex decision-making
- Real-time collaboration analytics and optimization
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import json
from collections import defaultdict
import networkx as nx
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity

from ..events.event_bus import LangGraphEventBus, event_bus
from ..events.types import (
    AgentRegistrationEvent,
    AgentPerformanceEvent,
    WorkflowCompletedEvent
)
from ..monitoring.workflow_monitor import WorkflowMonitor

logger = logging.getLogger(__name__)


class SkillLevel(Enum):
    """Agent skill proficiency levels."""
    NOVICE = 1
    INTERMEDIATE = 2
    ADVANCED = 3
    EXPERT = 4


class CollaborationRole(Enum):
    """Roles in collaborative workflows."""
    LEADER = "leader"
    SPECIALIST = "specialist"
    COORDINATOR = "coordinator"
    REVIEWER = "reviewer"
    EXECUTOR = "executor"


class DecisionMethod(Enum):
    """Methods for consensus building."""
    MAJORITY_VOTE = "majority_vote"
    WEIGHTED_VOTE = "weighted_vote"
    EXPERT_CONSENSUS = "expert_consensus"
    HIERARCHICAL = "hierarchical"


@dataclass
class AgentSkill:
    """Represents an agent's skill in a specific domain."""
    skill_name: str
    level: SkillLevel
    confidence: float  # 0.0 to 1.0
    experience_points: int
    last_updated: datetime
    performance_history: List[float]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert skill to dictionary."""
        data = asdict(self)
        data['level'] = self.level.value
        data['last_updated'] = self.last_updated.isoformat()
        return data


@dataclass
class TeamConfiguration:
    """Configuration for a collaborative team."""
    team_id: str
    task_requirements: Dict[str, Any]
    selected_agents: List[str]
    role_assignments: Dict[str, CollaborationRole]
    skill_coverage: Dict[str, float]
    estimated_performance: float
    formation_timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert team configuration to dictionary."""
        data = asdict(self)
        data['role_assignments'] = {
            agent: role.value for agent, role in self.role_assignments.items()
        }
        data['formation_timestamp'] = self.formation_timestamp.isoformat()
        return data


@dataclass
class CollaborationMetrics:
    """Metrics for collaboration performance."""
    team_id: str
    task_completion_time: float
    quality_score: float
    coordination_efficiency: float
    knowledge_sharing_score: float
    consensus_time: float
    individual_contributions: Dict[str, float]
    communication_patterns: Dict[str, int]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class SkillMatcher:
    """Matches agents to tasks based on skills and performance."""
    
    def __init__(self):
        self.agent_skills = {}  # agent_id -> {skill_name -> AgentSkill}
        self.skill_requirements = {}  # task_type -> {skill_name -> required_level}
        self.performance_history = defaultdict(list)
        
    async def register_agent_skills(
        self, 
        agent_id: str, 
        skills: List[AgentSkill]
    ):
        """Register or update agent skills."""
        if agent_id not in self.agent_skills:
            self.agent_skills[agent_id] = {}
        
        for skill in skills:
            self.agent_skills[agent_id][skill.skill_name] = skill
        
        logger.info(f"Registered {len(skills)} skills for agent {agent_id}")
    
    async def find_best_agents(
        self,
        task_requirements: Dict[str, Any],
        max_agents: int = 5
    ) -> List[Tuple[str, float]]:
        """
        Find best agents for a task based on skill matching.
        
        Args:
            task_requirements: Dictionary with required skills and levels
            max_agents: Maximum number of agents to return
            
        Returns:
            List of (agent_id, match_score) tuples sorted by score
        """
        try:
            required_skills = task_requirements.get('required_skills', {})
            preferred_skills = task_requirements.get('preferred_skills', {})
            
            agent_scores = []
            
            for agent_id, agent_skills in self.agent_skills.items():
                score = self._calculate_agent_match_score(
                    agent_skills, required_skills, preferred_skills
                )
                
                if score > 0:  # Only include agents with some matching skills
                    agent_scores.append((agent_id, score))
            
            # Sort by score descending
            agent_scores.sort(key=lambda x: x[1], reverse=True)
            
            return agent_scores[:max_agents]
            
        except Exception as e:
            logger.error(f"Error finding best agents: {e}")
            return []
    
    def _calculate_agent_match_score(
        self,
        agent_skills: Dict[str, AgentSkill],
        required_skills: Dict[str, int],
        preferred_skills: Dict[str, int]
    ) -> float:
        """Calculate how well an agent matches task requirements."""
        total_score = 0.0
        max_possible_score = 0.0
        
        # Check required skills
        for skill_name, required_level in required_skills.items():
            max_possible_score += required_level * 2  # Weight required skills more
            
            if skill_name in agent_skills:
                agent_skill = agent_skills[skill_name]
                skill_score = min(agent_skill.level.value / required_level, 1.0)
                skill_score *= agent_skill.confidence  # Factor in confidence
                total_score += skill_score * 2
            # No penalty for missing required skills, just no points
        
        # Check preferred skills
        for skill_name, preferred_level in preferred_skills.items():
            max_possible_score += preferred_level
            
            if skill_name in agent_skills:
                agent_skill = agent_skills[skill_name]
                skill_score = min(agent_skill.level.value / preferred_level, 1.0)
                skill_score *= agent_skill.confidence
                total_score += skill_score
        
        # Normalize score
        if max_possible_score > 0:
            return total_score / max_possible_score
        else:
            return 0.0
    
    async def update_agent_performance(
        self,
        agent_id: str,
        task_type: str,
        performance_score: float,
        skills_used: List[str]
    ):
        """Update agent performance and skill levels based on task completion."""
        try:
            # Update performance history
            self.performance_history[agent_id].append({
                'task_type': task_type,
                'score': performance_score,
                'timestamp': datetime.now(timezone.utc),
                'skills_used': skills_used
            })
            
            # Update skill levels based on performance
            if agent_id in self.agent_skills:
                for skill_name in skills_used:
                    if skill_name in self.agent_skills[agent_id]:
                        skill = self.agent_skills[agent_id][skill_name]
                        
                        # Update experience points
                        skill.experience_points += int(performance_score * 10)
                        
                        # Update performance history
                        skill.performance_history.append(performance_score)
                        if len(skill.performance_history) > 10:
                            skill.performance_history = skill.performance_history[-10:]
                        
                        # Update confidence based on recent performance
                        recent_avg = np.mean(skill.performance_history[-5:])
                        skill.confidence = min(1.0, recent_avg)
                        
                        # Level up based on experience
                        if skill.experience_points > 100 and skill.level == SkillLevel.NOVICE:
                            skill.level = SkillLevel.INTERMEDIATE
                        elif skill.experience_points > 300 and skill.level == SkillLevel.INTERMEDIATE:
                            skill.level = SkillLevel.ADVANCED
                        elif skill.experience_points > 600 and skill.level == SkillLevel.ADVANCED:
                            skill.level = SkillLevel.EXPERT
                        
                        skill.last_updated = datetime.now(timezone.utc)
            
            logger.debug(f"Updated performance for agent {agent_id}: {performance_score}")
            
        except Exception as e:
            logger.error(f"Error updating agent performance: {e}")
    
    def get_agent_skill_profile(self, agent_id: str) -> Dict[str, Any]:
        """Get comprehensive skill profile for an agent."""
        if agent_id not in self.agent_skills:
            return {"error": "Agent not found"}
        
        skills = self.agent_skills[agent_id]
        performance_history = self.performance_history.get(agent_id, [])
        
        return {
            "agent_id": agent_id,
            "skills": {name: skill.to_dict() for name, skill in skills.items()},
            "total_experience": sum(skill.experience_points for skill in skills.values()),
            "average_confidence": np.mean([skill.confidence for skill in skills.values()]) if skills else 0,
            "recent_performance": np.mean([p['score'] for p in performance_history[-10:]]) if performance_history else 0,
            "task_count": len(performance_history),
            "specializations": self._identify_specializations(skills)
        }
    
    def _identify_specializations(self, skills: Dict[str, AgentSkill]) -> List[str]:
        """Identify agent's specialization areas."""
        specializations = []
        
        for skill_name, skill in skills.items():
            if skill.level.value >= 3 and skill.confidence > 0.8:  # Advanced+ with high confidence
                specializations.append(skill_name)
        
        return specializations


class TeamFormationEngine:
    """Forms optimal teams for collaborative tasks."""
    
    def __init__(self, skill_matcher: SkillMatcher):
        self.skill_matcher = skill_matcher
        self.team_history = []
        self.formation_strategies = {
            'skill_based': self._form_skill_based_team,
            'performance_based': self._form_performance_based_team,
            'balanced': self._form_balanced_team,
            'specialized': self._form_specialized_team
        }
    
    async def form_team(
        self,
        task_requirements: Dict[str, Any],
        strategy: str = 'balanced',
        team_size: int = 3
    ) -> Optional[TeamConfiguration]:
        """
        Form an optimal team for a collaborative task.
        
        Args:
            task_requirements: Task requirements including skills needed
            strategy: Team formation strategy
            team_size: Desired team size
            
        Returns:
            TeamConfiguration or None if team formation fails
        """
        try:
            if strategy not in self.formation_strategies:
                logger.error(f"Unknown team formation strategy: {strategy}")
                return None
            
            formation_func = self.formation_strategies[strategy]
            team_config = await formation_func(task_requirements, team_size)
            
            if team_config:
                self.team_history.append(team_config)
                logger.info(f"Formed team {team_config.team_id} with {len(team_config.selected_agents)} agents")
            
            return team_config
            
        except Exception as e:
            logger.error(f"Error forming team: {e}")
            return None

    async def _form_skill_based_team(
        self,
        task_requirements: Dict[str, Any],
        team_size: int
    ) -> Optional[TeamConfiguration]:
        """Form team based purely on skill matching."""
        try:
            # Get best agents for the task
            best_agents = await self.skill_matcher.find_best_agents(
                task_requirements, max_agents=team_size * 2
            )

            if len(best_agents) < team_size:
                logger.warning(f"Not enough qualified agents found: {len(best_agents)} < {team_size}")
                return None

            # Select top agents
            selected_agents = [agent_id for agent_id, _ in best_agents[:team_size]]

            # Assign roles based on skills
            role_assignments = self._assign_roles_by_skills(selected_agents, task_requirements)

            # Calculate skill coverage
            skill_coverage = self._calculate_skill_coverage(selected_agents, task_requirements)

            # Estimate team performance
            estimated_performance = np.mean([score for _, score in best_agents[:team_size]])

            return TeamConfiguration(
                team_id=f"team_{datetime.now().timestamp()}",
                task_requirements=task_requirements,
                selected_agents=selected_agents,
                role_assignments=role_assignments,
                skill_coverage=skill_coverage,
                estimated_performance=estimated_performance,
                formation_timestamp=datetime.now(timezone.utc)
            )

        except Exception as e:
            logger.error(f"Error in skill-based team formation: {e}")
            return None

    async def _form_performance_based_team(
        self,
        task_requirements: Dict[str, Any],
        team_size: int
    ) -> Optional[TeamConfiguration]:
        """Form team based on historical performance."""
        try:
            # Get agents with performance history
            performance_agents = []

            for agent_id in self.skill_matcher.agent_skills.keys():
                performance_history = self.skill_matcher.performance_history.get(agent_id, [])
                if performance_history:
                    avg_performance = np.mean([p['score'] for p in performance_history[-10:]])
                    performance_agents.append((agent_id, avg_performance))

            # Sort by performance
            performance_agents.sort(key=lambda x: x[1], reverse=True)

            if len(performance_agents) < team_size:
                logger.warning("Not enough agents with performance history")
                return None

            # Select top performers
            selected_agents = [agent_id for agent_id, _ in performance_agents[:team_size]]

            # Assign roles
            role_assignments = self._assign_roles_by_performance(selected_agents)

            # Calculate skill coverage
            skill_coverage = self._calculate_skill_coverage(selected_agents, task_requirements)

            # Estimate performance
            estimated_performance = np.mean([score for _, score in performance_agents[:team_size]])

            return TeamConfiguration(
                team_id=f"team_{datetime.now().timestamp()}",
                task_requirements=task_requirements,
                selected_agents=selected_agents,
                role_assignments=role_assignments,
                skill_coverage=skill_coverage,
                estimated_performance=estimated_performance,
                formation_timestamp=datetime.now(timezone.utc)
            )

        except Exception as e:
            logger.error(f"Error in performance-based team formation: {e}")
            return None

    async def _form_balanced_team(
        self,
        task_requirements: Dict[str, Any],
        team_size: int
    ) -> Optional[TeamConfiguration]:
        """Form balanced team considering both skills and performance."""
        try:
            # Get skill-based candidates
            skill_candidates = await self.skill_matcher.find_best_agents(
                task_requirements, max_agents=team_size * 3
            )

            if not skill_candidates:
                return None

            # Score candidates by combining skill match and performance
            balanced_scores = []

            for agent_id, skill_score in skill_candidates:
                performance_history = self.skill_matcher.performance_history.get(agent_id, [])

                if performance_history:
                    performance_score = np.mean([p['score'] for p in performance_history[-5:]])
                else:
                    performance_score = 0.5  # Default for new agents

                # Combine scores (70% skill, 30% performance)
                combined_score = 0.7 * skill_score + 0.3 * performance_score
                balanced_scores.append((agent_id, combined_score))

            # Sort by combined score
            balanced_scores.sort(key=lambda x: x[1], reverse=True)

            # Select diverse team
            selected_agents = self._select_diverse_team(balanced_scores, team_size, task_requirements)

            # Assign roles
            role_assignments = self._assign_roles_balanced(selected_agents, task_requirements)

            # Calculate metrics
            skill_coverage = self._calculate_skill_coverage(selected_agents, task_requirements)
            estimated_performance = np.mean([
                score for agent_id, score in balanced_scores
                if agent_id in selected_agents
            ])

            return TeamConfiguration(
                team_id=f"team_{datetime.now().timestamp()}",
                task_requirements=task_requirements,
                selected_agents=selected_agents,
                role_assignments=role_assignments,
                skill_coverage=skill_coverage,
                estimated_performance=estimated_performance,
                formation_timestamp=datetime.now(timezone.utc)
            )

        except Exception as e:
            logger.error(f"Error in balanced team formation: {e}")
            return None

    async def _form_specialized_team(
        self,
        task_requirements: Dict[str, Any],
        team_size: int
    ) -> Optional[TeamConfiguration]:
        """Form team with specialized agents for complex tasks."""
        try:
            required_skills = task_requirements.get('required_skills', {})

            # Find specialists for each required skill
            specialists = {}
            for skill_name, required_level in required_skills.items():
                skill_specialists = []

                for agent_id, agent_skills in self.skill_matcher.agent_skills.items():
                    if skill_name in agent_skills:
                        skill = agent_skills[skill_name]
                        if skill.level.value >= required_level and skill.confidence > 0.7:
                            skill_specialists.append((agent_id, skill.level.value * skill.confidence))

                skill_specialists.sort(key=lambda x: x[1], reverse=True)
                specialists[skill_name] = skill_specialists

            # Select one specialist per key skill
            selected_agents = []
            used_agents = set()

            for skill_name, skill_specialists in specialists.items():
                for agent_id, score in skill_specialists:
                    if agent_id not in used_agents and len(selected_agents) < team_size:
                        selected_agents.append(agent_id)
                        used_agents.add(agent_id)
                        break

            # Fill remaining slots with best available agents
            if len(selected_agents) < team_size:
                remaining_candidates = await self.skill_matcher.find_best_agents(
                    task_requirements, max_agents=team_size * 2
                )

                for agent_id, _ in remaining_candidates:
                    if agent_id not in used_agents and len(selected_agents) < team_size:
                        selected_agents.append(agent_id)
                        used_agents.add(agent_id)

            if not selected_agents:
                return None

            # Assign specialist roles
            role_assignments = self._assign_specialist_roles(selected_agents, task_requirements)

            # Calculate metrics
            skill_coverage = self._calculate_skill_coverage(selected_agents, task_requirements)
            estimated_performance = 0.9  # Specialists typically perform well

            return TeamConfiguration(
                team_id=f"team_{datetime.now().timestamp()}",
                task_requirements=task_requirements,
                selected_agents=selected_agents,
                role_assignments=role_assignments,
                skill_coverage=skill_coverage,
                estimated_performance=estimated_performance,
                formation_timestamp=datetime.now(timezone.utc)
            )

        except Exception as e:
            logger.error(f"Error in specialized team formation: {e}")
            return None

    def _assign_roles_by_skills(
        self,
        selected_agents: List[str],
        task_requirements: Dict[str, Any]
    ) -> Dict[str, CollaborationRole]:
        """Assign roles based on agent skills."""
        role_assignments = {}

        # Find the most skilled agent as leader
        best_agent = None
        best_score = 0

        for agent_id in selected_agents:
            if agent_id in self.skill_matcher.agent_skills:
                agent_skills = self.skill_matcher.agent_skills[agent_id]
                total_skill = sum(skill.level.value * skill.confidence for skill in agent_skills.values())

                if total_skill > best_score:
                    best_score = total_skill
                    best_agent = agent_id

        # Assign roles
        for i, agent_id in enumerate(selected_agents):
            if agent_id == best_agent:
                role_assignments[agent_id] = CollaborationRole.LEADER
            elif i == 1:
                role_assignments[agent_id] = CollaborationRole.COORDINATOR
            else:
                role_assignments[agent_id] = CollaborationRole.SPECIALIST

        return role_assignments

    def _assign_roles_by_performance(self, selected_agents: List[str]) -> Dict[str, CollaborationRole]:
        """Assign roles based on performance history."""
        role_assignments = {}

        # Sort by performance
        agent_performance = []
        for agent_id in selected_agents:
            performance_history = self.skill_matcher.performance_history.get(agent_id, [])
            avg_performance = np.mean([p['score'] for p in performance_history[-5:]]) if performance_history else 0.5
            agent_performance.append((agent_id, avg_performance))

        agent_performance.sort(key=lambda x: x[1], reverse=True)

        # Assign roles based on performance ranking
        for i, (agent_id, _) in enumerate(agent_performance):
            if i == 0:
                role_assignments[agent_id] = CollaborationRole.LEADER
            elif i == 1:
                role_assignments[agent_id] = CollaborationRole.COORDINATOR
            else:
                role_assignments[agent_id] = CollaborationRole.EXECUTOR

        return role_assignments

    def _assign_roles_balanced(
        self,
        selected_agents: List[str],
        task_requirements: Dict[str, Any]
    ) -> Dict[str, CollaborationRole]:
        """Assign roles in a balanced way considering skills and performance."""
        role_assignments = {}

        # Combine skill and performance scores for role assignment
        agent_scores = []
        for agent_id in selected_agents:
            skill_score = 0
            if agent_id in self.skill_matcher.agent_skills:
                agent_skills = self.skill_matcher.agent_skills[agent_id]
                skill_score = sum(skill.level.value * skill.confidence for skill in agent_skills.values())

            performance_history = self.skill_matcher.performance_history.get(agent_id, [])
            performance_score = np.mean([p['score'] for p in performance_history[-5:]]) if performance_history else 0.5

            combined_score = skill_score + performance_score
            agent_scores.append((agent_id, combined_score))

        agent_scores.sort(key=lambda x: x[1], reverse=True)

        # Assign roles
        for i, (agent_id, _) in enumerate(agent_scores):
            if i == 0:
                role_assignments[agent_id] = CollaborationRole.LEADER
            elif i == 1:
                role_assignments[agent_id] = CollaborationRole.COORDINATOR
            elif i == len(agent_scores) - 1:
                role_assignments[agent_id] = CollaborationRole.REVIEWER
            else:
                role_assignments[agent_id] = CollaborationRole.SPECIALIST

        return role_assignments

    def _assign_specialist_roles(
        self,
        selected_agents: List[str],
        task_requirements: Dict[str, Any]
    ) -> Dict[str, CollaborationRole]:
        """Assign roles for specialized teams."""
        role_assignments = {}
        required_skills = task_requirements.get('required_skills', {})

        # Assign leader to agent with most comprehensive skills
        best_leader = None
        max_skill_coverage = 0

        for agent_id in selected_agents:
            if agent_id in self.skill_matcher.agent_skills:
                agent_skills = self.skill_matcher.agent_skills[agent_id]
                coverage = sum(1 for skill_name in required_skills if skill_name in agent_skills)

                if coverage > max_skill_coverage:
                    max_skill_coverage = coverage
                    best_leader = agent_id

        # Assign roles
        for agent_id in selected_agents:
            if agent_id == best_leader:
                role_assignments[agent_id] = CollaborationRole.LEADER
            else:
                role_assignments[agent_id] = CollaborationRole.SPECIALIST

        return role_assignments

    def _select_diverse_team(
        self,
        candidates: List[Tuple[str, float]],
        team_size: int,
        task_requirements: Dict[str, Any]
    ) -> List[str]:
        """Select diverse team to maximize skill coverage."""
        selected_agents = []
        covered_skills = set()
        required_skills = set(task_requirements.get('required_skills', {}).keys())

        # First, ensure all required skills are covered
        for agent_id, _ in candidates:
            if len(selected_agents) >= team_size:
                break

            if agent_id in self.skill_matcher.agent_skills:
                agent_skills = set(self.skill_matcher.agent_skills[agent_id].keys())
                new_skills = agent_skills - covered_skills

                # Prioritize agents that cover new required skills
                if new_skills & required_skills:
                    selected_agents.append(agent_id)
                    covered_skills.update(agent_skills)

        # Fill remaining slots with best available agents
        for agent_id, _ in candidates:
            if len(selected_agents) >= team_size:
                break
            if agent_id not in selected_agents:
                selected_agents.append(agent_id)

        return selected_agents

    def _calculate_skill_coverage(
        self,
        selected_agents: List[str],
        task_requirements: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate how well the team covers required skills."""
        required_skills = task_requirements.get('required_skills', {})
        skill_coverage = {}

        for skill_name, required_level in required_skills.items():
            max_level = 0
            total_confidence = 0
            agent_count = 0

            for agent_id in selected_agents:
                if agent_id in self.skill_matcher.agent_skills:
                    agent_skills = self.skill_matcher.agent_skills[agent_id]
                    if skill_name in agent_skills:
                        skill = agent_skills[skill_name]
                        max_level = max(max_level, skill.level.value)
                        total_confidence += skill.confidence
                        agent_count += 1

            if agent_count > 0:
                avg_confidence = total_confidence / agent_count
                coverage_score = min(max_level / required_level, 1.0) * avg_confidence
            else:
                coverage_score = 0.0

            skill_coverage[skill_name] = coverage_score

        return skill_coverage


class CollaborativeLearning:
    """System for collaborative learning and knowledge sharing between agents."""

    def __init__(self):
        self.knowledge_base = {}  # skill_name -> {insights, best_practices, common_mistakes}
        self.learning_sessions = []
        self.knowledge_graph = nx.Graph()

    async def share_knowledge(
        self,
        source_agent: str,
        target_agents: List[str],
        skill_name: str,
        knowledge_type: str,
        content: Dict[str, Any]
    ):
        """Share knowledge between agents."""
        try:
            # Store knowledge in knowledge base
            if skill_name not in self.knowledge_base:
                self.knowledge_base[skill_name] = {
                    'insights': [],
                    'best_practices': [],
                    'common_mistakes': [],
                    'performance_tips': []
                }

            knowledge_entry = {
                'source_agent': source_agent,
                'content': content,
                'timestamp': datetime.now(timezone.utc),
                'validation_score': 0.0,
                'usage_count': 0
            }

            self.knowledge_base[skill_name][knowledge_type].append(knowledge_entry)

            # Update knowledge graph
            for target_agent in target_agents:
                self.knowledge_graph.add_edge(
                    source_agent,
                    target_agent,
                    skill=skill_name,
                    knowledge_type=knowledge_type,
                    timestamp=datetime.now(timezone.utc)
                )

            # Record learning session
            session = {
                'session_id': f"learning_{datetime.now().timestamp()}",
                'source_agent': source_agent,
                'target_agents': target_agents,
                'skill_name': skill_name,
                'knowledge_type': knowledge_type,
                'timestamp': datetime.now(timezone.utc)
            }

            self.learning_sessions.append(session)

            logger.info(f"Knowledge shared: {source_agent} -> {target_agents} on {skill_name}")

        except Exception as e:
            logger.error(f"Error sharing knowledge: {e}")

    async def get_relevant_knowledge(
        self,
        agent_id: str,
        skill_name: str,
        knowledge_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get relevant knowledge for an agent and skill."""
        try:
            if skill_name not in self.knowledge_base:
                return []

            skill_knowledge = self.knowledge_base[skill_name]
            relevant_knowledge = []

            # Get knowledge of specified type or all types
            knowledge_types = [knowledge_type] if knowledge_type else skill_knowledge.keys()

            for k_type in knowledge_types:
                if k_type in skill_knowledge:
                    for entry in skill_knowledge[k_type]:
                        # Filter out knowledge from the same agent
                        if entry['source_agent'] != agent_id:
                            relevant_knowledge.append({
                                'knowledge_type': k_type,
                                'content': entry['content'],
                                'source_agent': entry['source_agent'],
                                'validation_score': entry['validation_score'],
                                'timestamp': entry['timestamp'].isoformat()
                            })

            # Sort by validation score and recency
            relevant_knowledge.sort(
                key=lambda x: (x['validation_score'], x['timestamp']),
                reverse=True
            )

            return relevant_knowledge[:10]  # Return top 10 most relevant

        except Exception as e:
            logger.error(f"Error getting relevant knowledge: {e}")
            return []

    async def validate_knowledge(
        self,
        skill_name: str,
        knowledge_type: str,
        knowledge_index: int,
        validation_score: float,
        validator_agent: str
    ):
        """Validate shared knowledge based on usage and outcomes."""
        try:
            if skill_name in self.knowledge_base:
                if knowledge_type in self.knowledge_base[skill_name]:
                    knowledge_list = self.knowledge_base[skill_name][knowledge_type]
                    if 0 <= knowledge_index < len(knowledge_list):
                        entry = knowledge_list[knowledge_index]

                        # Update validation score (weighted average)
                        current_score = entry['validation_score']
                        usage_count = entry['usage_count']

                        new_score = (current_score * usage_count + validation_score) / (usage_count + 1)
                        entry['validation_score'] = new_score
                        entry['usage_count'] += 1

                        logger.debug(f"Knowledge validated: {skill_name}/{knowledge_type}[{knowledge_index}] = {new_score:.2f}")

        except Exception as e:
            logger.error(f"Error validating knowledge: {e}")

    def get_learning_analytics(self) -> Dict[str, Any]:
        """Get analytics about collaborative learning."""
        try:
            total_sessions = len(self.learning_sessions)
            unique_agents = set()
            skill_distribution = defaultdict(int)

            for session in self.learning_sessions:
                unique_agents.add(session['source_agent'])
                unique_agents.update(session['target_agents'])
                skill_distribution[session['skill_name']] += 1

            # Knowledge graph metrics
            if self.knowledge_graph.number_of_nodes() > 0:
                avg_connections = self.knowledge_graph.number_of_edges() / self.knowledge_graph.number_of_nodes()
                most_connected = max(
                    self.knowledge_graph.nodes(),
                    key=lambda n: self.knowledge_graph.degree(n)
                ) if self.knowledge_graph.nodes() else None
            else:
                avg_connections = 0
                most_connected = None

            return {
                'total_learning_sessions': total_sessions,
                'unique_agents_involved': len(unique_agents),
                'skills_shared': len(skill_distribution),
                'most_shared_skill': max(skill_distribution.items(), key=lambda x: x[1])[0] if skill_distribution else None,
                'average_connections_per_agent': avg_connections,
                'most_connected_agent': most_connected,
                'knowledge_base_size': sum(
                    len(skill_data[k_type])
                    for skill_data in self.knowledge_base.values()
                    for k_type in skill_data
                )
            }

        except Exception as e:
            logger.error(f"Error getting learning analytics: {e}")
            return {"error": str(e)}


class ConsensusBuilder:
    """Builds consensus among agents for complex decision-making."""

    def __init__(self):
        self.decision_history = []
        self.voting_sessions = {}

    async def initiate_consensus(
        self,
        decision_id: str,
        participating_agents: List[str],
        decision_context: Dict[str, Any],
        method: DecisionMethod = DecisionMethod.MAJORITY_VOTE,
        timeout_minutes: int = 30
    ) -> str:
        """
        Initiate a consensus-building session.

        Args:
            decision_id: Unique identifier for the decision
            participating_agents: List of agents participating in the decision
            decision_context: Context and options for the decision
            method: Consensus building method
            timeout_minutes: Timeout for the consensus session

        Returns:
            Session ID for tracking the consensus process
        """
        try:
            session_id = f"consensus_{datetime.now().timestamp()}"

            session = {
                'session_id': session_id,
                'decision_id': decision_id,
                'participating_agents': participating_agents,
                'decision_context': decision_context,
                'method': method,
                'start_time': datetime.now(timezone.utc),
                'timeout': datetime.now(timezone.utc) + timedelta(minutes=timeout_minutes),
                'votes': {},
                'status': 'active',
                'result': None
            }

            self.voting_sessions[session_id] = session

            logger.info(f"Initiated consensus session {session_id} for decision {decision_id}")
            return session_id

        except Exception as e:
            logger.error(f"Error initiating consensus: {e}")
            return ""

    async def submit_vote(
        self,
        session_id: str,
        agent_id: str,
        vote: Dict[str, Any]
    ) -> bool:
        """
        Submit a vote for a consensus session.

        Args:
            session_id: ID of the consensus session
            agent_id: ID of the voting agent
            vote: Vote content including choice and reasoning

        Returns:
            True if vote was accepted, False otherwise
        """
        try:
            if session_id not in self.voting_sessions:
                logger.error(f"Consensus session {session_id} not found")
                return False

            session = self.voting_sessions[session_id]

            if session['status'] != 'active':
                logger.error(f"Consensus session {session_id} is not active")
                return False

            if agent_id not in session['participating_agents']:
                logger.error(f"Agent {agent_id} not authorized to vote in session {session_id}")
                return False

            # Check timeout
            if datetime.now(timezone.utc) > session['timeout']:
                session['status'] = 'timeout'
                logger.warning(f"Consensus session {session_id} timed out")
                return False

            # Record vote
            session['votes'][agent_id] = {
                'vote': vote,
                'timestamp': datetime.now(timezone.utc)
            }

            logger.info(f"Vote submitted by {agent_id} in session {session_id}")

            # Check if all agents have voted
            if len(session['votes']) == len(session['participating_agents']):
                await self._finalize_consensus(session_id)

            return True

        except Exception as e:
            logger.error(f"Error submitting vote: {e}")
            return False

    async def _finalize_consensus(self, session_id: str):
        """Finalize consensus based on collected votes."""
        try:
            session = self.voting_sessions[session_id]
            method = session['method']

            if method == DecisionMethod.MAJORITY_VOTE:
                result = self._calculate_majority_vote(session)
            elif method == DecisionMethod.WEIGHTED_VOTE:
                result = self._calculate_weighted_vote(session)
            elif method == DecisionMethod.EXPERT_CONSENSUS:
                result = self._calculate_expert_consensus(session)
            elif method == DecisionMethod.HIERARCHICAL:
                result = self._calculate_hierarchical_decision(session)
            else:
                result = {'error': f'Unknown consensus method: {method}'}

            session['result'] = result
            session['status'] = 'completed'
            session['end_time'] = datetime.now(timezone.utc)

            # Add to decision history
            decision_record = {
                'session_id': session_id,
                'decision_id': session['decision_id'],
                'method': method.value,
                'participating_agents': session['participating_agents'],
                'result': result,
                'duration_minutes': (session['end_time'] - session['start_time']).total_seconds() / 60,
                'timestamp': session['end_time']
            }

            self.decision_history.append(decision_record)

            logger.info(f"Consensus finalized for session {session_id}: {result}")

        except Exception as e:
            logger.error(f"Error finalizing consensus: {e}")

    def _calculate_majority_vote(self, session: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate result based on majority vote."""
        try:
            vote_counts = defaultdict(int)

            for agent_id, vote_data in session['votes'].items():
                choice = vote_data['vote'].get('choice')
                if choice:
                    vote_counts[choice] += 1

            if not vote_counts:
                return {'decision': None, 'reason': 'No valid votes received'}

            # Find majority
            total_votes = sum(vote_counts.values())
            majority_threshold = total_votes / 2

            winning_choice = max(vote_counts.items(), key=lambda x: x[1])

            if winning_choice[1] > majority_threshold:
                return {
                    'decision': winning_choice[0],
                    'vote_counts': dict(vote_counts),
                    'confidence': winning_choice[1] / total_votes,
                    'method': 'majority_vote'
                }
            else:
                return {
                    'decision': winning_choice[0],
                    'vote_counts': dict(vote_counts),
                    'confidence': winning_choice[1] / total_votes,
                    'method': 'plurality_vote',
                    'note': 'No absolute majority, using plurality'
                }

        except Exception as e:
            logger.error(f"Error calculating majority vote: {e}")
            return {'error': str(e)}

    def _calculate_weighted_vote(self, session: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate result based on weighted vote (by agent expertise)."""
        try:
            weighted_votes = defaultdict(float)
            total_weight = 0

            for agent_id, vote_data in session['votes'].items():
                choice = vote_data['vote'].get('choice')
                weight = vote_data['vote'].get('confidence', 1.0)  # Use confidence as weight

                if choice:
                    weighted_votes[choice] += weight
                    total_weight += weight

            if not weighted_votes:
                return {'decision': None, 'reason': 'No valid weighted votes received'}

            # Find weighted winner
            winning_choice = max(weighted_votes.items(), key=lambda x: x[1])

            return {
                'decision': winning_choice[0],
                'weighted_scores': dict(weighted_votes),
                'confidence': winning_choice[1] / total_weight if total_weight > 0 else 0,
                'method': 'weighted_vote'
            }

        except Exception as e:
            logger.error(f"Error calculating weighted vote: {e}")
            return {'error': str(e)}

    def _calculate_expert_consensus(self, session: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate result based on expert consensus (higher weight for experts)."""
        try:
            # This would typically use agent skill levels to determine expertise
            # For now, we'll use a simplified approach
            expert_votes = defaultdict(float)

            for agent_id, vote_data in session['votes'].items():
                choice = vote_data['vote'].get('choice')
                expertise_level = vote_data['vote'].get('expertise_level', 1.0)

                if choice:
                    expert_votes[choice] += expertise_level

            if not expert_votes:
                return {'decision': None, 'reason': 'No expert votes received'}

            winning_choice = max(expert_votes.items(), key=lambda x: x[1])
            total_expertise = sum(expert_votes.values())

            return {
                'decision': winning_choice[0],
                'expert_scores': dict(expert_votes),
                'confidence': winning_choice[1] / total_expertise if total_expertise > 0 else 0,
                'method': 'expert_consensus'
            }

        except Exception as e:
            logger.error(f"Error calculating expert consensus: {e}")
            return {'error': str(e)}

    def _calculate_hierarchical_decision(self, session: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate result based on hierarchical decision-making."""
        try:
            # Find the highest-ranking agent's vote
            highest_rank = -1
            leader_vote = None

            for agent_id, vote_data in session['votes'].items():
                rank = vote_data['vote'].get('hierarchy_rank', 0)
                if rank > highest_rank:
                    highest_rank = rank
                    leader_vote = vote_data['vote'].get('choice')

            if leader_vote is None:
                # Fall back to majority vote if no hierarchy info
                return self._calculate_majority_vote(session)

            return {
                'decision': leader_vote,
                'leader_rank': highest_rank,
                'method': 'hierarchical',
                'confidence': 1.0  # Leader decision is final
            }

        except Exception as e:
            logger.error(f"Error calculating hierarchical decision: {e}")
            return {'error': str(e)}

    def get_consensus_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a consensus session."""
        if session_id not in self.voting_sessions:
            return None

        session = self.voting_sessions[session_id]

        return {
            'session_id': session_id,
            'decision_id': session['decision_id'],
            'status': session['status'],
            'participating_agents': session['participating_agents'],
            'votes_received': len(session['votes']),
            'votes_needed': len(session['participating_agents']),
            'time_remaining': max(0, (session['timeout'] - datetime.now(timezone.utc)).total_seconds()),
            'result': session.get('result')
        }

    def get_decision_analytics(self) -> Dict[str, Any]:
        """Get analytics about consensus decisions."""
        try:
            if not self.decision_history:
                return {'message': 'No decision history available'}

            total_decisions = len(self.decision_history)
            methods_used = defaultdict(int)
            avg_duration = 0
            successful_decisions = 0

            for decision in self.decision_history:
                methods_used[decision['method']] += 1
                avg_duration += decision['duration_minutes']

                if decision['result'] and 'error' not in decision['result']:
                    successful_decisions += 1

            avg_duration /= total_decisions
            success_rate = successful_decisions / total_decisions

            return {
                'total_decisions': total_decisions,
                'success_rate': success_rate,
                'average_duration_minutes': avg_duration,
                'methods_distribution': dict(methods_used),
                'most_used_method': max(methods_used.items(), key=lambda x: x[1])[0] if methods_used else None
            }

        except Exception as e:
            logger.error(f"Error getting decision analytics: {e}")
            return {'error': str(e)}


class CollaborationAnalytics:
    """Analytics and insights for multi-agent collaboration."""

    def __init__(self):
        self.collaboration_metrics = []
        self.team_performance_history = {}

    async def record_collaboration_metrics(
        self,
        team_config: TeamConfiguration,
        actual_performance: Dict[str, Any]
    ):
        """Record metrics from a completed collaboration."""
        try:
            metrics = CollaborationMetrics(
                team_id=team_config.team_id,
                task_completion_time=actual_performance.get('completion_time', 0),
                quality_score=actual_performance.get('quality_score', 0),
                coordination_efficiency=actual_performance.get('coordination_efficiency', 0),
                knowledge_sharing_score=actual_performance.get('knowledge_sharing_score', 0),
                consensus_time=actual_performance.get('consensus_time', 0),
                individual_contributions=actual_performance.get('individual_contributions', {}),
                communication_patterns=actual_performance.get('communication_patterns', {}),
                timestamp=datetime.now(timezone.utc)
            )

            self.collaboration_metrics.append(metrics)

            # Update team performance history
            if team_config.team_id not in self.team_performance_history:
                self.team_performance_history[team_config.team_id] = []

            self.team_performance_history[team_config.team_id].append(metrics)

            logger.info(f"Recorded collaboration metrics for team {team_config.team_id}")

        except Exception as e:
            logger.error(f"Error recording collaboration metrics: {e}")

    def analyze_team_performance(self, team_id: str) -> Dict[str, Any]:
        """Analyze performance trends for a specific team."""
        try:
            if team_id not in self.team_performance_history:
                return {'error': 'Team not found'}

            team_metrics = self.team_performance_history[team_id]

            if not team_metrics:
                return {'error': 'No metrics available for team'}

            # Calculate trends
            completion_times = [m.task_completion_time for m in team_metrics]
            quality_scores = [m.quality_score for m in team_metrics]
            coordination_scores = [m.coordination_efficiency for m in team_metrics]

            return {
                'team_id': team_id,
                'total_collaborations': len(team_metrics),
                'average_completion_time': np.mean(completion_times),
                'completion_time_trend': self._calculate_trend(completion_times),
                'average_quality_score': np.mean(quality_scores),
                'quality_trend': self._calculate_trend(quality_scores),
                'average_coordination_efficiency': np.mean(coordination_scores),
                'coordination_trend': self._calculate_trend(coordination_scores),
                'best_performance': {
                    'completion_time': min(completion_times),
                    'quality_score': max(quality_scores),
                    'coordination_efficiency': max(coordination_scores)
                },
                'improvement_rate': self._calculate_improvement_rate(team_metrics)
            }

        except Exception as e:
            logger.error(f"Error analyzing team performance: {e}")
            return {'error': str(e)}

    def get_collaboration_insights(self) -> Dict[str, Any]:
        """Get comprehensive collaboration insights."""
        try:
            if not self.collaboration_metrics:
                return {'message': 'No collaboration data available'}

            # Overall statistics
            total_collaborations = len(self.collaboration_metrics)
            avg_completion_time = np.mean([m.task_completion_time for m in self.collaboration_metrics])
            avg_quality = np.mean([m.quality_score for m in self.collaboration_metrics])
            avg_coordination = np.mean([m.coordination_efficiency for m in self.collaboration_metrics])

            # Team size analysis
            team_sizes = defaultdict(list)
            for metrics in self.collaboration_metrics:
                team_size = len(metrics.individual_contributions)
                team_sizes[team_size].append(metrics.quality_score)

            optimal_team_size = max(
                team_sizes.items(),
                key=lambda x: np.mean(x[1])
            )[0] if team_sizes else 0

            # Communication pattern analysis
            communication_patterns = defaultdict(int)
            for metrics in self.collaboration_metrics:
                for pattern, count in metrics.communication_patterns.items():
                    communication_patterns[pattern] += count

            # Success factors analysis
            high_performing_teams = [
                m for m in self.collaboration_metrics
                if m.quality_score > 0.8 and m.coordination_efficiency > 0.7
            ]

            success_factors = self._analyze_success_factors(high_performing_teams)

            return {
                'total_collaborations': total_collaborations,
                'average_metrics': {
                    'completion_time': avg_completion_time,
                    'quality_score': avg_quality,
                    'coordination_efficiency': avg_coordination
                },
                'optimal_team_size': optimal_team_size,
                'communication_patterns': dict(communication_patterns),
                'high_performing_teams_count': len(high_performing_teams),
                'success_factors': success_factors,
                'collaboration_trends': self._analyze_collaboration_trends()
            }

        except Exception as e:
            logger.error(f"Error getting collaboration insights: {e}")
            return {'error': str(e)}

    def recommend_team_improvements(self, team_id: str) -> List[Dict[str, Any]]:
        """Recommend improvements for a specific team."""
        try:
            team_analysis = self.analyze_team_performance(team_id)

            if 'error' in team_analysis:
                return []

            recommendations = []

            # Completion time recommendations
            if team_analysis['completion_time_trend'] > 0.1:  # Getting slower
                recommendations.append({
                    'category': 'efficiency',
                    'priority': 'high',
                    'recommendation': 'Focus on improving task coordination and parallel execution',
                    'expected_improvement': '20-30% faster completion times'
                })

            # Quality recommendations
            if team_analysis['average_quality_score'] < 0.7:
                recommendations.append({
                    'category': 'quality',
                    'priority': 'high',
                    'recommendation': 'Implement peer review processes and quality checkpoints',
                    'expected_improvement': '15-25% improvement in output quality'
                })

            # Coordination recommendations
            if team_analysis['average_coordination_efficiency'] < 0.6:
                recommendations.append({
                    'category': 'coordination',
                    'priority': 'medium',
                    'recommendation': 'Establish clearer communication protocols and role definitions',
                    'expected_improvement': '10-20% better coordination efficiency'
                })

            # Learning recommendations
            if team_analysis['improvement_rate'] < 0.05:  # Not improving much
                recommendations.append({
                    'category': 'learning',
                    'priority': 'medium',
                    'recommendation': 'Increase knowledge sharing sessions and collaborative learning',
                    'expected_improvement': 'Accelerated team learning and adaptation'
                })

            return recommendations

        except Exception as e:
            logger.error(f"Error generating team recommendations: {e}")
            return []

    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend direction (-1 to 1, negative is improving for time metrics)."""
        if len(values) < 2:
            return 0.0

        # Simple linear trend calculation
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]

        # Normalize slope
        value_range = max(values) - min(values)
        if value_range > 0:
            return slope / value_range
        else:
            return 0.0

    def _calculate_improvement_rate(self, metrics: List[CollaborationMetrics]) -> float:
        """Calculate overall improvement rate for a team."""
        if len(metrics) < 2:
            return 0.0

        # Calculate composite score for each collaboration
        composite_scores = []
        for m in metrics:
            # Higher quality and coordination are better, lower completion time is better
            score = (m.quality_score + m.coordination_efficiency) / 2 - (m.task_completion_time / 100)
            composite_scores.append(score)

        # Calculate improvement trend
        return self._calculate_trend(composite_scores)

    def _analyze_success_factors(self, high_performing_teams: List[CollaborationMetrics]) -> Dict[str, Any]:
        """Analyze common factors in high-performing teams."""
        if not high_performing_teams:
            return {}

        # Analyze team sizes
        team_sizes = [len(m.individual_contributions) for m in high_performing_teams]
        avg_team_size = np.mean(team_sizes)

        # Analyze communication patterns
        common_patterns = defaultdict(int)
        for metrics in high_performing_teams:
            for pattern, count in metrics.communication_patterns.items():
                common_patterns[pattern] += count

        # Analyze contribution distribution
        contribution_balance = []
        for metrics in high_performing_teams:
            contributions = list(metrics.individual_contributions.values())
            if contributions:
                balance = 1 - (np.std(contributions) / np.mean(contributions))  # Lower std = more balanced
                contribution_balance.append(balance)

        return {
            'optimal_team_size': round(avg_team_size, 1),
            'common_communication_patterns': dict(common_patterns),
            'average_contribution_balance': np.mean(contribution_balance) if contribution_balance else 0,
            'key_characteristics': [
                'Balanced individual contributions',
                'Effective communication patterns',
                'Optimal team size around 3-4 members'
            ]
        }

    def _analyze_collaboration_trends(self) -> Dict[str, Any]:
        """Analyze overall collaboration trends over time."""
        if len(self.collaboration_metrics) < 5:
            return {'message': 'Insufficient data for trend analysis'}

        # Sort by timestamp
        sorted_metrics = sorted(self.collaboration_metrics, key=lambda x: x.timestamp)

        # Calculate trends for key metrics
        quality_scores = [m.quality_score for m in sorted_metrics]
        completion_times = [m.task_completion_time for m in sorted_metrics]
        coordination_scores = [m.coordination_efficiency for m in sorted_metrics]

        return {
            'quality_trend': self._calculate_trend(quality_scores),
            'efficiency_trend': -self._calculate_trend(completion_times),  # Negative because lower is better
            'coordination_trend': self._calculate_trend(coordination_scores),
            'overall_improvement': (
                self._calculate_trend(quality_scores) +
                (-self._calculate_trend(completion_times)) +
                self._calculate_trend(coordination_scores)
            ) / 3
        }
