"""
Agent Switch Node for LangGraph-based Datagenius System.

This module provides explicit agent switching capabilities for user-controlled
routing in the Datagenius multi-agent system. Repurposed from RoutingNode
to focus only on explicit agent switch requests.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import re
from pathlib import Path
import yaml

from ..states.unified_state import UnifiedDatageniusState, ConversationMode, set_selected_agent
from .base_agent_node import BaseAgentNode

logger = logging.getLogger(__name__)


class AgentSwitchNode:
    """
    Repurposed routing node for explicit agent switches only.

    This node handles only user-requested agent switches, removing automatic
    routing analysis to implement user-centric routing architecture.
    """
    
    def __init__(self, available_agent_nodes: Dict[str, BaseAgentNode]):
        """
        Initialize the agent switch node for explicit agent switching.

        Args:
            available_agent_nodes: Dictionary of available agent nodes
        """
        self.available_agent_nodes = available_agent_nodes
        self.logger = logging.getLogger(__name__)

        # Load minimal configuration for agent switching
        self.switch_patterns = self._load_switch_patterns()
        self.default_agent_id = self._determine_default_agent_id()

        # Build performance-optimized agent lookup dictionary
        self.agent_lookup_dict = self._build_agent_lookup_dict()

        # Initialize rate limiting
        self.switch_request_counts = {}
        self.switch_rate_limit = 10  # Max 10 switches per minute per user
        self.rate_limit_window = 60  # 60 seconds

        self.logger.info(f"AgentSwitchNode initialized with {len(self.available_agent_nodes)} agents")
        self.logger.debug(f"Available agents: {list(self.available_agent_nodes.keys())}")
        self.logger.debug(f"Default agent: {self.default_agent_id}")
        self.logger.debug(f"Loaded {len(self.switch_patterns)} switch patterns")
        self.logger.debug(f"Built lookup dictionary with {len(self.agent_lookup_dict)} entries")

    def _load_switch_patterns(self) -> List[str]:
        """Load agent switch patterns from configuration file."""
        try:
            config_path = Path(__file__).parent.parent / "config" / "agent_switch_patterns.yaml"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    patterns = config.get('patterns', [])
                    self.logger.info(f"Loaded {len(patterns)} switch patterns from config")

                    # Load additional configuration
                    self._load_additional_config(config)

                    return patterns
            else:
                self.logger.warning(f"Config file not found: {config_path}, using defaults")
                return self._get_default_switch_patterns()
        except Exception as e:
            self.logger.error(f"Error loading switch patterns config: {e}")
            return self._get_default_switch_patterns()

    def _get_default_switch_patterns(self) -> List[str]:
        """Get default switch patterns as fallback."""
        return [
            r"switch to (\w+)",
            r"talk to (\w+)",
            r"connect me with (\w+)",
            r"I want to speak with (\w+)",
            r"can I talk to (\w+)",
            r"transfer me to (\w+)",
            r"route me to (\w+)",
            r"send me to (\w+)",
            r"go to (\w+)",
            r"take me to (\w+)"
        ]

    def _load_additional_config(self, config: Dict[str, Any]) -> None:
        """Load additional configuration from the config file."""
        # Load aliases for better agent matching
        self.agent_aliases = config.get('aliases', {})

        # Load rate limiting config
        rate_config = config.get('rate_limiting', {})
        self.switch_rate_limit = rate_config.get('max_switches_per_minute', 10)
        self.rate_limit_window = rate_config.get('rate_limit_window_seconds', 60)

        # Load security config
        security_config = config.get('security', {})
        self.max_message_length = security_config.get('max_message_length', 1000)
        self.agent_id_pattern = security_config.get('allowed_agent_id_pattern', r'^[a-zA-Z0-9_-]+$')
        self.max_agent_id_length = security_config.get('max_agent_id_length', 50)
        self.enable_audit_logging = security_config.get('enable_audit_logging', True)

        # Load performance config
        performance_config = config.get('performance', {})
        self.enable_agent_lookup_cache = performance_config.get('enable_agent_lookup_cache', True)
        self.max_concurrent_switches = performance_config.get('max_concurrent_switches', 5)

    def _determine_default_agent_id(self) -> Optional[str]:
        """Determine the default fallback agent ID."""
        # Prefer concierge agents as default
        for agent_id in self.available_agent_nodes.keys():
            if "concierge" in agent_id.lower():
                return agent_id

        # Fallback to first available agent
        if self.available_agent_nodes:
            return next(iter(self.available_agent_nodes.keys()))

        return None

    def _build_agent_lookup_dict(self) -> Dict[str, str]:
        """
        Build performance-optimized agent lookup dictionary.

        Creates multiple lookup keys for each agent to enable fast O(1) lookups
        instead of O(n) iterations through agent names.

        Returns:
            Dictionary mapping various agent name formats to agent IDs
        """
        lookup_dict = {}

        for agent_id in self.available_agent_nodes.keys():
            # Add exact match
            lookup_dict[agent_id.lower()] = agent_id

            # Add without hyphens/underscores
            clean_name = agent_id.lower().replace('-', '').replace('_', '')
            lookup_dict[clean_name] = agent_id

            # Add first part (before hyphen/underscore)
            if '-' in agent_id:
                first_part = agent_id.split('-')[0].lower()
                if first_part not in lookup_dict:  # Avoid conflicts
                    lookup_dict[first_part] = agent_id
            elif '_' in agent_id:
                first_part = agent_id.split('_')[0].lower()
                if first_part not in lookup_dict:
                    lookup_dict[first_part] = agent_id

            # Add configured aliases
            for alias_group, aliases in getattr(self, 'agent_aliases', {}).items():
                if alias_group.lower() in agent_id.lower():
                    for alias in aliases:
                        lookup_dict[alias.lower()] = agent_id

        return lookup_dict

    def _is_rate_limited(self, user_id: str) -> bool:
        """
        Check if user is rate limited for agent switches.

        Args:
            user_id: User identifier

        Returns:
            True if user is rate limited, False otherwise
        """
        current_time = datetime.now().timestamp()

        # Clean old entries
        self._cleanup_rate_limit_data(current_time)

        # Check current user's rate limit
        if user_id not in self.switch_request_counts:
            self.switch_request_counts[user_id] = []

        user_requests = self.switch_request_counts[user_id]
        recent_requests = [
            req_time for req_time in user_requests
            if current_time - req_time < self.rate_limit_window
        ]

        return len(recent_requests) >= self.switch_rate_limit

    def _record_switch_request(self, user_id: str) -> None:
        """Record a switch request for rate limiting."""
        current_time = datetime.now().timestamp()

        if user_id not in self.switch_request_counts:
            self.switch_request_counts[user_id] = []

        self.switch_request_counts[user_id].append(current_time)

    def _cleanup_rate_limit_data(self, current_time: float) -> None:
        """Clean up old rate limit data to prevent memory leaks."""
        cutoff_time = current_time - self.rate_limit_window

        for user_id in list(self.switch_request_counts.keys()):
            self.switch_request_counts[user_id] = [
                req_time for req_time in self.switch_request_counts[user_id]
                if req_time > cutoff_time
            ]

            # Remove empty entries
            if not self.switch_request_counts[user_id]:
                del self.switch_request_counts[user_id]

    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """Load intent patterns from configuration file."""
        try:
            config_path = Path(__file__).parent.parent / "config" / "routing_config.yaml"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    patterns = config.get('intent_patterns', {})
                    self.logger.debug(f"Loaded intent patterns from {config_path}")
                    return patterns
            else:
                self.logger.warning(f"Configuration file not found: {config_path}")
                return self._get_default_intent_patterns()
        except Exception as e:
            self.logger.error(f"Error loading intent patterns: {e}")
            return self._get_default_intent_patterns()

    def _load_routing_rules(self) -> Dict[str, Any]:
        """Load routing rules from configuration file."""
        try:
            config_path = Path(__file__).parent.parent / "config" / "routing_config.yaml"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    rules = config.get('routing_rules', {})
                    self.logger.debug(f"Loaded routing rules from {config_path}")
                    return rules
            else:
                self.logger.warning(f"Configuration file not found: {config_path}")
                return self._get_default_routing_rules()
        except Exception as e:
            self.logger.error(f"Error loading routing rules: {e}")
            return self._get_default_routing_rules()

    def _get_default_intent_patterns(self) -> Dict[str, List[str]]:
        """Get default intent patterns as fallback."""
        return {
            "greeting": ["hello", "hi", "hey", "good morning", "good afternoon", "good evening", "greetings"],
            "general_inquiry": ["what", "how", "why", "explain", "tell me", "describe", "help me understand"],
            "persona_recommendation": ["recommend", "suggest", "which agent", "best for", "who should", "what persona"],
            "data_analysis": ["analyze", "analysis", "statistics", "data", "chart", "graph", "metrics", "insights"],
            "visualization": ["visualize", "plot", "chart", "graph", "dashboard", "display", "show me"]
        }

    def _get_default_routing_rules(self) -> Dict[str, Any]:
        """Get default routing rules as fallback."""
        return {
            "default_mappings": {
                "greeting": "concierge",
                "general_inquiry": "concierge",
                "persona_recommendation": "concierge",
                "data_analysis": "analysis",
                "visualization": "analysis"
            }
        }

    def _build_agent_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Build a comprehensive index of agent capabilities."""
        capabilities = {}

        for agent_id, agent_node in self.agent_nodes.items():
            try:
                agent_info = {
                    "capabilities": getattr(agent_node, 'capabilities', []),
                    "supported_intents": getattr(agent_node, 'supported_intents', []),
                    "tools": getattr(agent_node, 'tools', []),
                    "agent_type": getattr(agent_node, 'agent_type', 'general'),
                    "priority": self._calculate_agent_priority(agent_id, agent_node)
                }
                capabilities[agent_id] = agent_info
                self.logger.debug(f"Agent {agent_id}: {agent_info}")
            except Exception as e:
                self.logger.warning(f"Could not extract capabilities for agent {agent_id}: {e}")
                capabilities[agent_id] = {
                    "capabilities": [],
                    "supported_intents": [],
                    "tools": [],
                    "agent_type": "general",
                    "priority": 0.5
                }

        return capabilities

    def _calculate_agent_priority(self, agent_id: str, agent_node: BaseAgentNode) -> float:
        """Calculate priority score for an agent based on its characteristics."""
        priority = 0.5  # Base priority

        # Concierge agents get higher priority for general queries
        if "concierge" in agent_id.lower():
            priority += 0.3

        # Specialized agents get priority for their domain
        if hasattr(agent_node, 'capabilities'):
            capability_count = len(agent_node.capabilities)
            if capability_count > 5:
                priority += 0.2  # Highly capable agents
            elif capability_count > 2:
                priority += 0.1  # Moderately capable agents

        return min(priority, 1.0)

    def _build_intent_mappings(self) -> Dict[str, List[str]]:
        """Build dynamic intent to agent mappings."""
        mappings = {}

        for agent_id, info in self.agent_capabilities.items():
            for intent in info["supported_intents"]:
                if intent not in mappings:
                    mappings[intent] = []
                mappings[intent].append(agent_id)

        return mappings

    def _build_keyword_index(self) -> Dict[str, List[str]]:
        """Build keyword to agent index for fast lookup."""
        index = {}

        for agent_id, info in self.agent_capabilities.items():
            # Index capabilities
            for capability in info["capabilities"]:
                keywords = capability.lower().split("_")
                for keyword in keywords:
                    if keyword not in index:
                        index[keyword] = []
                    if agent_id not in index[keyword]:
                        index[keyword].append(agent_id)

            # Index agent type
            agent_type = info["agent_type"].lower()
            if agent_type not in index:
                index[agent_type] = []
            if agent_id not in index[agent_type]:
                index[agent_type].append(agent_id)

        return index

    def _determine_default_agent(self) -> Optional[str]:
        """Determine the default fallback agent."""
        # Prefer concierge agents as default
        for agent_id in self.agent_nodes.keys():
            if "concierge" in agent_id.lower():
                return agent_id

        # Fallback to first available agent
        if self.agent_nodes:
            return next(iter(self.agent_nodes.keys()))

        return None
    
    async def execute(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Handle explicit agent switch requests only.

        This method only processes explicit user requests to switch agents,
        removing automatic routing analysis to implement user-centric routing.

        Args:
            state: Current workflow state

        Returns:
            Updated state with agent switch if requested
        """
        try:
            self.logger.info("🔄 AgentSwitchNode: Checking for explicit agent switch requests")

            # Validate state
            if not state:
                self.logger.error("❌ AgentSwitchNode: No state provided")
                return state or {}

            # Extract user ID for rate limiting
            user_id = state.get("user_id", "anonymous")

            # Check rate limiting
            if self._is_rate_limited(user_id):
                self.logger.warning(f"⚠️ AgentSwitchNode: Rate limit exceeded for user {user_id}")
                rate_limit_message = (
                    f"You've made too many agent switch requests recently. "
                    f"Please wait a moment before trying again. "
                    f"(Limit: {self.switch_rate_limit} switches per {self.rate_limit_window} seconds)"
                )
                state["messages"].append({
                    "role": "assistant",
                    "content": rate_limit_message,
                    "timestamp": datetime.now().isoformat(),
                    "agent_id": "agent_switch_node",
                    "metadata": {"type": "rate_limit_warning"}
                })
                return state

            # Extract current message
            current_message = state.get("current_message")
            if not current_message:
                # Check if there are any messages in the messages list
                messages = state.get("messages", [])
                if messages:
                    current_message = messages[-1]  # Use the last message
                    state["current_message"] = current_message
                    self.logger.info(f"✅ AgentSwitchNode: Using last message from history")
                else:
                    self.logger.info("ℹ️ AgentSwitchNode: No messages found, no switch needed")
                    return state

            message_content = current_message.get("content", "")
            self.logger.info(f"🔍 AgentSwitchNode: Analyzing message for switch requests: {message_content[:100]}...")

            # Only analyze for explicit switch requests
            if self._is_explicit_switch_request(message_content):
                # Record the switch request for rate limiting
                self._record_switch_request(user_id)

                target_agent_id = self._extract_target_agent(message_content)
                if target_agent_id and target_agent_id in self.available_agent_nodes:
                    self.logger.info(f"🔄 AgentSwitchNode: Explicit switch detected to: {target_agent_id}")

                    # Security: Validate agent ID format
                    if not self._is_valid_agent_id(target_agent_id):
                        self.logger.warning(f"⚠️ Invalid agent ID format: {target_agent_id}")
                        return state

                    state = set_selected_agent(state, target_agent_id, ConversationMode.AGENT_SWITCH)

                    # Add confirmation message with security audit
                    switch_confirmation_message = f"Switching you to {target_agent_id}. How can they help you?"
                    state["messages"].append({
                        "role": "assistant",
                        "content": switch_confirmation_message,
                        "timestamp": datetime.now().isoformat(),
                        "agent_id": "agent_switch_node",
                        "metadata": {
                            "type": "agent_switch_confirmation",
                            "target_agent": target_agent_id,
                            "user_id": user_id
                        }
                    })

                    # Add security audit log
                    self._log_agent_switch_audit(user_id, target_agent_id, "success")

                else:
                    self.logger.info(f"⚠️ AgentSwitchNode: Switch requested but target agent '{target_agent_id}' not found")
                    # Add error message
                    available_agent_list = ', '.join(self.available_agent_nodes.keys())
                    error_message = f"I couldn't find an agent named '{target_agent_id}'. Available agents: {available_agent_list}"
                    state["messages"].append({
                        "role": "assistant",
                        "content": error_message,
                        "timestamp": datetime.now().isoformat(),
                        "agent_id": "agent_switch_node",
                        "metadata": {
                            "type": "agent_switch_error",
                            "requested_agent": target_agent_id,
                            "user_id": user_id
                        }
                    })

                    # Add security audit log
                    self._log_agent_switch_audit(user_id, target_agent_id, "failed_invalid_agent")

            else:
                self.logger.info("ℹ️ AgentSwitchNode: No explicit switch request detected")

            state["updated_at"] = datetime.now().isoformat()
            return state

        except Exception as e:
            self.logger.error(f"❌ Error in AgentSwitchNode: {e}", exc_info=True)
            return state

    def _is_valid_agent_id(self, agent_id: str) -> bool:
        """
        Validate agent ID format for security.

        Args:
            agent_id: Agent ID to validate

        Returns:
            True if valid, False otherwise
        """
        if not agent_id or not isinstance(agent_id, str):
            return False

        # Use configured pattern for validation
        pattern = getattr(self, 'agent_id_pattern', r'^[a-zA-Z0-9_-]+$')
        if not re.match(pattern, agent_id):
            return False

        # Use configured length limits
        max_length = getattr(self, 'max_agent_id_length', 50)
        if len(agent_id) > max_length:
            return False

        return True

    def _log_agent_switch_audit(self, user_id: str, target_agent_id: str, status: str) -> None:
        """
        Log agent switch attempts for security auditing.

        Args:
            user_id: User making the switch request
            target_agent_id: Target agent ID
            status: Status of the switch attempt
        """
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": "agent_switch_attempt",
            "user_id": user_id,
            "target_agent_id": target_agent_id,
            "status": status,
            "source": "AgentSwitchNode"
        }

        # Log to security audit logger (separate from regular logs)
        security_logger = logging.getLogger("security_audit")
        security_logger.info(f"AGENT_SWITCH: {audit_entry}")

    def _is_explicit_switch_request(self, message: str) -> bool:
        """
        Detect explicit agent switch requests using natural language patterns.

        Args:
            message: User message content

        Returns:
            True if explicit switch request detected, False otherwise
        """
        # Input validation
        if not message or not isinstance(message, str):
            return False

        # Security: Limit message length to prevent DoS
        max_length = getattr(self, 'max_message_length', 1000)
        if len(message) > max_length:
            self.logger.warning(f"Message too long for switch detection: {len(message)} chars (max: {max_length})")
            return False

        message_lower = message.lower().strip()

        # Additional validation
        if not message_lower:
            return False

        # Check for explicit switch patterns using loaded patterns
        for pattern in self.switch_patterns:
            if re.search(pattern, message_lower):
                self.logger.info(f"🔍 Explicit switch pattern detected: {pattern}")
                return True

        return False

    def _extract_target_agent(self, message: str) -> Optional[str]:
        """
        Extract target agent from explicit switch request.

        Args:
            message: User message content

        Returns:
            Target agent ID if found, None otherwise
        """
        # Input validation
        if not message or not isinstance(message, str):
            return None

        # Security: Limit message length
        if len(message) > 1000:
            self.logger.warning(f"Message too long for agent extraction: {len(message)} chars")
            return None

        message_lower = message.lower().strip()

        if not message_lower:
            return None

        # Try to extract agent name from switch patterns
        for pattern in self.switch_patterns:
            match = re.search(pattern, message_lower)
            if match:
                potential_agent = match.group(1).lower()
                self.logger.info(f"🎯 Extracted potential agent: {potential_agent}")

                # Use optimized O(1) lookup instead of O(n) iteration
                if potential_agent in self.agent_lookup_dict:
                    matched_agent_id = self.agent_lookup_dict[potential_agent]
                    self.logger.info(f"✅ Fast lookup matched to agent: {matched_agent_id}")
                    return matched_agent_id

                # Fallback: partial matching for edge cases
                for lookup_key, agent_id in self.agent_lookup_dict.items():
                    if potential_agent in lookup_key or lookup_key in potential_agent:
                        self.logger.info(f"✅ Partial match found: {agent_id}")
                        return agent_id

        return None

    # Legacy routing analysis methods removed for AgentSwitchNode
    # These methods are no longer needed as we only handle explicit switches

    def get_available_agents(self) -> List[str]:
        """Get list of available agent IDs."""
        return list(self.available_agent_nodes.keys())

    def get_switch_patterns(self) -> List[str]:
        """Get agent switch patterns."""
        return self.switch_patterns.copy()

    def get_default_agent_id(self) -> Optional[str]:
        """Get default agent ID."""
        return self.default_agent_id


# Legacy RoutingNode class for backward compatibility
class RoutingNode(AgentSwitchNode):
    """
    Legacy RoutingNode class that extends AgentSwitchNode for backward compatibility.

    This ensures existing code that references RoutingNode continues to work
    while the new AgentSwitchNode provides the updated functionality.
    """

    def __init__(self, agent_nodes: Dict[str, BaseAgentNode]):
        """Initialize with backward compatibility."""
        super().__init__(agent_nodes)
        self.logger.info("🔄 RoutingNode initialized as AgentSwitchNode for backward compatibility")

    # Legacy methods for backward compatibility only
    def _detect_intent(self, message: str) -> str:  # pylint: disable=unused-argument
        """Legacy method for backward compatibility."""
        return "general_inquiry"

    def _analyze_context(self, state: UnifiedDatageniusState) -> Dict[str, Any]:  # pylint: disable=unused-argument
        """Legacy method for backward compatibility."""
        return {}

    def _score_agents(self, message: str, intent: str, context_factors: Dict[str, Any]) -> Dict[str, float]:  # pylint: disable=unused-argument
        """Legacy method for backward compatibility."""
        return {"concierge": 5.0}

    def _select_agent(self, agent_scores: Dict[str, float], state: UnifiedDatageniusState) -> str:  # pylint: disable=unused-argument
        """Legacy method for backward compatibility."""
        return self.default_agent_id or "concierge"

    def _calculate_confidence(self, agent_scores: Dict[str, float], selected_agent: str) -> float:  # pylint: disable=unused-argument
        """Legacy method for backward compatibility."""
        return 0.5

    def _default_routing(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """Legacy method for backward compatibility."""
        state["updated_at"] = datetime.now().isoformat()
        return state
    
    def get_available_agents(self) -> List[str]:
        """Get list of available agent IDs."""
        return list(self.agent_nodes.keys())
    
    def get_agent_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Get comprehensive agent capabilities."""
        return self.agent_capabilities.copy()

    def get_intent_mappings(self) -> Dict[str, List[str]]:
        """Get dynamic intent to agent mappings."""
        return self.intent_mappings.copy()

    def get_keyword_index(self) -> Dict[str, List[str]]:
        """Get keyword to agent index."""
        return self.keyword_index.copy()

    def add_agent_capability(self, agent_id: str, capability: str) -> None:
        """
        Dynamically add a capability to an agent.

        Args:
            agent_id: Agent ID
            capability: Capability to add
        """
        if agent_id in self.agent_capabilities:
            if capability not in self.agent_capabilities[agent_id]["capabilities"]:
                self.agent_capabilities[agent_id]["capabilities"].append(capability)
                # Rebuild keyword index
                self.keyword_index = self._build_keyword_index()
                self.logger.info(f"Added capability '{capability}' to agent {agent_id}")

    def update_agent_priority(self, agent_id: str, priority: float) -> None:
        """
        Update agent priority score.

        Args:
            agent_id: Agent ID
            priority: New priority score (0.0 to 1.0)
        """
        if agent_id in self.agent_capabilities:
            self.agent_capabilities[agent_id]["priority"] = max(0.0, min(1.0, priority))
            self.logger.info(f"Updated priority for agent {agent_id} to {priority}")
