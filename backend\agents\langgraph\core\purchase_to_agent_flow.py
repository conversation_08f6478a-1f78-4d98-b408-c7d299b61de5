"""
Purchase-to-Agent Flow Manager for Marketplace Integration.

This module manages the complete data flow from persona purchase to agent
availability in the backend system, including registration, configuration,
and cross-agent intelligence integration.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
import uuid

from .marketplace_database_manager import marketplace_db_manager
from .persona_configuration_manager import persona_config_manager

logger = logging.getLogger(__name__)


@dataclass
class PurchaseEvent:
    """Represents a persona purchase event."""
    purchase_id: str
    user_id: str
    persona_id: str
    purchase_timestamp: datetime
    payment_status: str
    metadata: Dict[str, Any]


@dataclass
class AgentRegistrationResult:
    """Result of agent registration process."""
    success: bool
    agent_id: Optional[str]
    persona_id: str
    user_id: str
    registration_timestamp: datetime
    errors: List[str]
    warnings: List[str]


class PurchaseToAgentFlowManager:
    """
    Manages the complete flow from persona purchase to agent availability.
    
    Features:
    - Purchase event processing
    - Agent registration and configuration
    - Cross-agent intelligence integration
    - Error handling and retry mechanisms
    - Performance monitoring and metrics
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_manager = marketplace_db_manager
        self.config_manager = persona_config_manager
        
        # Flow state tracking
        self._active_flows: Dict[str, Dict[str, Any]] = {}
        self._completed_flows: Dict[str, AgentRegistrationResult] = {}
        self._failed_flows: Dict[str, Dict[str, Any]] = {}
        
        # Configuration
        self.max_retry_attempts = 3
        self.retry_delay = timedelta(seconds=30)
        self.flow_timeout = timedelta(minutes=10)
        
        # Event handlers
        self._event_handlers: Dict[str, List[callable]] = {
            "purchase_received": [],
            "agent_registered": [],
            "registration_failed": [],
            "flow_completed": []
        }
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        
        # Initialize flow manager
        self._initialize_flow_manager()
    
    def _initialize_flow_manager(self) -> None:
        """Initialize the flow manager."""
        try:
            self.logger.info("Initializing purchase-to-agent flow manager...")
            
            # Start background tasks
            self._start_background_tasks()
            
            self.logger.info("Purchase-to-agent flow manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize flow manager: {e}")
            raise
    
    def _start_background_tasks(self) -> None:
        """Start background tasks for flow monitoring and cleanup."""
        try:
            # Flow monitoring task
            task = asyncio.create_task(self._flow_monitoring_task())
            self._background_tasks.append(task)
            
            # Retry failed flows task
            task = asyncio.create_task(self._retry_failed_flows_task())
            self._background_tasks.append(task)
            
            # Cleanup completed flows task
            task = asyncio.create_task(self._cleanup_completed_flows_task())
            self._background_tasks.append(task)
            
            self.logger.info("Background tasks started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start background tasks: {e}")
    
    async def process_purchase_event(
        self,
        purchase_event: PurchaseEvent
    ) -> AgentRegistrationResult:
        """
        Process a persona purchase event and register the agent.
        
        Args:
            purchase_event: Purchase event data
            
        Returns:
            Agent registration result
        """
        flow_id = f"{purchase_event.user_id}_{purchase_event.persona_id}_{purchase_event.purchase_id}"
        
        try:
            self.logger.info(f"Processing purchase event for flow {flow_id}")
            
            # Initialize flow tracking
            self._active_flows[flow_id] = {
                "purchase_event": purchase_event,
                "start_time": datetime.utcnow(),
                "status": "processing",
                "retry_count": 0,
                "last_error": None
            }
            
            # Trigger purchase received event
            await self._trigger_event("purchase_received", {
                "flow_id": flow_id,
                "purchase_event": purchase_event
            })
            
            # Validate purchase event
            validation_result = await self._validate_purchase_event(purchase_event)
            if not validation_result["is_valid"]:
                raise ValueError(f"Invalid purchase event: {validation_result['errors']}")
            
            # Check if persona configuration exists
            persona_config = await self.config_manager.get_persona_configuration(
                purchase_event.persona_id
            )
            
            if not persona_config:
                # Create default configuration for purchased persona
                await self._create_default_persona_configuration(purchase_event.persona_id)
                persona_config = await self.config_manager.get_persona_configuration(
                    purchase_event.persona_id
                )
            
            # Register agent with marketplace
            registration_result = await self._register_agent_with_marketplace(
                purchase_event, persona_config
            )
            
            if registration_result.success:
                # Register with cross-agent intelligence
                await self._register_with_cross_agent_intelligence(
                    registration_result.agent_id,
                    purchase_event.user_id,
                    persona_config
                )
                
                # Update flow status
                self._active_flows[flow_id]["status"] = "completed"
                self._completed_flows[flow_id] = registration_result
                
                # Trigger events
                await self._trigger_event("agent_registered", {
                    "flow_id": flow_id,
                    "registration_result": registration_result
                })
                
                await self._trigger_event("flow_completed", {
                    "flow_id": flow_id,
                    "registration_result": registration_result
                })
                
                self.logger.info(f"Successfully completed flow {flow_id}")
                
            else:
                # Handle registration failure
                self._active_flows[flow_id]["status"] = "failed"
                self._active_flows[flow_id]["last_error"] = registration_result.errors
                
                await self._trigger_event("registration_failed", {
                    "flow_id": flow_id,
                    "registration_result": registration_result
                })
                
                self.logger.error(f"Registration failed for flow {flow_id}: {registration_result.errors}")
            
            return registration_result
            
        except Exception as e:
            self.logger.error(f"Error processing purchase event for flow {flow_id}: {e}")
            
            # Update flow status
            if flow_id in self._active_flows:
                self._active_flows[flow_id]["status"] = "error"
                self._active_flows[flow_id]["last_error"] = str(e)
            
            # Create error result
            error_result = AgentRegistrationResult(
                success=False,
                agent_id=None,
                persona_id=purchase_event.persona_id,
                user_id=purchase_event.user_id,
                registration_timestamp=datetime.utcnow(),
                errors=[str(e)],
                warnings=[]
            )
            
            return error_result
        
        finally:
            # Clean up active flow
            if flow_id in self._active_flows:
                del self._active_flows[flow_id]
    
    async def _validate_purchase_event(
        self,
        purchase_event: PurchaseEvent
    ) -> Dict[str, Any]:
        """
        Validate purchase event data.
        
        Args:
            purchase_event: Purchase event to validate
            
        Returns:
            Validation result
        """
        try:
            errors = []
            warnings = []
            
            # Required fields validation
            if not purchase_event.purchase_id:
                errors.append("Purchase ID is required")
            
            if not purchase_event.user_id:
                errors.append("User ID is required")
            
            if not purchase_event.persona_id:
                errors.append("Persona ID is required")
            
            # Payment status validation
            valid_statuses = ["completed", "pending", "processing"]
            if purchase_event.payment_status not in valid_statuses:
                errors.append(f"Invalid payment status: {purchase_event.payment_status}")
            
            # Check if user already has this persona
            user_personas = await self.db_manager.get_user_purchased_personas(purchase_event.user_id)
            if purchase_event.persona_id in user_personas:
                warnings.append("User already has this persona")
            
            return {
                "is_valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings
            }
            
        except Exception as e:
            self.logger.error(f"Error validating purchase event: {e}")
            return {
                "is_valid": False,
                "errors": [f"Validation error: {str(e)}"],
                "warnings": []
            }
    
    async def _create_default_persona_configuration(
        self,
        persona_id: str
    ) -> bool:
        """
        Create default configuration for a persona.
        
        Args:
            persona_id: Persona identifier
            
        Returns:
            True if created successfully
        """
        try:
            # Get industry template if available
            industry_template = await self.config_manager.get_industry_template("general")
            
            # Create default configuration
            default_config = {
                "persona_id": persona_id,
                "name": persona_id.replace("_", " ").title(),
                "description": f"AI assistant specialized in {persona_id}",
                "industry_specialization": "general",
                "methodology_framework": "UNDERSTAND_ASSESS_EXECUTE_DELIVER",
                "enable_cross_agent_intelligence": True,
                "enable_business_profile_context": True,
                "specialized_tools": industry_template.get("specialized_tools", []) if industry_template else [],
                "compliance_requirements": industry_template.get("compliance_requirements", []) if industry_template else [],
                "capabilities": ["general_assistance", "data_analysis", "communication"],
                "skills": ["problem_solving", "information_processing", "user_interaction"],
                "pricing_model": "fixed",
                "base_price": 10.0,
                "certification_level": "basic",
                "version": "1.0.0",
                "is_active": True
            }
            
            # Create configuration
            success = await self.config_manager.create_persona_configuration(
                persona_id, default_config, validate=True
            )
            
            if success:
                self.logger.info(f"Created default configuration for persona {persona_id}")
            else:
                self.logger.error(f"Failed to create default configuration for persona {persona_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error creating default configuration for persona {persona_id}: {e}")
            return False

    async def _register_agent_with_marketplace(
        self,
        purchase_event: PurchaseEvent,
        persona_config: Dict[str, Any]
    ) -> AgentRegistrationResult:
        """
        Register agent with marketplace system.

        Args:
            purchase_event: Purchase event data
            persona_config: Persona configuration

        Returns:
            Agent registration result
        """
        try:
            agent_id = f"{purchase_event.persona_id}_{purchase_event.user_id}_{uuid.uuid4().hex[:8]}"

            self.logger.info(f"Registering agent {agent_id} for persona {purchase_event.persona_id}")

            # Register agent capabilities with marketplace
            try:
                from ..marketplace.capability_marketplace import CapabilityMarketplace
                capability_marketplace = CapabilityMarketplace()

                # Prepare capabilities for registration
                capabilities = []
                for capability_name in persona_config.get("capabilities", []):
                    capability_def = {
                        "name": capability_name,
                        "type": "general",
                        "description": f"{capability_name} capability for {purchase_event.persona_id}",
                        "pricing_model": persona_config.get("pricing_model", "fixed"),
                        "base_price": persona_config.get("base_price", 10.0),
                        "performance_metrics": {
                            "success_rate": 1.0,
                            "avg_response_time": 2.0,
                            "reliability_score": 1.0
                        },
                        "sla_requirements": {
                            "max_response_time": 30.0,
                            "min_availability": 0.99,
                            "max_error_rate": 0.01
                        }
                    }
                    capabilities.append(capability_def)

                # Register capabilities
                await capability_marketplace.register_agent_capabilities(agent_id, capabilities)

            except ImportError:
                self.logger.warning("Capability marketplace not available, skipping capability registration")
            except Exception as e:
                self.logger.error(f"Error registering capabilities: {e}")

            # Save agent registration to database
            registration_data = {
                "agent_id": agent_id,
                "persona_id": purchase_event.persona_id,
                "user_id": purchase_event.user_id,
                "purchase_id": purchase_event.purchase_id,
                "configuration": persona_config,
                "registration_timestamp": datetime.utcnow(),
                "status": "active"
            }

            # Store in database (using persona configuration as proxy)
            await self.db_manager.save_persona_configuration(
                f"agent_{agent_id}",
                registration_data,
                industry_specialization=persona_config.get("industry_specialization"),
                methodology_framework=persona_config.get("methodology_framework"),
                enable_cross_agent_intelligence=persona_config.get("enable_cross_agent_intelligence", True),
                specialized_tools=persona_config.get("specialized_tools", []),
                compliance_requirements=persona_config.get("compliance_requirements", []),
                workflow_patterns=persona_config.get("workflow_patterns", []),
                performance_optimization=persona_config.get("performance_optimization", {})
            )

            # Create successful registration result
            result = AgentRegistrationResult(
                success=True,
                agent_id=agent_id,
                persona_id=purchase_event.persona_id,
                user_id=purchase_event.user_id,
                registration_timestamp=datetime.utcnow(),
                errors=[],
                warnings=[]
            )

            self.logger.info(f"Successfully registered agent {agent_id}")
            return result

        except Exception as e:
            self.logger.error(f"Error registering agent: {e}")

            # Create failed registration result
            result = AgentRegistrationResult(
                success=False,
                agent_id=None,
                persona_id=purchase_event.persona_id,
                user_id=purchase_event.user_id,
                registration_timestamp=datetime.utcnow(),
                errors=[str(e)],
                warnings=[]
            )

            return result

    async def _register_with_cross_agent_intelligence(
        self,
        agent_id: str,
        user_id: str,
        persona_config: Dict[str, Any]
    ) -> bool:
        """
        Register agent with cross-agent intelligence system.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            persona_config: Persona configuration

        Returns:
            True if registration successful
        """
        try:
            if not persona_config.get("enable_cross_agent_intelligence", True):
                self.logger.info(f"Cross-agent intelligence disabled for agent {agent_id}")
                return True

            # Import cross-agent intelligence service
            try:
                from ..intelligence.cross_agent_intelligence import CrossAgentIntelligenceService
                cross_agent_service = CrossAgentIntelligenceService()

                # Register agent with cross-agent intelligence
                await cross_agent_service.register_agent(
                    agent_id=agent_id,
                    business_profile=None,  # Will be loaded from user context
                    capabilities=persona_config.get("capabilities", []),
                    specializations=persona_config.get("specialized_tools", [])
                )

                self.logger.info(f"Registered agent {agent_id} with cross-agent intelligence")
                return True

            except ImportError:
                self.logger.warning("Cross-agent intelligence service not available")
                return False

        except Exception as e:
            self.logger.error(f"Error registering with cross-agent intelligence: {e}")
            return False

    async def _flow_monitoring_task(self) -> None:
        """Background task to monitor active flows."""
        while True:
            try:
                current_time = datetime.utcnow()

                # Check for timed out flows
                timed_out_flows = []
                for flow_id, flow_data in self._active_flows.items():
                    start_time = flow_data["start_time"]
                    if current_time - start_time > self.flow_timeout:
                        timed_out_flows.append(flow_id)

                # Handle timed out flows
                for flow_id in timed_out_flows:
                    flow_data = self._active_flows[flow_id]
                    self.logger.warning(f"Flow {flow_id} timed out")

                    # Move to failed flows
                    self._failed_flows[flow_id] = {
                        **flow_data,
                        "failure_reason": "timeout",
                        "failed_at": current_time
                    }

                    # Remove from active flows
                    del self._active_flows[flow_id]

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Error in flow monitoring task: {e}")
                await asyncio.sleep(60)

    async def _retry_failed_flows_task(self) -> None:
        """Background task to retry failed flows."""
        while True:
            try:
                current_time = datetime.utcnow()

                # Find flows eligible for retry
                retry_flows = []
                for flow_id, flow_data in self._failed_flows.items():
                    retry_count = flow_data.get("retry_count", 0)
                    failed_at = flow_data.get("failed_at", current_time)

                    if (retry_count < self.max_retry_attempts and
                        current_time - failed_at > self.retry_delay):
                        retry_flows.append(flow_id)

                # Retry eligible flows
                for flow_id in retry_flows:
                    flow_data = self._failed_flows[flow_id]
                    purchase_event = flow_data["purchase_event"]

                    self.logger.info(f"Retrying failed flow {flow_id}")

                    # Increment retry count
                    flow_data["retry_count"] = flow_data.get("retry_count", 0) + 1

                    # Move back to active flows
                    self._active_flows[flow_id] = flow_data
                    del self._failed_flows[flow_id]

                    # Retry processing
                    try:
                        await self.process_purchase_event(purchase_event)
                    except Exception as e:
                        self.logger.error(f"Error retrying flow {flow_id}: {e}")

                await asyncio.sleep(300)  # Check every 5 minutes

            except Exception as e:
                self.logger.error(f"Error in retry failed flows task: {e}")
                await asyncio.sleep(300)

    async def _cleanup_completed_flows_task(self) -> None:
        """Background task to clean up old completed flows."""
        while True:
            try:
                current_time = datetime.utcnow()
                cleanup_threshold = current_time - timedelta(hours=24)

                # Find old completed flows
                old_flows = []
                for flow_id, result in self._completed_flows.items():
                    if result.registration_timestamp < cleanup_threshold:
                        old_flows.append(flow_id)

                # Clean up old flows
                for flow_id in old_flows:
                    del self._completed_flows[flow_id]
                    self.logger.debug(f"Cleaned up completed flow {flow_id}")

                # Clean up old failed flows
                old_failed_flows = []
                for flow_id, flow_data in self._failed_flows.items():
                    failed_at = flow_data.get("failed_at", current_time)
                    if failed_at < cleanup_threshold:
                        old_failed_flows.append(flow_id)

                for flow_id in old_failed_flows:
                    del self._failed_flows[flow_id]
                    self.logger.debug(f"Cleaned up failed flow {flow_id}")

                await asyncio.sleep(3600)  # Run every hour

            except Exception as e:
                self.logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(3600)

    async def _trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """
        Trigger event handlers.

        Args:
            event_type: Type of event
            event_data: Event data
        """
        try:
            handlers = self._event_handlers.get(event_type, [])
            for handler in handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(event_data)
                    else:
                        handler(event_data)
                except Exception as e:
                    self.logger.error(f"Error in event handler for {event_type}: {e}")

        except Exception as e:
            self.logger.error(f"Error triggering event {event_type}: {e}")

    def add_event_handler(self, event_type: str, handler: callable) -> None:
        """
        Add event handler.

        Args:
            event_type: Type of event
            handler: Handler function
        """
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)

    async def get_flow_status(self, flow_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of a specific flow.

        Args:
            flow_id: Flow identifier

        Returns:
            Flow status or None if not found
        """
        if flow_id in self._active_flows:
            return {
                "status": "active",
                "data": self._active_flows[flow_id]
            }
        elif flow_id in self._completed_flows:
            return {
                "status": "completed",
                "data": self._completed_flows[flow_id]
            }
        elif flow_id in self._failed_flows:
            return {
                "status": "failed",
                "data": self._failed_flows[flow_id]
            }
        else:
            return None

    async def cleanup(self) -> None:
        """Cleanup resources and stop background tasks."""
        try:
            # Cancel background tasks
            for task in self._background_tasks:
                if not task.done():
                    task.cancel()

            # Wait for tasks to complete
            if self._background_tasks:
                await asyncio.gather(*self._background_tasks, return_exceptions=True)

            # Clear flow data
            self._active_flows.clear()
            self._completed_flows.clear()
            self._failed_flows.clear()

            self.logger.info("Purchase-to-agent flow manager cleaned up successfully")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


# Global instance (lazy initialization)
_purchase_to_agent_flow_manager = None

def get_purchase_to_agent_flow_manager() -> PurchaseToAgentFlowManager:
    """Get the global purchase-to-agent flow manager instance (lazy initialization)."""
    global _purchase_to_agent_flow_manager
    if _purchase_to_agent_flow_manager is None:
        _purchase_to_agent_flow_manager = PurchaseToAgentFlowManager()
    return _purchase_to_agent_flow_manager

# For backward compatibility
purchase_to_agent_flow_manager = None
