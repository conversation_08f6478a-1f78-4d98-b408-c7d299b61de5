"""
Visualization Persona

This module provides a consolidated visualization agent that uses the unified conversation agent base
for consistent AI-powered responses and specialized data visualization capabilities.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .unified_conversation_agent import UnifiedConversationAgent

logger = logging.getLogger(__name__)


class VisualizationPersona(UnifiedConversationAgent):
    """
    Visualization persona that uses the unified conversation base.
    
    This persona provides:
    - Data visualization and charting
    - Dashboard creation
    - Interactive visualizations
    - Chart recommendations
    - Visual storytelling
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the visualization persona.
        
        Args:
            config: Optional configuration for the persona
        """
        # Get agent_id from config or use default
        agent_id = config.get('agent_id', 'visualization') if config else 'visualization'
        
        # Initialize parent class
        super().__init__(
            agent_id=agent_id,
            agent_type="visualization",
            config=config
        )
        
        # Visualization-specific configuration
        self.chart_types = config.get('chart_types', ['bar', 'line', 'pie', 'scatter', 'histogram', 'heatmap']) if config else ['bar', 'line', 'pie', 'scatter', 'histogram', 'heatmap']
        self.dashboard_layouts = config.get('dashboard_layouts', ['grid', 'flow', 'tabbed', 'sidebar']) if config else ['grid', 'flow', 'tabbed', 'sidebar']
        self.color_schemes = config.get('color_schemes', ['default', 'colorblind', 'high_contrast', 'brand']) if config else ['default', 'colorblind', 'high_contrast', 'brand']
        
        logger.info("VisualizationPersona initialized")

    def _get_specialized_capabilities(self) -> List[str]:
        """Get visualization-specific capabilities."""
        return [
            "data_visualization",
            "chart_creation",
            "dashboard_design",
            "interactive_visualizations",
            "visual_storytelling",
            "chart_recommendations",
            "color_optimization",
            "accessibility_design",
            "responsive_layouts",
            "export_formats"
        ]

    def _get_agent_name(self) -> str:
        """Get human-readable agent name."""
        return "Data Visualization Specialist"

    def _get_agent_description(self) -> str:
        """Get agent description."""
        return "Expert in data visualization, dashboard creation, and visual storytelling"

    def _determine_intent_type(self, message: str) -> str:
        """Determine the intent type for visualization conversations."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["dashboard", "board", "overview"]):
            return "dashboard_creation"
        elif any(word in message_lower for word in ["chart", "graph", "plot"]):
            return "chart_creation"
        elif any(word in message_lower for word in ["interactive", "dynamic", "clickable"]):
            return "interactive_visualization"
        elif any(word in message_lower for word in ["color", "theme", "style"]):
            return "visual_styling"
        elif any(word in message_lower for word in ["export", "download", "save"]):
            return "export_visualization"
        else:
            return "general_visualization"

    def _get_greeting_specialization(self) -> str:
        """Get visualization-specific greeting content."""
        return ("**📊 I specialize in:**\n"
                "• **Data Visualization** - Charts, graphs, and plots\n"
                "• **Dashboard Creation** - Interactive data dashboards\n"
                "• **Visual Storytelling** - Compelling data narratives\n"
                "• **Chart Recommendations** - Best visualization for your data\n"
                "• **Accessibility Design** - Inclusive and accessible visuals\n"
                "• **Export Options** - Multiple formats and resolutions")

    def _generate_capabilities_response(self) -> str:
        """Generate a response about visualization capabilities."""
        return ("I'm your **Data Visualization Specialist** with comprehensive visual design capabilities:\n\n"
                "**📊 Chart Creation:**\n"
                "• Statistical charts and graphs\n"
                "• Interactive and dynamic visualizations\n"
                "• Custom chart types and layouts\n\n"
                "**📈 Dashboard Design:**\n"
                "• Multi-panel dashboards\n"
                "• Real-time data displays\n"
                "• Responsive and mobile-friendly layouts\n\n"
                "**🎨 Visual Enhancement:**\n"
                "• Color optimization and theming\n"
                "• Accessibility-compliant designs\n"
                "• Brand-consistent styling\n\n"
                f"**Chart Types:** {', '.join(self.chart_types)}\n"
                f"**Dashboard Layouts:** {', '.join(self.dashboard_layouts)}\n"
                f"**Color Schemes:** {', '.join(self.color_schemes)}")

    async def _enhance_response(self, original_message: str, base_response: str, 
                              context: Optional[Dict[str, Any]] = None) -> str:
        """Enhance the response with visualization-specific information."""
        try:
            enhanced_response = base_response
            
            # Add chart type recommendations
            if self._is_chart_request(original_message):
                enhanced_response += f"\n\n📊 **Chart Options**: I can create {', '.join(self.chart_types)} charts optimized for your data type and audience."
            
            # Add dashboard layout suggestions
            if self._is_dashboard_request(original_message):
                enhanced_response += f"\n\n📈 **Dashboard Layouts**: Choose from {', '.join(self.dashboard_layouts)} layouts to best present your data story."
            
            # Add accessibility information
            if self._is_accessibility_related(original_message):
                enhanced_response += "\n\n♿ **Accessibility**: I ensure all visualizations are colorblind-friendly, screen reader compatible, and meet WCAG guidelines."
            
            # Add export format options
            if self._is_export_related(original_message):
                enhanced_response += "\n\n💾 **Export Formats**: I can generate visualizations in PNG, SVG, PDF, and interactive HTML formats for various use cases."
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing visualization response: {e}")
            return base_response

    def _is_chart_request(self, message: str) -> bool:
        """Check if the message is a chart request."""
        chart_keywords = ["chart", "graph", "plot", "visualization", "visual"]
        return any(keyword in message.lower() for keyword in chart_keywords)

    def _is_dashboard_request(self, message: str) -> bool:
        """Check if the message is a dashboard request."""
        dashboard_keywords = ["dashboard", "board", "overview", "summary", "panel"]
        return any(keyword in message.lower() for keyword in dashboard_keywords)

    def _is_accessibility_related(self, message: str) -> bool:
        """Check if the message mentions accessibility."""
        accessibility_keywords = ["accessibility", "colorblind", "screen reader", "inclusive"]
        return any(keyword in message.lower() for keyword in accessibility_keywords)

    def _is_export_related(self, message: str) -> bool:
        """Check if the message is about exporting."""
        export_keywords = ["export", "download", "save", "format", "file"]
        return any(keyword in message.lower() for keyword in export_keywords)

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this visualization persona."""
        base_info = super().get_agent_info()
        base_info.update({
            "specialization": "data_visualization_and_dashboards",
            "chart_types": self.chart_types,
            "dashboard_layouts": self.dashboard_layouts,
            "color_schemes": self.color_schemes,
            "supports_interactive_charts": True,
            "supports_dashboard_creation": True,
            "supports_accessibility_design": True
        })
        return base_info


# Backward compatibility aliases
RefactoredVisualizationAgent = VisualizationPersona
UserSelectedVisualizationAgent = VisualizationPersona
VisualizationAgent = VisualizationPersona
