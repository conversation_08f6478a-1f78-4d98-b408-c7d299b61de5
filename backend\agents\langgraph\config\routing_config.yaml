# LangGraph Intelligent Router Configuration
# This file defines routing patterns, agent mappings, and workflow configurations
# for the Datagenius multi-agent system

# Intent Detection Patterns
# These patterns are used to detect user intent from messages
intent_patterns:
  greeting:
    - "hello"
    - "hi"
    - "hey"
    - "good morning"
    - "good afternoon"
    - "good evening"
    - "greetings"
  
  general_inquiry:
    - "what"
    - "how"
    - "why"
    - "explain"
    - "tell me"
    - "describe"
    - "help me understand"
  
  persona_recommendation:
    - "recommend"
    - "suggest"
    - "which agent"
    - "best for"
    - "who should"
    - "what persona"
    - "which ai"
  
  data_analysis:
    - "analyze"
    - "analysis"
    - "statistics"
    - "data"
    - "chart"
    - "graph"
    - "metrics"
    - "insights"
    - "trends"
    - "patterns"
  
  visualization:
    - "visualize"
    - "plot"
    - "chart"
    - "graph"
    - "dashboard"
    - "display"
    - "show me"
    - "create chart"
  
  marketing:
    - "marketing"
    - "campaign"
    - "content"
    - "social media"
    - "advertising"
    - "promotion"
    - "brand"
    - "audience"
  
  classification:
    - "classify"
    - "categorize"
    - "label"
    - "tag"
    - "group"
    - "organize"
    - "sort"

# Agent Type Mappings
# Maps agent types to their capabilities and routing priorities
agent_types:
  concierge:
    capabilities:
      - "greeting"
      - "general_inquiry"
      - "persona_recommendation"
      - "routing"
    priority: 1
    fallback: true
    description: "General purpose agent for greetings and routing"
  
  analysis:
    capabilities:
      - "data_analysis"
      - "visualization"
      - "statistics"
      - "insights"
    priority: 2
    description: "Specialized agent for data analysis and visualization"
  
  marketing:
    capabilities:
      - "marketing"
      - "content_creation"
      - "campaign"
      - "social_media"
    priority: 2
    description: "Specialized agent for marketing and content creation"
  
  classification:
    capabilities:
      - "classification"
      - "categorization"
      - "labeling"
    priority: 3
    description: "Specialized agent for classification tasks"

# Routing Rules
# Define how intents map to agent types with complexity considerations
routing_rules:
  default_mappings:
    greeting: "concierge"
    general_inquiry: "concierge"
    persona_recommendation: "concierge"
    data_analysis: "analysis"
    visualization: "analysis"
    statistics: "analysis"
    marketing: "marketing"
    content_creation: "marketing"
    campaign: "marketing"
    classification: "composable-classifier-ai"
    categorization: "composable-classifier-ai"
  
  complexity_overrides:
    high:
      data_analysis: "advanced_analysis"  # If available
      visualization: "advanced_analysis"  # If available
    
    low:
      # Simple requests can be handled by concierge
      data_analysis: "concierge"
      marketing: "concierge"

# Multi-Agent Collaboration Triggers
collaboration_triggers:
  keywords:
    - "comprehensive analysis"
    - "multiple perspectives"
    - "cross-functional"
    - "analyze and create"
    - "research and develop"
    - "compare and recommend"
    - "full report"
    - "detailed study"
  
  complexity_threshold: "high"
  agent_count_threshold: 2

# Quality Thresholds
quality_settings:
  minimum_quality_score: 0.6
  retry_threshold: 0.7
  escalation_threshold: 0.4
  max_retries: 2

# Performance Settings
performance:
  cache_routing_decisions: true
  cache_duration_minutes: 30
  enable_learning: true
  learning_window_hours: 24
  
# Monitoring Settings
monitoring:
  track_routing_decisions: true
  track_agent_performance: true
  track_user_preferences: true
  log_level: "INFO"

# Extensibility Settings
extensibility:
  auto_discover_agents: true
  refresh_interval_minutes: 15
  allow_custom_patterns: true
  enable_runtime_updates: true
