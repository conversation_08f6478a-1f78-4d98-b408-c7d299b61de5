{"enabled": true, "log_all_plugin_operations": true, "log_security_violations": true, "log_permission_checks": true, "audit_file_path": "backend/logs/plugin_security_audit.log", "retention_days": 90, "alert_on_violations": true, "audit_levels": {"debug": {"enabled": false, "log_detailed_execution": true, "log_variable_access": true, "log_function_calls": true}, "info": {"enabled": true, "log_plugin_lifecycle": true, "log_permission_grants": true, "log_resource_usage": true}, "warning": {"enabled": true, "log_permission_denials": true, "log_resource_limits": true, "log_suspicious_activity": true}, "error": {"enabled": true, "log_security_violations": true, "log_execution_failures": true, "log_sandbox_breaches": true}, "critical": {"enabled": true, "log_system_compromises": true, "log_data_breaches": true, "immediate_alert": true}}, "audit_categories": {"plugin_lifecycle": {"enabled": true, "events": ["plugin_loaded", "plugin_initialized", "plugin_executed", "plugin_unloaded", "plugin_error"]}, "security_events": {"enabled": true, "events": ["permission_check", "permission_denied", "sandbox_violation", "security_scan_result", "suspicious_activity"]}, "resource_usage": {"enabled": true, "events": ["memory_usage", "cpu_usage", "execution_time", "network_access", "file_access"]}, "data_access": {"enabled": true, "events": ["data_read", "data_write", "data_delete", "sensitive_data_access", "data_export"]}}, "alert_configuration": {"enabled": true, "alert_channels": ["log_file", "system_notification"], "alert_thresholds": {"security_violations_per_hour": 5, "permission_denials_per_hour": 10, "failed_executions_per_hour": 20, "resource_limit_breaches_per_hour": 15}, "escalation_rules": {"critical_violations": {"immediate_alert": true, "disable_plugin": true, "notify_admin": true}, "repeated_violations": {"threshold": 3, "action": "temporary_disable", "duration_minutes": 60}, "suspicious_patterns": {"threshold": 5, "action": "enhanced_monitoring", "duration_minutes": 120}}}, "log_format": {"timestamp_format": "ISO8601", "include_stack_trace": true, "include_plugin_metadata": true, "include_user_context": true, "include_system_context": true, "structured_logging": true, "log_level_colors": false}, "retention_policy": {"default_retention_days": 90, "critical_events_retention_days": 365, "archive_old_logs": true, "compression_enabled": true, "max_log_file_size_mb": 100, "max_total_log_size_gb": 10}, "privacy_settings": {"anonymize_user_data": true, "hash_sensitive_fields": true, "exclude_personal_info": true, "gdpr_compliant": true, "data_minimization": true}}