"""
Permission-Based Collaboration Flow

This module implements collaboration flows that require user permission
before agents can communicate with each other. It ensures that all
agent-to-agent interactions are user-controlled and transparent.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..permissions.user_permission_manager import UserPermissionManager, PermissionRequest, PermissionResponse
from ..tools.network_collaboration_tools import NetworkCollaborationTools
from ..core.agent_registry import network_registry

logger = logging.getLogger(__name__)


class CollaborationFlowStatus(Enum):
    """Status of a collaboration flow."""
    INITIATED = "initiated"
    AWAITING_PERMISSION = "awaiting_permission"
    PERMISSION_GRANTED = "permission_granted"
    PERMISSION_DENIED = "permission_denied"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class CollaborationFlowRequest:
    """Request to start a collaboration flow."""
    flow_id: str
    requesting_agent_id: str
    user_id: str
    conversation_id: str
    collaboration_type: str
    target_agents: List[str]
    task_description: str
    required_capabilities: List[str]
    priority: str = "normal"
    timeout_minutes: int = 5
    context: Optional[Dict[str, Any]] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class CollaborationFlowResult:
    """Result of a collaboration flow."""
    flow_id: str
    status: CollaborationFlowStatus
    results: Dict[str, Any]
    participating_agents: List[str]
    execution_time_seconds: float
    user_permissions: Dict[str, bool]
    metadata: Optional[Dict[str, Any]] = None
    completed_at: datetime = None

    def __post_init__(self):
        if self.completed_at is None:
            self.completed_at = datetime.now()


class PermissionBasedCollaborationFlow:
    """
    Manages permission-based collaboration flows between agents.
    
    This class ensures that:
    1. All agent collaborations require explicit user permission
    2. Users are informed about what each collaboration will do
    3. Users can approve or deny each collaboration request
    4. Collaboration flows are transparent and auditable
    5. Failed permissions gracefully degrade the experience
    """

    def __init__(self):
        """Initialize the permission-based collaboration flow manager."""
        self.permission_manager = UserPermissionManager()
        self.active_flows: Dict[str, CollaborationFlowRequest] = {}
        self.completed_flows: List[CollaborationFlowResult] = []
        
        # Configuration
        self.max_concurrent_flows = 10
        self.default_timeout_minutes = 5
        self.auto_cleanup_hours = 24
        
        # Callbacks for flow events
        self.flow_callbacks: Dict[str, List[Callable]] = {
            'flow_started': [],
            'permission_requested': [],
            'permission_granted': [],
            'permission_denied': [],
            'flow_completed': [],
            'flow_failed': []
        }
        
        logger.info("PermissionBasedCollaborationFlow initialized")

    async def initiate_collaboration_flow(self, flow_request: CollaborationFlowRequest) -> str:
        """
        Initiate a new collaboration flow with permission requirements.
        
        Args:
            flow_request: The collaboration flow request
            
        Returns:
            Flow ID for tracking the collaboration
        """
        try:
            logger.info(f"Initiating collaboration flow {flow_request.flow_id}")
            
            # Validate the request
            if not self._validate_flow_request(flow_request):
                raise ValueError("Invalid collaboration flow request")
            
            # Check if we're at max concurrent flows
            if len(self.active_flows) >= self.max_concurrent_flows:
                raise Exception("Maximum concurrent collaboration flows reached")
            
            # Store the active flow
            self.active_flows[flow_request.flow_id] = flow_request
            
            # Trigger flow started callbacks
            await self._trigger_callbacks('flow_started', flow_request)
            
            # Request permissions for each target agent
            permission_results = await self._request_collaboration_permissions(flow_request)
            
            # Process the collaboration based on permission results
            flow_result = await self._process_collaboration_with_permissions(
                flow_request, permission_results
            )
            
            # Clean up and store result
            if flow_request.flow_id in self.active_flows:
                del self.active_flows[flow_request.flow_id]
            
            self.completed_flows.append(flow_result)
            
            # Trigger completion callbacks
            if flow_result.status == CollaborationFlowStatus.COMPLETED:
                await self._trigger_callbacks('flow_completed', flow_result)
            else:
                await self._trigger_callbacks('flow_failed', flow_result)
            
            logger.info(f"Collaboration flow {flow_request.flow_id} completed with status: {flow_result.status}")
            return flow_request.flow_id
            
        except Exception as e:
            logger.error(f"Error initiating collaboration flow: {e}")
            # Clean up on error
            if flow_request.flow_id in self.active_flows:
                del self.active_flows[flow_request.flow_id]
            raise

    async def _request_collaboration_permissions(self, flow_request: CollaborationFlowRequest) -> Dict[str, bool]:
        """
        Request user permissions for each agent in the collaboration.
        
        Args:
            flow_request: The collaboration flow request
            
        Returns:
            Dictionary mapping agent IDs to permission granted status
        """
        permission_results = {}
        
        for target_agent_id in flow_request.target_agents:
            try:
                # Get agent information for permission context
                target_agent = network_registry.get_agent(target_agent_id)
                if not target_agent:
                    logger.warning(f"Target agent {target_agent_id} not found")
                    permission_results[target_agent_id] = False
                    continue
                
                # Create detailed task description for permission request
                detailed_description = self._create_permission_description(
                    flow_request, target_agent
                )
                
                # Request permission from user
                logger.info(f"Requesting permission for collaboration with {target_agent_id}")
                await self._trigger_callbacks('permission_requested', {
                    'flow_id': flow_request.flow_id,
                    'target_agent': target_agent_id,
                    'description': detailed_description
                })
                
                permission_granted = await self.permission_manager.request_collaboration_permission(
                    user_id=flow_request.user_id,
                    requesting_agent=flow_request.requesting_agent_id,
                    specialist_type=target_agent.agent_type,
                    task_description=detailed_description,
                    required_capability=flow_request.required_capabilities[0] if flow_request.required_capabilities else "general",
                    timeout_minutes=flow_request.timeout_minutes
                )
                
                permission_results[target_agent_id] = permission_granted
                
                # Trigger permission result callbacks
                if permission_granted:
                    await self._trigger_callbacks('permission_granted', {
                        'flow_id': flow_request.flow_id,
                        'target_agent': target_agent_id
                    })
                else:
                    await self._trigger_callbacks('permission_denied', {
                        'flow_id': flow_request.flow_id,
                        'target_agent': target_agent_id
                    })
                
            except Exception as e:
                logger.error(f"Error requesting permission for agent {target_agent_id}: {e}")
                permission_results[target_agent_id] = False
        
        return permission_results

    async def _process_collaboration_with_permissions(self, flow_request: CollaborationFlowRequest,
                                                    permission_results: Dict[str, bool]) -> CollaborationFlowResult:
        """
        Process the collaboration using only agents with granted permissions.
        
        Args:
            flow_request: The collaboration flow request
            permission_results: Results of permission requests
            
        Returns:
            Collaboration flow result
        """
        start_time = datetime.now()
        
        try:
            # Filter agents based on permissions
            approved_agents = [
                agent_id for agent_id, granted in permission_results.items() if granted
            ]
            
            if not approved_agents:
                return CollaborationFlowResult(
                    flow_id=flow_request.flow_id,
                    status=CollaborationFlowStatus.PERMISSION_DENIED,
                    results={"error": "No permissions granted for collaboration"},
                    participating_agents=[],
                    execution_time_seconds=0,
                    user_permissions=permission_results
                )
            
            # Create collaboration tools for the requesting agent
            collaboration_tools = NetworkCollaborationTools(flow_request.requesting_agent_id)
            
            # Execute collaboration with approved agents
            collaboration_results = {}
            
            if flow_request.collaboration_type == "parallel":
                collaboration_results = await self._execute_parallel_collaboration(
                    collaboration_tools, approved_agents, flow_request
                )
            elif flow_request.collaboration_type == "sequential":
                collaboration_results = await self._execute_sequential_collaboration(
                    collaboration_tools, approved_agents, flow_request
                )
            else:  # Default to consultation
                collaboration_results = await self._execute_consultation_collaboration(
                    collaboration_tools, approved_agents, flow_request
                )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return CollaborationFlowResult(
                flow_id=flow_request.flow_id,
                status=CollaborationFlowStatus.COMPLETED,
                results=collaboration_results,
                participating_agents=approved_agents,
                execution_time_seconds=execution_time,
                user_permissions=permission_results,
                metadata={
                    "collaboration_type": flow_request.collaboration_type,
                    "requesting_agent": flow_request.requesting_agent_id,
                    "approved_agents_count": len(approved_agents),
                    "denied_agents_count": len([p for p in permission_results.values() if not p])
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing collaboration: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return CollaborationFlowResult(
                flow_id=flow_request.flow_id,
                status=CollaborationFlowStatus.FAILED,
                results={"error": str(e)},
                participating_agents=[],
                execution_time_seconds=execution_time,
                user_permissions=permission_results
            )

    async def _execute_consultation_collaboration(self, collaboration_tools: NetworkCollaborationTools,
                                                approved_agents: List[str],
                                                flow_request: CollaborationFlowRequest) -> Dict[str, Any]:
        """Execute consultation collaboration with approved agents."""
        try:
            results = {}
            
            # Consult with the first approved agent (best match)
            if approved_agents:
                agent_id = approved_agents[0]
                agent = network_registry.get_agent(agent_id)
                
                if agent:
                    result = await collaboration_tools.consult_specialist(
                        specialist=agent,
                        query=flow_request.task_description,
                        context=flow_request.context
                    )
                    results[agent_id] = result
            
            return {
                "collaboration_type": "consultation",
                "results": results,
                "success": bool(results)
            }
            
        except Exception as e:
            logger.error(f"Error in consultation collaboration: {e}")
            return {"collaboration_type": "consultation", "error": str(e), "success": False}

    async def _execute_parallel_collaboration(self, collaboration_tools: NetworkCollaborationTools,
                                            approved_agents: List[str],
                                            flow_request: CollaborationFlowRequest) -> Dict[str, Any]:
        """Execute parallel collaboration with approved agents."""
        try:
            tasks = []
            
            for agent_id in approved_agents:
                agent = network_registry.get_agent(agent_id)
                if agent:
                    task = collaboration_tools.consult_specialist(
                        specialist=agent,
                        query=flow_request.task_description,
                        context=flow_request.context
                    )
                    tasks.append((agent_id, task))
            
            # Execute all tasks in parallel
            results = {}
            if tasks:
                completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
                
                for (agent_id, _), result in zip(tasks, completed_tasks):
                    if isinstance(result, Exception):
                        results[agent_id] = {"success": False, "error": str(result)}
                    else:
                        results[agent_id] = result
            
            return {
                "collaboration_type": "parallel",
                "results": results,
                "success": bool(results)
            }
            
        except Exception as e:
            logger.error(f"Error in parallel collaboration: {e}")
            return {"collaboration_type": "parallel", "error": str(e), "success": False}

    async def _execute_sequential_collaboration(self, collaboration_tools: NetworkCollaborationTools,
                                              approved_agents: List[str],
                                              flow_request: CollaborationFlowRequest) -> Dict[str, Any]:
        """Execute sequential collaboration with approved agents."""
        try:
            results = {}
            current_context = flow_request.context or {}
            
            for agent_id in approved_agents:
                agent = network_registry.get_agent(agent_id)
                if agent:
                    result = await collaboration_tools.consult_specialist(
                        specialist=agent,
                        query=flow_request.task_description,
                        context=current_context
                    )
                    
                    results[agent_id] = result
                    
                    # Pass results to next agent
                    if isinstance(result, str):
                        current_context["previous_result"] = result
                    elif isinstance(result, dict) and result.get("success"):
                        current_context.update(result.get("metadata", {}))
            
            return {
                "collaboration_type": "sequential",
                "results": results,
                "final_context": current_context,
                "success": bool(results)
            }
            
        except Exception as e:
            logger.error(f"Error in sequential collaboration: {e}")
            return {"collaboration_type": "sequential", "error": str(e), "success": False}

    def _validate_flow_request(self, flow_request: CollaborationFlowRequest) -> bool:
        """Validate a collaboration flow request."""
        if not flow_request.flow_id:
            return False
        if not flow_request.requesting_agent_id:
            return False
        if not flow_request.user_id:
            return False
        if not flow_request.target_agents:
            return False
        if not flow_request.task_description:
            return False
        return True

    def _create_permission_description(self, flow_request: CollaborationFlowRequest, target_agent) -> str:
        """Create a detailed description for permission requests."""
        return (
            f"I'd like to collaborate with the {target_agent.name} to {flow_request.task_description}. "
            f"This will help me provide you with more comprehensive and accurate assistance. "
            f"The {target_agent.agent_type} specialist can contribute expertise in "
            f"{', '.join([cap.name for cap in target_agent.capabilities[:3]])}."
        )

    async def _trigger_callbacks(self, event_type: str, data: Any) -> None:
        """Trigger callbacks for flow events."""
        callbacks = self.flow_callbacks.get(event_type, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                logger.error(f"Error in flow callback: {e}")

    def add_flow_callback(self, event_type: str, callback: Callable) -> None:
        """Add a callback for flow events."""
        if event_type in self.flow_callbacks:
            self.flow_callbacks[event_type].append(callback)

    def get_flow_stats(self) -> Dict[str, Any]:
        """Get collaboration flow statistics."""
        total_flows = len(self.completed_flows)
        successful_flows = len([f for f in self.completed_flows if f.status == CollaborationFlowStatus.COMPLETED])
        
        return {
            "total_flows": total_flows,
            "successful_flows": successful_flows,
            "success_rate": (successful_flows / total_flows * 100) if total_flows > 0 else 0,
            "active_flows": len(self.active_flows)
        }


# Global instance
permission_based_collaboration_flow = PermissionBasedCollaborationFlow()
