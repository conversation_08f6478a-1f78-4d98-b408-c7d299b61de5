"""
Dashboard event handler for LangGraph system.

This module handles dashboard-related events including update notifications
and real-time data synchronization.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from ..event_bus import LangGraphEvent

logger = logging.getLogger(__name__)


class DashboardEventHandler:
    """Handler for dashboard-related events."""
    
    def __init__(self):
        self.processed_events = 0
        self.dashboard_updates = {}
        self.widget_subscriptions = {}
        self.update_queue = {}
        self.metrics = {
            "total_updates": 0,
            "successful_updates": 0,
            "failed_updates": 0,
            "average_update_time": 0.0
        }
    
    async def handle_dashboard_update(self, event: LangGraphEvent):
        """Handle dashboard update event."""
        try:
            data = event.data
            dashboard_id = data.get("dashboard_id")
            widget_id = data.get("widget_id")
            update_data = data.get("update_data", {})
            update_type = data.get("update_type", "data_refresh")
            
            logger.info(f"Processing dashboard update for {dashboard_id}/{widget_id}")
            
            # Track update
            update_key = f"{dashboard_id}:{widget_id}"
            if update_key not in self.dashboard_updates:
                self.dashboard_updates[update_key] = []
            
            update_record = {
                "update_data": update_data,
                "update_type": update_type,
                "timestamp": event.timestamp,
                "processed": False
            }
            
            self.dashboard_updates[update_key].append(update_record)
            
            # Keep only last 50 updates per widget
            if len(self.dashboard_updates[update_key]) > 50:
                self.dashboard_updates[update_key] = self.dashboard_updates[update_key][-50:]
            
            # Queue update for processing
            await self._queue_dashboard_update(dashboard_id, widget_id, update_data, update_type)
            
            self.metrics["total_updates"] += 1
            self.processed_events += 1
            
            logger.debug(f"Successfully queued dashboard update for {dashboard_id}/{widget_id}")
            
        except Exception as e:
            logger.error(f"Error handling dashboard update event: {e}")
            self.metrics["failed_updates"] += 1
            raise
    
    async def _queue_dashboard_update(self, dashboard_id: str, widget_id: str, 
                                    update_data: Dict[str, Any], update_type: str):
        """Queue dashboard update for processing."""
        try:
            update_key = f"{dashboard_id}:{widget_id}"
            
            if update_key not in self.update_queue:
                self.update_queue[update_key] = []
            
            # Add to queue
            self.update_queue[update_key].append({
                "dashboard_id": dashboard_id,
                "widget_id": widget_id,
                "update_data": update_data,
                "update_type": update_type,
                "queued_at": datetime.now(),
                "attempts": 0
            })
            
            # Process queue (in real implementation, this might be handled by a separate worker)
            await self._process_update_queue(update_key)
            
        except Exception as e:
            logger.error(f"Error queuing dashboard update: {e}")
            raise
    
    async def _process_update_queue(self, update_key: str):
        """Process queued updates for a specific dashboard/widget."""
        try:
            if update_key not in self.update_queue:
                return
            
            queue = self.update_queue[update_key]
            processed_updates = []
            
            for update in queue:
                try:
                    start_time = datetime.now()
                    
                    # Simulate update processing (in real implementation, this would
                    # send updates to the frontend via WebSocket or similar)
                    await self._send_update_to_frontend(update)
                    
                    processing_time = (datetime.now() - start_time).total_seconds()
                    
                    # Mark as processed
                    update["processed_at"] = datetime.now()
                    update["processing_time"] = processing_time
                    processed_updates.append(update)
                    
                    # Update metrics
                    self.metrics["successful_updates"] += 1
                    await self._update_average_processing_time(processing_time)
                    
                    logger.debug(f"Successfully processed update for {update_key}")
                    
                except Exception as e:
                    update["attempts"] += 1
                    update["last_error"] = str(e)
                    
                    if update["attempts"] >= 3:
                        logger.error(f"Failed to process update for {update_key} after 3 attempts: {e}")
                        processed_updates.append(update)  # Remove from queue
                        self.metrics["failed_updates"] += 1
                    else:
                        logger.warning(f"Retrying update for {update_key}, attempt {update['attempts']}")
            
            # Remove processed updates from queue
            for processed in processed_updates:
                if processed in queue:
                    queue.remove(processed)
            
        except Exception as e:
            logger.error(f"Error processing update queue for {update_key}: {e}")
    
    async def _send_update_to_frontend(self, update: Dict[str, Any]):
        """Send update to frontend (placeholder implementation)."""
        try:
            dashboard_id = update["dashboard_id"]
            widget_id = update["widget_id"]
            update_data = update["update_data"]
            update_type = update["update_type"]
            
            # In a real implementation, this would use WebSocket, Server-Sent Events,
            # or another real-time communication mechanism to send updates to the frontend
            
            logger.debug(f"Sending {update_type} update to frontend for {dashboard_id}/{widget_id}")
            
            # Simulate processing time
            import asyncio
            await asyncio.sleep(0.01)  # 10ms simulated processing time
            
        except Exception as e:
            logger.error(f"Error sending update to frontend: {e}")
            raise
    
    async def _update_average_processing_time(self, processing_time: float):
        """Update average processing time metric."""
        try:
            successful_updates = self.metrics["successful_updates"]
            current_avg = self.metrics["average_update_time"]
            
            if successful_updates > 0:
                self.metrics["average_update_time"] = (
                    (current_avg * (successful_updates - 1) + processing_time) / successful_updates
                )
            
        except Exception as e:
            logger.error(f"Error updating average processing time: {e}")
    
    def subscribe_to_widget_updates(self, dashboard_id: str, widget_id: str, 
                                  callback: callable):
        """Subscribe to updates for a specific widget."""
        subscription_key = f"{dashboard_id}:{widget_id}"
        
        if subscription_key not in self.widget_subscriptions:
            self.widget_subscriptions[subscription_key] = []
        
        self.widget_subscriptions[subscription_key].append(callback)
        logger.debug(f"Added subscription for {subscription_key}")
    
    def unsubscribe_from_widget_updates(self, dashboard_id: str, widget_id: str, 
                                      callback: callable):
        """Unsubscribe from widget updates."""
        subscription_key = f"{dashboard_id}:{widget_id}"
        
        if subscription_key in self.widget_subscriptions:
            try:
                self.widget_subscriptions[subscription_key].remove(callback)
                logger.debug(f"Removed subscription for {subscription_key}")
            except ValueError:
                logger.warning(f"Callback not found for {subscription_key}")
    
    def get_dashboard_update_history(self, dashboard_id: str, 
                                   widget_id: Optional[str] = None,
                                   limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get update history for a dashboard or specific widget."""
        updates = []
        
        for update_key, update_list in self.dashboard_updates.items():
            key_dashboard_id, key_widget_id = update_key.split(":", 1)
            
            if key_dashboard_id == dashboard_id:
                if widget_id is None or key_widget_id == widget_id:
                    for update in update_list:
                        update_data = update.copy()
                        update_data["dashboard_id"] = key_dashboard_id
                        update_data["widget_id"] = key_widget_id
                        updates.append(update_data)
        
        # Sort by timestamp (most recent first)
        updates.sort(key=lambda x: x["timestamp"], reverse=True)
        
        # Apply limit
        if limit:
            updates = updates[:limit]
        
        return updates
    
    def get_pending_updates(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all pending updates in the queue."""
        return {key: queue.copy() for key, queue in self.update_queue.items() if queue}
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get dashboard handler metrics."""
        total_queue_size = sum(len(queue) for queue in self.update_queue.values())
        
        return {
            "processed_events": self.processed_events,
            "tracked_widgets": len(self.dashboard_updates),
            "active_subscriptions": len(self.widget_subscriptions),
            "pending_updates": total_queue_size,
            **self.metrics
        }
