import { useEffect, useRef, useCallback } from 'react';

interface SmartRefreshOptions {
  onRefresh: (source: string) => void;
  isEnabled?: boolean;
  activeTab?: string;
  refreshTrigger?: number;
}

interface RefreshEvent {
  source: string;
  timestamp: number;
  priority: number;
}

const REFRESH_PRIORITIES = {
  manual: 1,
  mount: 2,
  trigger: 3,
  visibility: 4,
  focus: 5,
  periodic: 6,
} as const;

const PERIODIC_INTERVAL = 30000; // 30 seconds
const FOCUS_DEBOUNCE = 1000; // 1 second
const VISIBILITY_DEBOUNCE = 1000; // 1 second

export function useSmartRefresh({
  onRefresh,
  isEnabled = true,
  activeTab,
  refreshTrigger,
}: SmartRefreshOptions) {
  const lastRefreshRef = useRef<{ [key: string]: number }>({});
  const periodicIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const focusTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const visibilityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(false);

  // Centralized refresh coordinator
  const coordinatedRefresh = useCallback((event: RefreshEvent) => {
    if (!isEnabled) {
      console.log(`Smart refresh: Skipping ${event.source} - refresh disabled`);
      return;
    }

    const now = Date.now();
    const lastRefresh = lastRefreshRef.current[event.source] || 0;
    const timeSinceLastRefresh = now - lastRefresh;

    // Minimum intervals for different sources
    const minIntervals = {
      manual: 0, // No limit for manual
      mount: 0, // No limit for mount
      trigger: 1000, // 1 second
      visibility: VISIBILITY_DEBOUNCE,
      focus: FOCUS_DEBOUNCE,
      periodic: PERIODIC_INTERVAL - 1000, // Allow slight overlap
    };

    const minInterval = minIntervals[event.source as keyof typeof minIntervals] || 1000;

    if (timeSinceLastRefresh < minInterval) {
      console.log(`Smart refresh: Rate limiting ${event.source} (${timeSinceLastRefresh}ms < ${minInterval}ms)`);
      return;
    }

    // Check if a higher priority refresh happened recently
    const recentHighPriorityRefresh = Object.entries(lastRefreshRef.current).some(([source, timestamp]) => {
      const sourcePriority = REFRESH_PRIORITIES[source as keyof typeof REFRESH_PRIORITIES] || 10;
      return sourcePriority < event.priority && (now - timestamp) < 2000; // 2 second window
    });

    if (recentHighPriorityRefresh) {
      console.log(`Smart refresh: Skipping ${event.source} - higher priority refresh happened recently`);
      return;
    }

    console.log(`Smart refresh: Executing ${event.source} refresh`);
    lastRefreshRef.current[event.source] = now;
    onRefresh(event.source);
  }, [isEnabled, onRefresh]);

  // Component mount refresh
  useEffect(() => {
    if (!mountedRef.current) {
      mountedRef.current = true;
      coordinatedRefresh({
        source: 'mount',
        timestamp: Date.now(),
        priority: REFRESH_PRIORITIES.mount,
      });
    }
  }, [coordinatedRefresh]);

  // Refresh trigger (from external events)
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      coordinatedRefresh({
        source: 'trigger',
        timestamp: Date.now(),
        priority: REFRESH_PRIORITIES.trigger,
      });
    }
  }, [refreshTrigger, coordinatedRefresh]);

  // Visibility change refresh (debounced)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) return;

      // Clear existing timeout
      if (visibilityTimeoutRef.current) {
        clearTimeout(visibilityTimeoutRef.current);
      }

      // Debounce visibility changes
      visibilityTimeoutRef.current = setTimeout(() => {
        coordinatedRefresh({
          source: 'visibility',
          timestamp: Date.now(),
          priority: REFRESH_PRIORITIES.visibility,
        });
      }, VISIBILITY_DEBOUNCE);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (visibilityTimeoutRef.current) {
        clearTimeout(visibilityTimeoutRef.current);
      }
    };
  }, [coordinatedRefresh]);

  // Window focus refresh (debounced)
  useEffect(() => {
    const handleFocus = () => {
      // Clear existing timeout
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }

      // Debounce focus events
      focusTimeoutRef.current = setTimeout(() => {
        coordinatedRefresh({
          source: 'focus',
          timestamp: Date.now(),
          priority: REFRESH_PRIORITIES.focus,
        });
      }, FOCUS_DEBOUNCE);
    };

    window.addEventListener('focus', handleFocus);
    return () => {
      window.removeEventListener('focus', handleFocus);
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }
    };
  }, [coordinatedRefresh]);

  // Periodic refresh (only when on manage tab)
  useEffect(() => {
    if (activeTab === 'manage' && isEnabled) {
      periodicIntervalRef.current = setInterval(() => {
        coordinatedRefresh({
          source: 'periodic',
          timestamp: Date.now(),
          priority: REFRESH_PRIORITIES.periodic,
        });
      }, PERIODIC_INTERVAL);

      return () => {
        if (periodicIntervalRef.current) {
          clearInterval(periodicIntervalRef.current);
        }
      };
    } else {
      // Clear interval if not on manage tab or disabled
      if (periodicIntervalRef.current) {
        clearInterval(periodicIntervalRef.current);
        periodicIntervalRef.current = null;
      }
    }
  }, [activeTab, isEnabled, coordinatedRefresh]);

  // Manual refresh function
  const manualRefresh = useCallback(() => {
    coordinatedRefresh({
      source: 'manual',
      timestamp: Date.now(),
      priority: REFRESH_PRIORITIES.manual,
    });
  }, [coordinatedRefresh]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (periodicIntervalRef.current) {
        clearInterval(periodicIntervalRef.current);
      }
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }
      if (visibilityTimeoutRef.current) {
        clearTimeout(visibilityTimeoutRef.current);
      }
    };
  }, []);

  return {
    manualRefresh,
    lastRefreshTimes: lastRefreshRef.current,
  };
}
