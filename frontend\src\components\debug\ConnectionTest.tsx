import React, { useState, useEffect } from 'react';

interface ConnectionTestProps {
  className?: string;
}

const ConnectionTest: React.FC<ConnectionTestProps> = ({ className = '' }) => {
  const [status, setStatus] = useState<'testing' | 'success' | 'error'>('testing');
  const [message, setMessage] = useState('Testing connection...');
  const [details, setDetails] = useState<any>(null);

  const testConnection = async () => {
    setStatus('testing');
    setMessage('Testing connection...');
    setDetails(null);

    try {
      // Test 1: Direct fetch to backend health endpoint
      console.log('Testing direct connection to backend...');
      const healthResponse = await fetch('http://localhost:8000/health', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!healthResponse.ok) {
        throw new Error(`Health check failed: ${healthResponse.status} ${healthResponse.statusText}`);
      }

      const healthData = await healthResponse.json();
      console.log('Health check response:', healthData);

      // Test 2: Test API endpoint
      console.log('Testing API endpoint...');
      const apiResponse = await fetch('http://localhost:8000/api/v1/personas', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('API response status:', apiResponse.status);
      console.log('API response headers:', Object.fromEntries(apiResponse.headers.entries()));

      if (apiResponse.status === 401) {
        // 401 is expected for unauthenticated requests
        setStatus('success');
        setMessage('✅ Backend is accessible! (401 Unauthorized is expected without authentication)');
        setDetails({
          health: healthData,
          apiStatus: apiResponse.status,
          apiStatusText: apiResponse.statusText,
          corsHeaders: {
            'Access-Control-Allow-Origin': apiResponse.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': apiResponse.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': apiResponse.headers.get('Access-Control-Allow-Headers'),
          }
        });
      } else if (apiResponse.ok) {
        const apiData = await apiResponse.json();
        setStatus('success');
        setMessage('✅ Backend is fully accessible!');
        setDetails({
          health: healthData,
          api: apiData,
          corsHeaders: {
            'Access-Control-Allow-Origin': apiResponse.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': apiResponse.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': apiResponse.headers.get('Access-Control-Allow-Headers'),
          }
        });
      } else {
        throw new Error(`API test failed: ${apiResponse.status} ${apiResponse.statusText}`);
      }

    } catch (error: any) {
      console.error('Connection test failed:', error);
      setStatus('error');
      
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        setMessage('❌ Connection refused - Backend server is not running or not accessible');
        setDetails({
          error: 'ERR_CONNECTION_REFUSED',
          suggestion: 'Make sure the backend server is running on http://localhost:8000',
          command: 'cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000'
        });
      } else {
        setMessage(`❌ Connection failed: ${error.message}`);
        setDetails({
          error: error.message,
          type: error.name,
          stack: error.stack
        });
      }
    }
  };

  useEffect(() => {
    testConnection();
  }, []);

  const getStatusColor = () => {
    switch (status) {
      case 'testing': return 'text-blue-600';
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className={`p-4 border rounded-lg ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Backend Connection Test</h3>
        <button
          onClick={testConnection}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          disabled={status === 'testing'}
        >
          {status === 'testing' ? 'Testing...' : 'Test Again'}
        </button>
      </div>

      <div className={`mb-4 ${getStatusColor()}`}>
        <p className="font-medium">{message}</p>
      </div>

      {details && (
        <div className="bg-gray-50 p-3 rounded text-sm">
          <h4 className="font-semibold mb-2">Details:</h4>
          <pre className="whitespace-pre-wrap overflow-auto max-h-64">
            {JSON.stringify(details, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-4 text-sm text-gray-600">
        <p><strong>Expected Backend URL:</strong> http://localhost:8000</p>
        <p><strong>API Base URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'}</p>
        <p><strong>Environment:</strong> {import.meta.env.MODE}</p>
      </div>
    </div>
  );
};

export default ConnectionTest;
