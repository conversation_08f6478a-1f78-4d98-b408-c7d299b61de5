"""
Concierge Persona

This module provides a consolidated concierge agent that uses the unified conversation agent base
for consistent AI-powered responses and specialized guidance capabilities.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .unified_conversation_agent import UnifiedConversationAgent

logger = logging.getLogger(__name__)


class ConciergePersona(UnifiedConversationAgent):
    """
    Concierge persona that uses the unified conversation base.
    
    This persona provides:
    - AI-powered conversation and guidance
    - Persona recommendations
    - Platform navigation assistance
    - Workflow coordination
    - Business context awareness
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the concierge persona.
        
        Args:
            config: Optional configuration for the persona
        """
        # Get agent_id from config or use default
        agent_id = config.get('agent_id', 'concierge') if config else 'concierge'
        
        # Initialize parent class
        super().__init__(
            agent_id=agent_id,
            agent_type="concierge",
            config=config
        )
        
        # Concierge-specific configuration
        self.recommendation_threshold = config.get('recommendation_threshold', 0.7) if config else 0.7
        self.max_recommendations = config.get('max_recommendations', 3) if config else 3
        self.available_personas = config.get('available_personas', ['marketing', 'analysis', 'classification', 'visualization']) if config else ['marketing', 'analysis', 'classification', 'visualization']
        
        logger.info("ConciergePersona initialized")

    def _get_specialized_capabilities(self) -> List[str]:
        """Get concierge-specific capabilities."""
        return [
            "persona_recommendation",
            "intent_analysis",
            "conversation_management",
            "data_attachment_assistance",
            "workflow_coordination",
            "user_guidance",
            "business_context_awareness",
            "platform_navigation",
            "task_routing"
        ]

    def _get_agent_name(self) -> str:
        """Get human-readable agent name."""
        return "Datagenius Concierge"

    def _get_agent_description(self) -> str:
        """Get agent description."""
        return "Your intelligent guide to Datagenius AI personas and platform capabilities"

    def _determine_intent_type(self, message: str) -> str:
        """Determine the intent type for concierge conversations."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["recommend", "suggest", "which", "what persona"]):
            return "persona_recommendation"
        elif any(word in message_lower for word in ["help", "how", "guide", "tutorial"]):
            return "platform_guidance"
        elif any(word in message_lower for word in ["upload", "data", "file", "attach"]):
            return "data_assistance"
        elif any(word in message_lower for word in ["hello", "hi", "start", "begin"]):
            return "greeting"
        else:
            return "general_guidance"

    def _get_greeting_specialization(self) -> str:
        """Get concierge-specific greeting content."""
        return ("**🎯 I'm here to help you:**\n"
                "• **Find the Right Persona** - Get matched with the perfect AI specialist\n"
                "• **Navigate the Platform** - Learn how to use Datagenius effectively\n"
                "• **Upload and Manage Data** - Get assistance with file uploads and data preparation\n"
                "• **Coordinate Workflows** - Help you work efficiently across different AI personas\n"
                "• **Business Context** - Understand your specific needs and goals\n"
                "• **Platform Guidance** - Answer questions about features and capabilities")

    def _generate_capabilities_response(self) -> str:
        """Generate a response about concierge capabilities."""
        return ("I'm your **Datagenius Concierge** - your intelligent guide to the platform:\n\n"
                "**🎯 Persona Matching:**\n"
                "• Recommend the best AI persona for your specific needs\n"
                "• Explain what each persona specializes in\n"
                "• Help you switch between personas efficiently\n\n"
                "**📚 Platform Guidance:**\n"
                "• Navigate features and capabilities\n"
                "• Learn best practices for different tasks\n"
                "• Get help with platform workflows\n\n"
                "**📁 Data Assistance:**\n"
                "• Upload and manage your data files\n"
                "• Prepare data for analysis\n"
                "• Understand data requirements\n\n"
                f"**Available Personas:** {', '.join(self.available_personas)}\n"
                f"**Recommendation Accuracy:** {self.recommendation_threshold * 100}%+")

    async def _enhance_response(self, original_message: str, base_response: str, 
                              context: Optional[Dict[str, Any]] = None) -> str:
        """Enhance the response with concierge-specific information."""
        try:
            enhanced_response = base_response
            
            # Add persona recommendations for relevant queries
            if self._is_persona_request(original_message):
                enhanced_response += f"\n\n🎯 **Available Personas**: I can connect you with {', '.join(self.available_personas)} specialists based on your needs."
            
            # Add platform guidance for help requests
            if self._is_help_request(original_message):
                enhanced_response += "\n\n📚 **Platform Tip**: You can always ask me to recommend the best persona for your specific task, or get guidance on using any platform feature."
            
            # Add data assistance for upload queries
            if self._is_data_related(original_message):
                enhanced_response += "\n\n📁 **Data Assistance**: I can help you upload files, prepare data for analysis, and connect you with the right persona for your data tasks."
            
            # Add workflow coordination tips
            if self._is_workflow_related(original_message):
                enhanced_response += "\n\n⚡ **Workflow Tip**: I can help you coordinate between different personas to accomplish complex tasks efficiently."
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing concierge response: {e}")
            return base_response

    def _is_persona_request(self, message: str) -> bool:
        """Check if the message is requesting persona recommendations."""
        persona_keywords = ["persona", "recommend", "suggest", "which", "best", "specialist"]
        return any(keyword in message.lower() for keyword in persona_keywords)

    def _is_help_request(self, message: str) -> bool:
        """Check if the message is requesting help or guidance."""
        help_keywords = ["help", "how", "guide", "tutorial", "learn", "explain"]
        return any(keyword in message.lower() for keyword in help_keywords)

    def _is_data_related(self, message: str) -> bool:
        """Check if the message is data-related."""
        data_keywords = ["data", "file", "upload", "csv", "excel", "document"]
        return any(keyword in message.lower() for keyword in data_keywords)

    def _is_workflow_related(self, message: str) -> bool:
        """Check if the message is workflow-related."""
        workflow_keywords = ["workflow", "process", "coordinate", "multiple", "together"]
        return any(keyword in message.lower() for keyword in workflow_keywords)

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this concierge persona."""
        base_info = super().get_agent_info()
        base_info.update({
            "specialization": "platform_guidance_and_coordination",
            "available_personas": self.available_personas,
            "recommendation_threshold": self.recommendation_threshold,
            "max_recommendations": self.max_recommendations,
            "supports_persona_recommendation": True,
            "supports_platform_guidance": True,
            "supports_workflow_coordination": True
        })
        return base_info


# Backward compatibility aliases
SimpleConciergeAgent = ConciergePersona
ConciergeAgent = ConciergePersona
