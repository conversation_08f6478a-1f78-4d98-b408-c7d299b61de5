"""
Unified Tool Manager for LangGraph-based Datagenius System.

This module provides a consolidated tool management system that eliminates
duplication across different agent implementations while maintaining
specialized tool capabilities for different personas.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime
import json

try:
    from ..tools.mcp_integration import <PERSON><PERSON><PERSON><PERSON><PERSON>anager, MCPToolRegistry
except ImportError:
    # Fallback to the correct location
    try:
        from ...tools.mcp.agent_integration import MCPToolManager
        from ..tools.mcp_integration import MCPToolRegistry
    except ImportError:
        # Create mock classes if not available
        class MCPToolManager:
            def __init__(self):
                pass
            async def execute_tool(self, tool_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
                return {"result": "Tool execution not available", "success": False}

        class MCPToolRegistry:
            def __init__(self):
                pass
            def get_all_tools(self) -> Dict[str, Any]:
                return {}
from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class UnifiedToolManager:
    """
    Unified tool manager that consolidates tool execution across all personas.
    
    This manager eliminates code duplication by providing a single interface
    for tool execution while maintaining persona-specific tool configurations.
    """
    
    def __init__(self):
        """Initialize the unified tool manager."""
        self.logger = logging.getLogger(__name__)
        
        # Core MCP tool manager
        self.mcp_manager = MCPToolManager()
        self.tool_registry = MCPToolRegistry()
        
        # Tool execution cache
        self.execution_cache: Dict[str, Any] = {}
        
        # Performance metrics
        self.metrics = {
            "tools_executed": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "cache_hits": 0,
            "average_execution_time": 0.0
        }
        
        # Persona-specific tool mappings
        self.persona_tool_mappings = {
            "analysis": [
                "data_analyzer",
                "chart_generator", 
                "report_generator",
                "pandasai_query",
                "statistical_analyzer",
                "trend_detector",
                "data_cleaner",
                "visualization_engine"
            ],
            "marketing": [
                "content_generator",
                "social_media_poster",
                "campaign_analyzer",
                "brand_strategy_generator",
                "audience_analyzer",
                "performance_tracker",
                "competitor_analyzer",
                "email_marketing_generator"
            ],
            "concierge": [
                "persona_recommender",
                "data_attachment_assistant",
                "context_manager",
                "conversation_state_manager",
                "intent_analyzer",
                "workflow_coordinator"
            ],
            "classification": [
                "text_classifier",
                "data_categorizer",
                "sentiment_analyzer",
                "entity_extractor",
                "topic_modeler",
                "document_classifier",
                "content_organizer"
            ]
        }
        
        self.logger.info("UnifiedToolManager initialized")
    
    async def execute_tools(
        self,
        tools: List[str],
        context: Dict[str, Any],
        state: UnifiedDatageniusState,
        persona_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute tools with unified management.
        
        Args:
            tools: List of tool names to execute
            context: Execution context
            state: Current workflow state
            persona_type: Optional persona type for specialized handling
            
        Returns:
            Dictionary of tool execution results
        """
        start_time = datetime.now()
        results = {}
        
        try:
            self.logger.info(f"Executing {len(tools)} tools for {persona_type or 'unknown'} persona")
            
            # Filter tools based on persona if specified
            if persona_type:
                available_tools = self.get_tools_for_persona(persona_type)
                tools = [tool for tool in tools if tool in available_tools]
            
            # Execute tools concurrently where possible
            execution_tasks = []
            for tool_name in tools:
                task = self._execute_single_tool(tool_name, context, state)
                execution_tasks.append((tool_name, task))
            
            # Wait for all tool executions
            for tool_name, task in execution_tasks:
                try:
                    result = await task
                    results[tool_name] = result
                    self.metrics["successful_executions"] += 1
                except Exception as e:
                    self.logger.error(f"Error executing tool {tool_name}: {e}")
                    results[tool_name] = {"error": str(e), "success": False}
                    self.metrics["failed_executions"] += 1
            
            # Update metrics
            self._update_execution_metrics(start_time, len(tools))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in tool execution: {e}")
            self.metrics["failed_executions"] += len(tools)
            return {"error": str(e), "success": False}
    
    async def _execute_single_tool(
        self,
        tool_name: str,
        context: Dict[str, Any],
        state: UnifiedDatageniusState
    ) -> Dict[str, Any]:
        """
        Execute a single tool.
        
        Args:
            tool_name: Name of the tool to execute
            context: Execution context
            state: Current workflow state
            
        Returns:
            Tool execution result
        """
        # Check cache first
        cache_key = self._generate_cache_key(tool_name, context)
        if cache_key in self.execution_cache:
            self.metrics["cache_hits"] += 1
            return self.execution_cache[cache_key]
        
        # Execute tool through MCP manager
        result = await self.mcp_manager.execute_tool(tool_name, context, state)
        
        # Cache result if successful
        if result.get("success", False):
            self.execution_cache[cache_key] = result
        
        self.metrics["tools_executed"] += 1
        return result
    
    def get_tools_for_persona(self, persona_type: str) -> List[str]:
        """
        Get available tools for a specific persona type.
        
        Args:
            persona_type: Type of persona
            
        Returns:
            List of available tool names
        """
        return self.persona_tool_mappings.get(persona_type, [])
    
    def register_persona_tools(self, persona_type: str, tools: List[str]) -> None:
        """
        Register tools for a specific persona type.
        
        Args:
            persona_type: Type of persona
            tools: List of tool names
        """
        self.persona_tool_mappings[persona_type] = tools
        self.logger.info(f"Registered {len(tools)} tools for {persona_type} persona")
    
    def add_tool_to_persona(self, persona_type: str, tool_name: str) -> None:
        """
        Add a tool to a specific persona type.
        
        Args:
            persona_type: Type of persona
            tool_name: Name of the tool to add
        """
        if persona_type not in self.persona_tool_mappings:
            self.persona_tool_mappings[persona_type] = []
        
        if tool_name not in self.persona_tool_mappings[persona_type]:
            self.persona_tool_mappings[persona_type].append(tool_name)
            self.logger.info(f"Added tool {tool_name} to {persona_type} persona")
    
    def remove_tool_from_persona(self, persona_type: str, tool_name: str) -> None:
        """
        Remove a tool from a specific persona type.
        
        Args:
            persona_type: Type of persona
            tool_name: Name of the tool to remove
        """
        if persona_type in self.persona_tool_mappings:
            if tool_name in self.persona_tool_mappings[persona_type]:
                self.persona_tool_mappings[persona_type].remove(tool_name)
                self.logger.info(f"Removed tool {tool_name} from {persona_type} persona")
    
    def _generate_cache_key(self, tool_name: str, context: Dict[str, Any]) -> str:
        """
        Generate cache key for tool execution.
        
        Args:
            tool_name: Name of the tool
            context: Execution context
            
        Returns:
            Cache key string
        """
        # Create a simplified context for caching
        cache_context = {
            "tool": tool_name,
            "message": context.get("message", "")[:100],  # First 100 chars
            "persona_type": context.get("persona_config", {}).get("agent_type", "")
        }
        
        return f"{tool_name}_{hash(json.dumps(cache_context, sort_keys=True))}"
    
    def _update_execution_metrics(self, start_time: datetime, tool_count: int) -> None:
        """
        Update execution metrics.
        
        Args:
            start_time: Execution start time
            tool_count: Number of tools executed
        """
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # Update average execution time
        current_avg = self.metrics["average_execution_time"]
        total_executions = self.metrics["tools_executed"]
        
        if total_executions > 0:
            new_avg = ((current_avg * (total_executions - tool_count)) + execution_time) / total_executions
            self.metrics["average_execution_time"] = new_avg
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get tool execution metrics.
        
        Returns:
            Dictionary of metrics
        """
        return self.metrics.copy()
    
    def clear_cache(self) -> None:
        """Clear the execution cache."""
        self.execution_cache.clear()
        self.logger.info("Cleared tool execution cache")
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the tool manager.
        
        Returns:
            Health check results
        """
        try:
            # Check MCP manager health
            mcp_health = await self.mcp_manager.health_check()
            
            # Check tool registry
            registry_health = self.tool_registry.get_health_status()
            
            return {
                "status": "healthy",
                "mcp_manager": mcp_health,
                "tool_registry": registry_health,
                "metrics": self.get_metrics(),
                "cache_size": len(self.execution_cache)
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global instance for use across the system
unified_tool_manager = UnifiedToolManager()
