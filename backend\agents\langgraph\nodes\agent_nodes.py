"""
Agent execution nodes for LangGraph workflows.

This module provides node implementations for executing different types
of agents within LangGraph workflows.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from ..states.agent_state import DatageniusAgentState, update_agent_transition, add_error
# Removed circular import - agent_factory will be passed as parameter

logger = logging.getLogger(__name__)


class AgentExecutionNode:
    """
    Generic agent execution node for LangGraph workflows.
    
    This node can execute any registered agent and integrate the results
    into the workflow state.
    """

    def __init__(self, agent_id: str, agent_factory_instance):
        """
        Initialize the agent execution node.

        Args:
            agent_id: Identifier of the agent to execute
            agent_factory_instance: Agent factory for agent management (required)
        """
        self.agent_id = agent_id
        self.agent_factory = agent_factory_instance
        self.logger = logging.getLogger(f"{__name__}.{agent_id}")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Execute the agent and update state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with agent results
        """
        try:
            start_time = datetime.now()
            
            # Update agent transition
            state = update_agent_transition(state, self.agent_id)
            
            # Get agent instance
            agent = self.agent_factory.create_agent_node(self.agent_id)
            if not agent:
                raise ValueError(f"Agent {self.agent_id} not found in registry")

            # Prepare agent context
            agent_context = self._prepare_agent_context(state)

            # Execute agent
            result = await self._execute_agent(agent, agent_context)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Enhance result with metadata
            enhanced_result = {
                "agent_id": self.agent_id,
                "result": result,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "context": agent_context
            }
            
            # Store result in state
            state["agent_outputs"][self.agent_id] = enhanced_result
            
            # Add response message if content is available
            if isinstance(result, dict) and "content" in result:
                response_message = {
                    "role": "assistant",
                    "content": result["content"],
                    "agent_id": self.agent_id,
                    "timestamp": datetime.now().isoformat()
                }
                state["messages"].append(response_message)
            
            # Update execution metrics
            state["execution_metrics"]["agent_transitions"] = state["execution_metrics"].get("agent_transitions", 0) + 1
            state["execution_metrics"][f"{self.agent_id}_execution_time"] = execution_time

            self.logger.info(f"Agent {self.agent_id} executed successfully in {execution_time:.2f}s")
            return state

        except Exception as e:
            self.logger.error(f"Agent {self.agent_id} execution failed: {e}")
            error_context = {
                "agent_id": self.agent_id,
                "context": agent_context if 'agent_context' in locals() else {}
            }
            return add_error(state, "agent_execution_error", str(e), error_context)

    def _prepare_agent_context(self, state: DatageniusAgentState) -> Dict[str, Any]:
        """
        Prepare context for agent execution.
        
        Args:
            state: Current workflow state
            
        Returns:
            Context dictionary for agent execution
        """
        # Get latest message
        latest_message = state["messages"][-1] if state["messages"] else {}
        
        return {
            "message": latest_message.get("content", ""),
            "message_context": latest_message,
            "user_id": state["user_id"],
            "conversation_id": state["conversation_id"],
            "business_profile_id": state.get("business_profile_id"),
            "business_context": state.get("business_context", {}),
            "attached_files": state.get("attached_files", []),
            "shared_insights": state.get("shared_insights", []),
            "collaboration_opportunities": state.get("collaboration_opportunities", []),
            "tool_results": state.get("tool_results", {}),
            "workflow_context": {
                "workflow_id": state.get("workflow_id"),
                "workflow_status": state.get("workflow_status"),
                "current_step": state.get("current_step", 0)
            }
        }

    async def _execute_agent(self, agent, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the agent with the given context.
        
        Args:
            agent: Agent instance to execute
            context: Execution context
            
        Returns:
            Agent execution result
        """
        # This method adapts to different agent interfaces
        # Different agents might have different execution methods
        
        if hasattr(agent, 'process_message'):
            # Standard message processing interface
            return await agent.process_message(context)
        elif hasattr(agent, 'execute'):
            # Generic execution interface
            return await agent.execute(context)
        elif hasattr(agent, 'run'):
            # Alternative execution interface
            return await agent.run(context)
        else:
            # Fallback - try to call the agent directly
            return await agent(context)


class SpecializedAgentNode(AgentExecutionNode):
    """
    Specialized agent node with additional capabilities.
    
    This extends the basic agent execution node with specialized
    functionality for specific agent types.
    """

    def __init__(self, agent_id: str, agent_type: str, specialized_config: Dict[str, Any] = None, **kwargs):
        """
        Initialize the specialized agent node.
        
        Args:
            agent_id: Identifier of the agent to execute
            agent_type: Type of the agent (e.g., "analysis", "marketing")
            specialized_config: Additional configuration for specialized behavior
            **kwargs: Additional arguments for parent class
        """
        super().__init__(agent_id, **kwargs)
        self.agent_type = agent_type
        self.specialized_config = specialized_config or {}

    def _prepare_agent_context(self, state: DatageniusAgentState) -> Dict[str, Any]:
        """Prepare specialized context based on agent type."""
        base_context = super()._prepare_agent_context(state)
        
        # Add type-specific context
        if self.agent_type == "analysis":
            base_context.update({
                "data_sources": state.get("attached_files", []),
                "analysis_type": self.specialized_config.get("analysis_type", "general"),
                "visualization_preferences": self.specialized_config.get("visualization", {})
            })
        elif self.agent_type == "marketing":
            base_context.update({
                "brand_context": state.get("business_context", {}).get("brand_info", {}),
                "target_audience": self.specialized_config.get("target_audience", {}),
                "campaign_objectives": self.specialized_config.get("objectives", [])
            })
        elif self.agent_type == "concierge":
            base_context.update({
                "available_agents": list(self.agent_factory.get_available_agents()),
                "routing_history": state.get("agent_history", []),
                "user_preferences": state.get("user_preferences", {})
            })
        
        return base_context


class ConditionalAgentNode:
    """
    Agent node that executes conditionally based on state.
    
    This node can decide whether to execute an agent based on
    conditions evaluated against the current state.
    """

    def __init__(self, agent_id: str, condition_func: callable, agent_factory_instance):
        """
        Initialize the conditional agent node.

        Args:
            agent_id: Identifier of the agent to execute
            condition_func: Function that evaluates whether to execute the agent
            agent_factory_instance: Agent factory for agent management (required)
        """
        self.agent_id = agent_id
        self.condition_func = condition_func
        self.base_node = AgentExecutionNode(agent_id, agent_factory_instance)
        self.logger = logging.getLogger(f"{__name__}.conditional.{agent_id}")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Conditionally execute the agent.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state (potentially unchanged if condition not met)
        """
        try:
            # Evaluate condition
            should_execute = self.condition_func(state)
            
            if should_execute:
                self.logger.info(f"Condition met, executing agent {self.agent_id}")
                return await self.base_node(state)
            else:
                self.logger.info(f"Condition not met, skipping agent {self.agent_id}")
                
                # Add a note about the skipped execution
                state["execution_metrics"][f"{self.agent_id}_skipped"] = datetime.now().isoformat()
                return state

        except Exception as e:
            self.logger.error(f"Error in conditional agent execution: {e}")
            return add_error(state, "conditional_agent_error", str(e), {"agent_id": self.agent_id})


# Factory functions for creating agent nodes

def create_agent_node(agent_id: str, agent_factory_instance, **kwargs) -> AgentExecutionNode:
    """Create a standard agent execution node."""
    return AgentExecutionNode(agent_id, agent_factory_instance, **kwargs)


def create_specialized_agent_node(agent_id: str, agent_type: str, agent_factory_instance, **kwargs) -> SpecializedAgentNode:
    """Create a specialized agent execution node."""
    return SpecializedAgentNode(agent_id, agent_type, agent_factory_instance=agent_factory_instance, **kwargs)


def create_conditional_agent_node(agent_id: str, condition_func: callable, **kwargs) -> ConditionalAgentNode:
    """Create a conditional agent execution node."""
    return ConditionalAgentNode(agent_id, condition_func, **kwargs)


# Common condition functions

def has_data_files(state: DatageniusAgentState) -> bool:
    """Condition: Execute only if data files are attached."""
    return bool(state.get("attached_files"))


def requires_analysis(state: DatageniusAgentState) -> bool:
    """Condition: Execute only if analysis is requested."""
    analysis = state.get("routing_analysis", {})
    intent = analysis.get("intent", "")
    return intent in ["data_analysis", "visualization", "statistics"]


def high_complexity(state: DatageniusAgentState) -> bool:
    """Condition: Execute only for high complexity requests."""
    analysis = state.get("routing_analysis", {})
    complexity = analysis.get("complexity", "low")
    return complexity == "high"
