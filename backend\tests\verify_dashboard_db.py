#!/usr/bin/env python3
"""
Database verification script for dashboard operations.
Verifies that all database tables and operations are working correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.database import get_db, User, DataSource
from app.models.dashboard_customization import Dashboard, DashboardSection, DashboardWidget
from app.config import DATABASE_URL
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_database_connection():
    """Verify basic database connection."""
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            assert result.fetchone()[0] == 1
        logger.info("✓ Database connection successful")
        return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

def verify_dashboard_tables():
    """Verify that all dashboard tables exist and are accessible."""
    try:
        db = next(get_db())
        
        # Test basic table queries
        tables_to_check = [
            (User, "users"),
            (Dashboard, "dashboards"),
            (DashboardSection, "dashboard_sections"),
            (DashboardWidget, "dashboard_widgets"),
            (DataSource, "data_sources")
        ]
        
        for model, table_name in tables_to_check:
            try:
                count = db.query(model).count()
                logger.info(f"✓ Table {table_name}: {count} records")
            except Exception as e:
                logger.error(f"❌ Table {table_name} error: {e}")
                return False
        
        db.close()
        return True
    except Exception as e:
        logger.error(f"❌ Table verification failed: {e}")
        return False

def verify_dashboard_operations():
    """Verify that dashboard CRUD operations work."""
    try:
        db = next(get_db())
        
        # Create a test user if none exists
        test_user = db.query(User).first()
        if not test_user:
            logger.info("No users found, creating test user...")
            test_user = User(
                email="<EMAIL>",
                username="testuser",
                is_active=True,
                is_verified=True,
                is_superuser=False
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)
            logger.info(f"✓ Created test user: {test_user.id}")
        else:
            logger.info(f"✓ Using existing user: {test_user.id}")
        
        # Test dashboard creation
        test_dashboard = Dashboard(
            id="test-dashboard-123",
            user_id=test_user.id,
            name="Test Dashboard",
            description="Test dashboard for verification",
            is_default=False,
            is_public=False,
            layout_config={"columns": 12, "rows": 6},
            theme_config={"primary_color": "#3B82F6"},
            refresh_interval=300
        )
        
        # Check if test dashboard already exists
        existing_dashboard = db.query(Dashboard).filter(Dashboard.id == "test-dashboard-123").first()
        if existing_dashboard:
            logger.info("✓ Test dashboard already exists")
            test_dashboard = existing_dashboard
        else:
            db.add(test_dashboard)
            db.commit()
            db.refresh(test_dashboard)
            logger.info(f"✓ Created test dashboard: {test_dashboard.id}")
        
        # Test section creation
        test_section = DashboardSection(
            id="test-section-123",
            dashboard_id=test_dashboard.id,
            user_id=test_user.id,
            name="Test Section",
            description="Test section for verification",
            color="#10B981",
            icon="Grid",
            layout_config={"columns": 12, "rows": 6},
            customization={},
            position=1,
            is_active=True
        )
        
        existing_section = db.query(DashboardSection).filter(DashboardSection.id == "test-section-123").first()
        if existing_section:
            logger.info("✓ Test section already exists")
            test_section = existing_section
        else:
            db.add(test_section)
            db.commit()
            db.refresh(test_section)
            logger.info(f"✓ Created test section: {test_section.id}")
        
        # Test widget creation
        test_widget = DashboardWidget(
            id="test-widget-123",
            section_id=test_section.id,
            user_id=test_user.id,  # Fix: Add missing user_id
            title="Test Widget",
            widget_type="chart",
            data_config={"dataSourceId": "test-source"},
            visualization_config={"chartType": "line"},
            position_config={"x": 0, "y": 0, "w": 4, "h": 3},
            customization={},
            refresh_interval=300,
            is_active=True
        )
        
        existing_widget = db.query(DashboardWidget).filter(DashboardWidget.id == "test-widget-123").first()
        if existing_widget:
            logger.info("✓ Test widget already exists")
        else:
            db.add(test_widget)
            db.commit()
            db.refresh(test_widget)
            logger.info(f"✓ Created test widget: {test_widget.id}")
        
        # Test queries
        dashboard_count = db.query(Dashboard).filter(Dashboard.user_id == test_user.id).count()
        section_count = db.query(DashboardSection).filter(DashboardSection.user_id == test_user.id).count()
        widget_count = db.query(DashboardWidget).join(DashboardSection).filter(DashboardSection.user_id == test_user.id).count()
        
        logger.info(f"✓ User {test_user.id} has {dashboard_count} dashboards, {section_count} sections, {widget_count} widgets")
        
        db.close()
        return True
    except Exception as e:
        logger.error(f"❌ Dashboard operations failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_security_functions():
    """Verify that dashboard security functions work."""
    try:
        from app.security.dashboard_security import dashboard_security
        from unittest.mock import Mock
        
        # Create mock user
        mock_user = Mock()
        mock_user.id = 1
        mock_user.email = "<EMAIL>"
        mock_user.is_superuser = False
        
        # Create mock database session
        db = next(get_db())
        
        # Test dashboard limits check
        limits = dashboard_security.check_dashboard_limits(mock_user, db)
        assert 'can_create_dashboard' in limits
        assert 'can_create_widget' in limits
        logger.info("✓ Dashboard security functions working")
        
        db.close()
        return True
    except Exception as e:
        logger.error(f"❌ Security functions failed: {e}")
        return False

def main():
    """Run all database verification tests."""
    logger.info("Starting Dashboard Database Verification")
    logger.info("=" * 50)
    
    tests = [
        ("Database Connection", verify_database_connection),
        ("Dashboard Tables", verify_dashboard_tables),
        ("Dashboard Operations", verify_dashboard_operations),
        ("Security Functions", verify_security_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\nRunning: {test_name}")
        if test_func():
            passed += 1
        else:
            logger.error(f"FAILED: {test_name}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All database verification tests passed!")
        return 0
    else:
        logger.error("❌ Some tests failed. Check the logs above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
