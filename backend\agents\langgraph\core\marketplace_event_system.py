"""
Marketplace Event System for LangGraph Integration.

This module provides event-driven architecture for marketplace operations,
agent lifecycle management, and real-time coordination between components.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

logger = logging.getLogger(__name__)


class EventType(Enum):
    """Marketplace event types."""
    # Purchase events
    PERSONA_PURCHASED = "persona_purchased"
    PURCHASE_COMPLETED = "purchase_completed"
    PURCHASE_FAILED = "purchase_failed"
    
    # Agent lifecycle events
    AGENT_REGISTERED = "agent_registered"
    AGENT_INSTANTIATED = "agent_instantiated"
    AGENT_CONFIGURED = "agent_configured"
    AGENT_DEACTIVATED = "agent_deactivated"
    
    # Capability events
    CAPABILITY_REGISTERED = "capability_registered"
    CAPABILITY_REQUESTED = "capability_requested"
    CAPABILITY_MATCHED = "capability_matched"
    
    # Cross-agent intelligence events
    AGENT_INTELLIGENCE_REGISTERED = "agent_intelligence_registered"
    CROSS_AGENT_COORDINATION = "cross_agent_coordination"
    INTELLIGENCE_SHARED = "intelligence_shared"
    
    # Configuration events
    CONFIGURATION_CREATED = "configuration_created"
    CONFIGURATION_UPDATED = "configuration_updated"
    CONFIGURATION_VALIDATED = "configuration_validated"
    
    # System events
    MARKETPLACE_INITIALIZED = "marketplace_initialized"
    SYSTEM_ERROR = "system_error"
    HEALTH_CHECK = "health_check"


@dataclass
class MarketplaceEvent:
    """Represents a marketplace event."""
    event_id: str
    event_type: EventType
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    persona_id: Optional[str] = None
    agent_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        return {
            "event_id": self.event_id,
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source,
            "data": self.data,
            "metadata": self.metadata,
            "correlation_id": self.correlation_id,
            "user_id": self.user_id,
            "persona_id": self.persona_id,
            "agent_id": self.agent_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MarketplaceEvent':
        """Create event from dictionary."""
        return cls(
            event_id=data["event_id"],
            event_type=EventType(data["event_type"]),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            source=data["source"],
            data=data["data"],
            metadata=data.get("metadata", {}),
            correlation_id=data.get("correlation_id"),
            user_id=data.get("user_id"),
            persona_id=data.get("persona_id"),
            agent_id=data.get("agent_id")
        )


@dataclass
class EventSubscription:
    """Represents an event subscription."""
    subscription_id: str
    event_types: Set[EventType]
    handler: Callable[[MarketplaceEvent], None]
    filter_criteria: Optional[Dict[str, Any]] = None
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_triggered: Optional[datetime] = None
    trigger_count: int = 0


class MarketplaceEventSystem:
    """
    Event-driven architecture system for marketplace operations.
    
    Features:
    - Event publishing and subscription
    - Event filtering and routing
    - Event persistence and replay
    - Real-time event streaming
    - Event correlation and tracing
    - Performance monitoring
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Event storage
        self._events: Dict[str, MarketplaceEvent] = {}
        self._event_history: List[MarketplaceEvent] = []
        self._subscriptions: Dict[str, EventSubscription] = {}
        
        # Event queues
        self._event_queue: asyncio.Queue = asyncio.Queue()
        self._priority_queue: asyncio.Queue = asyncio.Queue()
        
        # Configuration
        self.max_event_history = 10000
        self.event_retention_days = 30
        self.max_concurrent_handlers = 50
        self.handler_timeout = 30.0
        
        # Performance tracking
        self._event_metrics: Dict[str, Dict[str, Any]] = {}
        self._handler_metrics: Dict[str, Dict[str, Any]] = {}
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        self._is_running = False
        
        # Event correlation
        self._correlation_map: Dict[str, List[str]] = {}
        
        # Initialize system
        self._initialize_event_system()
    
    def _initialize_event_system(self) -> None:
        """Initialize the event system."""
        try:
            self.logger.info("Initializing marketplace event system...")
            
            # Start background tasks
            self._start_background_tasks()
            
            # Publish initialization event
            asyncio.create_task(self._publish_system_event(
                EventType.MARKETPLACE_INITIALIZED,
                {"initialized_at": datetime.utcnow().isoformat()}
            ))
            
            self._is_running = True
            self.logger.info("Marketplace event system initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize event system: {e}")
            raise
    
    def _start_background_tasks(self) -> None:
        """Start background tasks for event processing."""
        try:
            # Event processing task
            task = asyncio.create_task(self._event_processing_task())
            self._background_tasks.append(task)
            
            # Priority event processing task
            task = asyncio.create_task(self._priority_event_processing_task())
            self._background_tasks.append(task)
            
            # Event cleanup task
            task = asyncio.create_task(self._event_cleanup_task())
            self._background_tasks.append(task)
            
            # Metrics collection task
            task = asyncio.create_task(self._metrics_collection_task())
            self._background_tasks.append(task)
            
            self.logger.info("Background tasks started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start background tasks: {e}")
    
    async def publish_event(
        self,
        event_type: EventType,
        data: Dict[str, Any],
        source: str,
        priority: bool = False,
        correlation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        persona_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Publish an event to the system.
        
        Args:
            event_type: Type of event
            data: Event data
            source: Event source
            priority: Whether this is a priority event
            correlation_id: Correlation ID for event tracing
            user_id: Associated user ID
            persona_id: Associated persona ID
            agent_id: Associated agent ID
            metadata: Additional metadata
            
        Returns:
            Event ID
        """
        try:
            # Create event
            event = MarketplaceEvent(
                event_id=str(uuid.uuid4()),
                event_type=event_type,
                timestamp=datetime.utcnow(),
                source=source,
                data=data,
                metadata=metadata or {},
                correlation_id=correlation_id,
                user_id=user_id,
                persona_id=persona_id,
                agent_id=agent_id
            )
            
            # Store event
            self._events[event.event_id] = event
            self._event_history.append(event)
            
            # Update correlation map
            if correlation_id:
                if correlation_id not in self._correlation_map:
                    self._correlation_map[correlation_id] = []
                self._correlation_map[correlation_id].append(event.event_id)
            
            # Queue event for processing
            if priority:
                await self._priority_queue.put(event)
            else:
                await self._event_queue.put(event)
            
            # Update metrics
            self._update_event_metrics(event_type, "published")
            
            self.logger.debug(f"Published event {event.event_id} of type {event_type.value}")
            return event.event_id
            
        except Exception as e:
            self.logger.error(f"Error publishing event: {e}")
            raise
    
    async def subscribe(
        self,
        event_types: List[EventType],
        handler: Callable[[MarketplaceEvent], None],
        filter_criteria: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Subscribe to events.
        
        Args:
            event_types: List of event types to subscribe to
            handler: Event handler function
            filter_criteria: Optional filter criteria
            
        Returns:
            Subscription ID
        """
        try:
            subscription_id = str(uuid.uuid4())
            
            subscription = EventSubscription(
                subscription_id=subscription_id,
                event_types=set(event_types),
                handler=handler,
                filter_criteria=filter_criteria
            )
            
            self._subscriptions[subscription_id] = subscription
            
            self.logger.info(f"Created subscription {subscription_id} for events {[et.value for et in event_types]}")
            return subscription_id
            
        except Exception as e:
            self.logger.error(f"Error creating subscription: {e}")
            raise
    
    async def unsubscribe(self, subscription_id: str) -> bool:
        """
        Unsubscribe from events.
        
        Args:
            subscription_id: Subscription ID
            
        Returns:
            True if unsubscribed successfully
        """
        try:
            if subscription_id in self._subscriptions:
                del self._subscriptions[subscription_id]
                self.logger.info(f"Removed subscription {subscription_id}")
                return True
            else:
                self.logger.warning(f"Subscription {subscription_id} not found")
                return False
                
        except Exception as e:
            self.logger.error(f"Error removing subscription: {e}")
            return False

    async def _event_processing_task(self) -> None:
        """Background task to process events."""
        while self._is_running:
            try:
                # Get event from queue
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)

                # Process event
                await self._process_event(event)

                # Mark task as done
                self._event_queue.task_done()

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in event processing task: {e}")
                await asyncio.sleep(1)

    async def _priority_event_processing_task(self) -> None:
        """Background task to process priority events."""
        while self._is_running:
            try:
                # Get priority event from queue
                event = await asyncio.wait_for(self._priority_queue.get(), timeout=1.0)

                # Process event with higher priority
                await self._process_event(event, priority=True)

                # Mark task as done
                self._priority_queue.task_done()

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in priority event processing task: {e}")
                await asyncio.sleep(1)

    async def _process_event(self, event: MarketplaceEvent, priority: bool = False) -> None:
        """
        Process an event by notifying all relevant subscribers.

        Args:
            event: Event to process
            priority: Whether this is a priority event
        """
        try:
            start_time = datetime.utcnow()

            # Find matching subscriptions
            matching_subscriptions = []
            for subscription in self._subscriptions.values():
                if (subscription.is_active and
                    event.event_type in subscription.event_types and
                    self._matches_filter(event, subscription.filter_criteria)):
                    matching_subscriptions.append(subscription)

            # Process subscriptions
            if matching_subscriptions:
                # Create handler tasks
                handler_tasks = []
                for subscription in matching_subscriptions:
                    task = asyncio.create_task(
                        self._execute_handler(subscription, event)
                    )
                    handler_tasks.append(task)

                # Wait for handlers to complete (with timeout)
                if handler_tasks:
                    await asyncio.wait_for(
                        asyncio.gather(*handler_tasks, return_exceptions=True),
                        timeout=self.handler_timeout
                    )

            # Update metrics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self._update_event_metrics(event.event_type, "processed", processing_time)

            self.logger.debug(f"Processed event {event.event_id} with {len(matching_subscriptions)} handlers")

        except asyncio.TimeoutError:
            self.logger.warning(f"Event processing timed out for event {event.event_id}")
            self._update_event_metrics(event.event_type, "timeout")
        except Exception as e:
            self.logger.error(f"Error processing event {event.event_id}: {e}")
            self._update_event_metrics(event.event_type, "error")

    async def _execute_handler(
        self,
        subscription: EventSubscription,
        event: MarketplaceEvent
    ) -> None:
        """
        Execute an event handler.

        Args:
            subscription: Event subscription
            event: Event to handle
        """
        try:
            start_time = datetime.utcnow()

            # Execute handler
            if asyncio.iscoroutinefunction(subscription.handler):
                await subscription.handler(event)
            else:
                subscription.handler(event)

            # Update subscription metrics
            subscription.last_triggered = datetime.utcnow()
            subscription.trigger_count += 1

            # Update handler metrics
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            self._update_handler_metrics(subscription.subscription_id, "success", execution_time)

        except Exception as e:
            self.logger.error(f"Error executing handler {subscription.subscription_id}: {e}")
            self._update_handler_metrics(subscription.subscription_id, "error")

    def _matches_filter(
        self,
        event: MarketplaceEvent,
        filter_criteria: Optional[Dict[str, Any]]
    ) -> bool:
        """
        Check if event matches filter criteria.

        Args:
            event: Event to check
            filter_criteria: Filter criteria

        Returns:
            True if event matches filter
        """
        if not filter_criteria:
            return True

        try:
            for key, value in filter_criteria.items():
                if key == "user_id" and event.user_id != value:
                    return False
                elif key == "persona_id" and event.persona_id != value:
                    return False
                elif key == "agent_id" and event.agent_id != value:
                    return False
                elif key == "source" and event.source != value:
                    return False
                elif key in event.data and event.data[key] != value:
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Error checking filter criteria: {e}")
            return False

    async def _event_cleanup_task(self) -> None:
        """Background task to clean up old events."""
        while self._is_running:
            try:
                current_time = datetime.utcnow()
                retention_threshold = current_time - timedelta(days=self.event_retention_days)

                # Clean up old events from history
                self._event_history = [
                    event for event in self._event_history
                    if event.timestamp > retention_threshold
                ]

                # Clean up old events from storage
                old_event_ids = [
                    event_id for event_id, event in self._events.items()
                    if event.timestamp < retention_threshold
                ]

                for event_id in old_event_ids:
                    del self._events[event_id]

                # Clean up correlation map
                old_correlations = []
                for correlation_id, event_ids in self._correlation_map.items():
                    # Remove event IDs that no longer exist
                    valid_event_ids = [
                        event_id for event_id in event_ids
                        if event_id in self._events
                    ]

                    if valid_event_ids:
                        self._correlation_map[correlation_id] = valid_event_ids
                    else:
                        old_correlations.append(correlation_id)

                for correlation_id in old_correlations:
                    del self._correlation_map[correlation_id]

                # Limit event history size
                if len(self._event_history) > self.max_event_history:
                    self._event_history = self._event_history[-self.max_event_history:]

                if old_event_ids or old_correlations:
                    self.logger.debug(f"Cleaned up {len(old_event_ids)} old events and {len(old_correlations)} correlations")

                await asyncio.sleep(3600)  # Run every hour

            except Exception as e:
                self.logger.error(f"Error in event cleanup task: {e}")
                await asyncio.sleep(3600)

    async def _metrics_collection_task(self) -> None:
        """Background task to collect and log metrics."""
        while self._is_running:
            try:
                # Log event metrics
                total_events = len(self._events)
                active_subscriptions = len([s for s in self._subscriptions.values() if s.is_active])

                self.logger.info(f"Event system metrics: {total_events} total events, "
                               f"{active_subscriptions} active subscriptions")

                # Log detailed metrics for each event type
                for event_type, metrics in self._event_metrics.items():
                    self.logger.debug(f"Event type {event_type}: {metrics}")

                await asyncio.sleep(300)  # Run every 5 minutes

            except Exception as e:
                self.logger.error(f"Error in metrics collection task: {e}")
                await asyncio.sleep(300)

    def _update_event_metrics(
        self,
        event_type: EventType,
        metric_type: str,
        value: Optional[float] = None
    ) -> None:
        """Update event metrics."""
        try:
            event_type_str = event_type.value

            if event_type_str not in self._event_metrics:
                self._event_metrics[event_type_str] = {
                    "published": 0,
                    "processed": 0,
                    "timeout": 0,
                    "error": 0,
                    "avg_processing_time": 0.0,
                    "total_processing_time": 0.0
                }

            metrics = self._event_metrics[event_type_str]
            metrics[metric_type] += 1

            if metric_type == "processed" and value is not None:
                metrics["total_processing_time"] += value
                metrics["avg_processing_time"] = (
                    metrics["total_processing_time"] / metrics["processed"]
                )

        except Exception as e:
            self.logger.error(f"Error updating event metrics: {e}")

    def _update_handler_metrics(
        self,
        subscription_id: str,
        metric_type: str,
        value: Optional[float] = None
    ) -> None:
        """Update handler metrics."""
        try:
            if subscription_id not in self._handler_metrics:
                self._handler_metrics[subscription_id] = {
                    "success": 0,
                    "error": 0,
                    "avg_execution_time": 0.0,
                    "total_execution_time": 0.0
                }

            metrics = self._handler_metrics[subscription_id]
            metrics[metric_type] += 1

            if metric_type == "success" and value is not None:
                metrics["total_execution_time"] += value
                metrics["avg_execution_time"] = (
                    metrics["total_execution_time"] / metrics["success"]
                )

        except Exception as e:
            self.logger.error(f"Error updating handler metrics: {e}")

    async def _publish_system_event(
        self,
        event_type: EventType,
        data: Dict[str, Any]
    ) -> None:
        """Publish a system event."""
        try:
            await self.publish_event(
                event_type=event_type,
                data=data,
                source="marketplace_event_system",
                priority=True
            )
        except Exception as e:
            self.logger.error(f"Error publishing system event: {e}")

    async def get_events_by_correlation(self, correlation_id: str) -> List[MarketplaceEvent]:
        """Get all events with a specific correlation ID."""
        try:
            event_ids = self._correlation_map.get(correlation_id, [])
            events = [self._events[event_id] for event_id in event_ids if event_id in self._events]
            return sorted(events, key=lambda e: e.timestamp)
        except Exception as e:
            self.logger.error(f"Error getting events by correlation: {e}")
            return []

    async def get_event_metrics(self) -> Dict[str, Any]:
        """Get event system metrics."""
        return {
            "total_events": len(self._events),
            "active_subscriptions": len([s for s in self._subscriptions.values() if s.is_active]),
            "event_metrics": self._event_metrics,
            "handler_metrics": self._handler_metrics,
            "queue_sizes": {
                "normal": self._event_queue.qsize(),
                "priority": self._priority_queue.qsize()
            }
        }

    async def cleanup(self) -> None:
        """Cleanup resources and stop background tasks."""
        try:
            self._is_running = False

            # Cancel background tasks
            for task in self._background_tasks:
                if not task.done():
                    task.cancel()

            # Wait for tasks to complete
            if self._background_tasks:
                await asyncio.gather(*self._background_tasks, return_exceptions=True)

            # Clear data
            self._events.clear()
            self._event_history.clear()
            self._subscriptions.clear()
            self._correlation_map.clear()

            self.logger.info("Marketplace event system cleaned up successfully")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


# Global instance (lazy initialization)
_marketplace_event_system = None

def get_marketplace_event_system() -> MarketplaceEventSystem:
    """Get the global marketplace event system instance (lazy initialization)."""
    global _marketplace_event_system
    if _marketplace_event_system is None:
        _marketplace_event_system = MarketplaceEventSystem()
    return _marketplace_event_system

# For backward compatibility
marketplace_event_system = None
