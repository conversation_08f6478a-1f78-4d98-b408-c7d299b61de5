{"enabled": true, "global_settings": {"algorithm": "sliding_window", "cleanup_interval_seconds": 300, "enable_metrics": true, "enable_alerts": true, "log_violations": true}, "environments": {"development": {"enabled": true, "requests_per_minute": 1000, "burst_size": 100, "window_size_seconds": 60, "sub_windows": 6, "strict_mode": false, "whitelist_enabled": true, "whitelisted_ips": ["127.0.0.1", "::1", "localhost"], "rate_limit_headers": true, "custom_limits": {"auth": {"requests_per_minute": 50, "burst_size": 10, "window_size_seconds": 300}, "api": {"requests_per_minute": 500, "burst_size": 50, "window_size_seconds": 60}, "upload": {"requests_per_minute": 20, "burst_size": 5, "window_size_seconds": 3600}}}, "staging": {"enabled": true, "requests_per_minute": 500, "burst_size": 50, "window_size_seconds": 60, "sub_windows": 6, "strict_mode": true, "whitelist_enabled": false, "whitelisted_ips": [], "rate_limit_headers": true, "custom_limits": {"auth": {"requests_per_minute": 30, "burst_size": 5, "window_size_seconds": 300}, "api": {"requests_per_minute": 300, "burst_size": 30, "window_size_seconds": 60}, "upload": {"requests_per_minute": 10, "burst_size": 2, "window_size_seconds": 3600}}}, "production": {"enabled": true, "requests_per_minute": 100, "burst_size": 20, "window_size_seconds": 60, "sub_windows": 6, "strict_mode": true, "whitelist_enabled": false, "whitelisted_ips": [], "rate_limit_headers": false, "custom_limits": {"auth": {"requests_per_minute": 10, "burst_size": 2, "window_size_seconds": 300}, "api": {"requests_per_minute": 60, "burst_size": 10, "window_size_seconds": 60}, "upload": {"requests_per_minute": 5, "burst_size": 1, "window_size_seconds": 3600}}}, "testing": {"enabled": false, "requests_per_minute": 10000, "burst_size": 1000, "window_size_seconds": 60, "sub_windows": 1, "strict_mode": false, "whitelist_enabled": true, "whitelisted_ips": ["*"], "rate_limit_headers": false, "custom_limits": {"auth": {"requests_per_minute": 1000, "burst_size": 100, "window_size_seconds": 60}, "api": {"requests_per_minute": 5000, "burst_size": 500, "window_size_seconds": 60}, "upload": {"requests_per_minute": 100, "burst_size": 50, "window_size_seconds": 60}}}}, "violation_handling": {"block_duration_seconds": 300, "progressive_penalties": true, "max_violations_before_ban": 5, "ban_duration_seconds": 3600, "notify_on_violations": true, "log_violations": true}, "monitoring": {"enabled": true, "metrics_collection": true, "alert_thresholds": {"high_violation_rate": 0.1, "sustained_violations": 10, "unusual_traffic_spike": 5.0}, "dashboard_enabled": true, "export_metrics": true}}