"""
Cart Repository Implementation.

Provides specialized repository operations for Cart entities,
replacing the cart-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional
from sqlalchemy.orm import Session

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import CartItem
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class CartRepository(BaseRepository[CartItem]):
    """Repository for CartItem entity operations."""

    def __init__(self, session: Session):
        super().__init__(session, CartItem)
        self.logger = logger

    def get_cart_items(self, user_id: int) -> List[CartItem]:
        """
        Get all cart items for a user.

        Args:
            user_id: ID of the user

        Returns:
            List of cart items for the user
        """
        try:
            return self.session.query(CartItem).filter(
                CartItem.user_id == user_id
            ).all()

        except Exception as e:
            self.logger.error(f"Failed to get cart items: {e}")
            raise RepositoryError(
                f"Failed to get cart items: {str(e)}",
                entity_type="CartItem",
                operation="get_cart_items"
            )
    
    def get_cart_item(self, cart_item_id: str) -> Optional[CartItem]:
        """
        Get a cart item by ID.

        Args:
            cart_item_id: ID of the cart item

        Returns:
            Cart item or None if not found
        """
        try:
            return self.session.query(CartItem).filter(
                CartItem.id == cart_item_id
            ).first()

        except Exception as e:
            self.logger.error(f"Failed to get cart item: {e}")
            raise RepositoryError(
                f"Failed to get cart item: {str(e)}",
                entity_type="CartItem",
                operation="get_cart_item"
            )
    
    def add_to_cart(
        self,
        user_id: int,
        persona_id: str,
        quantity: int = 1
    ) -> CartItem:
        """
        Add an item to the user's cart.

        Args:
            user_id: ID of the user
            persona_id: ID of the persona to add
            quantity: Quantity to add (default: 1)

        Returns:
            Cart item (new or updated)
        """
        try:
            # Check if the item already exists in the cart
            cart_item = self.session.query(CartItem).filter(
                CartItem.user_id == user_id,
                CartItem.persona_id == persona_id
            ).first()

            if cart_item:
                # Update quantity
                cart_item.quantity += quantity
                self.session.commit()
                self.session.refresh(cart_item)
                self.logger.info(f"Updated cart item {cart_item.id} quantity to {cart_item.quantity}")
                return cart_item
            else:
                # Create new cart item
                cart_item = CartItem(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    persona_id=persona_id,
                    quantity=quantity
                )
                self.session.add(cart_item)
                self.session.commit()
                self.session.refresh(cart_item)
                self.logger.info(f"Created new cart item {cart_item.id}")
                return cart_item

        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to add item to cart: {e}")
            raise RepositoryError(
                f"Failed to add item to cart: {str(e)}",
                entity_type="CartItem",
                operation="add_to_cart"
            )
    
    def remove_from_cart(
        self,
        cart_item_id: str
    ) -> bool:
        """
        Remove an item from the cart by cart item ID.

        Args:
            cart_item_id: ID of the cart item to remove

        Returns:
            True if item was removed, False if not found
        """
        try:
            cart_item = self.session.query(CartItem).filter(
                CartItem.id == cart_item_id
            ).first()

            if not cart_item:
                self.logger.warning(f"Cart item {cart_item_id} not found")
                return False

            self.session.delete(cart_item)
            self.session.commit()

            self.logger.info(f"Removed cart item {cart_item_id}")
            return True

        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to remove cart item: {e}")
            raise RepositoryError(
                f"Failed to remove cart item: {str(e)}",
                entity_type="CartItem",
                operation="remove_from_cart"
            )
    
    def update_cart_item_quantity(
        self,
        cart_item_id: str,
        quantity: int
    ) -> Optional[CartItem]:
        """
        Update the quantity of a cart item.

        Args:
            cart_item_id: ID of the cart item
            quantity: New quantity (0 to remove item)

        Returns:
            Updated cart item or None if not found
        """
        try:
            cart_item = self.session.query(CartItem).filter(
                CartItem.id == cart_item_id
            ).first()

            if not cart_item:
                self.logger.warning(f"Cart item {cart_item_id} not found")
                return None

            if quantity <= 0:
                # Remove item if quantity is 0 or negative
                self.session.delete(cart_item)
                self.session.commit()
                self.logger.info(f"Removed cart item {cart_item_id} (quantity was {quantity})")
                return None
            else:
                # Update quantity
                cart_item.quantity = quantity
                self.session.commit()
                self.session.refresh(cart_item)
                self.logger.info(f"Updated cart item {cart_item_id} quantity to {quantity}")
                return cart_item

        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update cart item quantity: {e}")
            raise RepositoryError(
                f"Failed to update cart item quantity: {str(e)}",
                entity_type="CartItem",
                operation="update_cart_item_quantity"
            )
    
    def clear_cart(self, user_id: int) -> bool:
        """
        Clear all items from the user's cart.

        Args:
            user_id: ID of the user

        Returns:
            True if cart was cleared, False if no items found
        """
        try:
            cart_items = self.session.query(CartItem).filter(
                CartItem.user_id == user_id
            ).all()

            if not cart_items:
                self.logger.info(f"No cart items found for user {user_id}")
                return False

            for cart_item in cart_items:
                self.session.delete(cart_item)

            self.session.commit()

            self.logger.info(f"Cleared {len(cart_items)} items from cart for user {user_id}")
            return True

        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to clear cart: {e}")
            raise RepositoryError(
                f"Failed to clear cart: {str(e)}",
                entity_type="CartItem",
                operation="clear_cart"
            )
    
    def get_cart_item_count(self, user_id: int) -> int:
        """
        Get the total number of items in the user's cart.

        Args:
            user_id: ID of the user

        Returns:
            Total number of items in the cart
        """
        try:
            cart_items = self.session.query(CartItem).filter(
                CartItem.user_id == user_id
            ).all()

            return sum(item.quantity for item in cart_items)

        except Exception as e:
            self.logger.error(f"Failed to get cart item count: {e}")
            raise RepositoryError(
                f"Failed to get cart item count: {str(e)}",
                entity_type="CartItem",
                operation="get_cart_item_count"
            )
