import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from './AuthContext';
import { AIPersona } from '@/data/aiPersonas';

// Define the cart item type
export interface CartItem {
  id: string;
  persona_id: string;
  quantity: number;
  created_at: string;
  user_id: number;
}

// Define the cart context type
interface CartContextType {
  items: CartItem[];
  totalItems: number;
  addToCart: (personaId: string, quantity?: number) => Promise<void>;
  removeFromCart: (cartItemId: string) => Promise<void>;
  updateQuantity: (cartItemId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  isLoading: boolean;
}

// Create the cart context with default values
const CartContext = createContext<CartContextType>({
  items: [],
  totalItems: 0,
  addToCart: async () => {},
  removeFromCart: async () => {},
  updateQuantity: async () => {},
  clearCart: async () => {},
  isLoading: false,
});

// Cart provider props
interface CartProviderProps {
  children: ReactNode;
}

// API base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

// Create the cart provider component
export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();

  // Fetch cart items when the component mounts or when the user logs in
  useEffect(() => {
    if (isAuthenticated) {
      fetchCartItems();
    } else {
      setItems([]);
      setTotalItems(0);
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  // Fetch cart items from the API
  const fetchCartItems = async () => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/cart`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch cart items');
      }

      const data = await response.json();
      setItems(data.items);
      setTotalItems(data.total_items);
    } catch (error) {
      console.error('Error fetching cart items:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch cart items',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add an item to the cart
  const addToCart = async (personaId: string, quantity: number = 1) => {
    if (!isAuthenticated) {
      toast({
        title: 'Error',
        description: 'You must be logged in to add items to your cart',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/cart/add`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          persona_id: personaId,
          quantity,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add item to cart');
      }

      const newItem = await response.json();
      
      // Check if the item already exists in the cart
      const existingItemIndex = items.findIndex(item => item.persona_id === personaId);
      
      if (existingItemIndex >= 0) {
        // Update existing item
        const updatedItems = [...items];
        updatedItems[existingItemIndex] = newItem;
        setItems(updatedItems);
      } else {
        // Add new item
        setItems([...items, newItem]);
      }
      
      // Update total items
      setTotalItems(prevTotal => prevTotal + quantity);
      
      toast({
        title: 'Success',
        description: 'Item added to cart',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error adding item to cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to add item to cart',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Remove an item from the cart
  const removeFromCart = async (cartItemId: string) => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/cart/remove`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cart_item_id: cartItemId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to remove item from cart');
      }

      // Find the item to remove
      const itemToRemove = items.find(item => item.id === cartItemId);
      
      if (itemToRemove) {
        // Update total items
        setTotalItems(prevTotal => prevTotal - itemToRemove.quantity);
        
        // Remove the item from the list
        setItems(items.filter(item => item.id !== cartItemId));
      }
      
      toast({
        title: 'Success',
        description: 'Item removed from cart',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error removing item from cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove item from cart',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update the quantity of an item in the cart
  const updateQuantity = async (cartItemId: string, quantity: number) => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/cart/update/${cartItemId}?quantity=${quantity}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to update item quantity');
      }

      const updatedItem = await response.json();
      
      // Find the item to update
      const itemIndex = items.findIndex(item => item.id === cartItemId);
      
      if (itemIndex >= 0) {
        // Calculate the difference in quantity
        const oldQuantity = items[itemIndex].quantity;
        const quantityDiff = quantity - oldQuantity;
        
        // Update total items
        setTotalItems(prevTotal => prevTotal + quantityDiff);
        
        // Update the item in the list
        const updatedItems = [...items];
        updatedItems[itemIndex] = updatedItem;
        setItems(updatedItems);
      }
      
      toast({
        title: 'Success',
        description: 'Item quantity updated',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error updating item quantity:', error);
      toast({
        title: 'Error',
        description: 'Failed to update item quantity',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Clear the cart
  const clearCart = async () => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/cart/clear`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to clear cart');
      }

      // Clear the cart
      setItems([]);
      setTotalItems(0);
      
      toast({
        title: 'Success',
        description: 'Cart cleared',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error clearing cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to clear cart',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create the context value
  const contextValue: CartContextType = {
    items,
    totalItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    isLoading,
  };

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
};

// Custom hook to use the cart context
export const useCart = () => useContext(CartContext);
