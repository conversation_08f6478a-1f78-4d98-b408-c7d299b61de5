"""
MCP Tool Integration with LangGraph Nodes.

This module provides seamless integration between existing MCP tools
and LangGraph workflow nodes, allowing tools to be executed as part
of graph-based workflows.
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import asyncio

from ..states.agent_state import DatageniusAgentState, add_tool_result, add_error
from ...tools.mcp.base import BaseMCPTool

logger = logging.getLogger(__name__)


class MCPToolNode:
    """
    LangGraph node that executes MCP tools within workflows.
    
    This class wraps MCP tools to make them compatible with LangGraph
    workflows while preserving all their functionality and agent awareness.
    """

    def __init__(self, tool_name: str, mcp_tool: BaseMCPTool, argument_mapper: Optional[Callable] = None):
        """
        Initialize the MCP tool node.
        
        Args:
            tool_name: Name identifier for the tool
            mcp_tool: The MCP tool instance to execute
            argument_mapper: Optional function to map state to tool arguments
        """
        self.tool_name = tool_name
        self.mcp_tool = mcp_tool
        self.argument_mapper = argument_mapper or self._default_argument_mapper
        self.logger = logging.getLogger(f"{__name__}.{tool_name}")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Execute MCP tool and update state.

        Args:
            state: Current workflow state

        Returns:
            Updated state with tool results
        """
        try:
            start_time = datetime.now()
            agent_identity = state.get("current_agent", "unknown")

            # Publish tool execution start event
            await self._publish_tool_event(
                state,
                "started",
                {
                    "tool_args": tool_args
                }
            )

            # Extract tool arguments from state
            tool_args = self.argument_mapper(state)

            # Publish tool execution progress event
            await self._publish_tool_event(
                state,
                "progress",
                {
                    "status_message": f"Executing {self.tool_name}..."
                }
            )

            # Add agent identity for agent-aware tools
            if hasattr(self.mcp_tool, 'execute_with_agent_awareness'):
                tool_result = await self.mcp_tool.execute_with_agent_awareness(
                    arguments=tool_args,
                    agent_identity=agent_identity
                )
            else:
                # Fallback to standard execution
                tool_result = await self.mcp_tool.execute(tool_args)

            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()

            # Publish tool execution completion event
            await self._publish_tool_event(
                state,
                "completed",
                {
                    "execution_time": execution_time,
                    "result_summary": {
                        "success": True,
                        "tool_name": self.tool_name
                    }
                }
            )

            # Enhance result with metadata
            enhanced_result = {
                "tool_name": self.tool_name,
                "result": tool_result,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "agent_context": agent_identity
            }

            # Update state with results
            state = add_tool_result(state, self.tool_name, enhanced_result)

            # Update execution metrics
            state["execution_metrics"]["tool_executions"] = state["execution_metrics"].get("tool_executions", 0) + 1
            state["execution_metrics"][f"{self.tool_name}_execution_time"] = execution_time

            self.logger.info(f"MCP tool {self.tool_name} executed successfully in {execution_time:.2f}s")
            return state

        except Exception as e:
            self.logger.error(f"MCP tool {self.tool_name} execution failed: {e}")

            # Publish tool execution error event
            await self._publish_tool_event(
                state,
                "failed",
                {
                    "error_message": str(e),
                    "execution_time": (datetime.now() - start_time).total_seconds() if 'start_time' in locals() else None,
                    "error_details": {
                        "tool_name": self.tool_name,
                        "error_type": type(e).__name__
                    }
                }
            )

            error_context = {
                "tool_name": self.tool_name,
                "arguments": tool_args if 'tool_args' in locals() else {},
                "agent": state.get("current_agent", "unknown")
            }
            return add_error(state, "mcp_tool_execution_error", str(e), error_context)

    def _default_argument_mapper(self, state: DatageniusAgentState) -> Dict[str, Any]:
        """
        Default argument mapper that extracts common arguments from state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dictionary of arguments for the tool
        """
        # Common argument mappings for different tool types
        base_args = {
            "user_id": state.get("user_id"),
            "conversation_id": state.get("conversation_id"),
            "business_profile_id": state.get("business_profile_id"),
            "business_context": state.get("business_context", {}),
            "attached_files": state.get("attached_files", [])
        }
        
        # Add message content if available
        if state.get("messages"):
            latest_message = state["messages"][-1]
            base_args["message"] = latest_message.get("content", "")
            base_args["message_context"] = latest_message
        
        # Tool-specific argument mapping
        tool_specific_args = self._get_tool_specific_arguments(state)
        base_args.update(tool_specific_args)
        
        return base_args

    def _get_tool_specific_arguments(self, state: DatageniusAgentState) -> Dict[str, Any]:
        """
        Get tool-specific arguments based on tool name and type.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dictionary of tool-specific arguments
        """
        tool_mappings = {
            # Data access tools
            "data_access_tool": {
                "query": state["messages"][-1]["content"] if state.get("messages") else "",
                "filters": state.get("data_filters", {}),
                "limit": state.get("data_limit", 100)
            },
            
            # Code execution tools
            "code_execution_tool": {
                "code": state.get("generated_code", ""),
                "language": state.get("code_language", "python"),
                "context": state.get("execution_context", {})
            },
            
            # Visualization tools
            "visualization_tool": {
                "data": state["tool_results"].get("data_access_tool", {}).get("result", {}),
                "chart_type": state.get("requested_chart_type", "auto"),
                "title": state.get("chart_title", ""),
                "options": state.get("chart_options", {})
            },
            
            # Analysis tools
            "statistical_analysis": {
                "data": state["tool_results"].get("data_access_tool", {}).get("result", {}),
                "analysis_type": state.get("analysis_type", "descriptive"),
                "parameters": state.get("analysis_parameters", {})
            },
            
            # Marketing tools
            "marketing_strategy_generation": {
                "brand_info": state.get("business_context", {}).get("brand_info", {}),
                "target_audience": state.get("target_audience", {}),
                "campaign_type": state.get("campaign_type", "general"),
                "objectives": state.get("marketing_objectives", [])
            },
            
            # Text processing tools
            "text_processing": {
                "text": state["messages"][-1]["content"] if state.get("messages") else "",
                "operation": state.get("text_operation", "summarize"),
                "params": state.get("text_params", {})
            },
            
            # Feedback collection tools
            "feedback_collection_tool": {
                "response_content": state.get("agent_outputs", {}).get(state.get("current_agent", ""), {}),
                "interaction_id": state.get("conversation_id"),
                "agent_id": state.get("current_agent")
            }
        }
        
        return tool_mappings.get(self.tool_name, {})


class MCPToolChainNode:
    """
    LangGraph node that executes a chain of MCP tools.
    
    This allows for complex tool compositions within a single node,
    useful for multi-step operations that should be atomic.
    """

    def __init__(self, chain_name: str, tool_chain: List[MCPToolNode], execution_mode: str = "sequential"):
        """
        Initialize the MCP tool chain node.
        
        Args:
            chain_name: Name identifier for the tool chain
            tool_chain: List of MCPToolNode instances to execute
            execution_mode: "sequential" or "parallel" execution
        """
        self.chain_name = chain_name
        self.tool_chain = tool_chain
        self.execution_mode = execution_mode
        self.logger = logging.getLogger(f"{__name__}.{chain_name}")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Execute tool chain and update state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with all tool results
        """
        try:
            start_time = datetime.now()
            
            if self.execution_mode == "sequential":
                # Execute tools sequentially, passing state between them
                for tool_node in self.tool_chain:
                    state = await tool_node(state)
                    
                    # Check for errors and stop if critical error occurred
                    if state.get("error_history") and self._has_critical_error(state):
                        break
                        
            elif self.execution_mode == "parallel":
                # Execute tools in parallel
                tasks = [tool_node(state.copy()) for tool_node in self.tool_chain]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Merge results back into state
                state = self._merge_parallel_results(state, results)
            
            # Calculate total execution time
            total_time = (datetime.now() - start_time).total_seconds()
            
            # Update execution metrics
            state["execution_metrics"][f"{self.chain_name}_chain_time"] = total_time
            
            self.logger.info(f"Tool chain {self.chain_name} completed in {total_time:.2f}s")
            return state

        except Exception as e:
            self.logger.error(f"Tool chain {self.chain_name} execution failed: {e}")
            return add_error(state, "tool_chain_execution_error", str(e), {"chain_name": self.chain_name})

    def _has_critical_error(self, state: DatageniusAgentState) -> bool:
        """Check if state contains critical errors that should stop execution."""
        error_history = state.get("error_history", [])
        if not error_history:
            return False
        
        # Check for critical error types
        critical_error_types = ["authentication_error", "permission_error", "system_error"]
        recent_errors = error_history[-3:]  # Check last 3 errors
        
        return any(error.get("type") in critical_error_types for error in recent_errors)

    def _merge_parallel_results(self, base_state: DatageniusAgentState, results: List[Any]) -> DatageniusAgentState:
        """Merge results from parallel tool execution."""
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Handle exception from parallel execution
                error_context = {"tool_index": i, "chain_name": self.chain_name}
                base_state = add_error(base_state, "parallel_tool_error", str(result), error_context)
            elif isinstance(result, dict):
                # Merge successful result
                # This is a simplified merge - in production, more sophisticated merging might be needed
                for key, value in result.items():
                    if key in ["tool_results", "agent_outputs"]:
                        base_state[key].update(value)
                    elif key == "error_history":
                        base_state[key].extend(value)
                    elif key == "execution_metrics":
                        base_state[key].update(value)
        
        return base_state

    async def _publish_tool_event(self, state: DatageniusAgentState, event_type: str, event_data: dict):
        """
        Publish tool execution events through the LangGraph event system.

        Args:
            state: Current workflow state
            event_type: Type of event (started, progress, completed, failed)
            event_data: Additional event data
        """
        try:
            # Get required context from state
            conversation_id = state.get("conversation_id")
            message_id = state.get("current_message_id")
            workflow_id = state.get("workflow_id", "unknown")
            agent_type = state.get("current_agent", "unknown")

            if not conversation_id or not message_id:
                self.logger.debug(f"No conversation context available for tool event: {event_type}")
                return

            # Import event types here to avoid circular imports
            from ..events.types import (
                ToolExecutionStartedEvent,
                ToolExecutionProgressEvent,
                ToolExecutionCompletedEvent,
                ToolExecutionFailedEvent
            )
            from ..events.event_bus import event_bus

            # Create appropriate event based on type
            if event_type == "started":
                event = ToolExecutionStartedEvent(
                    tool_name=self.tool_name,
                    agent_type=agent_type,
                    workflow_id=workflow_id,
                    conversation_id=conversation_id,
                    message_id=message_id,
                    tool_args=event_data.get("tool_args", {})
                )
            elif event_type == "progress":
                event = ToolExecutionProgressEvent(
                    tool_name=self.tool_name,
                    agent_type=agent_type,
                    workflow_id=workflow_id,
                    conversation_id=conversation_id,
                    message_id=message_id,
                    progress=event_data.get("progress"),
                    status_message=event_data.get("status_message")
                )
            elif event_type == "completed":
                event = ToolExecutionCompletedEvent(
                    tool_name=self.tool_name,
                    agent_type=agent_type,
                    workflow_id=workflow_id,
                    conversation_id=conversation_id,
                    message_id=message_id,
                    execution_time=event_data.get("execution_time", 0),
                    result_summary=event_data.get("result_summary", {})
                )
            elif event_type == "failed":
                event = ToolExecutionFailedEvent(
                    tool_name=self.tool_name,
                    agent_type=agent_type,
                    workflow_id=workflow_id,
                    conversation_id=conversation_id,
                    message_id=message_id,
                    error_message=event_data.get("error_message", "Unknown error"),
                    execution_time=event_data.get("execution_time"),
                    error_details=event_data.get("error_details", {})
                )
            else:
                self.logger.warning(f"Unknown tool event type: {event_type}")
                return

            # Publish event through the event bus
            await event_bus.publish(event)

            self.logger.debug(f"Published tool {event_type} event for {self.tool_name}")

        except Exception as e:
            # Don't fail tool execution if event publishing fails
            self.logger.warning(f"Failed to publish tool event {event_type}: {str(e)}")


def create_mcp_tool_node(tool_name: str, mcp_tool: BaseMCPTool, **kwargs) -> MCPToolNode:
    """
    Factory function to create MCP tool nodes.
    
    Args:
        tool_name: Name for the tool node
        mcp_tool: MCP tool instance
        **kwargs: Additional arguments for MCPToolNode
        
    Returns:
        Configured MCPToolNode instance
    """
    return MCPToolNode(tool_name, mcp_tool, **kwargs)


def create_mcp_tool_chain(chain_name: str, tools: List[Dict[str, Any]], execution_mode: str = "sequential") -> MCPToolChainNode:
    """
    Factory function to create MCP tool chain nodes.
    
    Args:
        chain_name: Name for the tool chain
        tools: List of tool configurations
        execution_mode: Execution mode for the chain
        
    Returns:
        Configured MCPToolChainNode instance
    """
    tool_nodes = []
    for tool_config in tools:
        tool_node = create_mcp_tool_node(**tool_config)
        tool_nodes.append(tool_node)
    
    return MCPToolChainNode(chain_name, tool_nodes, execution_mode)
