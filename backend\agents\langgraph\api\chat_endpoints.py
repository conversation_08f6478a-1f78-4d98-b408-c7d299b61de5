"""
Migrated Chat Endpoints for LangGraph-based Datagenius System.

This module provides chat endpoints that use LangGraph workflows
instead of the legacy orchestrator system.
"""

import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..core.workflow_manager import WorkflowManager
from ..core.agent_factory import agent_factory
from ....app.database import (
    get_db, create_conversation, get_conversation, get_user_conversations,
    update_conversation, delete_conversation, create_message, get_conversation_messages,
    update_message, get_message, Message, Conversation
)
from ....app.auth import get_current_active_user, get_current_user_from_token
from ....app.models.chat import (
    ConversationCreate, ConversationResponse, ConversationListResponse,
    MessageCreate, MessageResponse, SendMessageRequest, SendMessageResponse
)
from ....app.utils.json_utils import ensure_serializable, sanitize_metadata, sanitize_json

logger = logging.getLogger(__name__)


# Enhanced request models for LangGraph
class LangGraphSendMessageRequest(BaseModel):
    """Enhanced message request for LangGraph workflows."""
    conversation_id: str
    message: str
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    workflow_type: str = Field("default", description="Type of workflow to use")
    streaming: bool = Field(False, description="Enable streaming response")
    business_profile_id: Optional[str] = None


class LangGraphSendMessageResponse(BaseModel):
    """Enhanced message response for LangGraph workflows."""
    conversation_id: str
    user_message: MessageResponse
    ai_message: Optional[MessageResponse] = None
    workflow_id: str
    workflow_status: str
    execution_time: Optional[float] = None
    agent_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class LangGraphChatRouter:
    """
    Chat router that uses LangGraph workflows instead of legacy orchestrator.
    
    This router maintains compatibility with existing chat API while
    routing through the new LangGraph system.
    """
    
    def __init__(self):
        """Initialize the LangGraph chat router."""
        self.router = APIRouter(prefix="/chat/v2", tags=["Chat V2 (LangGraph)"])
        self.workflow_manager = WorkflowManager()
        self.logger = logging.getLogger(__name__)
        
        # Initialize workflow manager with agents
        self._initialize_workflow_manager()
        
        # Setup routes
        self._setup_routes()
        
        self.logger.info("LangGraphChatRouter initialized")
    
    def _initialize_workflow_manager(self) -> None:
        """Initialize workflow manager with agent nodes."""
        try:
            # Create and register agent nodes
            agents = agent_factory.create_all_agents()
            for agent_id, agent_node in agents.items():
                self.workflow_manager.register_agent_node(agent_id, agent_node)
            
            self.logger.info(f"Initialized workflow manager with {len(agents)} agents")
            
        except Exception as e:
            self.logger.error(f"Error initializing workflow manager: {e}", exc_info=True)
    
    def _setup_routes(self) -> None:
        """Setup chat API routes."""
        
        @self.router.post("/conversations", response_model=ConversationResponse)
        async def create_new_conversation(
            conversation: ConversationCreate,
            db: Session = Depends(get_db),
            current_user = Depends(get_current_active_user)
        ):
            """
            Create a new conversation (LangGraph version).
            
            Args:
                conversation: Conversation creation request
                db: Database session
                current_user: Current authenticated user
                
            Returns:
                Created conversation
            """
            try:
                self.logger.info(f"User {current_user.id} creating new conversation with persona {conversation.persona_id}")
                
                # Validate persona exists in agent factory
                available_agents = agent_factory.get_available_agents()
                if conversation.persona_id not in available_agents:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Persona '{conversation.persona_id}' not available. Available: {available_agents}"
                    )
                
                # Create conversation in database
                db_conversation = create_conversation(
                    db=db,
                    user_id=current_user.id,
                    title=conversation.title,
                    persona_id=conversation.persona_id,
                    metadata=conversation.metadata
                )
                
                return ConversationResponse(
                    id=db_conversation.id,
                    user_id=db_conversation.user_id,
                    title=db_conversation.title,
                    persona_id=db_conversation.persona_id,
                    created_at=db_conversation.created_at,
                    updated_at=db_conversation.updated_at,
                    is_archived=db_conversation.is_archived,
                    metadata=db_conversation.conversation_metadata or {}
                )
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error creating conversation: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/conversations", response_model=ConversationListResponse)
        async def get_conversations(
            db: Session = Depends(get_db),
            current_user = Depends(get_current_active_user),
            limit: int = Query(50, ge=1, le=100),
            offset: int = Query(0, ge=0)
        ):
            """
            Get user conversations.
            
            Args:
                db: Database session
                current_user: Current authenticated user
                limit: Maximum number of conversations to return
                offset: Number of conversations to skip
                
            Returns:
                List of conversations
            """
            try:
                conversations = get_user_conversations(
                    db=db,
                    user_id=current_user.id,
                    limit=limit,
                    offset=offset
                )
                
                conversation_responses = []
                for conv in conversations:
                    conversation_responses.append(ConversationResponse(
                        id=conv.id,
                        user_id=conv.user_id,
                        title=conv.title,
                        persona_id=conv.persona_id,
                        created_at=conv.created_at,
                        updated_at=conv.updated_at,
                        is_archived=conv.is_archived,
                        metadata=conv.conversation_metadata or {}
                    ))
                
                return ConversationListResponse(
                    conversations=conversation_responses,
                    total=len(conversation_responses)
                )
                
            except Exception as e:
                self.logger.error(f"Error getting conversations: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.post("/send", response_model=LangGraphSendMessageResponse)
        async def send_message_to_agent(
            request: LangGraphSendMessageRequest,
            db: Session = Depends(get_db),
            current_user = Depends(get_current_active_user)
        ):
            """
            Send a message to an agent using LangGraph workflow.
            
            Args:
                request: Message sending request
                db: Database session
                current_user: Current authenticated user
                
            Returns:
                Message response with workflow information
            """
            try:
                start_time = datetime.now()
                
                # Validate conversation exists
                conversation = get_conversation(db, request.conversation_id)
                if not conversation:
                    raise HTTPException(status_code=404, detail="Conversation not found")
                
                if conversation.user_id != current_user.id:
                    raise HTTPException(status_code=403, detail="Access denied")
                
                # Create user message in database
                user_message_id = str(uuid.uuid4())
                user_db_message = create_message(
                    db=db,
                    message_id=user_message_id,
                    conversation_id=request.conversation_id,
                    sender="user",
                    content=request.message,
                    metadata=request.metadata or {}
                )
                
                # Prepare context for workflow
                enhanced_context = {
                    "conversation_id": request.conversation_id,
                    "persona_id": conversation.persona_id,
                    "business_profile_id": request.business_profile_id,
                    "user_preferences": getattr(current_user, "preferences", {}),
                    **(request.context or {})
                }
                
                # Create and execute workflow
                workflow_id = await self.workflow_manager.create_workflow(
                    user_id=current_user.id,
                    conversation_id=request.conversation_id,
                    message=request.message,
                    context=enhanced_context,
                    workflow_type=request.workflow_type
                )
                
                if request.streaming:
                    # For streaming, return immediate response and handle streaming separately
                    return StreamingResponse(
                        self._stream_workflow_response(
                            workflow_id, request.conversation_id, user_db_message, db
                        ),
                        media_type="text/plain"
                    )
                
                # Execute workflow
                workflow_result = await self.workflow_manager.execute_workflow(workflow_id)
                
                # Extract AI response
                ai_response_content = "I apologize, but I couldn't generate a response."
                if workflow_result.get("status") == "completed":
                    result_data = workflow_result.get("result", {})
                    messages = result_data.get("messages", [])
                    if messages:
                        last_message = messages[-1]
                        if last_message.get("type") == "agent":
                            ai_response_content = last_message.get("content", ai_response_content)
                
                # Create AI message in database
                ai_message_id = str(uuid.uuid4())
                ai_metadata = {
                    "workflow_id": workflow_id,
                    "workflow_status": workflow_result.get("status"),
                    "agent_id": workflow_result.get("result", {}).get("current_agent"),
                    "execution_time": workflow_result.get("execution_time"),
                    **(workflow_result.get("result", {}).get("execution_metrics", {}))
                }
                
                ai_db_message = create_message(
                    db=db,
                    message_id=ai_message_id,
                    conversation_id=request.conversation_id,
                    sender="ai",
                    content=ai_response_content,
                    metadata=sanitize_metadata(ai_metadata)
                )
                
                # Update conversation timestamp
                update_conversation(db, request.conversation_id)
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return LangGraphSendMessageResponse(
                    conversation_id=request.conversation_id,
                    user_message=MessageResponse(
                        id=user_db_message.id,
                        conversation_id=user_db_message.conversation_id,
                        sender=user_db_message.sender,
                        content=user_db_message.content,
                        metadata=user_db_message.message_metadata,
                        created_at=user_db_message.created_at
                    ),
                    ai_message=MessageResponse(
                        id=ai_db_message.id,
                        conversation_id=ai_db_message.conversation_id,
                        sender=ai_db_message.sender,
                        content=ai_db_message.content,
                        metadata=ai_db_message.message_metadata,
                        created_at=ai_db_message.created_at
                    ),
                    workflow_id=workflow_id,
                    workflow_status=workflow_result.get("status", "unknown"),
                    execution_time=execution_time,
                    agent_id=workflow_result.get("result", {}).get("current_agent"),
                    metadata=ai_metadata
                )
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error sending message: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
        async def get_conversation_messages_endpoint(
            conversation_id: str,
            db: Session = Depends(get_db),
            current_user = Depends(get_current_active_user),
            limit: int = Query(50, ge=1, le=100),
            offset: int = Query(0, ge=0)
        ):
            """
            Get messages for a conversation.
            
            Args:
                conversation_id: Conversation identifier
                db: Database session
                current_user: Current authenticated user
                limit: Maximum number of messages to return
                offset: Number of messages to skip
                
            Returns:
                List of messages
            """
            try:
                # Validate conversation access
                conversation = get_conversation(db, conversation_id)
                if not conversation:
                    raise HTTPException(status_code=404, detail="Conversation not found")
                
                if conversation.user_id != current_user.id:
                    raise HTTPException(status_code=403, detail="Access denied")
                
                # Get messages
                messages = get_conversation_messages(
                    db=db,
                    conversation_id=conversation_id,
                    limit=limit,
                    offset=offset
                )
                
                message_responses = []
                for msg in messages:
                    message_responses.append(MessageResponse(
                        id=msg.id,
                        conversation_id=msg.conversation_id,
                        sender=msg.sender,
                        content=msg.content,
                        metadata=msg.message_metadata or {},
                        created_at=msg.created_at
                    ))
                
                return message_responses
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error getting messages: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _stream_workflow_response(
        self,
        workflow_id: str,
        conversation_id: str,
        user_message: Message,
        db: Session
    ):
        """
        Stream workflow execution response.
        
        Args:
            workflow_id: Workflow identifier
            conversation_id: Conversation identifier
            user_message: User message from database
            db: Database session
            
        Yields:
            Streaming response data
        """
        try:
            # This would be implemented with LangGraph's streaming capabilities
            # For now, we'll simulate streaming and then execute
            
            yield f"data: {{\"type\": \"status\", \"content\": \"Processing your request...\"}}\n\n"
            
            # Execute workflow
            workflow_result = await self.workflow_manager.execute_workflow(workflow_id)
            
            # Stream the result
            if workflow_result.get("status") == "completed":
                result_data = workflow_result.get("result", {})
                messages = result_data.get("messages", [])
                
                for message in messages:
                    if message.get("type") == "agent":
                        yield f"data: {{\"type\": \"message\", \"content\": \"{message.get('content', '')}\"}}\n\n"
            
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            yield f"data: {{\"type\": \"error\", \"content\": \"Error: {str(e)}\"}}\n\n"
    
    def get_router(self) -> APIRouter:
        """Get the FastAPI router."""
        return self.router


# Global router instance
langgraph_chat_router = LangGraphChatRouter()
