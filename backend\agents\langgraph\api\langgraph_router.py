"""
LangGraph API Router for Datagenius.

This module provides the main API router that integrates with the
LangGraph workflow manager, replacing the legacy orchestrator-based
routing system.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import asyncio

from ..core.workflow_manager import WorkflowManager
from ..core.agent_factory import agent_factory
from ..tools.mcp_integration import mcp_tool_registry
from ....app.database import get_db
from ....app.auth import get_current_user

logger = logging.getLogger(__name__)


# Request/Response Models
class ChatRequest(BaseModel):
    """Chat request model."""
    message: str = Field(..., description="User message")
    conversation_id: Optional[str] = Field(None, description="Conversation ID")
    business_profile_id: Optional[str] = Field(None, description="Business profile ID")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context")
    streaming: bool = Field(False, description="Enable streaming response")
    workflow_type: str = Field("default", description="Workflow type")


class ChatResponse(BaseModel):
    """Chat response model."""
    workflow_id: str = Field(..., description="Workflow ID")
    conversation_id: str = Field(..., description="Conversation ID")
    message: str = Field(..., description="Agent response")
    status: str = Field(..., description="Workflow status")
    agent_id: Optional[str] = Field(None, description="Responding agent ID")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Response metadata")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")


class WorkflowStatusResponse(BaseModel):
    """Workflow status response model."""
    workflow_id: str = Field(..., description="Workflow ID")
    status: str = Field(..., description="Current status")
    phase: str = Field(..., description="Current phase")
    current_agent: Optional[str] = Field(None, description="Current agent")
    progress: Dict[str, Any] = Field(default_factory=dict, description="Progress information")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="Execution metrics")


class AgentListResponse(BaseModel):
    """Agent list response model."""
    agents: List[Dict[str, Any]] = Field(..., description="Available agents")
    total: int = Field(..., description="Total number of agents")


class LangGraphRouter:
    """
    Main API router for LangGraph-based Datagenius system.
    
    This router provides all API endpoints for interacting with
    the LangGraph workflow system.
    """
    
    def __init__(self):
        """Initialize the LangGraph router."""
        self.router = APIRouter(prefix="/api/v1/langgraph", tags=["LangGraph"])
        self.workflow_manager = WorkflowManager()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self._initialize_components()
        
        # Setup routes
        self._setup_routes()
        
        self.logger.info("LangGraphRouter initialized")
    
    def _initialize_components(self) -> None:
        """Initialize LangGraph components."""
        try:
            # Create and register agent nodes
            agents = agent_factory.create_all_agents()
            for agent_id, agent_node in agents.items():
                self.workflow_manager.register_agent_node(agent_id, agent_node)
            
            # Initialize MCP tools
            asyncio.create_task(self._initialize_mcp_tools())
            
            self.logger.info("LangGraph components initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing components: {e}", exc_info=True)
    
    async def _initialize_mcp_tools(self) -> None:
        """Initialize MCP tools asynchronously."""
        try:
            await mcp_tool_registry.discover_and_register_tools()
            
            # Register tools with workflow manager
            for tool_name, tool_node in mcp_tool_registry.get_all_tools().items():
                self.workflow_manager.register_tool_node(tool_name, tool_node)
            
            self.logger.info("MCP tools initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing MCP tools: {e}")
    
    def _setup_routes(self) -> None:
        """Setup API routes."""
        
        @self.router.post("/chat", response_model=ChatResponse)
        async def chat(
            request: ChatRequest,
            current_user = Depends(get_current_user),
            db = Depends(get_db)
        ):
            """
            Process a chat message through LangGraph workflow.
            
            Args:
                request: Chat request
                current_user: Current authenticated user
                db: Database session
                
            Returns:
                Chat response
            """
            try:
                # Generate conversation ID if not provided
                conversation_id = request.conversation_id or str(uuid.uuid4())
                
                # Create workflow
                workflow_id = await self.workflow_manager.create_workflow(
                    user_id=current_user.id,
                    conversation_id=conversation_id,
                    message=request.message,
                    context={
                        "business_profile_id": request.business_profile_id,
                        "user_preferences": getattr(current_user, "preferences", {}),
                        **request.context
                    },
                    workflow_type=request.workflow_type
                )
                
                # Execute workflow
                if request.streaming:
                    # Return streaming response
                    return StreamingResponse(
                        self._stream_workflow_execution(workflow_id),
                        media_type="text/plain"
                    )
                else:
                    # Execute and return complete response
                    result = await self.workflow_manager.execute_workflow(workflow_id)
                    
                    return ChatResponse(
                        workflow_id=workflow_id,
                        conversation_id=conversation_id,
                        message=result.get("message", {}).get("content", ""),
                        status=result.get("status", "unknown"),
                        agent_id=result.get("result", {}).get("current_agent"),
                        metadata=result.get("result", {}).get("execution_metrics", {}),
                        execution_time=result.get("execution_time")
                    )
                    
            except Exception as e:
                self.logger.error(f"Error in chat endpoint: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/workflow/{workflow_id}/status", response_model=WorkflowStatusResponse)
        async def get_workflow_status(
            workflow_id: str,
            current_user = Depends(get_current_user)
        ):
            """
            Get workflow status.
            
            Args:
                workflow_id: Workflow identifier
                current_user: Current authenticated user
                
            Returns:
                Workflow status
            """
            try:
                status = await self.workflow_manager.get_workflow_status(workflow_id)
                
                if status.get("status") == "not_found":
                    raise HTTPException(status_code=404, detail="Workflow not found")
                
                return WorkflowStatusResponse(**status)
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error getting workflow status: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/agents", response_model=AgentListResponse)
        async def list_agents(
            current_user = Depends(get_current_user)
        ):
            """
            List available agents.
            
            Args:
                current_user: Current authenticated user
                
            Returns:
                List of available agents
            """
            try:
                agents = []
                
                for agent_id in agent_factory.get_available_agents():
                    agent_metadata = agent_factory.agent_metadata.get(agent_id, {})
                    
                    agents.append({
                        "id": agent_id,
                        "name": agent_id.title(),
                        "description": agent_metadata.get("description", ""),
                        "capabilities": agent_metadata.get("capabilities", []),
                        "tools": agent_metadata.get("tools", []),
                        "supported_intents": agent_metadata.get("supported_intents", [])
                    })
                
                return AgentListResponse(
                    agents=agents,
                    total=len(agents)
                )
                
            except Exception as e:
                self.logger.error(f"Error listing agents: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/metrics")
        async def get_metrics(
            current_user = Depends(get_current_user)
        ):
            """
            Get system metrics.
            
            Args:
                current_user: Current authenticated user
                
            Returns:
                System metrics
            """
            try:
                return {
                    "workflow_manager": self.workflow_manager.get_metrics(),
                    "agent_factory": agent_factory.get_factory_stats(),
                    "mcp_tools": mcp_tool_registry.get_registry_stats(),
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as e:
                self.logger.error(f"Error getting metrics: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/workflow/{workflow_id}/progress")
        async def get_workflow_progress(
            workflow_id: str,
            current_user = Depends(get_current_user)
        ):
            """
            Get detailed workflow progress information.

            Args:
                workflow_id: Workflow identifier
                current_user: Current authenticated user

            Returns:
                Detailed progress information
            """
            try:
                status = await self.workflow_manager.get_workflow_status(workflow_id)

                if status.get("status") == "not_found":
                    raise HTTPException(status_code=404, detail="Workflow not found")

                # Get detailed progress from checkpointer
                state = await self.workflow_manager.checkpointer.get_state(workflow_id)

                progress_info = {
                    "workflow_id": workflow_id,
                    "current_step": status.get("progress", {}).get("current_step", 0),
                    "total_steps": status.get("progress", {}).get("total_steps", 1),
                    "completion_percentage": 0,
                    "current_phase": status.get("phase", "unknown"),
                    "current_agent": status.get("current_agent"),
                    "step_history": [],
                    "estimated_completion": None
                }

                if state:
                    progress_info.update({
                        "step_history": state.get("step_history", []),
                        "agent_history": state.get("agent_history", []),
                        "tool_execution_history": state.get("tool_execution_history", []),
                        "performance_data": state.get("execution_metrics", {})
                    })

                    # Calculate completion percentage
                    current_step = progress_info["current_step"]
                    total_steps = progress_info["total_steps"]
                    if total_steps > 0:
                        progress_info["completion_percentage"] = (current_step / total_steps) * 100

                return progress_info

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error getting workflow progress: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.router.get("/workflow/{workflow_id}/metrics")
        async def get_workflow_metrics(
            workflow_id: str,
            current_user = Depends(get_current_user)
        ):
            """
            Get workflow performance metrics.

            Args:
                workflow_id: Workflow identifier
                current_user: Current authenticated user

            Returns:
                Performance metrics
            """
            try:
                state = await self.workflow_manager.checkpointer.get_state(workflow_id)

                if not state:
                    raise HTTPException(status_code=404, detail="Workflow not found")

                metrics = {
                    "workflow_id": workflow_id,
                    "execution_metrics": state.get("execution_metrics", {}),
                    "performance_data": state.get("performance_data", {}),
                    "timing_data": state.get("timing_data", {}),
                    "resource_usage": state.get("resource_usage", {}),
                    "agent_performance": {},
                    "tool_performance": {}
                }

                # Calculate agent performance metrics
                for agent_id in state.get("agent_history", []):
                    if agent_id in self.workflow_manager.agent_nodes:
                        agent_node = self.workflow_manager.agent_nodes[agent_id]
                        metrics["agent_performance"][agent_id] = agent_node.get_performance_metrics()

                # Calculate tool performance metrics
                for tool_name in state.get("tool_status", {}).keys():
                    if tool_name in self.workflow_manager.tool_nodes:
                        tool_node = self.workflow_manager.tool_nodes[tool_name]
                        metrics["tool_performance"][tool_name] = tool_node.get_performance_metrics()

                return metrics

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error getting workflow metrics: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.router.get("/workflows/active")
        async def list_active_workflows(
            current_user = Depends(get_current_user),
            limit: int = Query(50, ge=1, le=100),
            offset: int = Query(0, ge=0)
        ):
            """
            List active workflows for the current user.

            Args:
                current_user: Current authenticated user
                limit: Maximum number of workflows to return
                offset: Number of workflows to skip

            Returns:
                List of active workflows
            """
            try:
                # This would require extending the checkpointer to support user-based queries
                # For now, return workflow manager metrics
                manager_metrics = self.workflow_manager.get_metrics()

                return {
                    "active_workflows": [],  # Would be populated from database query
                    "total_active": 0,
                    "user_id": current_user.id,
                    "system_metrics": manager_metrics,
                    "timestamp": datetime.now().isoformat()
                }

            except Exception as e:
                self.logger.error(f"Error listing active workflows: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.router.post("/workflow/{workflow_id}/cancel")
        async def cancel_workflow(
            workflow_id: str,
            current_user = Depends(get_current_user)
        ):
            """
            Cancel a running workflow.

            Args:
                workflow_id: Workflow identifier
                current_user: Current authenticated user

            Returns:
                Cancellation status
            """
            try:
                # Update workflow status to cancelled
                state = await self.workflow_manager.checkpointer.get_state(workflow_id)

                if not state:
                    raise HTTPException(status_code=404, detail="Workflow not found")

                # Update state to cancelled
                from ..states.unified_state import update_workflow_status, WorkflowStatus
                updated_state = update_workflow_status(state, WorkflowStatus.CANCELLED)

                # Save updated state
                await self.workflow_manager.checkpointer.save_state(workflow_id, updated_state)

                return {
                    "status": "cancelled",
                    "workflow_id": workflow_id,
                    "cancelled_at": datetime.now().isoformat()
                }

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error cancelling workflow: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _stream_workflow_execution(self, workflow_id: str):
        """
        Stream workflow execution progress.
        
        Args:
            workflow_id: Workflow identifier
            
        Yields:
            Workflow execution updates
        """
        try:
            # This would be implemented with LangGraph's streaming capabilities
            # For now, we'll simulate streaming
            
            yield f"data: {{\"status\": \"started\", \"workflow_id\": \"{workflow_id}\"}}\n\n"
            
            # Execute workflow
            result = await self.workflow_manager.execute_workflow(workflow_id)
            
            # Stream final result
            yield f"data: {{\"status\": \"completed\", \"result\": \"{result.get('message', {}).get('content', '')}\"}}\n\n"
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            yield f"data: {{\"status\": \"error\", \"error\": \"{str(e)}\"}}\n\n"
    
    def get_router(self) -> APIRouter:
        """Get the FastAPI router."""
        return self.router


# Global router instance
langgraph_router = LangGraphRouter()
