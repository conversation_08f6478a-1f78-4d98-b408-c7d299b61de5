"""
Agent Discovery Service for LangGraph Network Architecture.

This module provides agent discovery capabilities that enable agents to
find and connect with other agents based on capabilities, availability,
and collaboration patterns.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from .agent_registry import NetworkAgentRegistry, NetworkAgent, network_registry
from ..states.network_state import NetworkDatageniusState

logger = logging.getLogger(__name__)


class DiscoveryStrategy(str, Enum):
    """Strategies for agent discovery."""
    CAPABILITY_BASED = "capability_based"
    PERFORMANCE_BASED = "performance_based"
    AVAILABILITY_BASED = "availability_based"
    COLLABORATION_HISTORY = "collaboration_history"
    HYBRID = "hybrid"


class MatchingCriteria(str, Enum):
    """Criteria for agent matching."""
    EXACT_MATCH = "exact_match"
    PARTIAL_MATCH = "partial_match"
    FUZZY_MATCH = "fuzzy_match"
    SEMANTIC_MATCH = "semantic_match"


@dataclass
class DiscoveryRequest:
    """Request for agent discovery."""
    requesting_agent: str
    required_capabilities: List[str]
    preferred_capabilities: Optional[List[str]] = None
    exclude_agents: Optional[List[str]] = None
    max_results: int = 5
    strategy: DiscoveryStrategy = DiscoveryStrategy.HYBRID
    criteria: MatchingCriteria = MatchingCriteria.PARTIAL_MATCH
    min_confidence: float = 0.7
    include_busy_agents: bool = False
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class DiscoveryResult:
    """Result of agent discovery."""
    agent: NetworkAgent
    match_score: float
    matching_capabilities: List[str]
    confidence_scores: Dict[str, float]
    availability_score: float
    collaboration_score: float
    recommendation_reason: str


class AgentDiscoveryService:
    """
    Service for discovering and matching agents in the network.

    This service provides:
    - Capability-based agent discovery with O(1) indexed lookups
    - Performance-aware agent matching
    - Collaboration history analysis
    - Dynamic agent recommendations
    - Network topology optimization
    - Advanced caching strategies
    """

    def __init__(self, registry: NetworkAgentRegistry = None):
        """Initialize the discovery service."""
        self.registry = registry or network_registry
        self.discovery_cache: Dict[str, Tuple[List[DiscoveryResult], datetime]] = {}
        self.cache_ttl = timedelta(minutes=5)
        self.collaboration_weights = {
            "capability_match": 0.4,
            "performance_score": 0.3,
            "availability": 0.2,
            "collaboration_history": 0.1
        }

        # Advanced indexing for O(1) lookups
        self._capability_agent_index: Dict[str, Set[str]] = {}
        self._availability_index: Dict[bool, Set[str]] = {True: set(), False: set()}
        self._performance_index: Dict[str, float] = {}  # agent_id -> performance_score
        self._last_index_update = datetime.now()
        self._index_update_interval = timedelta(minutes=1)

        # Cache management
        self._max_cache_size = 1000
        self._cache_hit_count = 0
        self._cache_miss_count = 0

        # Initialize indexes
        self._rebuild_indexes()

        logger.info("AgentDiscoveryService initialized with indexed lookups")

    def _rebuild_indexes(self):
        """Rebuild all indexes for optimized lookups."""
        try:
            # Clear existing indexes
            self._capability_agent_index.clear()
            self._availability_index[True].clear()
            self._availability_index[False].clear()
            self._performance_index.clear()

            # Rebuild indexes from current agents
            for agent in self.registry.get_all_agents():
                # Index by capabilities
                for capability in agent.capabilities:
                    if capability.name not in self._capability_agent_index:
                        self._capability_agent_index[capability.name] = set()
                    self._capability_agent_index[capability.name].add(agent.agent_id)

                # Index by availability
                is_available = agent.is_available()
                self._availability_index[is_available].add(agent.agent_id)

                # Index by performance
                performance_score = self._calculate_performance_score(agent)
                self._performance_index[agent.agent_id] = performance_score

            self._last_index_update = datetime.now()
            logger.debug(f"Rebuilt indexes for {len(self.registry.get_all_agents())} agents")

        except Exception as e:
            logger.error(f"Error rebuilding indexes: {e}")

    def _update_indexes_if_needed(self):
        """Update indexes if they're stale."""
        if datetime.now() - self._last_index_update > self._index_update_interval:
            self._rebuild_indexes()

    def _get_agents_by_capability_indexed(self, capability: str) -> Set[str]:
        """Get agent IDs that have a specific capability using O(1) lookup."""
        self._update_indexes_if_needed()
        return self._capability_agent_index.get(capability, set())

    def _get_available_agents_indexed(self) -> Set[str]:
        """Get available agent IDs using O(1) lookup."""
        self._update_indexes_if_needed()
        return self._availability_index[True]

    def _manage_cache_size(self):
        """Manage cache size to prevent memory bloat."""
        if len(self.discovery_cache) > self._max_cache_size:
            # Remove oldest entries
            sorted_cache = sorted(
                self.discovery_cache.items(),
                key=lambda x: x[1][1]  # Sort by timestamp
            )
            # Keep only the newest 80% of entries
            keep_count = int(self._max_cache_size * 0.8)
            for key, _ in sorted_cache[:-keep_count]:
                del self.discovery_cache[key]
            logger.debug(f"Cache pruned to {len(self.discovery_cache)} entries")

    async def discover_agents(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """
        Discover agents based on the given request.
        
        Args:
            request: Discovery request with criteria
            
        Returns:
            List of discovery results sorted by match score
        """
        try:
            # Check cache first
            cache_key = self._create_cache_key(request)
            if cache_key in self.discovery_cache:
                cached_results, cached_time = self.discovery_cache[cache_key]
                if datetime.now() - cached_time < self.cache_ttl:
                    self._cache_hit_count += 1
                    logger.debug(f"Returning cached discovery results for {request.requesting_agent}")
                    return cached_results[:request.max_results]
                else:
                    # Remove stale cache entry
                    del self.discovery_cache[cache_key]

            self._cache_miss_count += 1

            # Ensure indexes are up to date
            self._update_indexes_if_needed()

            # Perform discovery based on strategy
            if request.strategy == DiscoveryStrategy.CAPABILITY_BASED:
                results = await self._capability_based_discovery(request)
            elif request.strategy == DiscoveryStrategy.PERFORMANCE_BASED:
                results = await self._performance_based_discovery(request)
            elif request.strategy == DiscoveryStrategy.AVAILABILITY_BASED:
                results = await self._availability_based_discovery(request)
            elif request.strategy == DiscoveryStrategy.COLLABORATION_HISTORY:
                results = await self._collaboration_history_discovery(request)
            else:  # HYBRID
                results = await self._hybrid_discovery(request)

            # Sort by match score
            results.sort(key=lambda r: r.match_score, reverse=True)

            # Limit results
            limited_results = results[:request.max_results]

            # Cache results and manage cache size
            self.discovery_cache[cache_key] = (limited_results, datetime.now())
            self._manage_cache_size()

            logger.info(f"Discovered {len(limited_results)} agents for {request.requesting_agent} (cache size: {len(self.discovery_cache)})")
            return limited_results

        except Exception as e:
            logger.error(f"Error in agent discovery: {e}")
            return []

    async def _capability_based_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents based on capabilities using indexed lookups."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)  # Don't include requesting agent

        # Use indexed lookup to get candidate agents
        candidate_agent_ids = set()

        # Get agents that have ALL required capabilities (intersection)
        for i, capability in enumerate(request.required_capabilities):
            agents_with_cap = self._get_agents_by_capability_indexed(capability)
            if i == 0:
                candidate_agent_ids = agents_with_cap.copy()
            else:
                candidate_agent_ids &= agents_with_cap

        # If no candidates found, return empty results
        if not candidate_agent_ids:
            return results

        # Filter by availability if needed
        if not request.include_busy_agents:
            available_agents = self._get_available_agents_indexed()
            candidate_agent_ids &= available_agents

        # Remove excluded agents
        candidate_agent_ids -= exclude_agents

        # Process only the candidate agents (much smaller set)
        for agent_id in candidate_agent_ids:
            agent = self.registry.get_agent(agent_id)
            if not agent:
                continue

            # Calculate capability match
            match_score, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )

            if match_score < request.min_confidence:
                continue

            # Calculate other scores
            availability_score = self._calculate_availability_score(agent)
            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)

            # Create result
            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"Indexed capability match ({len(matching_caps)} capabilities)"
            )

            results.append(result)

        # Sort by match score (descending) and limit results
        results.sort(key=lambda r: r.match_score, reverse=True)
        return results[:request.max_results]

    async def _performance_based_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents based on performance metrics."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)

        # Use indexed lookup to get candidate agents with required capabilities
        candidate_agent_ids = set()

        # Get agents that have ALL required capabilities (intersection)
        for i, capability in enumerate(request.required_capabilities):
            agents_with_cap = self._get_agents_by_capability_indexed(capability)
            if i == 0:
                candidate_agent_ids = agents_with_cap.copy()
            else:
                candidate_agent_ids &= agents_with_cap

        # If no candidates found, return empty results
        if not candidate_agent_ids:
            return results

        # Filter by availability if needed
        if not request.include_busy_agents:
            available_agents = self._get_available_agents_indexed()
            candidate_agent_ids &= available_agents

        # Remove excluded agents
        candidate_agent_ids -= exclude_agents

        # Process only the candidate agents, sorted by performance
        performance_candidates = []
        for agent_id in candidate_agent_ids:
            agent = self.registry.get_agent(agent_id)
            if not agent:
                continue

            # Use cached performance score if available
            performance_score = self._performance_index.get(agent_id)
            if performance_score is None:
                performance_score = self._calculate_performance_score(agent)
                self._performance_index[agent_id] = performance_score

            performance_candidates.append((agent, performance_score))

        # Sort by performance score (descending) for efficient processing
        performance_candidates.sort(key=lambda x: x[1], reverse=True)

        # Process top performers first
        for agent, performance_score in performance_candidates[:request.max_results * 2]:  # Process 2x to account for filtering
            capability_match, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )

            # Weight performance more heavily
            match_score = performance_score * 0.7 + capability_match * 0.3

            availability_score = self._calculate_availability_score(agent)
            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)

            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"High performance agent (score: {performance_score:.2f})"
            )

            results.append(result)

        # Sort by match score (descending)
        results.sort(key=lambda r: r.match_score, reverse=True)
        return results[:request.max_results]

    async def _availability_based_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents based on availability."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)

        for agent in self.registry.get_all_agents():
            if agent.agent_id in exclude_agents:
                continue

            # Check capability requirements
            has_required_caps = all(
                agent.can_handle_capability(cap, request.min_confidence)
                for cap in request.required_capabilities
            )
            
            if not has_required_caps:
                continue

            availability_score = self._calculate_availability_score(agent)
            
            # Skip unavailable agents unless explicitly included
            if not request.include_busy_agents and availability_score < 0.5:
                continue

            capability_match, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )
            
            # Weight availability more heavily
            match_score = availability_score * 0.6 + capability_match * 0.4
            
            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)

            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"Highly available agent (availability: {availability_score:.2f})"
            )
            
            results.append(result)

        return results

    async def _collaboration_history_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents based on collaboration history."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)

        for agent in self.registry.get_all_agents():
            if agent.agent_id in exclude_agents:
                continue
                
            if not request.include_busy_agents and not agent.is_available():
                continue

            # Check capability requirements
            has_required_caps = all(
                agent.can_handle_capability(cap, request.min_confidence)
                for cap in request.required_capabilities
            )
            
            if not has_required_caps:
                continue

            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)
            capability_match, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )
            
            # Weight collaboration history more heavily
            match_score = collaboration_score * 0.6 + capability_match * 0.4
            
            availability_score = self._calculate_availability_score(agent)

            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"Strong collaboration history (score: {collaboration_score:.2f})"
            )
            
            results.append(result)

        return results

    async def _hybrid_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents using hybrid approach combining all factors."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)

        for agent in self.registry.get_all_agents():
            if agent.agent_id in exclude_agents:
                continue
                
            if not request.include_busy_agents and not agent.is_available():
                continue

            # Check capability requirements
            has_required_caps = all(
                agent.can_handle_capability(cap, request.min_confidence)
                for cap in request.required_capabilities
            )
            
            if not has_required_caps:
                continue

            # Calculate all scores
            capability_match, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )
            performance_score = self._calculate_performance_score(agent)
            availability_score = self._calculate_availability_score(agent)
            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)

            # Calculate weighted hybrid score
            match_score = (
                capability_match * self.collaboration_weights["capability_match"] +
                performance_score * self.collaboration_weights["performance_score"] +
                availability_score * self.collaboration_weights["availability"] +
                collaboration_score * self.collaboration_weights["collaboration_history"]
            )

            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"Balanced match across all criteria (score: {match_score:.2f})"
            )
            
            results.append(result)

        return results

    def _calculate_capability_match(
        self,
        agent: NetworkAgent,
        required_capabilities: List[str],
        preferred_capabilities: List[str]
    ) -> Tuple[float, List[str], Dict[str, float]]:
        """Calculate capability match score."""
        matching_capabilities = []
        confidence_scores = {}
        total_score = 0.0
        max_possible_score = 0.0

        # Required capabilities (weighted more heavily)
        for cap in required_capabilities:
            max_possible_score += 2.0  # Weight required capabilities as 2x
            confidence = agent.get_capability_confidence(cap)
            if confidence > 0:
                matching_capabilities.append(cap)
                confidence_scores[cap] = confidence
                total_score += confidence * 2.0

        # Preferred capabilities
        for cap in preferred_capabilities:
            max_possible_score += 1.0
            confidence = agent.get_capability_confidence(cap)
            if confidence > 0:
                if cap not in matching_capabilities:
                    matching_capabilities.append(cap)
                confidence_scores[cap] = confidence
                total_score += confidence

        # Normalize score
        match_score = total_score / max_possible_score if max_possible_score > 0 else 0.0
        
        return match_score, matching_capabilities, confidence_scores

    def _calculate_performance_score(self, agent: NetworkAgent) -> float:
        """Calculate performance score based on metrics."""
        if not agent.performance_metrics:
            return 0.5  # Default score for agents without metrics
        
        # Average all performance metrics
        total_score = sum(agent.performance_metrics.values())
        return total_score / len(agent.performance_metrics)

    def _calculate_availability_score(self, agent: NetworkAgent) -> float:
        """Calculate availability score."""
        if not agent.is_available():
            return 0.0
        
        # Calculate based on current load
        load_ratio = agent.current_load / agent.max_concurrent_tasks
        availability_score = 1.0 - load_ratio
        
        # Bonus for idle agents
        if agent.current_load == 0:
            availability_score += 0.1
        
        return min(1.0, availability_score)

    def _calculate_collaboration_score(self, agent: NetworkAgent, requesting_agent: str) -> float:
        """Calculate collaboration score based on history."""
        # Get collaboration patterns from registry
        patterns = self.registry.collaboration_patterns.get(requesting_agent, {})
        collaboration_count = patterns.get(agent.agent_id, 0)
        
        # Normalize based on total collaborations
        total_collaborations = sum(patterns.values()) if patterns else 1
        collaboration_score = collaboration_count / total_collaborations
        
        return min(1.0, collaboration_score)

    def _create_cache_key(self, request: DiscoveryRequest) -> str:
        """Create cache key for discovery request."""
        key_data = {
            "requesting_agent": request.requesting_agent,
            "required_capabilities": sorted(request.required_capabilities),
            "preferred_capabilities": sorted(request.preferred_capabilities or []),
            "exclude_agents": sorted(request.exclude_agents or []),
            "strategy": request.strategy.value,
            "criteria": request.criteria.value,
            "min_confidence": request.min_confidence,
            "include_busy_agents": request.include_busy_agents
        }
        return json.dumps(key_data, sort_keys=True)

    async def find_specialist_for_capability(
        self,
        capability: str,
        requesting_agent: str,
        min_confidence: float = 0.8
    ) -> Optional[NetworkAgent]:
        """Find the best specialist for a specific capability."""
        request = DiscoveryRequest(
            requesting_agent=requesting_agent,
            required_capabilities=[capability],
            max_results=1,
            strategy=DiscoveryStrategy.PERFORMANCE_BASED,
            min_confidence=min_confidence
        )
        
        results = await self.discover_agents(request)
        return results[0].agent if results else None

    async def find_collaboration_partners(
        self,
        requesting_agent: str,
        task_capabilities: List[str],
        team_size: int = 3
    ) -> List[NetworkAgent]:
        """Find agents for collaborative work."""
        request = DiscoveryRequest(
            requesting_agent=requesting_agent,
            required_capabilities=task_capabilities,
            max_results=team_size,
            strategy=DiscoveryStrategy.HYBRID,
            include_busy_agents=False
        )
        
        results = await self.discover_agents(request)
        return [result.agent for result in results]

    def clear_cache(self):
        """Clear the discovery cache."""
        self.discovery_cache.clear()
        self._cache_hit_count = 0
        self._cache_miss_count = 0
        logger.info("Discovery cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        total_requests = self._cache_hit_count + self._cache_miss_count
        hit_rate = (self._cache_hit_count / total_requests * 100) if total_requests > 0 else 0

        return {
            "cache_size": len(self.discovery_cache),
            "max_cache_size": self._max_cache_size,
            "cache_hits": self._cache_hit_count,
            "cache_misses": self._cache_miss_count,
            "hit_rate_percent": round(hit_rate, 2),
            "index_last_updated": self._last_index_update.isoformat(),
            "capability_index_size": len(self._capability_agent_index),
            "performance_index_size": len(self._performance_index)
        }

    def force_index_rebuild(self):
        """Force a rebuild of all indexes."""
        self._rebuild_indexes()
        logger.info("Forced index rebuild completed")


# Global discovery service instance
discovery_service = AgentDiscoveryService()
