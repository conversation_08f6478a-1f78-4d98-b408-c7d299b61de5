"""
Visualization Integration Service.

This service integrates the workflow visualization system with the existing
LangGraph event-driven architecture and provides a unified interface for
visualization management.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..events.event_bus import event_bus
from ..events.handlers.visualization_handler import VisualizationEventHandler
from ..visualization.workflow_visualizer import WorkflowVisualizer
from ..monitoring.metrics import WorkflowMetrics

logger = logging.getLogger(__name__)


class VisualizationIntegrationService:
    """
    Service for integrating visualization with the LangGraph event system.
    
    This service provides:
    - Automatic visualization generation from workflow events
    - Real-time dashboard updates
    - Performance monitoring integration
    - Visualization caching and management
    """

    def __init__(self, metrics_system: Optional[WorkflowMetrics] = None):
        """
        Initialize the visualization integration service.
        
        Args:
            metrics_system: Optional metrics system for performance tracking
        """
        self.visualizer = WorkflowVisualizer()
        self.metrics_system = metrics_system or WorkflowMetrics()
        self.visualization_handler = VisualizationEventHandler(self.metrics_system)
        self.logger = logging.getLogger(__name__)
        
        # Service state
        self.is_running = False
        self.startup_time = None
        
        # Performance tracking
        self.service_metrics = {
            "visualizations_generated": 0,
            "dashboard_updates_sent": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0
        }

    async def start(self):
        """Start the visualization integration service."""
        try:
            if self.is_running:
                self.logger.warning("Visualization service is already running")
                return

            # Subscribe to relevant events
            await self._subscribe_to_events()
            
            # Start periodic cache cleanup
            asyncio.create_task(self._periodic_cache_cleanup())
            
            self.is_running = True
            self.startup_time = datetime.now()
            
            self.logger.info("Visualization integration service started successfully")

        except Exception as e:
            self.logger.error(f"Error starting visualization service: {e}")
            raise

    async def stop(self):
        """Stop the visualization integration service."""
        try:
            if not self.is_running:
                self.logger.warning("Visualization service is not running")
                return

            # Unsubscribe from events
            await self._unsubscribe_from_events()
            
            self.is_running = False
            
            self.logger.info("Visualization integration service stopped")

        except Exception as e:
            self.logger.error(f"Error stopping visualization service: {e}")

    async def _subscribe_to_events(self):
        """Subscribe to relevant events in the event bus."""
        try:
            # Subscribe to workflow completion events
            event_bus.subscribe(
                "workflow.completed", 
                self.visualization_handler.handle_workflow_completed
            )
            
            # Subscribe to dashboard update requests
            event_bus.subscribe(
                "dashboard.update_required",
                self.visualization_handler.handle_dashboard_update_request
            )
            
            # Subscribe to agent registration events
            event_bus.subscribe(
                "agent.registered",
                self.visualization_handler.handle_agent_registration
            )
            
            self.logger.debug("Subscribed to visualization events")

        except Exception as e:
            self.logger.error(f"Error subscribing to events: {e}")
            raise

    async def _unsubscribe_from_events(self):
        """Unsubscribe from events in the event bus."""
        try:
            # Unsubscribe from all event types
            event_bus.unsubscribe(
                "workflow.completed",
                self.visualization_handler.handle_workflow_completed
            )
            event_bus.unsubscribe(
                "dashboard.update_required",
                self.visualization_handler.handle_dashboard_update_request
            )
            event_bus.unsubscribe(
                "agent.registered",
                self.visualization_handler.handle_agent_registration
            )
            
            self.logger.debug("Unsubscribed from visualization events")

        except Exception as e:
            self.logger.error(f"Error unsubscribing from events: {e}")

    async def generate_workflow_visualization(
        self, 
        workflow_id: str,
        workflow_definition: Dict[str, Any],
        execution_history: Optional[List[Dict[str, Any]]] = None,
        force_regenerate: bool = False
    ) -> Dict[str, Any]:
        """
        Generate comprehensive visualization for a workflow.
        
        Args:
            workflow_id: Unique workflow identifier
            workflow_definition: Workflow structure definition
            execution_history: Optional execution history
            force_regenerate: Force regeneration even if cached
            
        Returns:
            Dictionary containing generated visualizations
        """
        try:
            # Check cache first
            if not force_regenerate:
                cached_viz = self.visualization_handler.get_cached_visualization(workflow_id)
                if cached_viz:
                    self.service_metrics["cache_hits"] += 1
                    return cached_viz

            self.service_metrics["cache_misses"] += 1

            # Generate interactive workflow explorer
            explorer_data = self.visualizer.create_interactive_workflow_explorer(
                workflow_definition,
                execution_history
            )

            # Cache the results
            self.visualization_handler.visualization_cache[workflow_id] = {
                **explorer_data,
                "generated_at": datetime.now().isoformat(),
                "workflow_definition": workflow_definition,
                "execution_history": execution_history
            }

            self.service_metrics["visualizations_generated"] += 1
            
            self.logger.info(f"Generated visualization for workflow {workflow_id}")
            return explorer_data

        except Exception as e:
            self.service_metrics["errors"] += 1
            self.logger.error(f"Error generating workflow visualization: {e}")
            return {"error": str(e)}

    async def get_real_time_dashboard_data(self, dashboard_id: str) -> Dict[str, Any]:
        """
        Get real-time dashboard data including visualizations and metrics.
        
        Args:
            dashboard_id: Dashboard identifier
            
        Returns:
            Dictionary with dashboard data
        """
        try:
            # Get real-time metrics
            real_time_metrics = await self.metrics_system.get_real_time_metrics()
            
            # Get recent visualizations
            recent_visualizations = self._get_recent_visualizations(limit=10)
            
            # Get system performance data
            performance_data = await self._get_performance_visualization_data()
            
            dashboard_data = {
                "dashboard_id": dashboard_id,
                "timestamp": datetime.now().isoformat(),
                "real_time_metrics": real_time_metrics,
                "recent_visualizations": recent_visualizations,
                "performance_data": performance_data,
                "service_status": {
                    "is_running": self.is_running,
                    "startup_time": self.startup_time.isoformat() if self.startup_time else None,
                    "metrics": self.service_metrics
                }
            }

            return dashboard_data

        except Exception as e:
            self.service_metrics["errors"] += 1
            self.logger.error(f"Error getting dashboard data: {e}")
            return {"error": str(e)}

    def _get_recent_visualizations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recently generated visualizations."""
        try:
            cache = self.visualization_handler.visualization_cache
            
            # Sort by generation time
            sorted_items = sorted(
                cache.items(),
                key=lambda x: x[1].get("generated_at", ""),
                reverse=True
            )
            
            recent_viz = []
            for workflow_id, data in sorted_items[:limit]:
                recent_viz.append({
                    "workflow_id": workflow_id,
                    "generated_at": data.get("generated_at"),
                    "has_timeline": bool(data.get("timeline")),
                    "has_sequence": bool(data.get("sequence")),
                    "has_structure": bool(data.get("structure")),
                    "execution_time": data.get("execution_time"),
                    "success": data.get("success")
                })
            
            return recent_viz

        except Exception as e:
            self.logger.error(f"Error getting recent visualizations: {e}")
            return []

    async def _get_performance_visualization_data(self) -> Dict[str, Any]:
        """Get data for performance visualizations."""
        try:
            from datetime import timedelta
            
            # Get last 24 hours of data
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            
            # Get time series metrics
            time_series = await self.metrics_system.get_time_series_metrics(
                start_time, end_time, interval="hour"
            )
            
            # Get agent performance comparison
            agent_comparison = await self.metrics_system.get_agent_performance_comparison(
                start_time, end_time
            )
            
            return {
                "time_series": time_series,
                "agent_comparison": agent_comparison,
                "period": {
                    "start": start_time.isoformat(),
                    "end": end_time.isoformat()
                }
            }

        except Exception as e:
            self.logger.error(f"Error getting performance visualization data: {e}")
            return {}

    async def _periodic_cache_cleanup(self):
        """Periodically clean up old visualizations from cache."""
        while self.is_running:
            try:
                # Clean cache every hour
                await asyncio.sleep(3600)
                
                if self.is_running:
                    self.visualization_handler.clear_cache(max_age_hours=24)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in periodic cache cleanup: {e}")

    async def create_performance_dashboard(
        self, 
        start_date: datetime, 
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        Create a comprehensive performance dashboard.
        
        Args:
            start_date: Start of analysis period
            end_date: End of analysis period
            
        Returns:
            Dictionary with performance dashboard data
        """
        try:
            # Get metrics summary
            metrics_summary = await self.metrics_system.get_metrics_summary(
                start_date, end_date
            )
            
            # Get efficiency metrics
            efficiency_metrics = await self.metrics_system.get_workflow_efficiency_metrics(
                start_date, end_date
            )
            
            # Get error analysis
            error_analysis = await self.metrics_system.get_error_analysis(
                start_date, end_date
            )
            
            # Generate performance visualizations
            performance_viz = {
                "metrics_summary": metrics_summary.__dict__ if hasattr(metrics_summary, '__dict__') else metrics_summary,
                "efficiency_analysis": efficiency_metrics,
                "error_analysis": error_analysis,
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "generated_at": datetime.now().isoformat()
            }

            return performance_viz

        except Exception as e:
            self.service_metrics["errors"] += 1
            self.logger.error(f"Error creating performance dashboard: {e}")
            return {"error": str(e)}

    def get_service_status(self) -> Dict[str, Any]:
        """Get the current status of the visualization service."""
        return {
            "is_running": self.is_running,
            "startup_time": self.startup_time.isoformat() if self.startup_time else None,
            "metrics": self.service_metrics,
            "handler_metrics": self.visualization_handler.get_handler_metrics(),
            "cached_visualizations": len(self.visualization_handler.visualization_cache),
            "event_bus_status": event_bus.running if hasattr(event_bus, 'running') else "unknown"
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check of the visualization service."""
        try:
            health_status = {
                "service_running": self.is_running,
                "event_bus_connected": True,  # Would check actual connection
                "metrics_system_available": self.metrics_system is not None,
                "cache_size": len(self.visualization_handler.visualization_cache),
                "error_rate": self.service_metrics["errors"] / max(
                    self.service_metrics["visualizations_generated"], 1
                ),
                "last_check": datetime.now().isoformat()
            }
            
            # Determine overall health
            if (health_status["service_running"] and 
                health_status["event_bus_connected"] and 
                health_status["error_rate"] < 0.1):
                health_status["status"] = "healthy"
            elif health_status["service_running"]:
                health_status["status"] = "degraded"
            else:
                health_status["status"] = "unhealthy"
            
            return health_status

        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }


# Global service instance
visualization_service = VisualizationIntegrationService()
