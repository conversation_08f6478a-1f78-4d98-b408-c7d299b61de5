"""
Dashboard Management API endpoints.
Provides comprehensive CRUD operations for dashboard management with proper authentication and validation.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime
from app.database import User, get_db
from app.auth import get_current_active_user
from app.dependencies import get_db_service
from app.repositories.database_service import DatabaseService
from sqlalchemy.orm import Session
from app.services.dashboard_service import DatageniusDashboardService
from app.models.dashboard_customization import (
    Dashboard, DashboardCreate, DashboardUpdate, DashboardResponse,
    DashboardLayoutResponse, DashboardError, DashboardDataSource,
    DashboardDataSourceCreate, DashboardDataSourceUpdate, DashboardDataSourceResponse,
    DashboardDataSourceAssignmentCreate, DashboardDataSourceAssignmentResponse, SectionCreate
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboards", tags=["dashboards"])


def get_dashboard_service(db: Session = Depends(get_db)) -> DatageniusDashboardService:
    """Dependency to get dashboard service."""
    return DatageniusDashboardService(db)


@router.get("/", response_model=List[DashboardResponse])
async def get_user_dashboards(
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Get all dashboards for the current user."""
    try:
        dashboards = await service.get_user_dashboards(current_user.id)
        
        return [
            DashboardResponse(
                id=dashboard.id,
                name=dashboard.name,
                description=dashboard.description,
                is_default=dashboard.is_default,
                is_public=dashboard.is_public,
                layout_config=dashboard.layout_config,
                theme_config=dashboard.theme_config,
                refresh_interval=dashboard.refresh_interval,
                created_at=dashboard.created_at.isoformat(),
                updated_at=dashboard.updated_at.isoformat(),
                section_count=0,  # Will be populated by service if needed
                widget_count=0    # Will be populated by service if needed
            )
            for dashboard in dashboards
        ]
    except Exception as e:
        logger.error(f"Error getting user dashboards: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboards"
        )


@router.post("/", response_model=DashboardResponse)
async def create_dashboard(
    dashboard_data: DashboardCreate,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Create a new dashboard."""
    try:
        dashboard = await service.create_dashboard(current_user.id, dashboard_data)
        
        return DashboardResponse(
            id=dashboard.id,
            name=dashboard.name,
            description=dashboard.description,
            is_default=dashboard.is_default,
            is_public=dashboard.is_public,
            layout_config=dashboard.layout_config,
            theme_config=dashboard.theme_config,
            refresh_interval=dashboard.refresh_interval,
            created_at=dashboard.created_at.isoformat(),
            updated_at=dashboard.updated_at.isoformat(),
            section_count=0,
            widget_count=0
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create dashboard"
        )


@router.get("/{dashboard_id}", response_model=DashboardResponse)
async def get_dashboard(
    dashboard_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Get a specific dashboard."""
    try:
        dashboard = await service.get_dashboard(dashboard_id, current_user.id)
        
        if not dashboard:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Dashboard not found"
            )
        
        return DashboardResponse(
            id=dashboard.id,
            name=dashboard.name,
            description=dashboard.description,
            is_default=dashboard.is_default,
            is_public=dashboard.is_public,
            layout_config=dashboard.layout_config,
            theme_config=dashboard.theme_config,
            refresh_interval=dashboard.refresh_interval,
            created_at=dashboard.created_at.isoformat(),
            updated_at=dashboard.updated_at.isoformat(),
            section_count=0,
            widget_count=0
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard"
        )


@router.put("/{dashboard_id}", response_model=DashboardResponse)
async def update_dashboard(
    dashboard_id: str,
    dashboard_data: DashboardUpdate,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Update an existing dashboard."""
    try:
        dashboard = await service.update_dashboard(dashboard_id, current_user.id, dashboard_data)
        
        return DashboardResponse(
            id=dashboard.id,
            name=dashboard.name,
            description=dashboard.description,
            is_default=dashboard.is_default,
            is_public=dashboard.is_public,
            layout_config=dashboard.layout_config,
            theme_config=dashboard.theme_config,
            refresh_interval=dashboard.refresh_interval,
            created_at=dashboard.created_at.isoformat(),
            updated_at=dashboard.updated_at.isoformat(),
            section_count=0,
            widget_count=0
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update dashboard"
        )


@router.delete("/{dashboard_id}")
async def delete_dashboard(
    dashboard_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Delete a dashboard."""
    try:
        await service.delete_dashboard(dashboard_id, current_user.id)
        return {"message": "Dashboard deleted successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error deleting dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete dashboard"
        )


@router.get("/{dashboard_id}/layout", response_model=DashboardLayoutResponse)
async def get_dashboard_layout(
    dashboard_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Get complete dashboard layout including sections and widgets."""
    try:
        layout = await service.get_dashboard_layout(dashboard_id, current_user.id)
        return DashboardLayoutResponse(**layout)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting dashboard layout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard layout"
        )


@router.post("/{dashboard_id}/duplicate", response_model=DashboardResponse)
async def duplicate_dashboard(
    dashboard_id: str,
    name: Optional[str] = Query(None, description="Name for the duplicated dashboard"),
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Duplicate an existing dashboard with all its sections and widgets."""
    try:
        # Get original dashboard
        original_dashboard = await service.get_dashboard(dashboard_id, current_user.id)
        if not original_dashboard:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Dashboard not found"
            )
        
        # Create new dashboard
        new_name = name or f"{original_dashboard.name} (Copy)"
        dashboard_data = DashboardCreate(
            name=new_name,
            description=original_dashboard.description,
            is_default=False,  # Duplicated dashboard should not be default
            is_public=original_dashboard.is_public,
            layout_config=original_dashboard.layout_config,
            theme_config=original_dashboard.theme_config,
            refresh_interval=original_dashboard.refresh_interval
        )
        
        new_dashboard = await service.create_dashboard(current_user.id, dashboard_data)
        
        # TODO: Duplicate sections and widgets (this would require additional service methods)
        
        return DashboardResponse(
            id=new_dashboard.id,
            name=new_dashboard.name,
            description=new_dashboard.description,
            is_default=new_dashboard.is_default,
            is_public=new_dashboard.is_public,
            layout_config=new_dashboard.layout_config,
            theme_config=new_dashboard.theme_config,
            refresh_interval=new_dashboard.refresh_interval,
            created_at=new_dashboard.created_at.isoformat(),
            updated_at=new_dashboard.updated_at.isoformat(),
            section_count=0,
            widget_count=0
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error duplicating dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to duplicate dashboard"
        )


@router.post("/{dashboard_id}/set-default")
async def set_default_dashboard(
    dashboard_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Set a dashboard as the default dashboard for the user."""
    try:
        dashboard_data = DashboardUpdate(is_default=True)
        await service.update_dashboard(dashboard_id, current_user.id, dashboard_data)
        return {"message": "Dashboard set as default successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error setting default dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set default dashboard"
        )


@router.get("/default", response_model=DashboardResponse)
async def get_default_dashboard(
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Get the user's default dashboard."""
    try:
        dashboards = await service.get_user_dashboards(current_user.id)

        # Find default dashboard or use first one
        default_dashboard = next((d for d in dashboards if d.is_default), None)
        if not default_dashboard and dashboards:
            default_dashboard = dashboards[0]

        if not default_dashboard:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No dashboards found for user"
            )

        return DashboardResponse(
            id=default_dashboard.id,
            name=default_dashboard.name,
            description=default_dashboard.description,
            is_default=default_dashboard.is_default,
            is_public=default_dashboard.is_public,
            layout_config=default_dashboard.layout_config,
            theme_config=default_dashboard.theme_config,
            refresh_interval=default_dashboard.refresh_interval,
            created_at=default_dashboard.created_at.isoformat(),
            updated_at=default_dashboard.updated_at.isoformat(),
            section_count=0,
            widget_count=0
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting default dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve default dashboard"
        )


@router.post("/initialize-default", response_model=DashboardResponse)
async def initialize_default_dashboard(
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Initialize a default dashboard for users who don't have any dashboards."""
    try:
        # Check if user already has dashboards
        existing_dashboards = await service.get_user_dashboards(current_user.id)
        if existing_dashboards:
            # Return the default one or first one
            default_dashboard = next((d for d in existing_dashboards if d.is_default), existing_dashboards[0])
            return DashboardResponse(
                id=default_dashboard.id,
                name=default_dashboard.name,
                description=default_dashboard.description,
                is_default=default_dashboard.is_default,
                is_public=default_dashboard.is_public,
                layout_config=default_dashboard.layout_config,
                theme_config=default_dashboard.theme_config,
                refresh_interval=default_dashboard.refresh_interval,
                created_at=default_dashboard.created_at.isoformat(),
                updated_at=default_dashboard.updated_at.isoformat(),
                section_count=0,
                widget_count=0
            )

        # Create default dashboard
        dashboard_data = DashboardCreate(
            name="My Dashboard",
            description="Default dashboard for data visualization and analytics",
            is_default=True,
            is_public=False,
            layout_config={
                "grid_size": 12,
                "row_height": 100,
                "margin": [10, 10],
                "container_padding": [10, 10]
            },
            theme_config={
                "primary_color": "#3B82F6",
                "secondary_color": "#10B981",
                "background_color": "#F9FAFB",
                "text_color": "#1F2937"
            },
            refresh_interval=300
        )

        dashboard = await service.create_dashboard(current_user.id, dashboard_data)

        # Create a default section to provide immediate usability
        try:
            logger.info(f"Creating default section for dashboard {dashboard.id}")
            default_section = SectionCreate(
                dashboard_id=dashboard.id,
                name="Overview",
                description="Main dashboard section for key metrics and visualizations",
                color="#3B82F6",
                icon="BarChart3",
                layout_config={
                    "columns": 12,
                    "rows": 6,
                    "grid_gap": 16,
                    "responsive": True
                }
            )
            section = await service.create_section(current_user.id, default_section)
            logger.info(f"Successfully created default section {section.id} for dashboard {dashboard.id}")
            section_count = 1
        except Exception as section_error:
            logger.error(f"Failed to create default section for dashboard {dashboard.id}: {section_error}", exc_info=True)
            section_count = 0

        return DashboardResponse(
            id=dashboard.id,
            name=dashboard.name,
            description=dashboard.description,
            is_default=dashboard.is_default,
            is_public=dashboard.is_public,
            layout_config=dashboard.layout_config,
            theme_config=dashboard.theme_config,
            refresh_interval=dashboard.refresh_interval,
            created_at=dashboard.created_at.isoformat(),
            updated_at=dashboard.updated_at.isoformat(),
            section_count=section_count,
            widget_count=0
        )
    except Exception as e:
        logger.error(f"Error initializing default dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initialize default dashboard"
        )


# Dashboard Data Source Management Endpoints

@router.get("/{dashboard_id}/data-sources", response_model=List[DashboardDataSourceAssignmentResponse])
async def get_dashboard_data_sources(
    dashboard_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Get all data source assignments for a dashboard."""
    try:
        data_source_assignments = await service.get_dashboard_data_sources(dashboard_id, current_user.id)
        return data_source_assignments
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting dashboard data source assignments: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard data source assignments"
        )


@router.post("/{dashboard_id}/data-sources", response_model=DashboardDataSourceAssignmentResponse)
async def add_dashboard_data_source(
    dashboard_id: str,
    assignment_data: DashboardDataSourceAssignmentCreate,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Add a data source assignment to a dashboard."""
    try:
        # Create a data source assignment by referencing an existing system data source
        assignment = await service.assign_system_data_source_to_dashboard(
            dashboard_id, current_user.id, assignment_data
        )
        return assignment
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error adding dashboard data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add dashboard data source"
        )


@router.put("/{dashboard_id}/data-sources/{data_source_id}", response_model=DashboardDataSourceResponse)
async def update_dashboard_data_source(
    dashboard_id: str,
    data_source_id: str,
    data_source_data: DashboardDataSourceUpdate,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Update a dashboard data source."""
    try:
        data_source = await service.update_dashboard_data_source(
            dashboard_id, data_source_id, current_user.id, data_source_data
        )
        return data_source
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating dashboard data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update dashboard data source"
        )


@router.delete("/{dashboard_id}/data-sources/{data_source_id}")
async def remove_dashboard_data_source(
    dashboard_id: str,
    data_source_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Remove a data source from a dashboard."""
    try:
        await service.remove_dashboard_data_source(dashboard_id, data_source_id, current_user.id)
        return {"message": "Data source removed from dashboard successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error removing dashboard data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove dashboard data source"
        )


@router.post("/{dashboard_id}/data-sources/bulk-assign")
async def bulk_assign_data_sources(
    dashboard_id: str,
    data_source_ids: List[str],
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_service)
):
    """Bulk assign multiple data sources to a dashboard."""
    try:
        result = await service.bulk_assign_data_sources(dashboard_id, data_source_ids, current_user.id)
        return {
            "message": f"Successfully assigned {result['assigned']} data sources",
            "assigned": result["assigned"],
            "failed": result["failed"],
            "errors": result["errors"]
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error bulk assigning data sources: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bulk assign data sources"
        )
