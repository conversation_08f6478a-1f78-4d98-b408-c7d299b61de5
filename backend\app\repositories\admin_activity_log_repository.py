"""
Admin Activity Log Repository Implementation.

Provides specialized repository operations for AdminActivityLog entities,
replacing the admin activity log-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from sqlalchemy.orm import Session
from sqlalchemy import and_, extract

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import AdminActivityLog
from ..models.schemas import AdminActivityLogCreate, AdminActivityLogUpdate, AdminActivityLogResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class AdminActivityLogRepository(BaseRepository[AdminActivityLog]):
    """Repository for AdminActivityLog entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, AdminActivityLog)
        self.logger = logger
    
    def log_admin_activity(
        self,
        admin_user_id: int,
        action: str,
        target_type: Optional[str] = None,
        target_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AdminActivityLog:
        """
        Log an admin activity.
        
        Args:
            admin_user_id: ID of the admin user performing the action
            action: Action performed (e.g., 'create_user', 'delete_persona')
            target_type: Type of target entity (e.g., 'user', 'persona')
            target_id: ID of the target entity
            details: Additional details about the action
            ip_address: IP address of the admin
            user_agent: User agent string
            
        Returns:
            Created admin activity log instance
            
        Raises:
            RepositoryError: If logging fails
        """
        try:
            log_data = {
                'id': str(uuid.uuid4()),
                'admin_user_id': admin_user_id,
                'action': action,
                'target_type': target_type,
                'target_id': target_id,
                'details': details or {},
                'ip_address': ip_address,
                'user_agent': user_agent
            }
            
            activity_log = AdminActivityLog(**log_data)
            
            self.session.add(activity_log)
            self.session.commit()
            self.session.refresh(activity_log)
            
            self.logger.info(f"Logged admin activity {activity_log.id}: {action} by admin {admin_user_id}")
            return activity_log
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to log admin activity: {e}")
            raise RepositoryError(
                f"Failed to log admin activity: {str(e)}",
                entity_type="AdminActivityLog",
                operation="log_admin_activity"
            )
    
    def get_admin_activities(
        self,
        skip: int = 0,
        limit: int = 100,
        admin_user_id: Optional[int] = None,
        action: Optional[str] = None,
        target_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[AdminActivityLog]:
        """
        Get admin activities with filtering and pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            admin_user_id: Optional admin user ID filter
            action: Optional action filter
            target_type: Optional target type filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of admin activity logs matching the criteria
        """
        try:
            query = self.session.query(AdminActivityLog)
            
            # Apply filters
            if admin_user_id:
                query = query.filter(AdminActivityLog.admin_user_id == admin_user_id)
            if action:
                query = query.filter(AdminActivityLog.action == action)
            if target_type:
                query = query.filter(AdminActivityLog.target_type == target_type)
            if start_date:
                query = query.filter(AdminActivityLog.created_at >= start_date)
            if end_date:
                query = query.filter(AdminActivityLog.created_at <= end_date)
            
            return query.order_by(AdminActivityLog.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get admin activities: {e}")
            raise RepositoryError(
                f"Failed to get admin activities: {str(e)}",
                entity_type="AdminActivityLog",
                operation="get_admin_activities"
            )
    
    def get_admin_activities_by_user(
        self,
        admin_user_id: int,
        skip: int = 0,
        limit: int = 100,
        action: Optional[str] = None
    ) -> List[AdminActivityLog]:
        """
        Get activities for a specific admin user.
        
        Args:
            admin_user_id: ID of the admin user
            skip: Number of records to skip
            limit: Maximum number of records to return
            action: Optional action filter
            
        Returns:
            List of activities for the admin user
        """
        try:
            query = self.session.query(AdminActivityLog).filter(
                AdminActivityLog.admin_user_id == admin_user_id
            )
            
            if action:
                query = query.filter(AdminActivityLog.action == action)
            
            return query.order_by(AdminActivityLog.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get admin activities by user: {e}")
            raise RepositoryError(
                f"Failed to get admin activities by user: {str(e)}",
                entity_type="AdminActivityLog",
                operation="get_admin_activities_by_user"
            )
    
    def get_activities_by_target(
        self,
        target_type: str,
        target_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[AdminActivityLog]:
        """
        Get activities for a specific target entity.
        
        Args:
            target_type: Type of the target entity
            target_id: ID of the target entity
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of activities for the target entity
        """
        try:
            return self.session.query(AdminActivityLog).filter(
                AdminActivityLog.target_type == target_type,
                AdminActivityLog.target_id == target_id
            ).order_by(AdminActivityLog.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get activities by target: {e}")
            raise RepositoryError(
                f"Failed to get activities by target: {str(e)}",
                entity_type="AdminActivityLog",
                operation="get_activities_by_target"
            )
    
    def get_activity_count_by_admin(self, admin_user_id: int) -> int:
        """
        Get the count of activities for an admin user.
        
        Args:
            admin_user_id: ID of the admin user
            
        Returns:
            Number of activities for the admin user
        """
        try:
            return self.session.query(AdminActivityLog).filter(
                AdminActivityLog.admin_user_id == admin_user_id
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get activity count by admin: {e}")
            raise RepositoryError(
                f"Failed to get activity count by admin: {str(e)}",
                entity_type="AdminActivityLog",
                operation="get_activity_count_by_admin"
            )
    
    def get_activity_count_by_action(self, action: str) -> int:
        """
        Get the count of activities by action type.
        
        Args:
            action: Action type to count
            
        Returns:
            Number of activities with the specified action
        """
        try:
            return self.session.query(AdminActivityLog).filter(
                AdminActivityLog.action == action
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get activity count by action: {e}")
            raise RepositoryError(
                f"Failed to get activity count by action: {str(e)}",
                entity_type="AdminActivityLog",
                operation="get_activity_count_by_action"
            )
    
    def get_daily_activity_count(
        self,
        target_date: date,
        admin_user_id: Optional[int] = None
    ) -> int:
        """
        Get the count of activities for a specific date.
        
        Args:
            target_date: Date to count activities for
            admin_user_id: Optional admin user ID filter
            
        Returns:
            Number of activities on the specified date
        """
        try:
            query = self.session.query(AdminActivityLog).filter(
                extract('date', AdminActivityLog.created_at) == target_date
            )
            
            if admin_user_id:
                query = query.filter(AdminActivityLog.admin_user_id == admin_user_id)
            
            return query.count()
            
        except Exception as e:
            self.logger.error(f"Failed to get daily activity count: {e}")
            raise RepositoryError(
                f"Failed to get daily activity count: {str(e)}",
                entity_type="AdminActivityLog",
                operation="get_daily_activity_count"
            )
    
    def get_recent_activities(
        self,
        limit: int = 50,
        admin_user_id: Optional[int] = None
    ) -> List[AdminActivityLog]:
        """
        Get the most recent admin activities.
        
        Args:
            limit: Maximum number of activities to return
            admin_user_id: Optional admin user ID filter
            
        Returns:
            List of recent admin activities
        """
        try:
            query = self.session.query(AdminActivityLog)
            
            if admin_user_id:
                query = query.filter(AdminActivityLog.admin_user_id == admin_user_id)
            
            return query.order_by(AdminActivityLog.created_at.desc()).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get recent activities: {e}")
            raise RepositoryError(
                f"Failed to get recent activities: {str(e)}",
                entity_type="AdminActivityLog",
                operation="get_recent_activities"
            )
    
    def search_activities(
        self,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        admin_user_id: Optional[int] = None
    ) -> List[AdminActivityLog]:
        """
        Search admin activities by action or details.
        
        Args:
            search_term: Term to search for
            skip: Number of records to skip
            limit: Maximum number of records to return
            admin_user_id: Optional admin user ID filter
            
        Returns:
            List of matching admin activities
        """
        try:
            query = self.session.query(AdminActivityLog).filter(
                AdminActivityLog.action.ilike(f"%{search_term}%")
            )
            
            if admin_user_id:
                query = query.filter(AdminActivityLog.admin_user_id == admin_user_id)
            
            return query.order_by(AdminActivityLog.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to search activities: {e}")
            raise RepositoryError(
                f"Failed to search activities: {str(e)}",
                entity_type="AdminActivityLog",
                operation="search_activities"
            )

    def get_admin_activity_logs_with_pagination(
        self,
        skip: int = 0,
        limit: int = 100,
        action: Optional[str] = None,
        entity_type: Optional[str] = None,
        admin_id: Optional[int] = None
    ) -> tuple[List[AdminActivityLog], int]:
        """
        Get admin activity logs with filtering and pagination, returning both logs and total count.

        Args:
            skip: Number of logs to skip
            limit: Maximum number of logs to return
            action: Filter by action
            entity_type: Filter by entity type
            admin_id: Filter by admin ID

        Returns:
            Tuple of (logs list, total count)
        """
        try:
            query = self.session.query(AdminActivityLog)

            # Apply filters
            if action:
                query = query.filter(AdminActivityLog.action == action)
            if entity_type:
                query = query.filter(AdminActivityLog.target_type == entity_type)
            if admin_id:
                query = query.filter(AdminActivityLog.admin_user_id == admin_id)

            # Get total count
            total = query.count()

            # Apply pagination
            logs = query.order_by(AdminActivityLog.created_at.desc()).offset(skip).limit(limit).all()

            return logs, total

        except Exception as e:
            self.logger.error(f"Failed to get admin activity logs with pagination: {e}")
            raise RepositoryError(
                f"Failed to get admin activity logs with pagination: {str(e)}",
                entity_type="AdminActivityLog",
                operation="get_admin_activity_logs_with_pagination"
            )
    
    def delete_old_activities(
        self,
        cutoff_date: datetime,
        batch_size: int = 1000
    ) -> int:
        """
        Delete old activity logs before a cutoff date.
        
        Args:
            cutoff_date: Date before which to delete activities
            batch_size: Number of records to delete in each batch
            
        Returns:
            Number of deleted records
        """
        try:
            deleted_count = 0
            
            while True:
                # Delete in batches to avoid locking issues
                batch = self.session.query(AdminActivityLog).filter(
                    AdminActivityLog.created_at < cutoff_date
                ).limit(batch_size).all()
                
                if not batch:
                    break
                
                for activity in batch:
                    self.session.delete(activity)
                
                self.session.commit()
                deleted_count += len(batch)
                
                self.logger.debug(f"Deleted batch of {len(batch)} old activities")
            
            self.logger.info(f"Deleted {deleted_count} old activity logs")
            return deleted_count
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to delete old activities: {e}")
            raise RepositoryError(
                f"Failed to delete old activities: {str(e)}",
                entity_type="AdminActivityLog",
                operation="delete_old_activities"
            )
