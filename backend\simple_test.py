#!/usr/bin/env python3
"""
Simple test to verify our database fixes.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_service_error():
    """Test DatabaseServiceError constructor fix."""
    try:
        # Import the fixed class
        from app.repositories.database_service import DatabaseServiceError
        
        print("Testing DatabaseServiceError constructor...")
        
        # Test creating error with operation parameter (this was failing before)
        error = DatabaseServiceError("Test error", operation="test_operation")
        
        # Verify the fix worked
        assert hasattr(error, 'operation'), "Operation attribute missing"
        assert error.operation == "test_operation", "Operation not set correctly"
        assert error.message == "Test error", "Message not set correctly"
        
        print("✓ DatabaseServiceError constructor fix working")
        return True
    except Exception as e:
        print(f"✗ DatabaseServiceError test failed: {e}")
        return False

def test_base_repository_session():
    """Test BaseRepository session attribute fix."""
    try:
        from sqlalchemy.orm import Session
        from sqlalchemy import create_engine
        from app.repositories.base_repository import BaseRepository
        from app.models.database_models import ProviderApiKey
        
        print("Testing BaseRepository session attribute...")
        
        # Create a dummy session for testing
        engine = create_engine("sqlite:///:memory:")
        session = Session(engine)
        
        # Create repository instance
        repo = BaseRepository(session, ProviderApiKey)
        
        # Test that both db and session attributes exist
        assert hasattr(repo, 'db'), "Repository missing 'db' attribute"
        assert hasattr(repo, 'session'), "Repository missing 'session' attribute"
        assert repo.db is repo.session, "db and session attributes should be the same"
        
        print("✓ BaseRepository session attribute fix working")
        return True
    except Exception as e:
        print(f"✗ BaseRepository test failed: {e}")
        return False

def main():
    """Run simple tests."""
    print("Starting simple database fixes verification...")
    
    tests = [
        ("DatabaseServiceError Constructor Fix", test_database_service_error),
        ("BaseRepository Session Fix", test_base_repository_session),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- Running {test_name} ---")
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print("\n" + "="*50)
    print("TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("✓ Database fixes are working correctly!")
        return 0
    else:
        print("✗ Some tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
