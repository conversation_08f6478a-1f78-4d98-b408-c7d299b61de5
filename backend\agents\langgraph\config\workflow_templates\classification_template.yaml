# Classification Agent Workflow Template
# Optimized for content classification, categorization, and organization workflows

template_id: "classification_workflow"
template_name: "Classification Agent Workflow"
version: "1.0.0"
description: "Efficient workflow template for classification agent with content organization and categorization capabilities"
agent_type: "classification"

# Template metadata
metadata:
  author: "Datagenius Team"
  created_date: "2024-01-26"
  last_modified: "2024-01-26"
  tags: ["classification", "categorization", "organization", "tagging", "efficient"]
  complexity: "medium"
  estimated_duration: "10-30 seconds"

# Workflow configuration
workflow_config:
  # Execution settings
  max_execution_steps: 6  # Moderate steps for classification tasks
  execution_timeout: 45   # 45 seconds for classification
  enable_parallel_execution: true  # Enable parallel classification
  
  # Loop prevention
  max_agent_executions: 3  # Limited iterations for classification
  min_execution_interval: 1.0  # 1 second between executions
  enable_infinite_loop_detection: true
  
  # Termination conditions
  auto_terminate_on_response: true  # Terminate after classification
  require_explicit_continuation: false
  enable_workflow_completion_signals: true

# Node configuration
nodes:
  # Entry point - classification agent
  entry:
    type: "agent"
    agent_id: "classification"
    config:
      max_retries: 2
      timeout: 30
      enable_fallback: true
      enable_batch_processing: true
    
  # Content preprocessing node
  content_preprocessing:
    type: "tool_execution"
    tools: ["text_cleaner", "format_normalizer", "content_extractor"]
    config:
      parallel_execution: true
      timeout: 15
      enable_batch_mode: true
    
  # Classification engine node
  classification_engine:
    type: "tool_execution"
    tools: ["text_classifier", "content_categorizer", "tag_generator"]
    config:
      parallel_execution: false  # Sequential for accuracy
      timeout: 20
      enable_confidence_scoring: true
    
  # Validation node
  validation:
    type: "validation"
    config:
      validate_classifications: true
      check_confidence_scores: true
      verify_category_consistency: true
      min_confidence_threshold: 0.7
    
  # Post-processing node
  post_processing:
    type: "tool_execution"
    tools: ["result_formatter", "hierarchy_builder", "metadata_enricher"]
    config:
      parallel_execution: true
      timeout: 10
      optional: true
    
  # Routing node
  routing:
    type: "routing"
    config:
      enable_intelligent_routing: false  # Simple routing for classification
      enable_collaboration: false
      max_routing_attempts: 1

# Edge configuration (routing patterns)
edges:
  # Start -> Classification Agent
  - from: "START"
    to: "classification"
    condition: "always"
    priority: 1
    
  # Classification -> Content Preprocessing
  - from: "classification"
    to: "content_preprocessing"
    condition: "content_available AND requires_preprocessing"
    priority: 1
    
  # Classification -> Classification Engine (direct)
  - from: "classification"
    to: "classification_engine"
    condition: "content_ready AND NOT requires_preprocessing"
    priority: 1
    
  # Content Preprocessing -> Classification Engine
  - from: "content_preprocessing"
    to: "classification_engine"
    condition: "preprocessing_complete"
    priority: 1
    
  # Classification Engine -> Validation
  - from: "classification_engine"
    to: "validation"
    condition: "classification_complete"
    priority: 1
    
  # Validation -> Post-processing
  - from: "validation"
    to: "post_processing"
    condition: "validation_passed AND post_processing_requested"
    priority: 1
    
  # Validation -> Classification Agent (for response)
  - from: "validation"
    to: "classification"
    condition: "validation_passed AND NOT post_processing_requested"
    priority: 1
    
  # Post-processing -> Classification Agent (for final response)
  - from: "post_processing"
    to: "classification"
    condition: "post_processing_complete"
    priority: 1
    
  # Classification Agent -> END
  - from: "classification"
    to: "END"
    condition: "workflow_complete OR classification_results_ready"
    priority: 1
    
  # Validation failure -> Classification Engine (retry)
  - from: "validation"
    to: "classification_engine"
    condition: "validation_failed AND retry_count < max_retries"
    priority: 2
    
  # Emergency termination
  - from: "*"
    to: "END"
    condition: "critical_error OR timeout OR max_executions_reached"
    priority: 3

# Termination conditions
termination_conditions:
  # Successful completion
  - condition: "classification_complete AND validation_passed AND results_formatted"
    action: "END"
    priority: 1
    
  # Workflow completion signal
  - condition: "workflow_complete"
    action: "END"
    priority: 1
    
  # Classification-specific termination
  - condition: "all_content_classified AND confidence_threshold_met"
    action: "END"
    priority: 1
    
  # No content to classify
  - condition: "no_content_provided OR content_empty"
    action: "END"
    priority: 2
    
  # Error conditions
  - condition: "classification_failed OR unsupported_content_type"
    action: "END"
    priority: 2
    
  # Resource limits
  - condition: "processing_timeout OR memory_limit_exceeded"
    action: "END"
    priority: 3
    
  # Loop prevention
  - condition: "infinite_loop_detected OR max_classification_attempts_reached"
    action: "END"
    priority: 3

# State management
state_management:
  # Required state fields
  required_fields:
    - "user_id"
    - "conversation_id"
    - "messages"
    - "content_to_classify"
    
  # Classification-specific state
  classification_state:
    - "content_items"
    - "classification_results"
    - "confidence_scores"
    - "categories"
    - "tags"
    
  # State validation rules
  validation_rules:
    - field: "content_to_classify"
      rule: "not_empty"
      error_action: "request_content"
      
    - field: "classification_results"
      rule: "valid_format"
      error_action: "reformat_results"
  
  # State persistence
  persist_classification_results: true
  enable_result_caching: true
  cleanup_on_completion: false  # Keep results for reference

# Performance optimization
performance:
  # Caching
  enable_classification_caching: true
  cache_duration: 1800  # 30 minutes
  cache_similar_content: true
  
  # Resource limits
  memory_limit_mb: 150  # Moderate for text processing
  cpu_time_limit_seconds: 30
  max_content_size_mb: 25
  
  # Batch processing
  enable_batch_classification: true
  max_batch_size: 100
  batch_timeout: 25
  
  # Parallel processing
  max_parallel_classifiers: 2
  enable_async_processing: true
  
  # Monitoring
  enable_performance_tracking: true
  track_classification_accuracy: true
  track_processing_speed: true
  track_confidence_scores: true

# Error handling
error_handling:
  # Retry configuration
  max_retries: 2
  retry_delay_seconds: 1.0
  exponential_backoff: false
  
  # Fallback strategies
  fallback_strategies:
    - trigger: "classification_failed"
      action: "use_basic_categorization"
      
    - trigger: "low_confidence_scores"
      action: "request_manual_review"
      
    - trigger: "unsupported_format"
      action: "convert_to_text"
      
    - trigger: "timeout"
      action: "return_partial_results"
  
  # Error responses
  classification_error_response: "I had trouble classifying some content. Here are the results I could determine."
  format_error_response: "The content format isn't fully supported. I'll classify what I can process."
  confidence_error_response: "Some classifications have low confidence. Please review the results."

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable: true
    required: false
    use_industry_categories: true
    
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable: false  # Disabled for classification simplicity
    
  # Content management
  content_management:
    enable: true
    supported_formats: ["text", "pdf", "docx", "html", "csv"]
    
  # Taxonomy management
  taxonomy_management:
    enable: true
    use_custom_taxonomies: true
    enable_hierarchy: true
    
  # MCP tools
  mcp_tools:
    enable: true
    max_tools: 3
    timeout: 20

# Classification configuration
classification_config:
  # Supported classification types
  classification_types:
    - "content_type"
    - "topic_category"
    - "sentiment"
    - "priority_level"
    - "document_type"
    - "industry_category"
    
  # Confidence thresholds
  confidence_thresholds:
    high_confidence: 0.9
    medium_confidence: 0.7
    low_confidence: 0.5
    rejection_threshold: 0.3
    
  # Category management
  categories:
    max_categories_per_item: 5
    enable_multi_label: true
    enable_hierarchical: true
    
  # Tag generation
  tag_generation:
    enable_auto_tagging: true
    max_tags_per_item: 10
    min_tag_confidence: 0.6

# Monitoring and metrics
monitoring:
  # Metrics to collect
  metrics:
    - "classification_accuracy"
    - "processing_speed"
    - "confidence_distribution"
    - "category_coverage"
    - "user_satisfaction"
    - "error_rate"
    
  # Performance thresholds
  thresholds:
    accuracy: 0.85
    processing_time: 30.0
    confidence_score: 0.7
    
  # Alerts
  alerts:
    - metric: "classification_accuracy"
      threshold: 0.8
      action: "retrain_models"
      
    - metric: "processing_time"
      threshold: 30.0
      action: "optimize_processing"

# Template validation
validation:
  # Required components
  required_components:
    - "classification_agent"
    - "classification_engine"
    - "validation_system"
    
  # Optional components
  optional_components:
    - "content_preprocessing"
    - "post_processing"
    - "taxonomy_management"
    
  # Validation rules
  rules:
    - rule: "max_execution_steps <= 8"
      error: "Classification workflows should be efficient and focused"
      
    - rule: "execution_timeout <= 60"
      error: "Classification should complete quickly for user experience"
