"""
Capability Marketplace

Core marketplace system for trading, discovering, and optimizing agent capabilities.
Provides a platform for agents to offer, discover, and trade capabilities based on
performance metrics and business requirements.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

from ..events.event_bus import event_bus, LangGraphEvent
from ..monitoring.metrics import MetricsCollector
from .capability_registry import CapabilityRegistry
from .trading_engine import TradingEngine
from .certification_system import CertificationSystem

logger = logging.getLogger(__name__)


class CapabilityStatus(Enum):
    """Capability status in the marketplace."""
    AVAILABLE = "available"
    IN_USE = "in_use"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"


class TradingMode(Enum):
    """Trading modes for capabilities."""
    AUCTION = "auction"
    FIXED_PRICE = "fixed_price"
    NEGOTIATION = "negotiation"
    FREE = "free"


@dataclass
class CapabilityListing:
    """Represents a capability listing in the marketplace."""
    capability_id: str
    agent_id: str
    name: str
    description: str
    category: str
    version: str
    status: CapabilityStatus
    trading_mode: TradingMode
    price: float = 0.0
    performance_score: float = 0.0
    certification_level: str = "basic"
    usage_count: int = 0
    success_rate: float = 0.0
    average_execution_time: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    requirements: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CapabilityRequest:
    """Represents a request for capabilities."""
    request_id: str
    requester_id: str
    requirements: Dict[str, Any]
    budget: float
    deadline: datetime
    priority: str = "medium"
    preferred_agents: List[str] = field(default_factory=list)
    excluded_agents: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)


class CapabilityMarketplace:
    """
    Core capability marketplace for agent capability trading and optimization.

    Features:
    - Capability discovery and listing
    - Performance-based pricing
    - Automated capability matching
    - Trading and bidding mechanisms
    - Quality certification integration
    - Real-time capability monitoring
    - Dynamic pricing algorithms
    - SLA management and enforcement
    """

    def __init__(self):
        self.registry = CapabilityRegistry()
        self.trading_engine = TradingEngine()
        self.certification_system = CertificationSystem()
        self.metrics = MetricsCollector("capability_marketplace")
        self.logger = logging.getLogger(__name__)

        # Marketplace state
        self.listings: Dict[str, CapabilityListing] = {}
        self.requests: Dict[str, CapabilityRequest] = {}
        self.active_trades: Dict[str, Dict] = {}
        self.capability_cache: Dict[str, Dict[str, Any]] = {}

        # Configuration
        self.matching_threshold = 0.7
        self.price_adjustment_factor = 0.1
        self.performance_weight = 0.4
        self.certification_weight = 0.3
        self.usage_weight = 0.3
        self.cache_ttl = 300  # 5 minutes
        self.max_concurrent_trades = 100
        self.min_capability_score = 0.6

        # Performance tracking
        self.performance_cache: Dict[str, Dict[str, float]] = {}
        self.cache_expiry: Dict[str, datetime] = {}
        self.sla_metrics: Dict[str, Dict[str, float]] = {}

        # Trading limits and controls
        self.rate_limits: Dict[str, int] = {
            "requests_per_minute": 60,
            "trades_per_hour": 20,
            "max_bid_amount": 1000.0
        }

        # Initialize marketplace
        self._initialize_marketplace()

    def _initialize_marketplace(self) -> None:
        """Initialize marketplace components and load configurations."""
        try:
            self.logger.info("Initializing capability marketplace...")

            # Load marketplace configuration
            self._load_marketplace_config()

            # Initialize trading engine (schedule async initialization)
            asyncio.create_task(self._async_initialize_trading_engine())

            # Initialize certification system (schedule async initialization)
            asyncio.create_task(self._async_initialize_certification_system())

            # Start background tasks
            self._start_background_tasks()

            self.logger.info("Capability marketplace initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize capability marketplace: {e}")
            raise

    async def _async_initialize_trading_engine(self) -> None:
        """Asynchronously initialize the trading engine."""
        try:
            await self.trading_engine.initialize()

            # Configure trading engine limits
            if hasattr(self.trading_engine, 'max_concurrent_trades'):
                self.trading_engine.max_concurrent_trades = self.max_concurrent_trades
            if hasattr(self.trading_engine, 'rate_limits'):
                self.trading_engine.rate_limits = self.rate_limits

            self.logger.info("Trading engine initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize trading engine: {e}")

    async def _async_initialize_certification_system(self) -> None:
        """Asynchronously initialize the certification system."""
        try:
            await self.certification_system.initialize()
            self.logger.info("Certification system initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize certification system: {e}")

    async def initialize(self) -> None:
        """Public async initialize method for external callers."""
        try:
            # Initialize async components
            await self._async_initialize_trading_engine()
            await self._async_initialize_certification_system()
            self.logger.info("CapabilityMarketplace async initialization completed")
        except Exception as e:
            self.logger.error(f"Failed to initialize CapabilityMarketplace: {e}")
            raise

    def _load_marketplace_config(self) -> None:
        """Load marketplace configuration from environment or defaults."""
        import os

        self.matching_threshold = float(os.getenv("MARKETPLACE_MATCHING_THRESHOLD", "0.7"))
        self.price_adjustment_factor = float(os.getenv("MARKETPLACE_PRICE_ADJUSTMENT", "0.1"))
        self.cache_ttl = int(os.getenv("MARKETPLACE_CACHE_TTL", "300"))
        self.max_concurrent_trades = int(os.getenv("MARKETPLACE_MAX_TRADES", "100"))

        self.logger.info(f"Loaded marketplace config: threshold={self.matching_threshold}, "
                        f"cache_ttl={self.cache_ttl}, max_trades={self.max_concurrent_trades}")

    def _start_background_tasks(self) -> None:
        """Start background tasks for marketplace maintenance."""
        try:
            # Start cache cleanup task
            asyncio.create_task(self._cache_cleanup_task())

            # Start performance monitoring task
            asyncio.create_task(self._performance_monitoring_task())

            # Start SLA monitoring task
            asyncio.create_task(self._sla_monitoring_task())

            self.logger.info("Background tasks started successfully")

        except Exception as e:
            self.logger.error(f"Failed to start background tasks: {e}")

    async def _cache_cleanup_task(self) -> None:
        """Background task to clean up expired cache entries."""
        while True:
            try:
                current_time = datetime.now()
                expired_keys = [
                    key for key, expiry in self.cache_expiry.items()
                    if current_time > expiry
                ]

                for key in expired_keys:
                    self.performance_cache.pop(key, None)
                    self.cache_expiry.pop(key, None)
                    self.capability_cache.pop(key, None)

                if expired_keys:
                    self.logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

                await asyncio.sleep(60)  # Run every minute

            except Exception as e:
                self.logger.error(f"Error in cache cleanup task: {e}")
                await asyncio.sleep(60)

    async def _performance_monitoring_task(self) -> None:
        """Background task to monitor capability performance."""
        while True:
            try:
                # Update performance metrics for all active capabilities
                for listing_id, listing in self.listings.items():
                    await self._update_capability_performance(listing_id, listing)

                await asyncio.sleep(300)  # Run every 5 minutes

            except Exception as e:
                self.logger.error(f"Error in performance monitoring task: {e}")
                await asyncio.sleep(300)

    async def _sla_monitoring_task(self) -> None:
        """Background task to monitor SLA compliance."""
        while True:
            try:
                # Check SLA compliance for all active trades
                for trade_id, trade_data in self.active_trades.items():
                    await self._check_sla_compliance(trade_id, trade_data)

                await asyncio.sleep(60)  # Run every minute

            except Exception as e:
                self.logger.error(f"Error in SLA monitoring task: {e}")
                await asyncio.sleep(60)

    async def register_agent_capabilities(
        self,
        agent_id: str,
        capabilities: List[Dict[str, Any]]
    ) -> bool:
        """
        Register agent capabilities in the marketplace.

        Args:
            agent_id: Agent identifier
            capabilities: List of capability definitions

        Returns:
            True if registration successful
        """
        try:
            self.logger.info(f"Registering capabilities for agent {agent_id}")

            for capability in capabilities:
                listing_id = f"{agent_id}_{capability.get('name', 'unknown')}"

                # Validate capability definition
                if not self._validate_capability(capability):
                    self.logger.warning(f"Invalid capability definition for {listing_id}")
                    continue

                # Create capability listing
                listing = CapabilityListing(
                    listing_id=listing_id,
                    agent_id=agent_id,
                    capability_name=capability["name"],
                    capability_type=capability.get("type", "general"),
                    description=capability.get("description", ""),
                    pricing_model=capability.get("pricing_model", "fixed"),
                    base_price=capability.get("base_price", 0.0),
                    performance_metrics=capability.get("performance_metrics", {}),
                    availability_schedule=capability.get("availability_schedule", {}),
                    sla_requirements=capability.get("sla_requirements", {}),
                    certification_level=capability.get("certification_level", "basic")
                )

                # Store listing
                self.listings[listing_id] = listing

                # Register with capability registry
                await self.registry.register_capability(agent_id, capability)

                # Initialize performance tracking
                self.performance_cache[listing_id] = {
                    "success_rate": 1.0,
                    "avg_response_time": 0.0,
                    "reliability_score": 1.0,
                    "last_updated": datetime.now()
                }

                self.logger.info(f"Registered capability {listing_id}")

            return True

        except Exception as e:
            self.logger.error(f"Error registering capabilities for agent {agent_id}: {e}")
            return False

    def _validate_capability(self, capability: Dict[str, Any]) -> bool:
        """
        Validate capability definition.

        Args:
            capability: Capability definition

        Returns:
            True if valid
        """
        required_fields = ["name", "type"]

        for field in required_fields:
            if field not in capability:
                return False

        # Validate capability type
        valid_types = ["analysis", "generation", "processing", "communication", "integration"]
        if capability["type"] not in valid_types:
            return False

        # Validate pricing model if present
        if "pricing_model" in capability:
            valid_pricing = ["fixed", "usage", "performance", "subscription"]
            if capability["pricing_model"] not in valid_pricing:
                return False

        return True

    async def find_capability_matches(
        self,
        requirements: Dict[str, Any],
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Find capability matches for given requirements.

        Args:
            requirements: Capability requirements
            max_results: Maximum number of results

        Returns:
            List of matching capabilities with scores
        """
        try:
            matches = []

            for listing_id, listing in self.listings.items():
                # Calculate match score
                score = await self._calculate_match_score(listing, requirements)

                if score >= self.matching_threshold:
                    # Get current performance metrics
                    performance = self.performance_cache.get(listing_id, {})

                    # Calculate final score including performance
                    final_score = self._calculate_final_score(score, performance)

                    match_data = {
                        "listing_id": listing_id,
                        "agent_id": listing.agent_id,
                        "capability_name": listing.capability_name,
                        "match_score": score,
                        "final_score": final_score,
                        "estimated_price": await self._calculate_dynamic_price(listing, requirements),
                        "performance_metrics": performance,
                        "sla_requirements": listing.sla_requirements,
                        "availability": await self._check_availability(listing)
                    }

                    matches.append(match_data)

            # Sort by final score and return top matches
            matches.sort(key=lambda x: x["final_score"], reverse=True)
            return matches[:max_results]

        except Exception as e:
            self.logger.error(f"Error finding capability matches: {e}")
            return []

    async def _calculate_match_score(
        self,
        listing: CapabilityListing,
        requirements: Dict[str, Any]
    ) -> float:
        """
        Calculate match score between capability and requirements.

        Args:
            listing: Capability listing
            requirements: Requirements

        Returns:
            Match score (0.0 to 1.0)
        """
        try:
            score = 0.0
            total_weight = 0.0

            # Type matching (weight: 0.3)
            if "type" in requirements:
                if listing.capability_type == requirements["type"]:
                    score += 0.3
                total_weight += 0.3

            # Performance requirements (weight: 0.4)
            if "performance_requirements" in requirements:
                perf_score = self._calculate_performance_match(
                    listing.performance_metrics,
                    requirements["performance_requirements"]
                )
                score += perf_score * 0.4
                total_weight += 0.4

            # Budget constraints (weight: 0.2)
            if "budget" in requirements:
                budget_score = self._calculate_budget_match(
                    listing.base_price,
                    requirements["budget"]
                )
                score += budget_score * 0.2
                total_weight += 0.2

            # SLA requirements (weight: 0.1)
            if "sla_requirements" in requirements:
                sla_score = self._calculate_sla_match(
                    listing.sla_requirements,
                    requirements["sla_requirements"]
                )
                score += sla_score * 0.1
                total_weight += 0.1

            return score / total_weight if total_weight > 0 else 0.0

        except Exception as e:
            self.logger.error(f"Error calculating match score: {e}")
            return 0.0

    def _initialize_execution_tracking(self):
        """Initialize execution history tracking system."""
        # This would typically load from database
        # For now, initialize empty tracking
        logger.info("Execution tracking system initialized")

    def _setup_event_handlers(self):
        """Setup event handlers for marketplace operations."""
        event_bus.subscribe("agent.capability_updated", self._handle_capability_update)
        event_bus.subscribe("workflow.completed", self._handle_workflow_completion)
        event_bus.subscribe("marketplace.capability_requested", self._handle_capability_request)
        event_bus.subscribe("workflow.execution_completed", self._handle_execution_completion)
    
    async def initialize(self):
        """Initialize the marketplace system."""
        try:
            await self.registry.initialize()
            await self.trading_engine.initialize()
            await self.certification_system.initialize()
            
            # Load existing listings
            await self._load_marketplace_data()
            
            # Start background tasks
            asyncio.create_task(self._marketplace_maintenance_loop())
            asyncio.create_task(self._price_optimization_loop())
            
            logger.info("Capability marketplace initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize capability marketplace: {e}")
            raise
    
    async def list_capability(self, capability_data: Dict[str, Any]) -> str:
        """
        List a capability in the marketplace.
        
        Args:
            capability_data: Capability information including agent_id, name, etc.
            
        Returns:
            Capability listing ID
        """
        try:
            # Validate capability data
            if not self._validate_capability_data(capability_data):
                raise ValueError("Invalid capability data")
            
            # Create capability listing
            listing = CapabilityListing(
                capability_id=capability_data["capability_id"],
                agent_id=capability_data["agent_id"],
                name=capability_data["name"],
                description=capability_data.get("description", ""),
                category=capability_data.get("category", "general"),
                version=capability_data.get("version", "1.0.0"),
                status=CapabilityStatus(capability_data.get("status", "available")),
                trading_mode=TradingMode(capability_data.get("trading_mode", "fixed_price")),
                price=capability_data.get("price", 0.0),
                tags=capability_data.get("tags", []),
                requirements=capability_data.get("requirements", {}),
                metadata=capability_data.get("metadata", {})
            )
            
            # Get performance metrics
            performance_data = await self._get_capability_performance(listing.capability_id)
            listing.performance_score = performance_data.get("score", 0.0)
            listing.success_rate = performance_data.get("success_rate", 0.0)
            listing.average_execution_time = performance_data.get("avg_execution_time", 0.0)
            
            # Get certification level
            listing.certification_level = await self.certification_system.get_certification_level(
                listing.capability_id
            )
            
            # Store listing
            self.listings[listing.capability_id] = listing
            
            # Register with capability registry
            await self.registry.register_capability(listing)
            
            # Publish event
            await event_bus.publish(LangGraphEvent(
                event_type="marketplace.capability_listed",
                timestamp=datetime.now(),
                source="capability_marketplace",
                data={
                    "capability_id": listing.capability_id,
                    "agent_id": listing.agent_id,
                    "name": listing.name,
                    "category": listing.category,
                    "trading_mode": listing.trading_mode.value,
                    "price": listing.price
                }
            ))
            
            # Update metrics
            self.metrics.increment("capabilities_listed")
            
            logger.info(f"Listed capability {listing.name} from agent {listing.agent_id}")
            return listing.capability_id
            
        except Exception as e:
            logger.error(f"Failed to list capability: {e}")
            raise
    
    async def request_capability(self, request_data: Dict[str, Any]) -> str:
        """
        Request a capability from the marketplace.
        
        Args:
            request_data: Request information including requirements, budget, etc.
            
        Returns:
            Request ID
        """
        try:
            # Create capability request
            request = CapabilityRequest(
                request_id=request_data["request_id"],
                requester_id=request_data["requester_id"],
                requirements=request_data["requirements"],
                budget=request_data.get("budget", 0.0),
                deadline=datetime.fromisoformat(request_data["deadline"]),
                priority=request_data.get("priority", "medium"),
                preferred_agents=request_data.get("preferred_agents", []),
                excluded_agents=request_data.get("excluded_agents", [])
            )
            
            # Store request
            self.requests[request.request_id] = request
            
            # Find matching capabilities
            matches = await self._find_capability_matches(request)
            
            # Initiate trading process
            if matches:
                trade_id = await self.trading_engine.initiate_trade(request, matches)
                self.active_trades[trade_id] = {
                    "request_id": request.request_id,
                    "matches": matches,
                    "status": "active",
                    "created_at": datetime.now()
                }
            
            # Publish event
            await event_bus.publish(LangGraphEvent(
                event_type="marketplace.capability_requested",
                timestamp=datetime.now(),
                source="capability_marketplace",
                data={
                    "request_id": request.request_id,
                    "requester_id": request.requester_id,
                    "requirements": request.requirements,
                    "matches_found": len(matches) if matches else 0
                }
            ))
            
            # Update metrics
            self.metrics.increment("capability_requests")
            
            logger.info(f"Created capability request {request.request_id} with {len(matches) if matches else 0} matches")
            return request.request_id

        except Exception as e:
            logger.error(f"Failed to create capability request: {e}")
            raise

    async def discover_capabilities(self,
                                  filters: Optional[Dict[str, Any]] = None,
                                  sort_by: str = "performance_score",
                                  limit: int = 50) -> List[CapabilityListing]:
        """
        Discover available capabilities in the marketplace.

        Args:
            filters: Optional filters for capability discovery
            sort_by: Sort criteria (performance_score, price, usage_count, etc.)
            limit: Maximum number of results

        Returns:
            List of matching capability listings
        """
        try:
            # Get all available capabilities
            available_capabilities = [
                listing for listing in self.listings.values()
                if listing.status == CapabilityStatus.AVAILABLE
            ]

            # Apply filters
            if filters:
                available_capabilities = self._apply_filters(available_capabilities, filters)

            # Sort capabilities
            available_capabilities = self._sort_capabilities(available_capabilities, sort_by)

            # Limit results
            results = available_capabilities[:limit]

            # Update metrics
            self.metrics.increment("capability_discoveries")

            logger.info(f"Discovered {len(results)} capabilities with filters: {filters}")
            return results

        except Exception as e:
            logger.error(f"Failed to discover capabilities: {e}")
            raise

    async def get_capability_recommendations(self,
                                           requester_id: str,
                                           context: Dict[str, Any]) -> List[Tuple[CapabilityListing, float]]:
        """
        Get AI-powered capability recommendations.

        Args:
            requester_id: ID of the requesting entity
            context: Context information for recommendations

        Returns:
            List of (capability, confidence_score) tuples
        """
        try:
            # Get requester history and preferences
            requester_profile = await self._get_requester_profile(requester_id)

            # Analyze context and requirements
            analyzed_requirements = await self._analyze_requirements(context)

            # Generate recommendations using ML model
            recommendations = await self._generate_recommendations(
                requester_profile, analyzed_requirements
            )

            # Score and rank recommendations
            scored_recommendations = []
            for capability_id, base_score in recommendations:
                if capability_id in self.listings:
                    listing = self.listings[capability_id]

                    # Calculate comprehensive score
                    final_score = self._calculate_recommendation_score(
                        listing, base_score, requester_profile, analyzed_requirements
                    )

                    scored_recommendations.append((listing, final_score))

            # Sort by score and return top recommendations
            scored_recommendations.sort(key=lambda x: x[1], reverse=True)

            # Update metrics
            self.metrics.increment("recommendations_generated")

            logger.info(f"Generated {len(scored_recommendations)} recommendations for {requester_id}")
            return scored_recommendations[:10]  # Top 10 recommendations

        except Exception as e:
            logger.error(f"Failed to generate recommendations: {e}")
            raise

    async def optimize_pricing(self, capability_id: str) -> float:
        """
        Optimize pricing for a capability based on market conditions.

        Args:
            capability_id: ID of the capability to optimize

        Returns:
            Optimized price
        """
        try:
            if capability_id not in self.listings:
                raise ValueError(f"Capability {capability_id} not found")

            listing = self.listings[capability_id]

            # Get market data
            market_data = await self._get_market_data(listing.category)

            # Calculate optimal price
            optimal_price = await self._calculate_optimal_price(listing, market_data)

            # Update listing price
            old_price = listing.price
            listing.price = optimal_price
            listing.last_updated = datetime.now()

            # Publish price update event
            await event_bus.publish(LangGraphEvent(
                event_type="marketplace.price_updated",
                timestamp=datetime.now(),
                source="capability_marketplace",
                data={
                    "capability_id": capability_id,
                    "old_price": old_price,
                    "new_price": optimal_price,
                    "optimization_factor": optimal_price / old_price if old_price > 0 else 1.0
                }
            ))

            # Update metrics
            self.metrics.increment("price_optimizations")

            logger.info(f"Optimized price for {capability_id}: {old_price} -> {optimal_price}")
            return optimal_price

        except Exception as e:
            logger.error(f"Failed to optimize pricing for {capability_id}: {e}")
            raise

    async def get_marketplace_analytics(self) -> Dict[str, Any]:
        """
        Get comprehensive marketplace analytics.

        Returns:
            Analytics data including trends, performance metrics, etc.
        """
        try:
            analytics = {
                "overview": {
                    "total_capabilities": len(self.listings),
                    "available_capabilities": len([
                        l for l in self.listings.values()
                        if l.status == CapabilityStatus.AVAILABLE
                    ]),
                    "active_requests": len(self.requests),
                    "active_trades": len(self.active_trades),
                    "total_agents": len(set(l.agent_id for l in self.listings.values()))
                },
                "performance": {
                    "average_success_rate": self._calculate_average_success_rate(),
                    "average_execution_time": self._calculate_average_execution_time(),
                    "top_performing_categories": await self._get_top_categories(),
                    "certification_distribution": await self._get_certification_distribution()
                },
                "market": {
                    "price_trends": await self._get_price_trends(),
                    "demand_patterns": await self._get_demand_patterns(),
                    "supply_analysis": await self._get_supply_analysis()
                },
                "recommendations": {
                    "optimization_opportunities": await self._identify_optimization_opportunities(),
                    "market_gaps": await self._identify_market_gaps(),
                    "growth_areas": await self._identify_growth_areas()
                }
            }

            # Update metrics
            self.metrics.increment("analytics_requests")

            return analytics

        except Exception as e:
            logger.error(f"Failed to generate marketplace analytics: {e}")
            raise

    # Event Handlers
    async def _handle_capability_update(self, event: LangGraphEvent):
        """Handle capability update events."""
        try:
            capability_id = event.data.get("capability_id")
            if capability_id in self.listings:
                # Update performance metrics
                performance_data = await self._get_capability_performance(capability_id)
                listing = self.listings[capability_id]
                listing.performance_score = performance_data.get("score", listing.performance_score)
                listing.success_rate = performance_data.get("success_rate", listing.success_rate)
                listing.average_execution_time = performance_data.get("avg_execution_time", listing.average_execution_time)
                listing.last_updated = datetime.now()

                # Trigger price optimization if needed
                if self._should_optimize_price(listing):
                    await self.optimize_pricing(capability_id)

        except Exception as e:
            logger.error(f"Error handling capability update: {e}")

    async def _handle_workflow_completion(self, event: LangGraphEvent):
        """Handle workflow completion events for usage tracking."""
        try:
            capability_id = event.data.get("capability_id")
            if capability_id in self.listings:
                listing = self.listings[capability_id]
                listing.usage_count += 1
                listing.last_updated = datetime.now()

                # Update success rate if execution data is available
                if "success" in event.data:
                    success = event.data["success"]
                    current_total = listing.usage_count * listing.success_rate
                    new_total = current_total + (1.0 if success else 0.0)
                    listing.success_rate = new_total / listing.usage_count

        except Exception as e:
            logger.error(f"Error handling workflow completion: {e}")

    async def _handle_capability_request(self, event: LangGraphEvent):
        """Handle new capability requests."""
        try:
            request_data = event.data
            await self.request_capability(request_data)
        except Exception as e:
            logger.error(f"Error handling capability request: {e}")

    async def _handle_execution_completion(self, event: LangGraphEvent):
        """Handle workflow execution completion events to track performance."""
        try:
            execution_data = event.data
            capability_id = execution_data.get("capability_id")

            if not capability_id:
                return

            # Record execution data for performance tracking
            execution_record = {
                "timestamp": datetime.now(),
                "success": execution_data.get("success", False),
                "execution_time": execution_data.get("execution_time", 0),
                "quality_score": execution_data.get("quality_score", 0.5),
                "error_message": execution_data.get("error_message"),
                "agent_id": execution_data.get("agent_id"),
                "workflow_id": execution_data.get("workflow_id")
            }

            # Store in execution history
            execution_key = f"executions_{capability_id}"
            if execution_key not in self._execution_history:
                self._execution_history[execution_key] = []

            self._execution_history[execution_key].append(execution_record)

            # Keep only recent executions (last 100 per capability)
            if len(self._execution_history[execution_key]) > 100:
                self._execution_history[execution_key] = self._execution_history[execution_key][-100:]

            # Clear performance cache for this capability to force recalculation
            if capability_id in self.performance_cache:
                del self.performance_cache[capability_id]
                del self.cache_expiry[capability_id]

            logger.debug(f"Recorded execution data for capability {capability_id}")

        except Exception as e:
            logger.error(f"Error handling execution completion: {e}")

    # Helper Methods
    def _validate_capability_data(self, data: Dict[str, Any]) -> bool:
        """Validate capability data before listing."""
        required_fields = ["capability_id", "agent_id", "name"]
        return all(field in data for field in required_fields)

    async def _get_capability_performance(self, capability_id: str) -> Dict[str, float]:
        """Get performance metrics for a capability from monitoring system."""
        try:
            # Get capability metadata
            capability = await self.registry.get_capability(capability_id)
            if not capability:
                logger.warning(f"Capability {capability_id} not found for performance lookup")
                return {"score": 0.0, "success_rate": 0.0, "avg_execution_time": 0.0}

            # Calculate performance metrics from historical data
            # Note: agent_id available for future agent-specific performance tracking

            # Get recent execution history (last 30 days)
            recent_executions = self._get_recent_executions(capability_id, days=30)

            if not recent_executions:
                # Return default values for new capabilities
                return {
                    "score": 0.7,  # Default score for new capabilities
                    "success_rate": 0.8,  # Conservative default
                    "avg_execution_time": capability.estimated_execution_time or 60.0
                }

            # Calculate actual performance metrics
            total_executions = len(recent_executions)
            successful_executions = [e for e in recent_executions if e.get("success", False)]
            success_rate = len(successful_executions) / total_executions if total_executions > 0 else 0.0

            # Calculate average execution time
            execution_times = [e.get("execution_time", 0) for e in recent_executions if e.get("execution_time")]
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 60.0

            # Calculate quality scores from successful executions
            quality_scores = [e.get("quality_score", 0.5) for e in successful_executions if e.get("quality_score")]
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.5

            # Calculate composite performance score
            # Weight: 40% success rate, 30% quality, 20% speed, 10% reliability
            speed_score = max(0.1, min(1.0, 120.0 / max(avg_execution_time, 1.0)))  # Normalize to 2 minutes baseline
            reliability_score = min(1.0, success_rate + 0.1)  # Slight bonus for consistency

            composite_score = (
                success_rate * 0.4 +
                avg_quality * 0.3 +
                speed_score * 0.2 +
                reliability_score * 0.1
            )

            # Update metrics
            self.metrics.set_gauge(f"capability_performance_{capability_id}", composite_score)
            self.metrics.increment("performance_calculations")

            return {
                "score": round(composite_score, 3),
                "success_rate": round(success_rate, 3),
                "avg_execution_time": round(avg_execution_time, 2),
                "quality_score": round(avg_quality, 3),
                "total_executions": total_executions
            }

        except Exception as e:
            logger.error(f"Error calculating performance for capability {capability_id}: {e}")
            # Return conservative defaults on error
            return {
                "score": 0.5,
                "success_rate": 0.5,
                "avg_execution_time": 120.0,
                "quality_score": 0.5,
                "total_executions": 0
            }

    def _get_recent_executions(self, capability_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get recent execution history for a capability."""
        # This would query the execution history database
        # For now, simulate with stored execution data
        cutoff_date = datetime.now() - timedelta(days=days)

        # Check if we have execution history stored
        execution_key = f"executions_{capability_id}"
        if hasattr(self, '_execution_history') and execution_key in self._execution_history:
            return [
                exec_data for exec_data in self._execution_history[execution_key]
                if exec_data.get("timestamp", datetime.now()) >= cutoff_date
            ]

        # Return empty list if no history available
        return []

    async def _find_capability_matches(self, request: CapabilityRequest) -> List[CapabilityListing]:
        """Find capabilities that match a request."""
        matches = []

        for listing in self.listings.values():
            if listing.status != CapabilityStatus.AVAILABLE:
                continue

            # Check agent preferences
            if request.preferred_agents and listing.agent_id not in request.preferred_agents:
                continue
            if listing.agent_id in request.excluded_agents:
                continue

            # Calculate match score
            match_score = self._calculate_match_score(listing, request)

            if match_score >= self.matching_threshold:
                matches.append(listing)

        # Sort by match score
        matches.sort(key=lambda x: self._calculate_match_score(x, request), reverse=True)
        return matches

    def _calculate_match_score(self, listing: CapabilityListing, request: CapabilityRequest) -> float:
        """Calculate how well a capability matches a request."""
        score = 0.0

        # Performance score weight
        score += listing.performance_score * self.performance_weight

        # Certification weight
        cert_scores = {"basic": 0.3, "intermediate": 0.6, "advanced": 0.9, "expert": 1.0}
        score += cert_scores.get(listing.certification_level, 0.3) * self.certification_weight

        # Usage history weight
        usage_score = min(listing.usage_count / 100.0, 1.0)  # Normalize to 0-1
        score += usage_score * self.usage_weight

        # Budget compatibility
        if request.budget > 0 and listing.price <= request.budget:
            score += 0.1  # Bonus for budget compatibility

        return min(score, 1.0)

    def _apply_filters(self, capabilities: List[CapabilityListing], filters: Dict[str, Any]) -> List[CapabilityListing]:
        """Apply filters to capability list."""
        filtered = capabilities

        if "category" in filters:
            filtered = [c for c in filtered if c.category == filters["category"]]

        if "min_performance" in filters:
            filtered = [c for c in filtered if c.performance_score >= filters["min_performance"]]

        if "max_price" in filters:
            filtered = [c for c in filtered if c.price <= filters["max_price"]]

        if "certification_level" in filters:
            filtered = [c for c in filtered if c.certification_level == filters["certification_level"]]

        if "tags" in filters:
            required_tags = set(filters["tags"])
            filtered = [c for c in filtered if required_tags.issubset(set(c.tags))]

        return filtered

    def _sort_capabilities(self, capabilities: List[CapabilityListing], sort_by: str) -> List[CapabilityListing]:
        """Sort capabilities by specified criteria."""
        if sort_by == "performance_score":
            return sorted(capabilities, key=lambda x: x.performance_score, reverse=True)
        elif sort_by == "price":
            return sorted(capabilities, key=lambda x: x.price)
        elif sort_by == "usage_count":
            return sorted(capabilities, key=lambda x: x.usage_count, reverse=True)
        elif sort_by == "success_rate":
            return sorted(capabilities, key=lambda x: x.success_rate, reverse=True)
        else:
            return capabilities

    # Background Tasks
    async def _marketplace_maintenance_loop(self):
        """Background task for marketplace maintenance."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes

                # Clean up expired requests
                await self._cleanup_expired_requests()

                # Update capability metrics
                await self._update_capability_metrics()

                # Check for inactive capabilities
                await self._check_inactive_capabilities()

            except Exception as e:
                logger.error(f"Error in marketplace maintenance: {e}")

    async def _price_optimization_loop(self):
        """Background task for price optimization."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour

                # Optimize prices for capabilities that need it
                for capability_id, listing in self.listings.items():
                    if self._should_optimize_price(listing):
                        await self.optimize_pricing(capability_id)

            except Exception as e:
                logger.error(f"Error in price optimization: {e}")

    def _should_optimize_price(self, listing: CapabilityListing) -> bool:
        """Check if a capability price should be optimized."""
        # Optimize if performance has changed significantly
        # or if it hasn't been updated in a while
        time_since_update = datetime.now() - listing.last_updated
        return (
            time_since_update > timedelta(hours=24) or
            listing.performance_score > 0.9 or
            listing.usage_count > 100
        )

    async def _cleanup_expired_requests(self):
        """Clean up expired capability requests."""
        current_time = datetime.now()
        expired_requests = [
            req_id for req_id, req in self.requests.items()
            if req.deadline < current_time
        ]

        for req_id in expired_requests:
            del self.requests[req_id]
            logger.info(f"Cleaned up expired request {req_id}")

    async def _update_capability_metrics(self):
        """Update metrics for all capabilities."""
        for capability_id in self.listings.keys():
            try:
                performance_data = await self._get_capability_performance(capability_id)
                listing = self.listings[capability_id]
                listing.performance_score = performance_data.get("score", listing.performance_score)
                listing.success_rate = performance_data.get("success_rate", listing.success_rate)
                listing.average_execution_time = performance_data.get("avg_execution_time", listing.average_execution_time)
            except Exception as e:
                logger.error(f"Error updating metrics for {capability_id}: {e}")

    async def _check_inactive_capabilities(self):
        """Check for and mark inactive capabilities."""
        current_time = datetime.now()
        for listing in self.listings.values():
            time_since_update = current_time - listing.last_updated
            if time_since_update > timedelta(days=7) and listing.status == CapabilityStatus.AVAILABLE:
                listing.status = CapabilityStatus.MAINTENANCE
                logger.warning(f"Marked capability {listing.capability_id} as maintenance due to inactivity")

    # Analytics Helper Methods
    def _calculate_average_success_rate(self) -> float:
        """Calculate average success rate across all capabilities."""
        if not self.listings:
            return 0.0
        return sum(l.success_rate for l in self.listings.values()) / len(self.listings)

    def _calculate_average_execution_time(self) -> float:
        """Calculate average execution time across all capabilities."""
        if not self.listings:
            return 0.0
        return sum(l.average_execution_time for l in self.listings.values()) / len(self.listings)

    async def _get_top_categories(self) -> List[Dict[str, Any]]:
        """Get top performing categories."""
        category_stats = {}
        for listing in self.listings.values():
            if listing.category not in category_stats:
                category_stats[listing.category] = {
                    "count": 0,
                    "total_performance": 0.0,
                    "total_usage": 0
                }

            stats = category_stats[listing.category]
            stats["count"] += 1
            stats["total_performance"] += listing.performance_score
            stats["total_usage"] += listing.usage_count

        # Calculate averages and sort
        categories = []
        for category, stats in category_stats.items():
            categories.append({
                "category": category,
                "count": stats["count"],
                "avg_performance": stats["total_performance"] / stats["count"],
                "total_usage": stats["total_usage"]
            })

        return sorted(categories, key=lambda x: x["avg_performance"], reverse=True)

    async def _get_certification_distribution(self) -> Dict[str, int]:
        """Get distribution of certification levels."""
        distribution = {}
        for listing in self.listings.values():
            level = listing.certification_level
            distribution[level] = distribution.get(level, 0) + 1
        return distribution

    async def _load_marketplace_data(self):
        """Load existing marketplace data from storage."""
        # This would load from database in production
        # For now, we'll start with empty state
        logger.info("Marketplace data loaded")

    # Advanced recommendation and analysis methods
    async def _get_requester_profile(self, requester_id: str) -> Dict[str, Any]:
        """Get comprehensive requester profile for personalized recommendations."""
        try:
            # Initialize default profile
            profile = {
                "preferences": {},
                "history": [],
                "budget_range": [0, 1000],
                "success_patterns": {},
                "preferred_agents": [],
                "quality_requirements": 0.7
            }

            # Get historical requests for this requester
            historical_requests = [
                req for req in self.requests.values()
                if req.requester_id == requester_id
            ]

            if not historical_requests:
                return profile

            # Analyze historical preferences
            categories_used = {}
            budget_history = []
            successful_interactions = []

            for request in historical_requests:
                # Track category preferences
                if hasattr(request, 'requirements') and 'category' in request.requirements:
                    category = request.requirements['category']
                    categories_used[category] = categories_used.get(category, 0) + 1

                # Track budget patterns
                if hasattr(request, 'budget'):
                    budget_history.append(request.budget)

                # Track successful interactions
                if hasattr(request, 'status') and request.status == 'completed':
                    successful_interactions.append(request)

            # Build preferences from historical data
            if categories_used:
                # Sort categories by usage frequency
                sorted_categories = sorted(categories_used.items(), key=lambda x: x[1], reverse=True)
                profile["preferences"]["preferred_categories"] = [cat for cat, _ in sorted_categories[:3]]
                profile["preferences"]["category_weights"] = dict(sorted_categories)

            # Calculate budget range from history
            if budget_history:
                profile["budget_range"] = [
                    min(budget_history),
                    max(budget_history)
                ]
                profile["preferences"]["avg_budget"] = sum(budget_history) / len(budget_history)

            # Identify success patterns
            if successful_interactions:
                # Find agents that delivered successful results
                successful_agents = {}
                for interaction in successful_interactions:
                    if hasattr(interaction, 'selected_agent') and interaction.selected_agent:
                        agent_id = interaction.selected_agent
                        successful_agents[agent_id] = successful_agents.get(agent_id, 0) + 1

                profile["preferred_agents"] = [
                    agent for agent, _ in sorted(successful_agents.items(), key=lambda x: x[1], reverse=True)[:5]
                ]

            # Set quality requirements based on historical patterns
            if successful_interactions:
                # Calculate average quality score from successful interactions
                quality_scores = []
                for interaction in successful_interactions:
                    if hasattr(interaction, 'quality_score') and interaction.quality_score:
                        quality_scores.append(interaction.quality_score)

                if quality_scores:
                    profile["quality_requirements"] = max(0.5, sum(quality_scores) / len(quality_scores))

            profile["history"] = [
                {
                    "request_id": req.request_id,
                    "category": req.requirements.get('category', 'unknown') if hasattr(req, 'requirements') else 'unknown',
                    "budget": req.budget if hasattr(req, 'budget') else 0,
                    "timestamp": req.created_at if hasattr(req, 'created_at') else datetime.now()
                }
                for req in historical_requests[-10:]  # Last 10 requests
            ]

            logger.debug(f"Built profile for requester {requester_id}: {len(historical_requests)} requests analyzed")
            return profile

        except Exception as e:
            logger.error(f"Error building requester profile for {requester_id}: {e}")
            return {
                "preferences": {},
                "history": [],
                "budget_range": [0, 1000],
                "success_patterns": {},
                "preferred_agents": [],
                "quality_requirements": 0.7
            }

    async def _analyze_requirements(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze requirements using intelligent parsing and categorization."""
        try:
            analysis = {
                "analyzed_needs": [],
                "complexity": "medium",
                "urgency": "normal",
                "estimated_duration": 3600.0,  # 1 hour default
                "required_capabilities": [],
                "quality_threshold": 0.7,
                "budget_recommendation": None
            }

            # Extract and analyze requirements
            requirements = context.get('requirements', {})
            if isinstance(requirements, str):
                # Parse natural language requirements
                analysis.update(self._parse_natural_language_requirements(requirements))
            elif isinstance(requirements, dict):
                # Analyze structured requirements
                analysis.update(self._analyze_structured_requirements(requirements))

            # Analyze context for additional insights
            if 'deadline' in context:
                deadline = context['deadline']
                if isinstance(deadline, str):
                    try:
                        deadline = datetime.fromisoformat(deadline.replace('Z', '+00:00'))
                    except:
                        deadline = datetime.now() + timedelta(hours=24)

                time_until_deadline = (deadline - datetime.now()).total_seconds()
                if time_until_deadline < 3600:  # Less than 1 hour
                    analysis["urgency"] = "critical"
                elif time_until_deadline < 86400:  # Less than 24 hours
                    analysis["urgency"] = "high"
                elif time_until_deadline > 604800:  # More than 1 week
                    analysis["urgency"] = "low"

            # Analyze budget context
            if 'budget' in context:
                budget = context['budget']
                if budget < 10:
                    analysis["complexity"] = "simple"
                    analysis["quality_threshold"] = 0.6
                elif budget > 100:
                    analysis["complexity"] = "complex"
                    analysis["quality_threshold"] = 0.9

                analysis["budget_recommendation"] = self._calculate_budget_recommendation(analysis)

            # Determine required capabilities based on analysis
            analysis["required_capabilities"] = self._identify_required_capabilities(analysis)

            logger.debug(f"Requirements analysis completed: {analysis['complexity']} complexity, {analysis['urgency']} urgency")
            return analysis

        except Exception as e:
            logger.error(f"Error analyzing requirements: {e}")
            return {
                "analyzed_needs": [],
                "complexity": "medium",
                "urgency": "normal",
                "estimated_duration": 3600.0,
                "required_capabilities": [],
                "quality_threshold": 0.7,
                "budget_recommendation": None
            }

    def _parse_natural_language_requirements(self, requirements_text: str) -> Dict[str, Any]:
        """Parse natural language requirements into structured analysis."""
        analysis = {}
        text_lower = requirements_text.lower()

        # Complexity analysis based on keywords
        complex_keywords = ['complex', 'advanced', 'sophisticated', 'comprehensive', 'detailed', 'multi-step']
        simple_keywords = ['simple', 'basic', 'quick', 'easy', 'straightforward']

        if any(keyword in text_lower for keyword in complex_keywords):
            analysis["complexity"] = "complex"
            analysis["estimated_duration"] = 7200.0  # 2 hours
        elif any(keyword in text_lower for keyword in simple_keywords):
            analysis["complexity"] = "simple"
            analysis["estimated_duration"] = 1800.0  # 30 minutes

        # Urgency analysis
        urgent_keywords = ['urgent', 'asap', 'immediately', 'critical', 'emergency']
        if any(keyword in text_lower for keyword in urgent_keywords):
            analysis["urgency"] = "critical"

        # Extract capability needs
        capability_keywords = {
            'data_analysis': ['analyze', 'analysis', 'data', 'statistics', 'metrics'],
            'content_generation': ['write', 'create', 'generate', 'content', 'article'],
            'automation': ['automate', 'script', 'workflow', 'process'],
            'research': ['research', 'investigate', 'find', 'search', 'gather']
        }

        identified_needs = []
        for capability, keywords in capability_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                identified_needs.append(capability)

        analysis["analyzed_needs"] = identified_needs
        return analysis

    def _analyze_structured_requirements(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze structured requirements dictionary."""
        analysis = {}

        # Direct mapping of structured fields
        if 'complexity' in requirements:
            analysis["complexity"] = requirements['complexity']

        if 'urgency' in requirements:
            analysis["urgency"] = requirements['urgency']

        if 'category' in requirements:
            analysis["analyzed_needs"] = [requirements['category']]

        if 'estimated_time' in requirements:
            analysis["estimated_duration"] = float(requirements['estimated_time'])

        return analysis

    def _calculate_budget_recommendation(self, analysis: Dict[str, Any]) -> float:
        """Calculate recommended budget based on analysis."""
        base_budget = 50.0  # Base budget

        # Adjust for complexity
        complexity_multipliers = {
            "simple": 0.5,
            "medium": 1.0,
            "complex": 2.0
        }
        complexity = analysis.get("complexity", "medium")
        budget = base_budget * complexity_multipliers.get(complexity, 1.0)

        # Adjust for urgency
        urgency_multipliers = {
            "low": 0.8,
            "normal": 1.0,
            "high": 1.3,
            "critical": 1.8
        }
        urgency = analysis.get("urgency", "normal")
        budget *= urgency_multipliers.get(urgency, 1.0)

        # Adjust for quality requirements
        quality_threshold = analysis.get("quality_threshold", 0.7)
        if quality_threshold > 0.8:
            budget *= 1.2
        elif quality_threshold > 0.9:
            budget *= 1.5

        return round(budget, 2)

    def _identify_required_capabilities(self, analysis: Dict[str, Any]) -> List[str]:
        """Identify required capabilities based on analysis."""
        capabilities = []

        # Add capabilities based on analyzed needs
        analyzed_needs = analysis.get("analyzed_needs", [])
        capabilities.extend(analyzed_needs)

        # Add capabilities based on complexity
        complexity = analysis.get("complexity", "medium")
        if complexity == "complex":
            capabilities.extend(["project_management", "quality_assurance"])

        # Add capabilities based on urgency
        urgency = analysis.get("urgency", "normal")
        if urgency in ["high", "critical"]:
            capabilities.append("rapid_execution")

        return list(set(capabilities))  # Remove duplicates

    def _calculate_recommendation_score(self, listing: CapabilityListing, base_score: float,
                                      profile: Dict, requirements: Dict) -> float:
        """Calculate final recommendation score with personalization."""
        score = base_score * listing.performance_score

        # Apply profile-based adjustments
        preferred_agents = profile.get("preferred_agents", [])
        if listing.agent_id in preferred_agents:
            score *= 1.2  # 20% bonus for preferred agents

        # Apply category preferences
        category_weights = profile.get("preferences", {}).get("category_weights", {})
        if listing.category in category_weights:
            # Normalize category weight (higher usage = higher score)
            max_weight = max(category_weights.values()) if category_weights else 1
            weight_factor = category_weights[listing.category] / max_weight
            score *= (0.8 + 0.4 * weight_factor)  # Scale between 0.8 and 1.2

        # Apply budget compatibility
        requester_budget = requirements.get("budget", 0)
        if requester_budget > 0 and listing.price > 0:
            if listing.price <= requester_budget:
                # Bonus for being within budget
                price_efficiency = (requester_budget - listing.price) / requester_budget
                score *= (1.0 + 0.1 * price_efficiency)  # Up to 10% bonus
            else:
                # Penalty for being over budget
                over_budget_factor = (listing.price - requester_budget) / requester_budget
                score *= max(0.5, 1.0 - 0.3 * over_budget_factor)  # Up to 30% penalty

        # Apply quality requirements
        quality_requirement = profile.get("quality_requirements", 0.7)
        if listing.performance_score < quality_requirement:
            score *= 0.7  # Penalty for not meeting quality requirements

        return min(1.0, max(0.0, score))  # Clamp between 0 and 1

    async def _generate_recommendations(self, profile: Dict, requirements: Dict) -> List[Tuple[str, float]]:
        """Generate intelligent capability recommendations based on profile and requirements."""
        try:
            recommendations = []

            # Get all available capabilities
            available_capabilities = [
                listing for listing in self.listings.values()
                if listing.status == CapabilityStatus.AVAILABLE
            ]

            if not available_capabilities:
                logger.warning("No available capabilities for recommendations")
                return []

            # Score each capability
            for listing in available_capabilities:
                try:
                    # Calculate base compatibility score
                    base_score = await self._calculate_base_compatibility_score(listing, requirements)

                    # Apply profile-based personalization
                    final_score = self._calculate_recommendation_score(listing, base_score, profile, requirements)

                    # Only include capabilities above minimum threshold
                    min_threshold = profile.get("quality_requirements", 0.7)
                    if final_score >= min_threshold * 0.8:  # Allow slightly lower for recommendations
                        recommendations.append((listing.capability_id, final_score))

                except Exception as e:
                    logger.error(f"Error scoring capability {listing.capability_id}: {e}")
                    continue

            # Sort by score (descending) and return top recommendations
            recommendations.sort(key=lambda x: x[1], reverse=True)

            # Limit to top 10 recommendations
            top_recommendations = recommendations[:10]

            logger.info(f"Generated {len(top_recommendations)} recommendations from {len(available_capabilities)} available capabilities")
            return top_recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            # Fallback to simple scoring
            return [(cap_id, 0.7) for cap_id in list(self.listings.keys())[:5]]

    async def _calculate_base_compatibility_score(self, listing: CapabilityListing, requirements: Dict) -> float:
        """Calculate base compatibility score between listing and requirements."""
        score = 0.0
        factors = 0

        # Category matching
        if 'category' in requirements:
            if listing.category == requirements['category']:
                score += 1.0
            else:
                # Partial credit for related categories
                related_score = self._get_category_similarity(listing.category, requirements['category'])
                score += related_score
            factors += 1

        # Budget compatibility
        if 'budget' in requirements and requirements['budget'] > 0:
            budget = requirements['budget']
            if listing.price <= budget:
                # Perfect score if within budget
                score += 1.0
            else:
                # Partial score based on how close it is
                over_budget_ratio = (listing.price - budget) / budget
                score += max(0.0, 1.0 - over_budget_ratio)
            factors += 1

        # Performance requirements
        if 'min_performance' in requirements:
            min_perf = requirements['min_performance']
            if listing.performance_score >= min_perf:
                score += 1.0
            else:
                # Partial score based on how close it is
                score += listing.performance_score / min_perf
            factors += 1

        # Complexity matching
        if 'complexity' in requirements:
            complexity_scores = {
                'simple': 0.3,
                'medium': 0.6,
                'complex': 1.0
            }
            req_complexity = requirements['complexity']
            listing_complexity = getattr(listing, 'complexity_level', 'medium')

            req_score = complexity_scores.get(req_complexity, 0.6)
            listing_score = complexity_scores.get(listing_complexity, 0.6)

            # Score based on capability to handle required complexity
            if listing_score >= req_score:
                score += 1.0
            else:
                score += listing_score / req_score
            factors += 1

        # Default factors if none specified
        if factors == 0:
            return 0.7  # Default moderate compatibility

        return score / factors

    def _get_category_similarity(self, category1: str, category2: str) -> float:
        """Calculate similarity between two categories."""
        # Define category relationships
        category_groups = {
            'data_analysis': ['analytics', 'statistics', 'reporting', 'visualization'],
            'content_generation': ['writing', 'content', 'documentation', 'copywriting'],
            'automation': ['scripting', 'workflow', 'integration', 'process'],
            'research': ['investigation', 'analysis', 'information_gathering'],
            'ai_ml': ['machine_learning', 'artificial_intelligence', 'prediction', 'classification']
        }

        # Check if categories are in the same group
        for group, categories in category_groups.items():
            if category1 in categories and category2 in categories:
                return 0.8  # High similarity within same group
            elif category1 == group and category2 in categories:
                return 0.9  # Very high similarity (exact match to group)
            elif category2 == group and category1 in categories:
                return 0.9  # Very high similarity (exact match to group)

        # Check for partial string matches
        if category1 in category2 or category2 in category1:
            return 0.6  # Moderate similarity for partial matches

        return 0.1  # Low similarity for unrelated categories

    async def _get_market_data(self, category: str) -> Dict[str, Any]:
        """Get comprehensive market data for pricing optimization."""
        try:
            # Get all listings in this category
            category_listings = [
                listing for listing in self.listings.values()
                if listing.category == category
            ]

            if not category_listings:
                return {
                    "avg_price": 50.0,
                    "demand": 0.5,
                    "supply": 0.5,
                    "price_range": [10.0, 100.0],
                    "total_listings": 0
                }

            # Calculate pricing statistics
            prices = [listing.price for listing in category_listings if listing.price > 0]
            avg_price = sum(prices) / len(prices) if prices else 50.0
            min_price = min(prices) if prices else 10.0
            max_price = max(prices) if prices else 100.0

            # Calculate demand (based on recent requests)
            recent_requests = [
                req for req in self.requests.values()
                if req.requirements.get('category') == category and
                (datetime.now() - req.created_at).days <= 7
            ]
            demand_score = min(1.0, len(recent_requests) / max(1, len(category_listings)))

            # Calculate supply (based on available vs total listings)
            available_listings = [
                listing for listing in category_listings
                if listing.status == CapabilityStatus.AVAILABLE
            ]
            supply_score = len(available_listings) / len(category_listings) if category_listings else 0.0

            return {
                "avg_price": round(avg_price, 2),
                "demand": round(demand_score, 2),
                "supply": round(supply_score, 2),
                "price_range": [round(min_price, 2), round(max_price, 2)],
                "total_listings": len(category_listings),
                "available_listings": len(available_listings),
                "recent_requests": len(recent_requests)
            }

        except Exception as e:
            logger.error(f"Error getting market data for category {category}: {e}")
            return {
                "avg_price": 50.0,
                "demand": 0.5,
                "supply": 0.5,
                "price_range": [10.0, 100.0],
                "total_listings": 0
            }

    async def _calculate_optimal_price(self, listing: CapabilityListing, market_data: Dict) -> float:
        """Calculate optimal price based on market conditions."""
        base_price = market_data.get("avg_price", 10.0)
        performance_multiplier = 1.0 + (listing.performance_score - 0.5)
        demand_multiplier = 1.0 + (market_data.get("demand", 0.5) - 0.5) * 0.5
        return base_price * performance_multiplier * demand_multiplier

    async def _get_price_trends(self) -> Dict[str, Any]:
        """Get price trend analysis."""
        return {"trend": "stable", "avg_change": 0.02, "volatility": 0.1}

    async def _get_demand_patterns(self) -> Dict[str, Any]:
        """Get demand pattern analysis."""
        return {"peak_hours": [9, 14, 16], "seasonal_trends": {}, "growth_rate": 0.15}

    async def _get_supply_analysis(self) -> Dict[str, Any]:
        """Get supply analysis."""
        return {"total_supply": len(self.listings), "utilization_rate": 0.65, "capacity_gaps": []}

    async def _identify_optimization_opportunities(self) -> List[Dict[str, Any]]:
        """Identify optimization opportunities."""
        return [{"type": "pricing", "capability_id": "test", "potential_improvement": 0.15}]

    async def _identify_market_gaps(self) -> List[Dict[str, Any]]:
        """Identify market gaps."""
        return [{"category": "data_analysis", "demand": 0.8, "supply": 0.3}]

    async def _identify_growth_areas(self) -> List[Dict[str, Any]]:
        """Identify growth areas."""
        return [{"area": "ai_automation", "growth_potential": 0.9, "investment_needed": "medium"}]
