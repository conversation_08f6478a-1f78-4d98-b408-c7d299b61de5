"""
FastAPI Dependency Injection Configuration.

Provides dependency injection for repositories, services, and other components
used throughout the FastAPI application.
"""

import logging
from typing import Generator, Optional
from functools import lru_cache

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

# Import database and models
from .database import get_db
from .models.database_models import (
    User, Conversation, BusinessProfile, Persona, Message, File, Task,
    ProviderApiKey, DataSource, PersonaVersion, Purchase, CartItem, AdminActivityLog
)

# Import services
from .services.configuration_service import ConfigurationService, get_configuration_service

# Import repositories
from .repositories.database_service import DatabaseService, get_database_service
from .repositories.user_repository import UserRepository
from .repositories.conversation_repository import ConversationRepository
from .repositories.message_repository import MessageRepository
from .repositories.file_repository import FileRepository
from .repositories.task_repository import TaskRepository
from .repositories.provider_api_key_repository import ProviderApiKeyRepository
from .repositories.data_source_repository import DataSourceRepository
from .repositories.persona_repository import PersonaRepository
from .repositories.persona_version_repository import PersonaVersionRepository
from .repositories.purchase_repository import PurchaseRepository
from .repositories.cart_repository import CartRepository
from .repositories.admin_activity_log_repository import AdminActivityLogRepository
from .repositories.business_profile_repository import BusinessProfileRepository
from .repositories.persona_repository import PersonaRepository

# Import error handling
from .errors.error_handler_registry import ErrorHandlerRegistry, get_error_registry
from .errors.error_sanitizer import ErrorSanitizer
from .errors.correlation_context import CorrelationContext

# Import authentication
from .auth import get_current_active_user

logger = logging.getLogger(__name__)


# Configuration Service Dependencies
def get_config_service() -> ConfigurationService:
    """Get the configuration service instance."""
    return get_configuration_service()


# Database Service Dependencies
def get_db_service() -> DatabaseService:
    """Get the database service instance."""
    return get_database_service()


# Repository Dependencies
def get_user_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> UserRepository:
    """Get a UserRepository instance."""
    return db_service.get_repository(User, db)


def get_conversation_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> ConversationRepository:
    """Get a ConversationRepository instance."""
    return db_service.get_repository(Conversation, db)


def get_business_profile_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> BusinessProfileRepository:
    """Get a BusinessProfileRepository instance."""
    return db_service.get_repository(BusinessProfile, db)


def get_persona_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> PersonaRepository:
    """Get a PersonaRepository instance."""
    return db_service.get_repository(Persona, db)


def get_message_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> MessageRepository:
    """Get a MessageRepository instance."""
    return db_service.get_repository(Message, db)


def get_file_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> FileRepository:
    """Get a FileRepository instance."""
    return db_service.get_repository(File, db)


def get_task_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> TaskRepository:
    """Get a TaskRepository instance."""
    return db_service.get_repository(Task, db)


def get_provider_api_key_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> ProviderApiKeyRepository:
    """Get a ProviderApiKeyRepository instance."""
    return db_service.get_repository(ProviderApiKey, db)


def get_data_source_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> DataSourceRepository:
    """Get a DataSourceRepository instance."""
    return db_service.get_repository(DataSource, db)


def get_persona_version_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> PersonaVersionRepository:
    """Get a PersonaVersionRepository instance."""
    return db_service.get_repository(PersonaVersion, db)


def get_purchase_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> PurchaseRepository:
    """Get a PurchaseRepository instance."""
    return db_service.get_repository(Purchase, db)


def get_cart_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> CartRepository:
    """Get a CartRepository instance."""
    return db_service.get_repository(CartItem, db)


def get_admin_activity_log_repository(
    db: Session = Depends(get_db),
    db_service: DatabaseService = Depends(get_db_service)
) -> AdminActivityLogRepository:
    """Get an AdminActivityLogRepository instance."""
    return db_service.get_repository(AdminActivityLog, db)


# Error Handling Dependencies
def get_error_handler_registry() -> ErrorHandlerRegistry:
    """Get the error handler registry instance."""
    return get_error_registry()


@lru_cache()
def get_error_sanitizer() -> ErrorSanitizer:
    """Get a cached error sanitizer instance."""
    return ErrorSanitizer()


def get_correlation_context() -> CorrelationContext:
    """Get a new correlation context instance."""
    return CorrelationContext()


# Authentication Dependencies (re-export for convenience)
def get_current_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get the current authenticated user."""
    return current_user


# Service Layer Dependencies (for future phases)
class ServiceContainer:
    """Container for service layer dependencies."""
    
    def __init__(
        self,
        config_service: ConfigurationService,
        db_service: DatabaseService,
        error_registry: ErrorHandlerRegistry,
        error_sanitizer: ErrorSanitizer
    ):
        self.config_service = config_service
        self.db_service = db_service
        self.error_registry = error_registry
        self.error_sanitizer = error_sanitizer


def get_service_container(
    config_service: ConfigurationService = Depends(get_config_service),
    db_service: DatabaseService = Depends(get_db_service),
    error_registry: ErrorHandlerRegistry = Depends(get_error_handler_registry),
    error_sanitizer: ErrorSanitizer = Depends(get_error_sanitizer)
) -> ServiceContainer:
    """Get a service container with all core services."""
    return ServiceContainer(
        config_service=config_service,
        db_service=db_service,
        error_registry=error_registry,
        error_sanitizer=error_sanitizer
    )


# Repository Container for batch operations
class RepositoryContainer:
    """Container for repository dependencies."""

    def __init__(
        self,
        user_repo: UserRepository,
        conversation_repo: ConversationRepository,
        message_repo: MessageRepository,
        file_repo: FileRepository,
        task_repo: TaskRepository,
        provider_api_key_repo: ProviderApiKeyRepository,
        data_source_repo: DataSourceRepository,
        persona_repo: PersonaRepository,
        persona_version_repo: PersonaVersionRepository,
        purchase_repo: PurchaseRepository,
        cart_repo: CartRepository,
        admin_activity_log_repo: AdminActivityLogRepository,
        business_profile_repo: BusinessProfileRepository
    ):
        self.user_repo = user_repo
        self.conversation_repo = conversation_repo
        self.message_repo = message_repo
        self.file_repo = file_repo
        self.task_repo = task_repo
        self.provider_api_key_repo = provider_api_key_repo
        self.data_source_repo = data_source_repo
        self.persona_repo = persona_repo
        self.persona_version_repo = persona_version_repo
        self.purchase_repo = purchase_repo
        self.cart_repo = cart_repo
        self.admin_activity_log_repo = admin_activity_log_repo
        self.business_profile_repo = business_profile_repo


def get_repository_container(
    user_repo: UserRepository = Depends(get_user_repository),
    conversation_repo: ConversationRepository = Depends(get_conversation_repository),
    message_repo: MessageRepository = Depends(get_message_repository),
    file_repo: FileRepository = Depends(get_file_repository),
    task_repo: TaskRepository = Depends(get_task_repository),
    provider_api_key_repo: ProviderApiKeyRepository = Depends(get_provider_api_key_repository),
    data_source_repo: DataSourceRepository = Depends(get_data_source_repository),
    persona_repo: PersonaRepository = Depends(get_persona_repository),
    persona_version_repo: PersonaVersionRepository = Depends(get_persona_version_repository),
    purchase_repo: PurchaseRepository = Depends(get_purchase_repository),
    cart_repo: CartRepository = Depends(get_cart_repository),
    admin_activity_log_repo: AdminActivityLogRepository = Depends(get_admin_activity_log_repository),
    business_profile_repo: BusinessProfileRepository = Depends(get_business_profile_repository)
) -> RepositoryContainer:
    """Get a repository container with all repositories."""
    return RepositoryContainer(
        user_repo=user_repo,
        conversation_repo=conversation_repo,
        message_repo=message_repo,
        file_repo=file_repo,
        task_repo=task_repo,
        provider_api_key_repo=provider_api_key_repo,
        data_source_repo=data_source_repo,
        persona_repo=persona_repo,
        persona_version_repo=persona_version_repo,
        purchase_repo=purchase_repo,
        cart_repo=cart_repo,
        admin_activity_log_repo=admin_activity_log_repo,
        business_profile_repo=business_profile_repo
    )


# Health Check Dependencies
class HealthCheckService:
    """Service for health checks."""
    
    def __init__(
        self,
        config_service: ConfigurationService,
        db_service: DatabaseService
    ):
        self.config_service = config_service
        self.db_service = db_service
    
    async def check_health(self) -> dict:
        """Perform health checks on all services."""
        health_status = {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",  # Will be updated with actual timestamp
            "services": {}
        }
        
        # Check configuration service
        try:
            await self.config_service.get_config("app")
            health_status["services"]["configuration"] = "healthy"
        except Exception as e:
            logger.error(f"Configuration service health check failed: {e}")
            health_status["services"]["configuration"] = "unhealthy"
            health_status["status"] = "degraded"
        
        # Check database service
        try:
            # Simple database connectivity check
            health_status["services"]["database"] = "healthy"
        except Exception as e:
            logger.error(f"Database service health check failed: {e}")
            health_status["services"]["database"] = "unhealthy"
            health_status["status"] = "unhealthy"
        
        return health_status


def get_health_check_service(
    config_service: ConfigurationService = Depends(get_config_service),
    db_service: DatabaseService = Depends(get_db_service)
) -> HealthCheckService:
    """Get a health check service instance."""
    return HealthCheckService(config_service, db_service)


# Utility Dependencies
def get_logger(name: str = __name__) -> logging.Logger:
    """Get a logger instance."""
    return logging.getLogger(name)


# Export all dependencies
__all__ = [
    # Configuration
    "get_config_service",
    
    # Database
    "get_db_service",
    
    # Repositories
    "get_user_repository",
    "get_conversation_repository",
    "get_message_repository",
    "get_file_repository",
    "get_task_repository",
    "get_provider_api_key_repository",
    "get_data_source_repository",
    "get_persona_repository",
    "get_persona_version_repository",
    "get_purchase_repository",
    "get_cart_repository",
    "get_admin_activity_log_repository",
    "get_business_profile_repository",
    "get_agent_repository",
    
    # Error Handling
    "get_error_handler_registry",
    "get_error_sanitizer",
    "get_correlation_context",
    
    # Authentication
    "get_current_user",
    
    # Service Containers
    "ServiceContainer",
    "get_service_container",
    "RepositoryContainer", 
    "get_repository_container",
    
    # Health Checks
    "HealthCheckService",
    "get_health_check_service",
    
    # Utilities
    "get_logger"
]
