"""
Dashboard-LangGraph Integration Bridge.

This module provides the bridge between LangGraph workflows and the dashboard system,
enabling real-time workflow-powered widgets and intelligent dashboard generation.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

# CompiledGraph is not available in current LangGraph version
# Using Any type instead

from ..states.agent_state import DatageniusAgentState
from ..events.event_bus import event_bus, LangGraphEvent, EventPriority
from ..monitoring.workflow_monitor import WorkflowMonitor

logger = logging.getLogger(__name__)


class WidgetUpdateType(Enum):
    """Types of widget updates."""
    DATA_REFRESH = "data_refresh"
    CONFIG_CHANGE = "config_change"
    WORKFLOW_RESULT = "workflow_result"
    ERROR_STATE = "error_state"


@dataclass
class WorkflowWidgetConfig:
    """Configuration for workflow-powered widgets."""
    widget_id: str
    dashboard_id: str
    workflow_template: str
    workflow_params: Dict[str, Any] = field(default_factory=dict)
    update_interval: int = 300  # seconds
    auto_refresh: bool = True
    cache_results: bool = True
    cache_ttl: int = 600  # seconds
    error_retry_count: int = 3
    error_retry_delay: int = 30  # seconds


@dataclass
class WorkflowResult:
    """Result from workflow execution."""
    widget_id: str
    workflow_id: str
    execution_id: str
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    execution_time: float = 0.0


class DashboardWorkflowBridge:
    """
    Bridge between LangGraph workflows and dashboard system.
    
    This class manages workflow-powered widgets, handles real-time updates,
    and provides intelligent dashboard generation capabilities.
    """
    
    def __init__(self, workflow_monitor: Optional[WorkflowMonitor] = None):
        self.workflow_monitor = workflow_monitor or WorkflowMonitor()
        self.active_widgets: Dict[str, WorkflowWidgetConfig] = {}
        self.workflow_cache: Dict[str, WorkflowResult] = {}
        self.update_callbacks: Dict[str, List[Callable]] = {}
        self.running_workflows: Dict[str, asyncio.Task] = {}
        self.error_counts: Dict[str, int] = {}
        
        # Subscribe to workflow events
        event_bus.subscribe("workflow.completed", self._handle_workflow_completed)
        event_bus.subscribe("workflow.failed", self._handle_workflow_failed)
        event_bus.subscribe("dashboard.widget_created", self._handle_widget_created)
        event_bus.subscribe("dashboard.widget_deleted", self._handle_widget_deleted)
        
        logger.info("Dashboard-LangGraph bridge initialized")
    
    async def register_workflow_widget(
        self, 
        config: WorkflowWidgetConfig,
        update_callback: Optional[Callable] = None
    ) -> bool:
        """
        Register a workflow-powered widget.
        
        Args:
            config: Widget configuration
            update_callback: Callback for widget updates
            
        Returns:
            bool: Success status
        """
        try:
            # Validate configuration
            if not self._validate_widget_config(config):
                logger.error(f"Invalid widget configuration for {config.widget_id}")
                return False
            
            # Store configuration
            self.active_widgets[config.widget_id] = config
            
            # Register update callback
            if update_callback:
                if config.widget_id not in self.update_callbacks:
                    self.update_callbacks[config.widget_id] = []
                self.update_callbacks[config.widget_id].append(update_callback)
            
            # Start auto-refresh if enabled
            if config.auto_refresh:
                await self._start_widget_refresh(config.widget_id)
            
            # Publish registration event
            await event_bus.publish(LangGraphEvent(
                event_type="dashboard.workflow_widget_registered",
                timestamp=datetime.now(),
                source="dashboard_bridge",
                data={
                    "widget_id": config.widget_id,
                    "dashboard_id": config.dashboard_id,
                    "workflow_template": config.workflow_template
                },
                priority=EventPriority.MEDIUM
            ))
            
            logger.info(f"Registered workflow widget {config.widget_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering workflow widget {config.widget_id}: {e}")
            return False
    
    async def execute_widget_workflow(
        self, 
        widget_id: str, 
        force_refresh: bool = False
    ) -> Optional[WorkflowResult]:
        """
        Execute workflow for a specific widget.
        
        Args:
            widget_id: Widget identifier
            force_refresh: Force execution even if cached result exists
            
        Returns:
            WorkflowResult: Execution result or None if failed
        """
        try:
            config = self.active_widgets.get(widget_id)
            if not config:
                logger.error(f"Widget {widget_id} not registered")
                return None
            
            # Check cache if not forcing refresh
            if not force_refresh and config.cache_results:
                cached_result = self._get_cached_result(widget_id)
                if cached_result:
                    logger.debug(f"Using cached result for widget {widget_id}")
                    return cached_result
            
            # Execute workflow
            execution_id = f"{widget_id}_{datetime.now().timestamp()}"
            start_time = datetime.now()
            
            logger.info(f"Executing workflow for widget {widget_id}")
            
            # Create workflow state
            state = DatageniusAgentState(
                user_query=f"Generate data for widget {widget_id}",
                workflow_params=config.workflow_params,
                metadata={
                    "widget_id": widget_id,
                    "dashboard_id": config.dashboard_id,
                    "execution_id": execution_id
                }
            )
            
            # Execute workflow (placeholder - would integrate with actual workflow execution)
            workflow_data = await self._execute_workflow_template(
                config.workflow_template, 
                state
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Create result
            result = WorkflowResult(
                widget_id=widget_id,
                workflow_id=config.workflow_template,
                execution_id=execution_id,
                data=workflow_data,
                metadata={
                    "execution_time": execution_time,
                    "cache_ttl": config.cache_ttl,
                    "timestamp": start_time.isoformat()
                },
                timestamp=start_time,
                success=True,
                execution_time=execution_time
            )
            
            # Cache result
            if config.cache_results:
                self.workflow_cache[widget_id] = result
            
            # Reset error count on success
            self.error_counts[widget_id] = 0
            
            # Notify callbacks
            await self._notify_widget_update(widget_id, result, WidgetUpdateType.WORKFLOW_RESULT)
            
            logger.info(f"Successfully executed workflow for widget {widget_id} in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error executing workflow for widget {widget_id}: {e}")
            
            # Handle error
            await self._handle_widget_error(widget_id, str(e))
            return None
    
    async def update_widget_config(
        self, 
        widget_id: str, 
        new_config: Dict[str, Any]
    ) -> bool:
        """
        Update widget configuration.
        
        Args:
            widget_id: Widget identifier
            new_config: New configuration parameters
            
        Returns:
            bool: Success status
        """
        try:
            config = self.active_widgets.get(widget_id)
            if not config:
                logger.error(f"Widget {widget_id} not registered")
                return False
            
            # Update configuration
            for key, value in new_config.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            # Clear cache if workflow params changed
            if "workflow_params" in new_config:
                self.workflow_cache.pop(widget_id, None)
            
            # Restart auto-refresh if interval changed
            if "update_interval" in new_config or "auto_refresh" in new_config:
                await self._restart_widget_refresh(widget_id)
            
            # Notify callbacks
            await self._notify_widget_update(widget_id, config, WidgetUpdateType.CONFIG_CHANGE)
            
            logger.info(f"Updated configuration for widget {widget_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating widget config {widget_id}: {e}")
            return False
    
    async def get_widget_status(self, widget_id: str) -> Dict[str, Any]:
        """
        Get current status of a workflow widget.
        
        Args:
            widget_id: Widget identifier
            
        Returns:
            Dict containing widget status information
        """
        config = self.active_widgets.get(widget_id)
        if not config:
            return {"error": "Widget not registered"}
        
        cached_result = self.workflow_cache.get(widget_id)
        is_running = widget_id in self.running_workflows
        error_count = self.error_counts.get(widget_id, 0)
        
        return {
            "widget_id": widget_id,
            "dashboard_id": config.dashboard_id,
            "workflow_template": config.workflow_template,
            "auto_refresh": config.auto_refresh,
            "update_interval": config.update_interval,
            "is_running": is_running,
            "has_cached_result": cached_result is not None,
            "last_execution": cached_result.timestamp.isoformat() if cached_result else None,
            "last_success": cached_result.success if cached_result else None,
            "error_count": error_count,
            "cache_ttl": config.cache_ttl
        }
    
    def _validate_widget_config(self, config: WorkflowWidgetConfig) -> bool:
        """Validate widget configuration."""
        if not config.widget_id or not config.dashboard_id:
            return False
        if not config.workflow_template:
            return False
        if config.update_interval < 30:  # Minimum 30 seconds
            return False
        return True
    
    def _get_cached_result(self, widget_id: str) -> Optional[WorkflowResult]:
        """Get cached result if still valid."""
        cached_result = self.workflow_cache.get(widget_id)
        if not cached_result:
            return None
        
        config = self.active_widgets.get(widget_id)
        if not config or not config.cache_results:
            return None
        
        # Check if cache is still valid
        cache_age = (datetime.now() - cached_result.timestamp).total_seconds()
        if cache_age > config.cache_ttl:
            self.workflow_cache.pop(widget_id, None)
            return None
        
        return cached_result
    
    async def _execute_workflow_template(
        self, 
        template_name: str, 
        state: DatageniusAgentState
    ) -> Dict[str, Any]:
        """Execute workflow template (placeholder implementation)."""
        # This would integrate with the actual workflow template system
        # For now, return mock data based on template type
        
        if "analysis" in template_name.lower():
            return {
                "chart_type": "line",
                "data": [
                    {"x": "Jan", "y": 100},
                    {"x": "Feb", "y": 120},
                    {"x": "Mar", "y": 140}
                ],
                "title": "Analysis Results",
                "insights": ["Upward trend observed", "Growth rate: 20%"]
            }
        elif "dashboard" in template_name.lower():
            return {
                "widgets": [
                    {"type": "metric", "value": 1234, "label": "Total Sales"},
                    {"type": "chart", "chart_type": "bar", "data": []}
                ],
                "layout": "grid",
                "title": "Generated Dashboard"
            }
        else:
            return {
                "result": "Workflow executed successfully",
                "timestamp": datetime.now().isoformat(),
                "template": template_name
            }

    async def _start_widget_refresh(self, widget_id: str):
        """Start auto-refresh task for widget."""
        config = self.active_widgets.get(widget_id)
        if not config or not config.auto_refresh:
            return

        # Cancel existing task if running
        if widget_id in self.running_workflows:
            self.running_workflows[widget_id].cancel()

        # Start new refresh task
        task = asyncio.create_task(self._widget_refresh_loop(widget_id))
        self.running_workflows[widget_id] = task

        logger.debug(f"Started auto-refresh for widget {widget_id}")

    async def _widget_refresh_loop(self, widget_id: str):
        """Auto-refresh loop for widget."""
        try:
            config = self.active_widgets.get(widget_id)
            if not config:
                return

            while config.auto_refresh:
                await asyncio.sleep(config.update_interval)

                # Check if widget still exists and auto-refresh is enabled
                current_config = self.active_widgets.get(widget_id)
                if not current_config or not current_config.auto_refresh:
                    break

                # Execute workflow
                await self.execute_widget_workflow(widget_id, force_refresh=True)

        except asyncio.CancelledError:
            logger.debug(f"Auto-refresh cancelled for widget {widget_id}")
        except Exception as e:
            logger.error(f"Error in auto-refresh loop for widget {widget_id}: {e}")

    async def _restart_widget_refresh(self, widget_id: str):
        """Restart auto-refresh for widget."""
        # Cancel existing task
        if widget_id in self.running_workflows:
            self.running_workflows[widget_id].cancel()
            del self.running_workflows[widget_id]

        # Start new task
        await self._start_widget_refresh(widget_id)

    async def _notify_widget_update(
        self,
        widget_id: str,
        data: Any,
        update_type: WidgetUpdateType
    ):
        """Notify registered callbacks about widget updates."""
        callbacks = self.update_callbacks.get(widget_id, [])

        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(widget_id, data, update_type)
                else:
                    callback(widget_id, data, update_type)
            except Exception as e:
                logger.error(f"Error in widget update callback for {widget_id}: {e}")

        # Publish update event
        await event_bus.publish(LangGraphEvent(
            event_type=f"dashboard.widget_updated.{update_type.value}",
            timestamp=datetime.now(),
            source="dashboard_bridge",
            data={
                "widget_id": widget_id,
                "update_type": update_type.value,
                "data": data if isinstance(data, dict) else str(data)
            },
            priority=EventPriority.MEDIUM
        ))

    async def _handle_widget_error(self, widget_id: str, error_message: str):
        """Handle widget execution errors."""
        # Increment error count
        self.error_counts[widget_id] = self.error_counts.get(widget_id, 0) + 1

        config = self.active_widgets.get(widget_id)
        if not config:
            return

        # Check if we should retry
        if self.error_counts[widget_id] <= config.error_retry_count:
            logger.info(f"Scheduling retry for widget {widget_id} (attempt {self.error_counts[widget_id]})")

            # Schedule retry
            asyncio.create_task(self._retry_widget_execution(widget_id, config.error_retry_delay))
        else:
            logger.error(f"Widget {widget_id} exceeded max retry count, disabling auto-refresh")

            # Disable auto-refresh after max retries
            config.auto_refresh = False
            if widget_id in self.running_workflows:
                self.running_workflows[widget_id].cancel()
                del self.running_workflows[widget_id]

        # Create error result
        error_result = WorkflowResult(
            widget_id=widget_id,
            workflow_id=config.workflow_template,
            execution_id=f"error_{datetime.now().timestamp()}",
            data={},
            metadata={"error_count": self.error_counts[widget_id]},
            timestamp=datetime.now(),
            success=False,
            error_message=error_message
        )

        # Notify callbacks
        await self._notify_widget_update(widget_id, error_result, WidgetUpdateType.ERROR_STATE)

    async def _retry_widget_execution(self, widget_id: str, delay: int):
        """Retry widget execution after delay."""
        await asyncio.sleep(delay)
        await self.execute_widget_workflow(widget_id, force_refresh=True)

    async def _handle_workflow_completed(self, event: LangGraphEvent):
        """Handle workflow completion events."""
        data = event.data
        widget_id = data.get("widget_id")

        if widget_id and widget_id in self.active_widgets:
            logger.debug(f"Workflow completed for widget {widget_id}")
            # Additional processing if needed

    async def _handle_workflow_failed(self, event: LangGraphEvent):
        """Handle workflow failure events."""
        data = event.data
        widget_id = data.get("widget_id")
        error_message = data.get("error_message", "Unknown error")

        if widget_id and widget_id in self.active_widgets:
            await self._handle_widget_error(widget_id, error_message)

    async def _handle_widget_created(self, event: LangGraphEvent):
        """Handle widget creation events."""
        data = event.data
        widget_id = data.get("widget_id")

        if widget_id:
            logger.info(f"Widget {widget_id} created, checking for workflow configuration")
            # Additional processing if needed

    async def _handle_widget_deleted(self, event: LangGraphEvent):
        """Handle widget deletion events."""
        data = event.data
        widget_id = data.get("widget_id")

        if widget_id and widget_id in self.active_widgets:
            await self.unregister_workflow_widget(widget_id)

    async def unregister_workflow_widget(self, widget_id: str) -> bool:
        """
        Unregister a workflow widget.

        Args:
            widget_id: Widget identifier

        Returns:
            bool: Success status
        """
        try:
            # Cancel running workflow
            if widget_id in self.running_workflows:
                self.running_workflows[widget_id].cancel()
                del self.running_workflows[widget_id]

            # Remove from active widgets
            self.active_widgets.pop(widget_id, None)

            # Clear cache
            self.workflow_cache.pop(widget_id, None)

            # Remove callbacks
            self.update_callbacks.pop(widget_id, None)

            # Reset error count
            self.error_counts.pop(widget_id, None)

            # Publish unregistration event
            await event_bus.publish(LangGraphEvent(
                event_type="dashboard.workflow_widget_unregistered",
                timestamp=datetime.now(),
                source="dashboard_bridge",
                data={"widget_id": widget_id},
                priority=EventPriority.MEDIUM
            ))

            logger.info(f"Unregistered workflow widget {widget_id}")
            return True

        except Exception as e:
            logger.error(f"Error unregistering workflow widget {widget_id}: {e}")
            return False

    async def get_all_widget_statuses(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all registered widgets."""
        statuses = {}
        for widget_id in self.active_widgets:
            statuses[widget_id] = await self.get_widget_status(widget_id)
        return statuses

    async def cleanup(self):
        """Cleanup resources."""
        # Cancel all running workflows
        for task in self.running_workflows.values():
            task.cancel()

        # Wait for tasks to complete
        if self.running_workflows:
            await asyncio.gather(*self.running_workflows.values(), return_exceptions=True)

        # Clear all data
        self.active_widgets.clear()
        self.workflow_cache.clear()
        self.update_callbacks.clear()
        self.running_workflows.clear()
        self.error_counts.clear()

        logger.info("Dashboard-LangGraph bridge cleaned up")
