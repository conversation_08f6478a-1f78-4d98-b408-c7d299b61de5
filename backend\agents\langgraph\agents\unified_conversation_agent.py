"""
Unified Conversation Agent Base Class

This module provides a consolidated base class for all agents that use the conversation tool
for AI responses, eliminating duplicate implementations and dependency issues.

This is the single base class for all Datagenius AI personas, consolidating functionality
from previous base classes while maintaining clean, dependency-free architecture.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class AgentCapability:
    """Represents an agent capability with metadata."""
    name: str
    description: str
    category: str = "general"
    confidence: float = 1.0


@dataclass
class AgentResponse:
    """Standardized agent response format."""
    message: str
    metadata: Dict[str, Any]
    success: bool = True
    capabilities_used: List[str] = None
    suggestions: List[str] = None


class UnifiedConversationAgent:
    """
    Unified base class for all Datagenius AI personas.

    This is the single, consolidated base class that provides:
    - AI-powered conversation using the conversation tool
    - Agent-specific specializations and capabilities
    - Workflow completion signals
    - Consistent response format
    - Capability management and discovery
    - Standardized agent information

    All Datagenius AI personas inherit from this class, eliminating the need
    for multiple base classes and ensuring consistent behavior across the platform.
    """

    def __init__(self, agent_id: str, agent_type: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the unified conversation agent.

        Args:
            agent_id: Unique identifier for this agent
            agent_type: Type of agent (concierge, marketing, analysis, etc.)
            config: Optional configuration dictionary
        """
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.config = config or {}
        
        # Define base capabilities that all agents share
        self.base_capabilities = [
            "conversation_management",
            "user_guidance", 
            "ai_conversation",
            "workflow_coordination"
        ]
        
        # Agent-specific capabilities (to be defined by subclasses)
        self.specialized_capabilities = self._get_specialized_capabilities()
        
        # Combined capabilities
        self.capabilities = self.base_capabilities + self.specialized_capabilities
        
        # Agent configuration
        self.agent_name = self._get_agent_name()
        self.agent_description = self._get_agent_description()
        
        logger.info(f"UnifiedConversationAgent {agent_id} ({agent_type}) initialized")

    def _get_specialized_capabilities(self) -> List[str]:
        """Get agent-specific capabilities. Override in subclasses."""
        return []

    def _get_agent_name(self) -> str:
        """Get human-readable agent name. Override in subclasses."""
        return f"{self.agent_type.title()} Assistant"

    def _get_agent_description(self) -> str:
        """Get agent description. Override in subclasses."""
        return f"AI assistant specialized in {self.agent_type} tasks"

    async def process_message(
        self,
        message: str,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a message using the conversation tool for AI responses.

        Args:
            message: The user's message
            user_id: User identifier
            conversation_id: Conversation identifier
            context: Additional context information

        Returns:
            Dict containing the agent's response and metadata
        """
        try:
            logger.debug(f"{self.agent_type} agent processing message: {message[:50]}...")

            # Handle greeting generation specially
            if message == "generate_greeting" or (context and context.get("is_initial_greeting")):
                logger.info(f"Generating initial greeting for {self.agent_type}")
                response_message = self._generate_greeting_message(context)
            else:
                # Use conversation tool for AI responses
                try:
                    response_message = await self._generate_ai_response(message, context)
                except Exception as e:
                    logger.error(f"Error generating AI response: {e}")
                    # Fallback to basic response if AI fails
                    response_message = self._generate_basic_response(message, context)
            
            # Add agent-specific enhancements
            enhanced_response = await self._enhance_response(message, response_message, context)
            
            return {
                "message": enhanced_response,
                "metadata": {
                    "agent_type": self.agent_type,
                    "agent_id": self.agent_id,
                    "capabilities_used": ["conversation_management", "user_guidance", "ai_conversation"],
                    "processing_method": "conversation_tool",
                    "timestamp": datetime.now().isoformat(),
                    "workflow_complete": True,  # Mark workflow as complete to prevent loops
                    "next_action": "END"  # Explicitly indicate workflow should end
                },
                "success": True  # Ensure success flag is set
            }
            
        except Exception as e:
            logger.error(f"Error in {self.agent_type} process_message: {e}", exc_info=True)
            return self._generate_fallback_response(e)

    async def _generate_ai_response(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate an AI response using the conversation tool.
        
        Args:
            message: The user's message
            context: Additional context
            
        Returns:
            AI-generated response message string
        """
        try:
            # Import the conversation tool
            from ...tools.mcp.conversation_tool import ConversationTool
            
            # Create conversation tool instance
            conversation_tool = ConversationTool()
            
            # Prepare arguments for the conversation tool
            tool_arguments = {
                "message": message,
                "conversation_history": context.get("conversation_history", []) if context else [],
                "user_context": {
                    "user_id": context.get("user_id") if context else None,
                    "business_profile": context.get("business_profile", {}) if context else {},
                    "agent_type": self.agent_type,
                    "agent_name": self.agent_name
                },
                "intent_type": self._determine_intent_type(message),
                "confidence": 0.8,
                "is_continuing_conversation": bool(context and context.get("conversation_history")),
                # Use default provider and model for now
                "provider": "groq",
                "model": "llama-3.1-8b-instant",
                "temperature": 0.7
            }
            
            # Execute the conversation tool
            result = await conversation_tool.execute(tool_arguments)
            
            # Extract the response from the tool result
            if result.get("success", False) and "content" in result:
                response_content = result["content"]
                if isinstance(response_content, list) and len(response_content) > 0:
                    # Extract text from the first content item
                    first_content = response_content[0]
                    if isinstance(first_content, dict) and "text" in first_content:
                        return first_content["text"]
                    elif isinstance(first_content, str):
                        return first_content
                elif isinstance(response_content, str):
                    return response_content
            
            # If we couldn't extract a proper response, fall back
            logger.warning("Could not extract proper response from conversation tool")
            return self._generate_basic_response(message, context)
            
        except Exception as e:
            logger.error(f"Error in _generate_ai_response: {e}")
            # Fall back to basic response
            return self._generate_basic_response(message, context)

    def _determine_intent_type(self, message: str) -> str:
        """Determine the intent type for the conversation tool. Override in subclasses."""
        return "general_question"

    def _generate_basic_response(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a basic response using simple logic. Override in subclasses for specialization.
        
        Args:
            message: The user's message
            context: Additional context
            
        Returns:
            Response message string
        """
        try:
            message_lower = message.lower()
            
            # Handle common queries
            if any(word in message_lower for word in ["hello", "hi", "hey", "start"]):
                return self._generate_greeting_message(context)
            
            if any(word in message_lower for word in ["help", "what can you do", "capabilities"]):
                return self._generate_capabilities_response()
            
            # Default helpful response
            return f"I'm your {self.agent_name}. I'm here to help you with {self.agent_type} tasks. What would you like to work on?"
            
        except Exception as e:
            logger.error(f"Error generating basic response: {e}")
            return f"I'm here to help with {self.agent_type} tasks! How can I assist you?"

    def _generate_greeting_message(self, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate an appropriate greeting message. Override in subclasses for specialization.

        Args:
            context: Optional context information

        Returns:
            Greeting message string
        """
        try:
            # Get user name if available
            user_name = ""
            if context:
                user_name = context.get("user_name", "")
                if not user_name and "user_info" in context:
                    user_name = context["user_info"].get("name", "")

            # Create personalized greeting
            if user_name:
                greeting = f"Hello {user_name}! "
            else:
                greeting = "Hello! "

            # Add main agent message
            greeting += f"I'm your **{self.agent_name}** - {self.agent_description}.\n\n"
            greeting += self._get_greeting_specialization()
            greeting += "\n\nWhat would you like to work on today?"

            return greeting

        except Exception as e:
            logger.error(f"Error generating greeting message: {e}")
            return f"Hello! I'm your {self.agent_name}. How can I help you today?"

    def _get_greeting_specialization(self) -> str:
        """Get agent-specific greeting content. Override in subclasses."""
        return f"I specialize in {self.agent_type} tasks and I'm here to help you achieve your goals."

    def _generate_capabilities_response(self) -> str:
        """Generate a response about agent capabilities. Override in subclasses."""
        return f"I'm a {self.agent_name} with the following capabilities:\n\n" + \
               "\n".join([f"• {cap.replace('_', ' ').title()}" for cap in self.specialized_capabilities])

    async def _enhance_response(self, original_message: str, base_response: str, 
                              context: Optional[Dict[str, Any]] = None) -> str:
        """
        Enhance the response with agent-specific information. Override in subclasses.
        
        Args:
            original_message: The user's original message
            base_response: The base response from the AI or basic logic
            context: Additional context
            
        Returns:
            Enhanced response string
        """
        # Base implementation just returns the base response
        # Subclasses can override to add agent-specific enhancements
        return base_response

    def _generate_fallback_response(self, error: Exception) -> Dict[str, Any]:
        """Generate a fallback response when processing fails."""
        return {
            "message": f"Hello! I'm your {self.agent_name}. I'm experiencing some technical difficulties, but I'm here to help you with {self.agent_type} tasks. Please try again or let me know how I can assist you.",
            "metadata": {
                "error": str(error),
                "agent_type": self.agent_type,
                "agent_id": self.agent_id,
                "fallback_response": True,
                "timestamp": datetime.now().isoformat(),
                "workflow_complete": True,
                "next_action": "END"
            },
            "success": True
        }

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this agent."""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "agent_name": self.agent_name,
            "description": self.agent_description,
            "capabilities": self.capabilities,
            "specialized_capabilities": self.specialized_capabilities,
            "fallback_mode": False  # This is NOT a fallback agent
        }

    # Compatibility methods for existing code
    async def handle_with_own_capabilities(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle message with agent capabilities (compatibility method).

        Args:
            message: The user's message
            context: Additional context

        Returns:
            Dict containing the response
        """
        return await self.process_message(message, context=context)

    async def process_user_message(self, message: str, user_id: str, conversation_id: str,
                                 context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process user message (compatibility method for legacy code).

        Args:
            message: The user's message
            user_id: User identifier
            conversation_id: Conversation identifier
            context: Additional context

        Returns:
            Dict containing the response
        """
        return await self.process_message(message, user_id=user_id, conversation_id=conversation_id, context=context)

    def has_capability(self, capability: str) -> bool:
        """Check if the agent has a specific capability."""
        return capability in self.capabilities

    def get_capabilities_by_category(self, category: str = None) -> List[str]:
        """Get capabilities, optionally filtered by category."""
        if category is None:
            return self.capabilities
        # For now, return all capabilities since we don't have category metadata
        # This can be enhanced later if needed
        return self.capabilities

    def is_compatible_with(self, other_agent_type: str) -> bool:
        """Check if this agent can work with another agent type."""
        # Basic compatibility - can be overridden by subclasses
        return True

    def get_status(self) -> Dict[str, Any]:
        """Get current agent status."""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "agent_name": self.agent_name,
            "is_active": True,
            "capabilities_count": len(self.capabilities),
            "specialized_capabilities_count": len(self.specialized_capabilities),
            "base_capabilities_count": len(self.base_capabilities)
        }
