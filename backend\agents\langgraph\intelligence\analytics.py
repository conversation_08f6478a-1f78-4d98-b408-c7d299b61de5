"""
Intelligence Analytics System for Cross-Agent Collaboration.

This module provides comprehensive analytics and monitoring for cross-agent
intelligence sharing, collaboration effectiveness, and performance optimization.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, Counter
import statistics

from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


@dataclass
class CollaborationMetrics:
    """Metrics for agent collaboration effectiveness."""
    total_collaborations: int
    successful_collaborations: int
    average_duration_minutes: float
    consensus_rate: float
    agent_participation_rate: Dict[str, float]
    collaboration_types: Dict[str, int]


@dataclass
class InsightSharingMetrics:
    """Metrics for cross-agent insight sharing."""
    total_insights_shared: int
    insights_by_type: Dict[str, int]
    insights_by_agent: Dict[str, int]
    average_relevance_score: float
    insight_utilization_rate: float
    top_insight_contributors: List[Tuple[str, int]]


@dataclass
class BusinessContextMetrics:
    """Metrics for business context utilization."""
    profiles_accessed: int
    context_updates_shared: int
    context_utilization_by_agent: Dict[str, int]
    average_context_freshness_hours: float
    most_active_profiles: List[Tuple[str, int]]


class IntelligenceAnalytics:
    """
    Analytics system for monitoring cross-agent intelligence and collaboration.
    
    Provides comprehensive metrics and insights on:
    - Agent collaboration effectiveness
    - Insight sharing patterns and utilization
    - Business context integration
    - Performance optimization opportunities
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the intelligence analytics system.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration parameters
        self.metrics_retention_days = self.config.get("metrics_retention_days", 30)
        self.analysis_window_hours = self.config.get("analysis_window_hours", 24)
        self.min_collaboration_duration = self.config.get("min_collaboration_duration", 1)  # minutes
    
    def analyze_collaboration_effectiveness(
        self,
        state: UnifiedDatageniusState,
        time_window_hours: Optional[int] = None
    ) -> CollaborationMetrics:
        """
        Analyze collaboration effectiveness metrics.
        
        Args:
            state: Current workflow state
            time_window_hours: Optional time window for analysis
            
        Returns:
            Collaboration effectiveness metrics
        """
        try:
            window_hours = time_window_hours or self.analysis_window_hours
            cutoff_time = datetime.now() - timedelta(hours=window_hours)
            
            # Analyze pending and completed decisions (collaborations)
            total_collaborations = 0
            successful_collaborations = 0
            collaboration_durations = []
            consensus_achieved = 0
            agent_participation = defaultdict(int)
            collaboration_types = Counter()
            
            # Analyze completed decisions
            for decision in state.get("pending_decisions", []):
                decision_time = datetime.fromisoformat(decision.get("created_at", ""))
                if decision_time < cutoff_time:
                    continue
                
                total_collaborations += 1
                
                # Check if collaboration was successful
                if decision.get("status") == "consensus_reached":
                    successful_collaborations += 1
                    consensus_achieved += 1
                    
                    # Calculate duration if completed
                    if decision.get("completed_at"):
                        start_time = datetime.fromisoformat(decision["created_at"])
                        end_time = datetime.fromisoformat(decision["completed_at"])
                        duration_minutes = (end_time - start_time).total_seconds() / 60
                        if duration_minutes >= self.min_collaboration_duration:
                            collaboration_durations.append(duration_minutes)
                
                # Track agent participation
                for agent in decision.get("participating_agents", []):
                    agent_participation[agent] += 1
                
                # Track collaboration type
                collab_type = decision.get("data", {}).get("collaboration_type", "unknown")
                collaboration_types[collab_type] += 1
            
            # Calculate metrics
            avg_duration = statistics.mean(collaboration_durations) if collaboration_durations else 0.0
            consensus_rate = consensus_achieved / total_collaborations if total_collaborations > 0 else 0.0
            
            # Calculate participation rates
            total_participations = sum(agent_participation.values())
            participation_rates = {
                agent: count / total_participations if total_participations > 0 else 0.0
                for agent, count in agent_participation.items()
            }
            
            return CollaborationMetrics(
                total_collaborations=total_collaborations,
                successful_collaborations=successful_collaborations,
                average_duration_minutes=avg_duration,
                consensus_rate=consensus_rate,
                agent_participation_rate=participation_rates,
                collaboration_types=dict(collaboration_types)
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing collaboration effectiveness: {e}")
            return CollaborationMetrics(
                total_collaborations=0,
                successful_collaborations=0,
                average_duration_minutes=0.0,
                consensus_rate=0.0,
                agent_participation_rate={},
                collaboration_types={}
            )
    
    def analyze_insight_sharing_patterns(
        self,
        state: UnifiedDatageniusState,
        time_window_hours: Optional[int] = None
    ) -> InsightSharingMetrics:
        """
        Analyze insight sharing patterns and effectiveness.
        
        Args:
            state: Current workflow state
            time_window_hours: Optional time window for analysis
            
        Returns:
            Insight sharing metrics
        """
        try:
            window_hours = time_window_hours or self.analysis_window_hours
            cutoff_time = datetime.now() - timedelta(hours=window_hours)
            
            # Analyze shared insights
            total_insights = 0
            insights_by_type = Counter()
            insights_by_agent = Counter()
            relevance_scores = []
            utilized_insights = 0
            
            for insight in state.get("shared_insights", []):
                insight_time = datetime.fromisoformat(insight.get("timestamp", ""))
                if insight_time < cutoff_time:
                    continue
                
                total_insights += 1
                
                # Track by type and agent
                insight_type = insight.get("insight_type", "unknown")
                source_agent = insight.get("source_agent", "unknown")
                
                insights_by_type[insight_type] += 1
                insights_by_agent[source_agent] += 1
                
                # Track relevance scores
                relevance = insight.get("relevance_score", 1.0)
                relevance_scores.append(relevance)
                
                # Check if insight was utilized (has target agents)
                if insight.get("target_agents"):
                    utilized_insights += 1
            
            # Calculate metrics
            avg_relevance = statistics.mean(relevance_scores) if relevance_scores else 0.0
            utilization_rate = utilized_insights / total_insights if total_insights > 0 else 0.0
            
            # Get top contributors
            top_contributors = insights_by_agent.most_common(5)
            
            return InsightSharingMetrics(
                total_insights_shared=total_insights,
                insights_by_type=dict(insights_by_type),
                insights_by_agent=dict(insights_by_agent),
                average_relevance_score=avg_relevance,
                insight_utilization_rate=utilization_rate,
                top_insight_contributors=top_contributors
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing insight sharing patterns: {e}")
            return InsightSharingMetrics(
                total_insights_shared=0,
                insights_by_type={},
                insights_by_agent={},
                average_relevance_score=0.0,
                insight_utilization_rate=0.0,
                top_insight_contributors=[]
            )
    
    def analyze_business_context_utilization(
        self,
        state: UnifiedDatageniusState,
        time_window_hours: Optional[int] = None
    ) -> BusinessContextMetrics:
        """
        Analyze business context utilization patterns.
        
        Args:
            state: Current workflow state
            time_window_hours: Optional time window for analysis
            
        Returns:
            Business context utilization metrics
        """
        try:
            window_hours = time_window_hours or self.analysis_window_hours
            cutoff_time = datetime.now() - timedelta(hours=window_hours)
            
            # Analyze business context usage
            profiles_accessed = set()
            context_updates = 0
            agent_context_usage = Counter()
            context_freshness_hours = []
            profile_activity = Counter()
            
            # Check business context in state
            business_context = state.get("business_context", {})
            if business_context:
                # Track profile access
                if state.get("business_profile_id"):
                    profiles_accessed.add(state["business_profile_id"])
                    profile_activity[state["business_profile_id"]] += 1
                
                # Check context freshness
                if business_context.get("loaded_at"):
                    loaded_time = datetime.fromisoformat(business_context["loaded_at"])
                    freshness_hours = (datetime.now() - loaded_time).total_seconds() / 3600
                    context_freshness_hours.append(freshness_hours)
                
                # Count context updates
                updates = business_context.get("updates", [])
                for update in updates:
                    update_time = datetime.fromisoformat(update.get("timestamp", ""))
                    if update_time >= cutoff_time:
                        context_updates += 1
                        source_agent = update.get("source_agent", "unknown")
                        agent_context_usage[source_agent] += 1
            
            # Analyze cross-agent context
            cross_agent_context = state.get("cross_agent_context", {})
            if cross_agent_context.get("business_profile"):
                profile_id = cross_agent_context["business_profile"].get("id")
                if profile_id:
                    profiles_accessed.add(profile_id)
                    profile_activity[profile_id] += 1
            
            # Calculate metrics
            avg_freshness = statistics.mean(context_freshness_hours) if context_freshness_hours else 0.0
            most_active_profiles = profile_activity.most_common(5)
            
            return BusinessContextMetrics(
                profiles_accessed=len(profiles_accessed),
                context_updates_shared=context_updates,
                context_utilization_by_agent=dict(agent_context_usage),
                average_context_freshness_hours=avg_freshness,
                most_active_profiles=most_active_profiles
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing business context utilization: {e}")
            return BusinessContextMetrics(
                profiles_accessed=0,
                context_updates_shared=0,
                context_utilization_by_agent={},
                average_context_freshness_hours=0.0,
                most_active_profiles=[]
            )
    
    def generate_intelligence_report(
        self,
        state: UnifiedDatageniusState,
        time_window_hours: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Generate comprehensive intelligence analytics report.
        
        Args:
            state: Current workflow state
            time_window_hours: Optional time window for analysis
            
        Returns:
            Comprehensive analytics report
        """
        try:
            # Gather all metrics
            collaboration_metrics = self.analyze_collaboration_effectiveness(state, time_window_hours)
            insight_metrics = self.analyze_insight_sharing_patterns(state, time_window_hours)
            context_metrics = self.analyze_business_context_utilization(state, time_window_hours)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                collaboration_metrics, insight_metrics, context_metrics
            )
            
            return {
                "report_generated_at": datetime.now().isoformat(),
                "analysis_window_hours": time_window_hours or self.analysis_window_hours,
                "collaboration_metrics": collaboration_metrics.__dict__,
                "insight_sharing_metrics": insight_metrics.__dict__,
                "business_context_metrics": context_metrics.__dict__,
                "recommendations": recommendations,
                "summary": {
                    "total_collaborations": collaboration_metrics.total_collaborations,
                    "total_insights_shared": insight_metrics.total_insights_shared,
                    "active_business_profiles": context_metrics.profiles_accessed,
                    "overall_effectiveness_score": self._calculate_effectiveness_score(
                        collaboration_metrics, insight_metrics, context_metrics
                    )
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating intelligence report: {e}")
            return {
                "report_generated_at": datetime.now().isoformat(),
                "error": str(e),
                "summary": {"overall_effectiveness_score": 0.0}
            }
    
    def _generate_recommendations(
        self,
        collab_metrics: CollaborationMetrics,
        insight_metrics: InsightSharingMetrics,
        context_metrics: BusinessContextMetrics
    ) -> List[str]:
        """Generate optimization recommendations based on metrics."""
        recommendations = []
        
        # Collaboration recommendations
        if collab_metrics.consensus_rate < 0.7:
            recommendations.append("Consider adjusting consensus thresholds or improving agent coordination")
        
        if collab_metrics.average_duration_minutes > 30:
            recommendations.append("Optimize collaboration workflows to reduce average duration")
        
        # Insight sharing recommendations
        if insight_metrics.insight_utilization_rate < 0.5:
            recommendations.append("Improve insight targeting and relevance scoring")
        
        if insight_metrics.average_relevance_score < 0.7:
            recommendations.append("Enhance insight quality and relevance algorithms")
        
        # Context utilization recommendations
        if context_metrics.average_context_freshness_hours > 24:
            recommendations.append("Increase frequency of business context updates")
        
        if context_metrics.context_updates_shared < 5:
            recommendations.append("Encourage more active business context sharing between agents")
        
        return recommendations
    
    def _calculate_effectiveness_score(
        self,
        collab_metrics: CollaborationMetrics,
        insight_metrics: InsightSharingMetrics,
        context_metrics: BusinessContextMetrics
    ) -> float:
        """Calculate overall effectiveness score (0.0-1.0)."""
        try:
            # Weight different aspects
            collaboration_score = collab_metrics.consensus_rate * 0.4
            insight_score = (insight_metrics.average_relevance_score * insight_metrics.insight_utilization_rate) * 0.4
            context_score = min(context_metrics.context_updates_shared / 10.0, 1.0) * 0.2
            
            return collaboration_score + insight_score + context_score
            
        except Exception:
            return 0.0
