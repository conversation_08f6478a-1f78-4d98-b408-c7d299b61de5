"""
Unified Configuration Service for Datagenius.

This service provides a centralized configuration management system that consolidates
all configuration sources and provides a single source of truth for application settings.

Phase 1 Implementation:
- Unified configuration hierarchy with environment-specific overrides
- Pydantic validation for all configuration models
- Hot-reloading capabilities for dynamic configuration updates
- Security integration with encrypted sensitive values
- Configuration audit logging and validation
"""

import os
import json
import yaml
import logging
import asyncio
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Union, Type, TypeVar
from dataclasses import dataclass, field
from contextlib import asynccontextmanager

from pydantic import BaseModel, Field, ValidationError, field_validator
from cryptography.fernet import Fernet
import aiofiles
import aiofiles.os

from ..settings.base import BaseConfig
from ..settings.app import AppConfig
from ..settings.database import DatabaseConfig
from ..settings.security import SecurityConfig
from ..settings.llm import LLMConfig
from ..utils.error_handlers import MCPError

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)


@dataclass
class ConfigurationMetadata:
    """Metadata for configuration entries."""
    source: str
    loaded_at: datetime
    version: str = "1.0.0"
    checksum: Optional[str] = None
    encrypted: bool = False
    validation_errors: List[str] = field(default_factory=list)


class ConfigurationError(MCPError):
    """Configuration-specific error."""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)
        self.config_key = config_key


class ConfigurationValidationError(ConfigurationError):
    """Configuration validation error."""
    
    def __init__(self, message: str, validation_errors: List[str], **kwargs):
        super().__init__(message, error_code="CONFIG_VALIDATION_ERROR", **kwargs)
        self.validation_errors = validation_errors


class ConfigurationSource:
    """Abstract base for configuration sources."""
    
    def __init__(self, name: str, priority: int = 0):
        self.name = name
        self.priority = priority
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    async def load_config(self, key: str) -> Optional[Dict[str, Any]]:
        """Load configuration by key."""
        raise NotImplementedError
    
    async def save_config(self, key: str, config: Dict[str, Any]) -> bool:
        """Save configuration by key."""
        raise NotImplementedError
    
    async def list_configs(self) -> List[str]:
        """List available configuration keys."""
        raise NotImplementedError


class FileConfigurationSource(ConfigurationSource):
    """File-based configuration source."""
    
    def __init__(self, config_dir: Path, name: str = "file", priority: int = 1):
        super().__init__(name, priority)
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.supported_extensions = ['.yaml', '.yml', '.json']
    
    async def load_config(self, key: str) -> Optional[Dict[str, Any]]:
        """Load configuration from file."""
        for ext in self.supported_extensions:
            config_file = self.config_dir / f"{key}{ext}"
            if await aiofiles.os.path.exists(config_file):
                try:
                    async with aiofiles.open(config_file, 'r', encoding='utf-8') as f:
                        content = await f.read()
                        if ext in ['.yaml', '.yml']:
                            return yaml.safe_load(content)
                        elif ext == '.json':
                            return json.loads(content)
                except Exception as e:
                    self.logger.error(f"Error loading config {key} from {config_file}: {e}")
        return None
    
    async def save_config(self, key: str, config: Dict[str, Any]) -> bool:
        """Save configuration to file."""
        try:
            config_file = self.config_dir / f"{key}.yaml"
            async with aiofiles.open(config_file, 'w', encoding='utf-8') as f:
                await f.write(yaml.dump(config, default_flow_style=False))
            return True
        except Exception as e:
            self.logger.error(f"Error saving config {key}: {e}")
            return False
    
    async def list_configs(self) -> List[str]:
        """List available configuration files."""
        configs = set()
        for ext in self.supported_extensions:
            for config_file in self.config_dir.glob(f"*{ext}"):
                configs.add(config_file.stem)
        return sorted(list(configs))


class EnvironmentConfigurationSource(ConfigurationSource):
    """Environment variable configuration source."""
    
    def __init__(self, prefix: str = "DATAGENIUS_", name: str = "environment", priority: int = 2):
        super().__init__(name, priority)
        self.prefix = prefix
    
    async def load_config(self, key: str) -> Optional[Dict[str, Any]]:
        """Load configuration from environment variables."""
        try:
            # Try direct environment variable
            env_var = f"{self.prefix}{key.upper()}"
            config_json = os.getenv(env_var)
            
            if config_json:
                return json.loads(config_json)
            
            # Try nested environment variables
            config = {}
            prefix_key = f"{self.prefix}{key.upper()}_"
            for env_key, env_value in os.environ.items():
                if env_key.startswith(prefix_key):
                    config_key = env_key[len(prefix_key):].lower()
                    # Try to parse as JSON, fallback to string
                    try:
                        config[config_key] = json.loads(env_value)
                    except json.JSONDecodeError:
                        config[config_key] = env_value
            
            return config if config else None
        except Exception as e:
            self.logger.error(f"Error loading config {key} from environment: {e}")
            return None
    
    async def save_config(self, key: str, config: Dict[str, Any]) -> bool:
        """Environment variables are read-only."""
        return False
    
    async def list_configs(self) -> List[str]:
        """List available environment configurations."""
        configs = set()
        for env_key in os.environ.keys():
            if env_key.startswith(self.prefix):
                # Extract config key
                config_key = env_key[len(self.prefix):].split('_')[0].lower()
                configs.add(config_key)
        return sorted(list(configs))


class ConfigurationService:
    """
    Unified Configuration Service.
    
    Provides centralized configuration management with:
    - Multiple configuration sources with priority ordering
    - Pydantic validation for type safety
    - Hot-reloading capabilities
    - Security integration with encryption
    - Configuration audit logging
    """
    
    def __init__(
        self,
        config_dir: Optional[Path] = None,
        environment: str = "development",
        enable_hot_reload: bool = True,
        encryption_key: Optional[str] = None
    ):
        self.environment = environment
        self.enable_hot_reload = enable_hot_reload
        self.logger = logging.getLogger(__name__)
        
        # Configuration storage
        self._config_cache: Dict[str, Any] = {}
        self._config_metadata: Dict[str, ConfigurationMetadata] = {}
        self._config_models: Dict[str, Type[BaseModel]] = {}
        
        # Configuration sources (ordered by priority)
        self._sources: List[ConfigurationSource] = []
        
        # Hot-reload monitoring
        self._reload_tasks: Dict[str, asyncio.Task] = {}
        self._reload_callbacks: Dict[str, List[callable]] = {}
        
        # Encryption support
        self._encryption_key = encryption_key
        self._cipher = None
        if encryption_key:
            self._cipher = Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)
        
        # Initialize default sources
        self._initialize_sources(config_dir)
        
        # Register default configuration models
        self._register_default_models()
    
    def _initialize_sources(self, config_dir: Optional[Path]):
        """Initialize default configuration sources."""
        # File source (lowest priority)
        if config_dir is None:
            config_dir = Path(__file__).parent.parent.parent / "config"
        
        file_source = FileConfigurationSource(config_dir, priority=1)
        self.add_source(file_source)
        
        # Environment source (highest priority)
        env_source = EnvironmentConfigurationSource(priority=2)
        self.add_source(env_source)
    
    def _register_default_models(self):
        """Register default configuration models."""
        self.register_model("app", AppConfig)
        self.register_model("database", DatabaseConfig)
        self.register_model("security", SecurityConfig)
        self.register_model("llm", LLMConfig)

    def add_source(self, source: ConfigurationSource):
        """Add a configuration source."""
        self._sources.append(source)
        # Sort by priority (higher priority first)
        self._sources.sort(key=lambda s: s.priority, reverse=True)
        self.logger.info(f"Added configuration source: {source.name} (priority: {source.priority})")

    def register_model(self, key: str, model_class: Type[T]):
        """Register a Pydantic model for configuration validation."""
        self._config_models[key] = model_class
        self.logger.debug(f"Registered configuration model: {key} -> {model_class.__name__}")

    async def load_config(
        self,
        key: str,
        model_class: Optional[Type[T]] = None,
        use_cache: bool = True,
        merge_sources: bool = True
    ) -> Optional[T]:
        """
        Load configuration by key with optional validation.

        Args:
            key: Configuration key
            model_class: Optional Pydantic model for validation
            use_cache: Whether to use cached configuration
            merge_sources: Whether to merge configurations from multiple sources

        Returns:
            Validated configuration instance or None
        """
        # Check cache first
        if use_cache and key in self._config_cache:
            cached_config = self._config_cache[key]
            if model_class:
                try:
                    return model_class(**cached_config)
                except ValidationError as e:
                    self.logger.error(f"Cached config validation failed for {key}: {e}")
            else:
                return cached_config

        # Load from sources
        config_data = await self._load_from_sources(key, merge_sources)
        if not config_data:
            return None

        # Apply environment-specific overrides
        config_data = await self._apply_environment_overrides(key, config_data)

        # Decrypt sensitive values
        config_data = self._decrypt_sensitive_values(config_data)

        # Validate with model if provided
        validated_config = None
        validation_errors = []

        if model_class or key in self._config_models:
            model = model_class or self._config_models[key]
            try:
                validated_config = model(**config_data)
                config_data = validated_config.model_dump()
            except ValidationError as e:
                validation_errors = [str(err) for err in e.errors()]
                self.logger.error(f"Configuration validation failed for {key}: {validation_errors}")
                raise ConfigurationValidationError(
                    f"Configuration validation failed for {key}",
                    validation_errors=validation_errors,
                    config_key=key
                )

        # Cache the configuration
        self._config_cache[key] = config_data
        self._config_metadata[key] = ConfigurationMetadata(
            source="merged" if merge_sources else "single",
            loaded_at=datetime.now(timezone.utc),
            validation_errors=validation_errors
        )

        # Set up hot-reload monitoring if enabled
        if self.enable_hot_reload:
            await self._setup_hot_reload(key)

        return validated_config if validated_config else config_data

    async def _load_from_sources(self, key: str, merge_sources: bool) -> Optional[Dict[str, Any]]:
        """Load configuration from sources."""
        if merge_sources:
            # Merge configurations from all sources (lower priority sources first)
            merged_config = {}
            for source in reversed(self._sources):
                try:
                    config = await source.load_config(key)
                    if config:
                        merged_config.update(config)
                        self.logger.debug(f"Merged config from {source.name} for key {key}")
                except Exception as e:
                    self.logger.error(f"Error loading from source {source.name}: {e}")

            return merged_config if merged_config else None
        else:
            # Load from first available source (highest priority first)
            for source in self._sources:
                try:
                    config = await source.load_config(key)
                    if config:
                        self.logger.debug(f"Loaded config from {source.name} for key {key}")
                        return config
                except Exception as e:
                    self.logger.error(f"Error loading from source {source.name}: {e}")

            return None

    async def _apply_environment_overrides(self, key: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment-specific configuration overrides."""
        env_key = f"{key}_{self.environment}"
        env_overrides = await self._load_from_sources(env_key, merge_sources=True)

        if env_overrides:
            config.update(env_overrides)
            self.logger.debug(f"Applied environment overrides for {key} in {self.environment}")

        return config

    def _decrypt_sensitive_values(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Decrypt sensitive configuration values."""
        if not self._cipher:
            return config

        def decrypt_recursive(obj):
            if isinstance(obj, dict):
                return {k: decrypt_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [decrypt_recursive(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith("encrypted:"):
                try:
                    encrypted_data = obj[10:]  # Remove "encrypted:" prefix
                    return self._cipher.decrypt(encrypted_data.encode()).decode()
                except Exception as e:
                    self.logger.error(f"Failed to decrypt value: {e}")
                    return obj
            else:
                return obj

        return decrypt_recursive(config)

    async def _setup_hot_reload(self, key: str):
        """Set up hot-reload monitoring for a configuration key."""
        if key in self._reload_tasks:
            return  # Already monitoring

        async def monitor_config():
            """Monitor configuration for changes."""
            last_modified = datetime.now(timezone.utc)

            while True:
                try:
                    await asyncio.sleep(5)  # Check every 5 seconds

                    # Check if configuration has changed
                    current_config = await self._load_from_sources(key, merge_sources=True)
                    if current_config:
                        # Simple change detection (could be improved with checksums)
                        if key not in self._config_cache or self._config_cache[key] != current_config:
                            self.logger.info(f"Configuration change detected for {key}")

                            # Reload configuration
                            await self.reload_config(key)

                            # Notify callbacks
                            await self._notify_reload_callbacks(key, current_config)

                            last_modified = datetime.now(timezone.utc)

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Error monitoring config {key}: {e}")

        # Start monitoring task
        self._reload_tasks[key] = asyncio.create_task(monitor_config())
        self.logger.debug(f"Started hot-reload monitoring for {key}")

    async def reload_config(self, key: str):
        """Manually reload a configuration key."""
        try:
            # Clear cache
            if key in self._config_cache:
                del self._config_cache[key]
            if key in self._config_metadata:
                del self._config_metadata[key]

            # Reload configuration
            model_class = self._config_models.get(key)
            await self.load_config(key, model_class, use_cache=False)

            self.logger.info(f"Successfully reloaded configuration: {key}")
        except Exception as e:
            self.logger.error(f"Error reloading configuration {key}: {e}")
            raise ConfigurationError(f"Failed to reload configuration: {key}", config_key=key)

    def add_reload_callback(self, key: str, callback: callable):
        """Add a callback to be notified when configuration changes."""
        if key not in self._reload_callbacks:
            self._reload_callbacks[key] = []
        self._reload_callbacks[key].append(callback)
        self.logger.debug(f"Added reload callback for {key}")

    async def _notify_reload_callbacks(self, key: str, new_config: Dict[str, Any]):
        """Notify callbacks of configuration changes."""
        callbacks = self._reload_callbacks.get(key, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(key, new_config)
                else:
                    callback(key, new_config)
            except Exception as e:
                self.logger.error(f"Error in reload callback for {key}: {e}")

    async def save_config(self, key: str, config: Union[Dict[str, Any], BaseModel]) -> bool:
        """Save configuration to the highest priority writable source."""
        try:
            # Convert Pydantic model to dict if needed
            if isinstance(config, BaseModel):
                config_data = config.model_dump()
            else:
                config_data = config

            # Encrypt sensitive values
            config_data = self._encrypt_sensitive_values(config_data)

            # Try to save to sources (highest priority first)
            for source in self._sources:
                try:
                    if await source.save_config(key, config_data):
                        self.logger.info(f"Saved configuration {key} to {source.name}")

                        # Update cache
                        self._config_cache[key] = config_data
                        self._config_metadata[key] = ConfigurationMetadata(
                            source=source.name,
                            loaded_at=datetime.now(timezone.utc)
                        )

                        return True
                except Exception as e:
                    self.logger.error(f"Error saving to source {source.name}: {e}")

            return False
        except Exception as e:
            self.logger.error(f"Error saving configuration {key}: {e}")
            raise ConfigurationError(f"Failed to save configuration: {key}", config_key=key)

    def _encrypt_sensitive_values(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive configuration values."""
        if not self._cipher:
            return config

        # Define sensitive keys that should be encrypted
        sensitive_keys = {
            'password', 'secret', 'key', 'token', 'api_key', 'private_key',
            'jwt_secret', 'database_password', 'redis_password'
        }

        def encrypt_recursive(obj, parent_key=""):
            if isinstance(obj, dict):
                return {k: encrypt_recursive(v, k.lower()) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [encrypt_recursive(item, parent_key) for item in obj]
            elif isinstance(obj, str) and any(sensitive in parent_key for sensitive in sensitive_keys):
                if not obj.startswith("encrypted:"):
                    try:
                        encrypted_data = self._cipher.encrypt(obj.encode()).decode()
                        return f"encrypted:{encrypted_data}"
                    except Exception as e:
                        self.logger.error(f"Failed to encrypt value for {parent_key}: {e}")
                return obj
            else:
                return obj

        return encrypt_recursive(config)

    async def get_config_metadata(self, key: str) -> Optional[ConfigurationMetadata]:
        """Get metadata for a configuration key."""
        return self._config_metadata.get(key)

    async def list_configurations(self) -> List[str]:
        """List all available configuration keys."""
        all_configs = set()
        for source in self._sources:
            try:
                configs = await source.list_configs()
                all_configs.update(configs)
            except Exception as e:
                self.logger.error(f"Error listing configs from {source.name}: {e}")

        return sorted(list(all_configs))

    async def validate_all_configurations(self) -> Dict[str, List[str]]:
        """Validate all registered configurations."""
        validation_results = {}

        for key, model_class in self._config_models.items():
            try:
                await self.load_config(key, model_class, use_cache=False)
                validation_results[key] = []  # No errors
            except ConfigurationValidationError as e:
                validation_results[key] = e.validation_errors
            except Exception as e:
                validation_results[key] = [str(e)]

        return validation_results

    async def cleanup(self):
        """Clean up resources and stop monitoring tasks."""
        # Cancel all reload tasks
        for key, task in self._reload_tasks.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        self._reload_tasks.clear()
        self.logger.info("Configuration service cleanup completed")

    @asynccontextmanager
    async def managed_lifecycle(self):
        """Context manager for proper lifecycle management."""
        try:
            yield self
        finally:
            await self.cleanup()


# Global configuration service instance
_config_service: Optional[ConfigurationService] = None


def get_configuration_service(
    config_dir: Optional[Path] = None,
    environment: Optional[str] = None,
    **kwargs
) -> ConfigurationService:
    """Get the global configuration service instance."""
    global _config_service

    if _config_service is None:
        env = environment or os.getenv("ENVIRONMENT", "development")
        _config_service = ConfigurationService(
            config_dir=config_dir,
            environment=env,
            **kwargs
        )

    return _config_service


async def initialize_configuration_service(**kwargs) -> ConfigurationService:
    """Initialize the global configuration service."""
    global _config_service

    if _config_service is not None:
        await _config_service.cleanup()

    _config_service = ConfigurationService(**kwargs)
    return _config_service
