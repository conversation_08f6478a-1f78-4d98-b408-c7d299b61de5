"""
Marketing Persona

This module provides a consolidated marketing agent that uses the unified conversation agent base
for consistent AI-powered responses and specialized marketing capabilities.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .unified_conversation_agent import UnifiedConversationAgent

logger = logging.getLogger(__name__)


class MarketingPersona(UnifiedConversationAgent):
    """
    Marketing persona that uses the unified conversation base.
    
    This persona provides:
    - Marketing strategy development
    - Campaign planning and optimization
    - Content creation and copywriting
    - Brand development guidance
    - Customer segmentation and targeting
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the marketing persona.
        
        Args:
            config: Optional configuration for the persona
        """
        # Get agent_id from config or use default
        agent_id = config.get('agent_id', 'marketing') if config else 'marketing'
        
        # Initialize parent class
        super().__init__(
            agent_id=agent_id,
            agent_type="marketing",
            config=config
        )
        
        # Marketing-specific configuration
        self.target_audiences = config.get('target_audiences', ['B2B', 'B2C', 'Enterprise', 'SMB']) if config else ['B2B', 'B2C', 'Enterprise', 'SMB']
        self.content_types = config.get('content_types', ['blog', 'social', 'email', 'ads', 'landing_page']) if config else ['blog', 'social', 'email', 'ads', 'landing_page']
        self.marketing_channels = config.get('marketing_channels', ['social_media', 'email', 'content', 'paid_ads', 'seo']) if config else ['social_media', 'email', 'content', 'paid_ads', 'seo']
        
        logger.info("MarketingPersona initialized")

    def _get_specialized_capabilities(self) -> List[str]:
        """Get marketing-specific capabilities."""
        return [
            "marketing_strategy",
            "campaign_planning",
            "content_creation",
            "market_analysis",
            "brand_development",
            "social_media_strategy",
            "advertising_optimization",
            "customer_segmentation",
            "lead_generation",
            "conversion_optimization"
        ]

    def _get_agent_name(self) -> str:
        """Get human-readable agent name."""
        return "Marketing Expert"

    def _get_agent_description(self) -> str:
        """Get agent description."""
        return "Strategic marketing assistant for campaign planning, content creation, and brand development"

    def _determine_intent_type(self, message: str) -> str:
        """Determine the intent type for marketing conversations."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["campaign", "advertising", "ads"]):
            return "campaign_planning"
        elif any(word in message_lower for word in ["content", "copy", "writing"]):
            return "content_creation"
        elif any(word in message_lower for word in ["strategy", "plan", "approach"]):
            return "strategy_development"
        elif any(word in message_lower for word in ["brand", "branding", "identity"]):
            return "brand_development"
        elif any(word in message_lower for word in ["social", "social media", "instagram", "facebook"]):
            return "social_media_marketing"
        else:
            return "general_marketing"

    def _get_greeting_specialization(self) -> str:
        """Get marketing-specific greeting content."""
        return ("**🎯 I specialize in:**\n"
                "• **Marketing Strategy** - Comprehensive planning and positioning\n"
                "• **Campaign Development** - From concept to execution\n"
                "• **Content Creation** - Compelling copy and creative assets\n"
                "• **Brand Development** - Building strong brand identity\n"
                "• **Customer Segmentation** - Targeting the right audiences\n"
                "• **Performance Optimization** - Maximizing ROI and conversions")

    def _generate_capabilities_response(self) -> str:
        """Generate a response about marketing capabilities."""
        return ("I'm your **Marketing Expert** with comprehensive capabilities:\n\n"
                "**🎯 Strategy & Planning:**\n"
                "• Marketing strategy development\n"
                "• Campaign planning and optimization\n"
                "• Market analysis and competitive research\n\n"
                "**✍️ Content & Creative:**\n"
                "• Content creation and copywriting\n"
                "• Brand development and messaging\n"
                "• Social media strategy\n\n"
                "**📊 Analytics & Optimization:**\n"
                "• Customer segmentation\n"
                "• Lead generation strategies\n"
                "• Conversion optimization\n\n"
                f"**Available Channels:** {', '.join(self.marketing_channels)}\n"
                f"**Content Types:** {', '.join(self.content_types)}\n"
                f"**Target Audiences:** {', '.join(self.target_audiences)}")

    async def _enhance_response(self, original_message: str, base_response: str, 
                              context: Optional[Dict[str, Any]] = None) -> str:
        """Enhance the response with marketing-specific information."""
        try:
            enhanced_response = base_response
            
            # Add marketing tips for relevant queries
            if self._is_campaign_related(original_message):
                enhanced_response += "\n\n💡 **Marketing Tip**: Consider A/B testing different approaches to optimize your campaign performance."
            
            # Add audience insights for segmentation queries
            if self._is_audience_related(original_message):
                enhanced_response += "\n\n🎯 **Audience Insight**: Understanding your target audience's pain points and motivations is key to effective messaging."
            
            # Add content suggestions
            if self._is_content_related(original_message):
                enhanced_response += "\n\n✍️ **Content Strategy**: Focus on providing value to your audience while subtly showcasing your expertise."
            
            # Add channel recommendations
            if self._is_strategy_related(original_message):
                enhanced_response += f"\n\n📢 **Recommended Channels**: {', '.join(self.marketing_channels[:3])} are great starting points for most businesses."
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing marketing response: {e}")
            return base_response

    def _is_campaign_related(self, message: str) -> bool:
        """Check if the message is campaign-related."""
        campaign_keywords = ["campaign", "advertising", "ads", "promotion", "launch"]
        return any(keyword in message.lower() for keyword in campaign_keywords)

    def _is_audience_related(self, message: str) -> bool:
        """Check if the message is audience-related."""
        audience_keywords = ["audience", "target", "customer", "segment", "persona"]
        return any(keyword in message.lower() for keyword in audience_keywords)

    def _is_content_related(self, message: str) -> bool:
        """Check if the message is content-related."""
        content_keywords = ["content", "copy", "writing", "blog", "social media", "email"]
        return any(keyword in message.lower() for keyword in content_keywords)

    def _is_strategy_related(self, message: str) -> bool:
        """Check if the message is strategy-related."""
        strategy_keywords = ["strategy", "plan", "approach", "framework", "roadmap"]
        return any(keyword in message.lower() for keyword in strategy_keywords)

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this marketing persona."""
        base_info = super().get_agent_info()
        base_info.update({
            "specialization": "marketing_strategy_and_content",
            "target_audiences": self.target_audiences,
            "content_types": self.content_types,
            "marketing_channels": self.marketing_channels,
            "supports_campaign_planning": True,
            "supports_content_creation": True,
            "supports_brand_development": True
        })
        return base_info


# Backward compatibility aliases
RefactoredMarketingAgent = MarketingPersona
UserSelectedMarketingAgent = MarketingPersona
MarketingAgent = MarketingPersona
