#!/usr/bin/env python3
"""
Test script to verify PersonaWorkflowBuilder can be instantiated without errors.
"""

import sys
import os
import asyncio
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_persona_workflow_builder():
    """Test PersonaWorkflowBuilder instantiation and basic functionality."""
    try:
        logger.info("Testing PersonaWorkflowBuilder instantiation...")
        
        # Import the PersonaWorkflowBuilder
        from agents.langgraph.core.persona_workflow_builder import PersonaWorkflowBuilder
        
        # Try to instantiate the class
        builder = PersonaWorkflowBuilder()
        logger.info("✅ PersonaWorkflowBuilder instantiated successfully!")
        
        # Test that all methodology frameworks are properly mapped
        logger.info("Testing methodology framework mappings...")
        expected_frameworks = [
            "UNDERSTAND_ASSESS_EXECUTE_DELIVER",
            "AGILE_ANALYTICS", 
            "LEAN_STARTUP",
            "DESIGN_THINKING"
        ]
        
        for framework in expected_frameworks:
            if framework in builder.methodology_frameworks:
                method = builder.methodology_frameworks[framework]
                if callable(method):
                    logger.info(f"✅ Framework '{framework}' properly mapped to method '{method.__name__}'")
                else:
                    logger.error(f"❌ Framework '{framework}' mapped to non-callable: {method}")
                    return False
            else:
                logger.error(f"❌ Framework '{framework}' not found in methodology_frameworks")
                return False
        
        logger.info("✅ All methodology frameworks properly mapped!")
        
        # Test that helper methods exist
        helper_methods = [
            '_should_continue_sprint',
            '_should_continue_lean_cycle', 
            '_should_continue_design_cycle'
        ]
        
        for method_name in helper_methods:
            if hasattr(builder, method_name):
                method = getattr(builder, method_name)
                if callable(method):
                    logger.info(f"✅ Helper method '{method_name}' exists and is callable")
                else:
                    logger.error(f"❌ Helper method '{method_name}' exists but is not callable")
                    return False
            else:
                logger.error(f"❌ Helper method '{method_name}' not found")
                return False
        
        logger.info("✅ All helper methods exist and are callable!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing PersonaWorkflowBuilder: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_persona_workflow_builder())
    if success:
        print("\n🎉 All tests passed! PersonaWorkflowBuilder is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Tests failed! Check the errors above.")
        sys.exit(1)
