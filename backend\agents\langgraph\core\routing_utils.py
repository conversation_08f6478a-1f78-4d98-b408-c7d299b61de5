"""
Routing Utilities for LangGraph User-Centric Architecture.

This module provides centralized utility functions for routing logic,
eliminating code duplication and improving maintainability.
"""

import logging
from typing import Dict, Any, Optional, List
from .routing_constants import (
    RoutingConstants, 
    RoutingContext, 
    ValidationError, 
    AgentNotFoundError,
    format_agent_node_name,
    is_concierge_agent
)
from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class RoutingValidator:
    """Centralized validation for routing operations."""
    
    @staticmethod
    def validate_state_parameter(state: UnifiedDatageniusState, method_name: str) -> bool:
        """
        Centralized state validation for all routing methods.
        
        Args:
            state: State to validate
            method_name: Name of calling method for logging
            
        Returns:
            True if state is valid, False otherwise
        """
        if state is None:
            logger.error(f"❌ State is None in {method_name}")
            return False
            
        if not isinstance(state, dict):
            logger.error(f"❌ State is not a dict in {method_name}: {type(state)}")
            return False
            
        return True
    
    @staticmethod
    def validate_agent_id(agent_id: str, agent_nodes: Dict[str, Any], context: str = "") -> bool:
        """
        Validate agent ID format and availability.
        
        Args:
            agent_id: Agent ID to validate
            agent_nodes: Available agent registry
            context: Context for logging
            
        Returns:
            True if agent ID is valid and available
        """
        if not agent_id or not isinstance(agent_id, str):
            logger.warning(f"⚠️ Invalid agent ID format: {agent_id} {context}")
            return False
            
        if agent_id not in agent_nodes:
            available_agents = list(agent_nodes.keys())
            logger.warning(f"⚠️ Agent '{agent_id}' not available in {available_agents} {context}")

            # Try to find a similar agent ID (for debugging)
            similar_agents = [a for a in available_agents if agent_id.replace('-', '').replace('_', '') in a.replace('-', '').replace('_', '')]
            if similar_agents:
                logger.info(f"🔍 Similar agents found: {similar_agents}")

            return False
            
        return True


class FallbackAgentSelector:
    """Centralized fallback agent selection logic."""
    
    def __init__(self, agent_nodes: Dict[str, Any]):
        """
        Initialize with agent registry.
        
        Args:
            agent_nodes: Available agent registry
        """
        self.agent_nodes = agent_nodes
        self._concierge_agent_cache = None
        self._initialize_cache()
    
    def _initialize_cache(self) -> None:
        """Initialize cached agent lookups for performance."""
        for agent_id in self.agent_nodes.keys():
            if is_concierge_agent(agent_id):
                self._concierge_agent_cache = agent_id
                logger.debug(f"🔧 Cached concierge agent: {agent_id}")
                break
    
    def get_concierge_agent(self) -> Optional[str]:
        """
        Get concierge agent ID with caching for performance.
        
        Returns:
            Concierge agent ID if available, None otherwise
        """
        # Use cached value if available and still valid
        if (self._concierge_agent_cache and 
            self._concierge_agent_cache in self.agent_nodes):
            return self._concierge_agent_cache
        
        # Refresh cache if stale
        self._initialize_cache()
        return self._concierge_agent_cache
    
    def get_fallback_agent(self, context: RoutingContext) -> str:
        """
        Centralized fallback agent selection logic.
        
        Args:
            context: Context for the fallback selection
            
        Returns:
            Fallback agent node name
            
        Raises:
            AgentNotFoundError: When no agents are available
        """
        context_str = context.value if isinstance(context, RoutingContext) else str(context)
        logger.info(f"{RoutingConstants.LOG_FALLBACK_CONCIERGE} for context: {context_str}")
        
        # Priority 1: Concierge agent
        concierge_agent = self.get_concierge_agent()
        if concierge_agent:
            logger.info(f"{RoutingConstants.SUCCESS_CONCIERGE_FALLBACK}: {concierge_agent}")
            return format_agent_node_name(concierge_agent)
        
        # Priority 2: First available agent
        if self.agent_nodes:
            fallback_agent = next(iter(self.agent_nodes.keys()))
            logger.info(f"{RoutingConstants.SUCCESS_FIRST_AVAILABLE}: {fallback_agent}")
            return format_agent_node_name(fallback_agent)
        
        # Emergency case
        logger.error(f"❌ {RoutingConstants.ERROR_NO_AGENTS_AVAILABLE} for {context_str}")
        raise AgentNotFoundError(f"No agents available for {context_str} routing")
    
    def refresh_cache(self) -> None:
        """Refresh the agent cache when agent registry changes."""
        self._concierge_agent_cache = None
        self._initialize_cache()


class RoutingDecisionMaker:
    """Centralized routing decision logic."""
    
    def __init__(self, agent_nodes: Dict[str, Any]):
        """
        Initialize with agent registry.
        
        Args:
            agent_nodes: Available agent registry
        """
        self.agent_nodes = agent_nodes
        self.validator = RoutingValidator()
        self.fallback_selector = FallbackAgentSelector(agent_nodes)
    
    def determine_entry_point(self, state: UnifiedDatageniusState) -> str:
        """
        Determine workflow entry point based on user-centric routing.
        
        Args:
            state: Current workflow state
            
        Returns:
            Entry point node name
            
        Raises:
            ValidationError: If state is invalid
            AgentNotFoundError: If no agents are available
        """
        if not self.validator.validate_state_parameter(state, "determine_entry_point"):
            raise ValidationError(RoutingConstants.ERROR_INVALID_STATE)
        
        logger.info(RoutingConstants.LOG_ENTRY_POINT_DETERMINATION)
        
        # Priority 1: Use selected agent if available
        selected_agent = state.get("selected_agent")
        logger.debug(f"🔍 Selected agent from state: {selected_agent}")
        logger.debug(f"🔍 Available agents: {list(self.agent_nodes.keys())}")

        if selected_agent and self.validator.validate_agent_id(selected_agent, self.agent_nodes, "(selected)"):
            logger.info(f"{RoutingConstants.SUCCESS_SELECTED_AGENT}: {selected_agent}")
            return format_agent_node_name(selected_agent)
        elif selected_agent:
            logger.warning(f"⚠️ Selected agent '{selected_agent}' failed validation")
        
        # Priority 2: Use current agent if no selected agent
        current_agent = state.get("current_agent")
        if current_agent and self.validator.validate_agent_id(current_agent, self.agent_nodes, "(current)"):
            logger.info(f"{RoutingConstants.SUCCESS_CURRENT_AGENT}: {current_agent}")
            return format_agent_node_name(current_agent)
        
        # Priority 3: Fallback logic
        try:
            return self.fallback_selector.get_fallback_agent(RoutingContext.ENTRY_POINT)
        except AgentNotFoundError:
            # Emergency fallback to routing node
            logger.warning(RoutingConstants.WARNING_NO_AGENTS)
            return RoutingConstants.ROUTING_FALLBACK
    
    def determine_tool_routing_target(self, state: UnifiedDatageniusState) -> str:
        """
        Determine where to route after tool execution.
        
        Args:
            state: Current workflow state
            
        Returns:
            Target agent node name
            
        Raises:
            ValidationError: If state is invalid
            AgentNotFoundError: If no agents are available
        """
        if not self.validator.validate_state_parameter(state, "determine_tool_routing_target"):
            raise ValidationError(RoutingConstants.ERROR_INVALID_STATE)
        
        logger.info(RoutingConstants.LOG_TOOL_ROUTING)
        
        # Priority 1: Route back to selected agent
        selected_agent = state.get("selected_agent")
        if selected_agent and self.validator.validate_agent_id(selected_agent, self.agent_nodes, "(tool->selected)"):
            logger.info(f"✅ Tool routing to selected agent: {selected_agent}")
            return format_agent_node_name(selected_agent)
        
        # Priority 2: Route back to current agent
        current_agent = state.get("current_agent")
        if current_agent and self.validator.validate_agent_id(current_agent, self.agent_nodes, "(tool->current)"):
            logger.info(f"✅ Tool routing to current agent: {current_agent}")
            return format_agent_node_name(current_agent)
        
        # Priority 3: Fallback logic
        return self.fallback_selector.get_fallback_agent(RoutingContext.TOOL_ROUTING)
    
    def update_agent_registry(self, agent_nodes: Dict[str, Any]) -> None:
        """
        Update agent registry and refresh caches.
        
        Args:
            agent_nodes: New agent registry
        """
        self.agent_nodes = agent_nodes
        self.fallback_selector.agent_nodes = agent_nodes
        self.fallback_selector.refresh_cache()
        logger.debug(f"🔄 Updated agent registry with {len(agent_nodes)} agents")


def create_routing_decision_maker(agent_nodes: Dict[str, Any]) -> RoutingDecisionMaker:
    """
    Factory function to create a routing decision maker.
    
    Args:
        agent_nodes: Available agent registry
        
    Returns:
        Configured RoutingDecisionMaker instance
    """
    return RoutingDecisionMaker(agent_nodes)
