# Chunking Performance Configuration
# Optimized settings for different use cases and performance requirements

# Performance profiles
performance_profiles:
  # Speed-optimized profile for real-time applications
  speed_optimized:
    description: "Optimized for fast processing and low latency"
    embedding_model: "sentence-transformers/all-MiniLM-L6-v2"  # Fast, 384 dimensions
    chunking_strategy:
      chunk_size: 600
      chunk_overlap: 100
      semantic_splitting: false
      preserve_structure: false
    batch_processing:
      enabled: true
      batch_size: 32
      parallel_workers: 4
    caching:
      enabled: true
      cache_embeddings: true
      cache_chunks: true
    
  # Quality-optimized profile for high accuracy requirements
  quality_optimized:
    description: "Optimized for high-quality embeddings and semantic understanding"
    embedding_model: "BAAI/bge-base-en-v1.5"  # High quality, 768 dimensions
    chunking_strategy:
      chunk_size: 1200
      chunk_overlap: 300
      semantic_splitting: true
      preserve_structure: true
    batch_processing:
      enabled: true
      batch_size: 16
      parallel_workers: 2
    caching:
      enabled: true
      cache_embeddings: true
      cache_chunks: true
      
  # Balanced profile for general use
  balanced:
    description: "Balanced performance and quality for general applications"
    embedding_model: "BAAI/bge-small-en-v1.5"  # Good balance, 384 dimensions
    chunking_strategy:
      chunk_size: 800
      chunk_overlap: 160
      semantic_splitting: true
      preserve_structure: true
    batch_processing:
      enabled: true
      batch_size: 24
      parallel_workers: 3
    caching:
      enabled: true
      cache_embeddings: true
      cache_chunks: false
      
  # Memory-optimized profile for resource-constrained environments
  memory_optimized:
    description: "Optimized for low memory usage"
    embedding_model: "sentence-transformers/all-MiniLM-L6-v2"
    chunking_strategy:
      chunk_size: 400
      chunk_overlap: 50
      semantic_splitting: false
      preserve_structure: false
    batch_processing:
      enabled: true
      batch_size: 8
      parallel_workers: 1
    caching:
      enabled: false
      cache_embeddings: false
      cache_chunks: false

# Content-specific optimizations
content_optimizations:
  technical_documents:
    profile: "quality_optimized"
    custom_settings:
      chunk_size: 1000
      chunk_overlap: 200
      preserve_code_blocks: true
      extract_technical_terms: true
      
  research_papers:
    profile: "quality_optimized"
    custom_settings:
      chunk_size: 1500
      chunk_overlap: 300
      preserve_citations: true
      extract_key_concepts: true
      
  legal_documents:
    profile: "quality_optimized"
    custom_settings:
      chunk_size: 1200
      chunk_overlap: 250
      preserve_legal_structure: true
      extract_legal_entities: true
      
  financial_reports:
    profile: "balanced"
    custom_settings:
      chunk_size: 800
      chunk_overlap: 160
      preserve_tables: true
      extract_financial_metrics: true
      
  general_text:
    profile: "balanced"
    custom_settings:
      chunk_size: 1000
      chunk_overlap: 200
      
  structured_data:
    profile: "speed_optimized"
    custom_settings:
      chunk_size: 600
      chunk_overlap: 100
      preserve_structure: true

# Query optimization settings
query_optimization:
  # Different strategies for different query types
  semantic_search:
    embedding_model: "BAAI/bge-base-en-v1.5"
    top_k: 10
    score_threshold: 0.7
    rerank: true
    
  keyword_search:
    embedding_model: "sentence-transformers/all-MiniLM-L6-v2"
    top_k: 20
    score_threshold: 0.6
    rerank: false
    
  hybrid_search:
    embedding_model: "BAAI/bge-base-en-v1.5"
    top_k: 15
    score_threshold: 0.65
    rerank: true
    combine_scores: true

# Performance monitoring thresholds
performance_thresholds:
  embedding_time_ms:
    warning: 1000
    critical: 3000
  chunk_processing_time_ms:
    warning: 500
    critical: 1500
  query_response_time_ms:
    warning: 2000
    critical: 5000
  memory_usage_mb:
    warning: 512
    critical: 1024

# Caching configuration
caching:
  embedding_cache:
    enabled: true
    max_size: 10000
    ttl_seconds: 3600
    
  chunk_cache:
    enabled: true
    max_size: 5000
    ttl_seconds: 1800
    
  query_cache:
    enabled: true
    max_size: 1000
    ttl_seconds: 300

# Batch processing configuration
batch_processing:
  default_batch_size: 16
  max_batch_size: 64
  min_batch_size: 4
  adaptive_batching: true
  
  # Adaptive batching rules
  adaptive_rules:
    - condition: "document_size < 1000"
      batch_size: 32
    - condition: "document_size < 10000"
      batch_size: 16
    - condition: "document_size >= 10000"
      batch_size: 8

# Vector database optimization
vector_db_optimization:
  qdrant:
    collection_config:
      vectors:
        size: 384  # Default for all-MiniLM-L6-v2
        distance: "Cosine"
      optimizers_config:
        deleted_threshold: 0.2
        vacuum_min_vector_number: 1000
        default_segment_number: 0
        max_segment_size: 20000
        memmap_threshold: 50000
        indexing_threshold: 20000
        flush_interval_sec: 5
        max_optimization_threads: 2
    
    search_config:
      hnsw_ef: 128
      exact: false
      
  performance_tuning:
    parallel_indexing: true
    index_compression: true
    memory_mapping: true

# Default configuration selection
default_profile: "balanced"

# Environment-specific overrides
environment_overrides:
  development:
    profile: "speed_optimized"
    caching:
      enabled: false
      
  testing:
    profile: "memory_optimized"
    batch_processing:
      batch_size: 4
      
  production:
    profile: "balanced"
    performance_monitoring: true
    
  high_performance:
    profile: "quality_optimized"
    parallel_processing: true
