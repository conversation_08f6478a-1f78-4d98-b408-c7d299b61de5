import { defineConfig, PluginOption } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import fs from "fs"; // Added for file system operations
import YAML from "js-yaml"; // Added for YAML parsing

// Custom Vite plugin to convert components.yaml to components.json
function yamlToJsonPlugin(): PluginOption {
  return {
    name: "yaml-to-json",
    buildStart() {
      const yamlFilePath = path.resolve(__dirname, "components.yaml");
      const jsonFilePath = path.resolve(__dirname, "components.json");

      try {
        if (fs.existsSync(yamlFilePath)) {
          console.log(`Converting ${yamlFilePath} to ${jsonFilePath}`);
          const yamlContent = fs.readFileSync(yamlFilePath, "utf8");
          const jsonContent = YAML.load(yamlContent);
          // Add back the $schema property if it was commented out in YAML
          if (typeof jsonContent === 'object' && jsonContent !== null && !('$schema' in jsonContent)) {
            (jsonContent as any).$schema = "https://ui.shadcn.com/schema.json";
          }
          fs.writeFileSync(jsonFilePath, JSON.stringify(jsonContent, null, 2));
          console.log(`${jsonFilePath} created successfully.`);
        } else {
          console.warn(`${yamlFilePath} not found. Skipping conversion.`);
        }
      } catch (error) {
        console.error(`Error converting YAML to JSON: ${error}`);
        // Optionally, re-throw or handle error to stop build
      }
    },
  };
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
      // WebSocket proxy for dashboard connections
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
        changeOrigin: true,
        secure: false,
      },
    },
  },
  plugins: [
    yamlToJsonPlugin(), // Added custom plugin
    react()
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Improve chunk loading and error handling
    rollupOptions: {
      output: {
        manualChunks: {
          // Separate vendor chunks for better caching
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-tooltip'],
          'chart-vendor': ['recharts', 'framer-motion'],
          'utils-vendor': ['date-fns', 'lodash', 'clsx', 'class-variance-authority']
        },
        // Improve chunk naming for better debugging
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        }
      }
    },
    // Increase chunk size warning limit
    chunkSizeWarningLimit: 1000,
    // Enable source maps for better debugging
    sourcemap: mode === 'development'
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-tooltip',
      'framer-motion',
      'recharts',
      'date-fns',
      'clsx'
    ],
    // Force re-optimization on dependency changes
    force: mode === 'development'
  },
  // Improve error handling
  define: {
    // Ensure proper error boundaries
    __DEV__: mode === 'development'
  }
}));
