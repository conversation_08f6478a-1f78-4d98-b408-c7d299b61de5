{"enabled": true, "validation_mode": "strict", "fail_on_validation_error": true, "validation_schedule": {"startup_validation": true, "runtime_validation": true, "periodic_validation": true, "validation_interval_minutes": 60}, "validation_categories": {"configuration_validation": {"enabled": true, "validate_environment_vars": true, "validate_config_files": true, "validate_security_settings": true, "validate_database_config": true, "validate_cors_settings": true, "validate_session_config": true}, "secret_validation": {"enabled": true, "validate_jwt_secrets": true, "validate_database_credentials": true, "validate_api_keys": true, "validate_encryption_keys": true, "check_secret_strength": true, "check_secret_rotation": true}, "permission_validation": {"enabled": true, "validate_user_permissions": true, "validate_role_assignments": true, "validate_access_controls": true, "validate_plugin_permissions": true}, "network_validation": {"enabled": true, "validate_ssl_certificates": true, "validate_cors_origins": true, "validate_allowed_ips": true, "validate_firewall_rules": true}, "input_validation": {"enabled": true, "validate_request_format": true, "validate_request_size": true, "validate_file_uploads": true, "sanitize_user_input": true, "check_injection_attempts": true}}, "validation_rules": {"jwt_secret_validation": {"min_length": 32, "max_length": 512, "require_special_chars": true, "require_numbers": true, "require_mixed_case": true, "forbidden_patterns": ["your-secret-key", "development-only", "test-secret", "default-key", "changeme", "password123", "secret123"], "entropy_threshold": 4.0, "check_common_passwords": true}, "database_validation": {"require_ssl": true, "require_authentication": true, "validate_connection": true, "check_connection_timeout": true, "validate_credentials": true, "check_privilege_escalation": true}, "cors_validation": {"validate_origins": true, "require_https_in_production": true, "block_wildcard_in_production": true, "validate_methods": true, "validate_headers": true, "check_credentials_flag": true}, "session_validation": {"max_timeout_minutes": 480, "min_timeout_minutes": 30, "require_secure_cookies": true, "require_httponly_cookies": true, "validate_session_storage": true, "check_session_fixation": true}, "file_upload_validation": {"max_file_size_mb": 10, "allowed_extensions": ["pdf", "docx", "xlsx", "csv", "txt", "json", "yaml"], "blocked_extensions": ["exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar", "php", "asp", "jsp"], "scan_for_malware": true, "validate_mime_type": true, "check_file_headers": true}, "api_validation": {"validate_api_keys": true, "check_rate_limits": true, "validate_request_signatures": true, "check_timestamp_validity": true, "validate_nonce_uniqueness": true}}, "environment_specific_rules": {"development": {"validation_level": "basic", "allow_weak_secrets": false, "require_all_validations": false, "log_validation_failures": true, "fail_on_validation_error": false}, "staging": {"validation_level": "standard", "allow_weak_secrets": false, "require_all_validations": true, "log_validation_failures": true, "fail_on_validation_error": true}, "production": {"validation_level": "strict", "allow_weak_secrets": false, "require_all_validations": true, "log_validation_failures": true, "fail_on_validation_error": true, "additional_security_checks": true}, "testing": {"validation_level": "minimal", "allow_weak_secrets": true, "require_all_validations": false, "log_validation_failures": false, "fail_on_validation_error": false}}, "validation_responses": {"on_validation_failure": {"log_failure": true, "send_alert": true, "block_request": true, "quarantine_resource": false, "notify_administrators": true}, "on_critical_failure": {"log_failure": true, "send_alert": true, "block_request": true, "quarantine_resource": true, "notify_administrators": true, "emergency_shutdown": false}}, "monitoring": {"enabled": true, "track_validation_metrics": true, "generate_validation_reports": true, "alert_on_repeated_failures": true, "dashboard_integration": true}}