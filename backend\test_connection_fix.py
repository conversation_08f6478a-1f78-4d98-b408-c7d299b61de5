#!/usr/bin/env python3
"""
Test script to verify database connection pool fixes.
"""

import sys
import os
import requests
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_endpoint(endpoint_url, headers=None, timeout=10):
    """Test a single endpoint request."""
    try:
        response = requests.get(endpoint_url, headers=headers, timeout=timeout)
        return {
            'url': endpoint_url,
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'error': None
        }
    except Exception as e:
        return {
            'url': endpoint_url,
            'status_code': None,
            'success': False,
            'error': str(e)
        }

def test_concurrent_requests(base_url="http://localhost:8000", num_requests=20, num_threads=5):
    """Test concurrent requests to check for connection pool issues."""
    print(f"Testing {num_requests} concurrent requests with {num_threads} threads...")
    
    # Test endpoints that were having issues
    endpoints = [
        "/business-profiles/",
        "/data-sources/",
        "/health",
        "/providers"
    ]
    
    # Create request URLs
    test_urls = []
    for _ in range(num_requests // len(endpoints)):
        for endpoint in endpoints:
            test_urls.append(f"{base_url}{endpoint}")
    
    # Add remaining requests to fill up to num_requests
    while len(test_urls) < num_requests:
        test_urls.append(f"{base_url}/health")
    
    results = []
    start_time = time.time()
    
    # Execute concurrent requests
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        future_to_url = {
            executor.submit(test_endpoint, url): url 
            for url in test_urls
        }
        
        for future in as_completed(future_to_url):
            result = future.result()
            results.append(result)
            
            # Print progress
            if len(results) % 5 == 0:
                print(f"Completed {len(results)}/{num_requests} requests...")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Analyze results
    successful = sum(1 for r in results if r['success'])
    failed = len(results) - successful
    
    print(f"\n=== Test Results ===")
    print(f"Total requests: {len(results)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Success rate: {successful/len(results)*100:.1f}%")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Average time per request: {total_time/len(results):.3f} seconds")
    
    # Show failed requests
    if failed > 0:
        print(f"\n=== Failed Requests ===")
        for result in results:
            if not result['success']:
                print(f"URL: {result['url']}")
                print(f"Status: {result['status_code']}")
                print(f"Error: {result['error']}")
                print("---")
    
    # Check for connection pool errors
    connection_errors = [r for r in results if r['error'] and 'connection' in r['error'].lower()]
    timeout_errors = [r for r in results if r['error'] and 'timeout' in r['error'].lower()]
    
    if connection_errors:
        print(f"\n⚠️  Found {len(connection_errors)} connection-related errors")
    if timeout_errors:
        print(f"⚠️  Found {len(timeout_errors)} timeout errors")
    
    if successful == len(results):
        print("✅ All requests successful - connection pool issue appears to be fixed!")
        return True
    else:
        print("❌ Some requests failed - connection pool issue may still exist")
        return False

def test_sequential_requests(base_url="http://localhost:8000", num_requests=10):
    """Test sequential requests to establish baseline."""
    print(f"Testing {num_requests} sequential requests...")
    
    endpoint = "/business-profiles/"
    url = f"{base_url}{endpoint}"
    
    results = []
    start_time = time.time()
    
    for i in range(num_requests):
        result = test_endpoint(url)
        results.append(result)
        print(f"Request {i+1}: {'✅' if result['success'] else '❌'} ({result['status_code']})")
        time.sleep(0.1)  # Small delay between requests
    
    end_time = time.time()
    total_time = end_time - start_time
    
    successful = sum(1 for r in results if r['success'])
    print(f"\nSequential test: {successful}/{num_requests} successful")
    print(f"Total time: {total_time:.2f} seconds")
    
    return successful == num_requests

def main():
    """Run connection pool tests."""
    print("Database Connection Pool Fix Verification")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test if server is running
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not responding properly (status: {response.status_code})")
            return 1
    except Exception as e:
        print(f"❌ Cannot connect to server at {base_url}: {e}")
        print("Make sure the server is running with: python server.py")
        return 1
    
    print("✅ Server is running")
    
    # Run sequential test first
    print("\n" + "="*30)
    sequential_success = test_sequential_requests(base_url, 10)
    
    # Run concurrent test
    print("\n" + "="*30)
    concurrent_success = test_concurrent_requests(base_url, 30, 10)
    
    # Final result
    print("\n" + "="*50)
    if sequential_success and concurrent_success:
        print("🎉 All tests passed! Connection pool issue appears to be resolved.")
        return 0
    else:
        print("⚠️  Some tests failed. Connection pool issue may still exist.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
