#!/usr/bin/env python3
"""
Integration test for Phase 3 implementation.
Tests that all Phase 3 changes work correctly.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_workflow_manager_import():
    """Test that WorkflowManager can be imported from the new location."""
    print("Testing WorkflowManager import...")
    
    try:
        # Test the import that's used in main.py
        sys.path.append(str(backend_dir / "app" / "utils"))
        from import_utils import import_workflow_manager
        
        WorkflowManager = import_workflow_manager()
        print(f"✅ WorkflowManager imported successfully: {WorkflowManager}")
        return True
        
    except Exception as e:
        print(f"❌ WorkflowManager import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_inheritance():
    """Test configuration inheritance system."""
    print("\nTesting configuration inheritance...")
    
    try:
        # Direct import to avoid dependency issues
        sys.path.append(str(backend_dir / "agents" / "langgraph" / "config"))
        from config_manager import ConfigManager
        
        config_manager = ConfigManager(environment="development")
        
        # Test that configurations are loaded
        assert config_manager.langgraph_config is not None, "LangGraph config not loaded"
        assert config_manager.database_config is not None, "Database config not loaded"
        assert config_manager.logging_config is not None, "Logging config not loaded"
        
        # Test inheritance paths
        inheritance_info = config_manager.get_inheritance_info()
        assert len(inheritance_info["inheritance_paths"]) == 2, "Expected 2 inheritance paths"
        
        print("✅ Configuration inheritance working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Configuration inheritance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_legacy_removal():
    """Test that legacy directories have been removed."""
    print("\nTesting legacy removal...")
    
    try:
        # Check that legacy orchestration directory is removed
        orchestration_dir = backend_dir / "agents" / "orchestration"
        assert not orchestration_dir.exists(), f"Legacy orchestration directory still exists: {orchestration_dir}"
        
        # Check that migration directory is removed
        migration_dir = backend_dir / "agents" / "langgraph" / "migration"
        assert not migration_dir.exists(), f"Legacy migration directory still exists: {migration_dir}"
        
        print("✅ Legacy directories successfully removed")
        return True
        
    except Exception as e:
        print(f"❌ Legacy removal test failed: {e}")
        return False

def test_main_app_imports():
    """Test that main app imports work correctly."""
    print("\nTesting main app imports...")
    
    try:
        # Test that main.py can import the workflow manager
        main_py_path = backend_dir / "app" / "main.py"
        
        # Read main.py and check for correct imports
        with open(main_py_path, 'r') as f:
            content = f.read()
        
        # Check that it imports from LangGraph
        assert "from agents.langgraph.core.workflow_manager import WorkflowManager" in content, \
            "Main.py should import WorkflowManager from LangGraph"
        
        print("✅ Main app imports are correct")
        return True
        
    except Exception as e:
        print(f"❌ Main app imports test failed: {e}")
        return False

def main():
    """Run all Phase 3 integration tests."""
    print("🚀 Running Phase 3 Integration Tests...")
    print("=" * 50)
    
    tests = [
        test_legacy_removal,
        test_workflow_manager_import,
        test_config_inheritance,
        test_main_app_imports,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All Phase 3 integration tests passed!")
        print("\n✅ Phase 3: Legacy Removal implementation is complete and working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
