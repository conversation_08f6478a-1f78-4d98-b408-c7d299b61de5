"""
Classification Persona

This module provides a consolidated classification agent that uses the unified conversation agent base
for consistent AI-powered responses and specialized content classification capabilities.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .unified_conversation_agent import UnifiedConversationAgent

logger = logging.getLogger(__name__)


class ClassificationPersona(UnifiedConversationAgent):
    """
    Classification persona that uses the unified conversation base.
    
    This persona provides:
    - Text classification and categorization
    - Content organization and tagging
    - Document classification
    - Sentiment analysis
    - Topic modeling
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the classification persona.
        
        Args:
            config: Optional configuration for the persona
        """
        # Get agent_id from config or use default
        agent_id = config.get('agent_id', 'classification') if config else 'classification'
        
        # Initialize parent class
        super().__init__(
            agent_id=agent_id,
            agent_type="classification",
            config=config
        )
        
        # Classification-specific configuration
        self.classification_types = config.get('classification_types', ['sentiment', 'topic', 'category', 'intent']) if config else ['sentiment', 'topic', 'category', 'intent']
        self.supported_languages = config.get('supported_languages', ['en', 'es', 'fr', 'de']) if config else ['en', 'es', 'fr', 'de']
        self.confidence_threshold = config.get('confidence_threshold', 0.7) if config else 0.7
        
        logger.info("ClassificationPersona initialized")

    def _get_specialized_capabilities(self) -> List[str]:
        """Get classification-specific capabilities."""
        return [
            "text_classification",
            "content_categorization",
            "document_classification",
            "sentiment_analysis",
            "topic_modeling",
            "intent_detection",
            "content_tagging",
            "data_organization",
            "pattern_recognition",
            "automated_sorting"
        ]

    def _get_agent_name(self) -> str:
        """Get human-readable agent name."""
        return "Content Classification Specialist"

    def _get_agent_description(self) -> str:
        """Get agent description."""
        return "Expert in content classification, organization, and automated categorization"

    def _determine_intent_type(self, message: str) -> str:
        """Determine the intent type for classification conversations."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["sentiment", "emotion", "feeling"]):
            return "sentiment_analysis"
        elif any(word in message_lower for word in ["topic", "theme", "subject"]):
            return "topic_classification"
        elif any(word in message_lower for word in ["category", "categorize", "organize"]):
            return "content_categorization"
        elif any(word in message_lower for word in ["intent", "purpose", "goal"]):
            return "intent_detection"
        elif any(word in message_lower for word in ["tag", "label", "metadata"]):
            return "content_tagging"
        else:
            return "general_classification"

    def _get_greeting_specialization(self) -> str:
        """Get classification-specific greeting content."""
        return ("**🏷️ I specialize in:**\n"
                "• **Text Classification** - Automated content categorization\n"
                "• **Sentiment Analysis** - Emotion and opinion detection\n"
                "• **Topic Modeling** - Theme and subject identification\n"
                "• **Content Organization** - Systematic data structuring\n"
                "• **Intent Detection** - Purpose and goal recognition\n"
                "• **Automated Tagging** - Metadata and label generation")

    def _generate_capabilities_response(self) -> str:
        """Generate a response about classification capabilities."""
        return ("I'm your **Content Classification Specialist** with comprehensive categorization capabilities:\n\n"
                "**🏷️ Core Classification:**\n"
                "• Text and document classification\n"
                "• Content categorization and organization\n"
                "• Automated tagging and labeling\n\n"
                "**🎭 Sentiment & Intent:**\n"
                "• Sentiment analysis and emotion detection\n"
                "• Intent recognition and purpose identification\n"
                "• Opinion mining and attitude assessment\n\n"
                "**📚 Advanced Analysis:**\n"
                "• Topic modeling and theme extraction\n"
                "• Pattern recognition in content\n"
                "• Multi-language classification support\n\n"
                f"**Classification Types:** {', '.join(self.classification_types)}\n"
                f"**Supported Languages:** {', '.join(self.supported_languages)}\n"
                f"**Confidence Threshold:** {self.confidence_threshold}")

    async def _enhance_response(self, original_message: str, base_response: str, 
                              context: Optional[Dict[str, Any]] = None) -> str:
        """Enhance the response with classification-specific information."""
        try:
            enhanced_response = base_response
            
            # Add classification type suggestions
            if self._is_classification_request(original_message):
                enhanced_response += f"\n\n🏷️ **Classification Options**: I can perform {', '.join(self.classification_types)} classification to organize your content."
            
            # Add sentiment analysis insights
            if self._is_sentiment_related(original_message):
                enhanced_response += "\n\n🎭 **Sentiment Analysis**: I can detect positive, negative, and neutral sentiments, as well as specific emotions like joy, anger, fear, and surprise."
            
            # Add organization tips
            if self._is_organization_related(original_message):
                enhanced_response += "\n\n📚 **Organization Strategy**: I can help create taxonomies, tag systems, and hierarchical categorization structures for your content."
            
            # Add language support info
            if self._is_multilingual_related(original_message):
                enhanced_response += f"\n\n🌍 **Language Support**: I can classify content in {', '.join(self.supported_languages)} languages with high accuracy."
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing classification response: {e}")
            return base_response

    def _is_classification_request(self, message: str) -> bool:
        """Check if the message is a classification request."""
        classification_keywords = ["classify", "categorize", "organize", "sort", "group"]
        return any(keyword in message.lower() for keyword in classification_keywords)

    def _is_sentiment_related(self, message: str) -> bool:
        """Check if the message is sentiment-related."""
        sentiment_keywords = ["sentiment", "emotion", "feeling", "opinion", "mood"]
        return any(keyword in message.lower() for keyword in sentiment_keywords)

    def _is_organization_related(self, message: str) -> bool:
        """Check if the message is about content organization."""
        org_keywords = ["organize", "structure", "taxonomy", "hierarchy", "system"]
        return any(keyword in message.lower() for keyword in org_keywords)

    def _is_multilingual_related(self, message: str) -> bool:
        """Check if the message mentions multiple languages."""
        lang_keywords = ["language", "multilingual", "translate", "international"]
        return any(keyword in message.lower() for keyword in lang_keywords)

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this classification persona."""
        base_info = super().get_agent_info()
        base_info.update({
            "specialization": "content_classification_and_organization",
            "classification_types": self.classification_types,
            "supported_languages": self.supported_languages,
            "confidence_threshold": self.confidence_threshold,
            "supports_sentiment_analysis": True,
            "supports_topic_modeling": True,
            "supports_multilingual_classification": True
        })
        return base_info


# Backward compatibility aliases
RefactoredClassificationAgent = ClassificationPersona
ClassificationAgent = ClassificationPersona
ClassificationAgentNode = ClassificationPersona
