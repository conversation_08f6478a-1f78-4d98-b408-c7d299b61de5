"""
Tests for configuration inheritance system.

This module tests the new configuration inheritance functionality
that allows LangGraph configurations to inherit from main app configurations.
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch

from agents.langgraph.config.config_manager import ConfigManager


class TestConfigurationInheritance:
    """Test suite for configuration inheritance."""

    @pytest.fixture
    def temp_config_dirs(self):
        """Create temporary configuration directories for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create main app config directory
            main_config_dir = temp_path / "config"
            main_config_dir.mkdir()
            
            # Create LangGraph config directory
            langgraph_config_dir = temp_path / "agents" / "langgraph" / "config"
            langgraph_config_dir.mkdir(parents=True)
            
            # Create base configuration in main config
            main_base_config = {
                "database": {
                    "host": "localhost",
                    "port": 5432,
                    "database": "datagenius",
                    "username": "user"
                },
                "logging": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            }
            
            with open(main_config_dir / "base.yaml", 'w') as f:
                yaml.dump(main_base_config, f)
            
            # Create development configuration in main config
            main_dev_config = {
                "database": {
                    "host": "dev-host",
                    "echo": True
                },
                "logging": {
                    "level": "DEBUG"
                }
            }
            
            with open(main_config_dir / "development.yaml", 'w') as f:
                yaml.dump(main_dev_config, f)
            
            # Create LangGraph-specific base configuration
            langgraph_base_config = {
                "langgraph": {
                    "enabled": False,
                    "max_workflow_duration": 3600
                }
            }
            
            with open(langgraph_config_dir / "base.yaml", 'w') as f:
                yaml.dump(langgraph_base_config, f)
            
            # Create LangGraph-specific development configuration
            langgraph_dev_config = {
                "langgraph": {
                    "enabled": True,
                    "rollout_percentage": 100.0
                },
                "logging": {
                    "log_file_path": "logs/langgraph_dev.log"
                }
            }
            
            with open(langgraph_config_dir / "development.yaml", 'w') as f:
                yaml.dump(langgraph_dev_config, f)
            
            yield {
                "main_config_dir": main_config_dir,
                "langgraph_config_dir": langgraph_config_dir,
                "temp_path": temp_path
            }

    def test_configuration_inheritance(self, temp_config_dirs):
        """Test that LangGraph configurations inherit from main app configurations."""
        # Initialize ConfigManager with test directories
        config_manager = ConfigManager(
            config_dir=temp_config_dirs["langgraph_config_dir"],
            environment="development"
        )
        
        # Override inheritance paths for testing
        config_manager._inheritance_paths = [
            temp_config_dirs["langgraph_config_dir"],
            temp_config_dirs["main_config_dir"]
        ]
        
        # Reload configuration with new inheritance paths
        config_manager._load_configuration()
        
        # Test that database configuration is inherited from main config
        assert config_manager.database_config is not None
        assert config_manager.database_config.host == "dev-host"  # From main dev config
        assert config_manager.database_config.port == 5432  # From main base config
        assert config_manager.database_config.database == "datagenius"  # From main base config
        
        # Test that logging configuration is merged
        assert config_manager.logging_config is not None
        assert config_manager.logging_config.level == "DEBUG"  # From main dev config
        assert "langgraph_dev.log" in config_manager.logging_config.log_file_path  # From LangGraph dev config
        
        # Test that LangGraph-specific configuration is loaded
        assert config_manager.langgraph_config is not None
        assert config_manager.langgraph_config.enabled is True  # From LangGraph dev config
        assert config_manager.langgraph_config.max_workflow_duration == 3600  # From LangGraph base config
        assert config_manager.langgraph_config.rollout_percentage == 100.0  # From LangGraph dev config

    def test_inheritance_info(self, temp_config_dirs):
        """Test that inheritance information is correctly reported."""
        config_manager = ConfigManager(
            config_dir=temp_config_dirs["langgraph_config_dir"],
            environment="development"
        )
        
        # Override inheritance paths for testing
        config_manager._inheritance_paths = [
            temp_config_dirs["langgraph_config_dir"],
            temp_config_dirs["main_config_dir"]
        ]
        
        inheritance_info = config_manager.get_inheritance_info()
        
        # Test inheritance paths are reported
        assert len(inheritance_info["inheritance_paths"]) == 2
        assert inheritance_info["inheritance_paths"][0]["path"] == str(temp_config_dirs["langgraph_config_dir"])
        assert inheritance_info["inheritance_paths"][1]["path"] == str(temp_config_dirs["main_config_dir"])
        
        # Test that loaded files are tracked
        assert len(inheritance_info["loaded_files"]) > 0

    def test_configuration_validation(self, temp_config_dirs):
        """Test configuration validation with inheritance."""
        config_manager = ConfigManager(
            config_dir=temp_config_dirs["langgraph_config_dir"],
            environment="development"
        )
        
        # Override inheritance paths for testing
        config_manager._inheritance_paths = [
            temp_config_dirs["langgraph_config_dir"],
            temp_config_dirs["main_config_dir"]
        ]
        
        # Reload and validate configuration
        config_manager._load_configuration()
        
        # Test that validation passes without errors
        with patch.object(config_manager.logger, 'warning') as mock_warning:
            config_manager._validate_configuration()
            # Should not have validation warnings for this test setup
            mock_warning.assert_not_called()

    def test_configuration_summary_with_inheritance(self, temp_config_dirs):
        """Test configuration summary includes inheritance information."""
        config_manager = ConfigManager(
            config_dir=temp_config_dirs["langgraph_config_dir"],
            environment="development"
        )
        
        # Override inheritance paths for testing
        config_manager._inheritance_paths = [
            temp_config_dirs["langgraph_config_dir"],
            temp_config_dirs["main_config_dir"]
        ]
        
        summary = config_manager.get_configuration_summary()
        
        # Test that inheritance paths are included in summary
        assert "inheritance_paths" in summary
        assert len(summary["inheritance_paths"]) == 2
        assert summary["environment"] == "development"
        assert summary["langgraph_enabled"] is True  # Should be True from inheritance
