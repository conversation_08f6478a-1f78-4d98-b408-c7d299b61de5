"""
Graph Visualization and Analysis for LangGraph Workflows.

This module provides visualization capabilities for workflow graphs,
performance analysis, and debugging support.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import json

try:
    import networkx as nx
    import matplotlib.pyplot as plt
    import matplotlib.patches as mpatches
    from matplotlib.colors import LinearSegmentedColormap
    VISUALIZATION_AVAILABLE = True
except ImportError:
    # Fallback for environments without visualization libraries
    nx = None
    plt = None
    mpatches = None
    LinearSegmentedColormap = None
    VISUALIZATION_AVAILABLE = False

from ..states.agent_state import DatageniusAgentState

logger = logging.getLogger(__name__)


class GraphVisualization:
    """
    Workflow graph visualization and analysis system.
    
    This class provides:
    - Workflow graph visualization
    - Performance heatmaps
    - Execution path analysis
    - Bottleneck identification
    """

    def __init__(self):
        """Initialize the graph visualization system."""
        self.logger = logging.getLogger(__name__)
        
        if not VISUALIZATION_AVAILABLE:
            self.logger.warning("Visualization libraries not available. Graph visualization disabled.")
        
        # Color schemes for different visualizations
        self.color_schemes = {
            "performance": ["#ff4444", "#ffaa44", "#ffff44", "#44ff44"],  # Red to Green
            "agent_type": {
                "concierge": "#4CAF50",
                "analysis": "#2196F3", 
                "marketing": "#FF9800",
                "classification": "#9C27B0",
                "tool": "#607D8B",
                "decision": "#795548",
                "utility": "#9E9E9E"
            },
            "execution_status": {
                "completed": "#4CAF50",
                "running": "#FF9800",
                "failed": "#F44336",
                "pending": "#9E9E9E"
            }
        }

    def create_workflow_graph(
        self, 
        workflow_definition: Dict[str, Any],
        execution_data: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Create a visual representation of the workflow graph.
        
        Args:
            workflow_definition: Workflow structure definition
            execution_data: Optional execution data for coloring nodes
            
        Returns:
            Path to the generated graph image or None if visualization unavailable
        """
        if not VISUALIZATION_AVAILABLE:
            self.logger.warning("Cannot create workflow graph: visualization libraries not available")
            return None
        
        try:
            # Create directed graph
            G = nx.DiGraph()
            
            # Add nodes from workflow definition
            nodes = workflow_definition.get("nodes", {})
            edges = workflow_definition.get("edges", [])
            
            for node_id, node_info in nodes.items():
                G.add_node(node_id, **node_info)
            
            # Add edges
            for edge in edges:
                if isinstance(edge, dict):
                    source = edge.get("source")
                    target = edge.get("target")
                    if source and target:
                        G.add_edge(source, target, **edge.get("properties", {}))
                elif isinstance(edge, (list, tuple)) and len(edge) >= 2:
                    G.add_edge(edge[0], edge[1])
            
            # Create visualization
            plt.figure(figsize=(12, 8))
            
            # Use hierarchical layout for better readability
            try:
                pos = nx.nx_agraph.graphviz_layout(G, prog='dot')
            except:
                # Fallback to spring layout if graphviz not available
                pos = nx.spring_layout(G, k=2, iterations=50)
            
            # Color nodes based on execution data or type
            node_colors = self._get_node_colors(G, execution_data)
            
            # Draw the graph
            nx.draw(G, pos, 
                   node_color=node_colors,
                   node_size=1000,
                   font_size=8,
                   font_weight='bold',
                   arrows=True,
                   arrowsize=20,
                   edge_color='gray',
                   with_labels=True)
            
            # Add legend
            self._add_legend(execution_data is not None)
            
            plt.title("Workflow Graph Visualization", fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            # Save the graph
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"workflow_graph_{timestamp}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"Workflow graph saved as {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error creating workflow graph: {e}")
            return None

    def create_performance_heatmap(
        self, 
        performance_data: Dict[str, Dict[str, float]],
        title: str = "Performance Heatmap"
    ) -> Optional[str]:
        """
        Create a performance heatmap visualization.
        
        Args:
            performance_data: Dictionary with performance metrics
            title: Title for the heatmap
            
        Returns:
            Path to the generated heatmap image or None if visualization unavailable
        """
        if not VISUALIZATION_AVAILABLE:
            self.logger.warning("Cannot create performance heatmap: visualization libraries not available")
            return None
        
        try:
            # Prepare data for heatmap
            agents = list(performance_data.keys())
            metrics = list(next(iter(performance_data.values())).keys()) if performance_data else []
            
            if not agents or not metrics:
                self.logger.warning("No data available for heatmap")
                return None
            
            # Create data matrix
            data_matrix = []
            for agent in agents:
                row = []
                for metric in metrics:
                    value = performance_data[agent].get(metric, 0.0)
                    row.append(value)
                data_matrix.append(row)
            
            # Create heatmap
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Create custom colormap
            colors = ['#ff4444', '#ffaa44', '#ffff44', '#44ff44']
            n_bins = 100
            cmap = LinearSegmentedColormap.from_list('performance', colors, N=n_bins)
            
            im = ax.imshow(data_matrix, cmap=cmap, aspect='auto')
            
            # Set ticks and labels
            ax.set_xticks(range(len(metrics)))
            ax.set_yticks(range(len(agents)))
            ax.set_xticklabels(metrics, rotation=45, ha='right')
            ax.set_yticklabels(agents)
            
            # Add colorbar
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Performance Score', rotation=270, labelpad=15)
            
            # Add value annotations
            for i in range(len(agents)):
                for j in range(len(metrics)):
                    text = ax.text(j, i, f'{data_matrix[i][j]:.2f}',
                                 ha="center", va="center", color="black", fontweight='bold')
            
            plt.title(title, fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            # Save the heatmap
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_heatmap_{timestamp}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"Performance heatmap saved as {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error creating performance heatmap: {e}")
            return None

    def create_execution_timeline(
        self, 
        execution_history: List[Dict[str, Any]],
        title: str = "Workflow Execution Timeline"
    ) -> Optional[str]:
        """
        Create a timeline visualization of workflow execution.
        
        Args:
            execution_history: List of execution events with timestamps
            title: Title for the timeline
            
        Returns:
            Path to the generated timeline image or None if visualization unavailable
        """
        if not VISUALIZATION_AVAILABLE:
            self.logger.warning("Cannot create execution timeline: visualization libraries not available")
            return None
        
        try:
            if not execution_history:
                self.logger.warning("No execution history available for timeline")
                return None
            
            # Sort events by timestamp
            sorted_events = sorted(execution_history, key=lambda x: x.get('timestamp', ''))
            
            # Create timeline
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # Extract data
            timestamps = []
            agents = []
            statuses = []
            
            for event in sorted_events:
                timestamp_str = event.get('timestamp', '')
                if timestamp_str:
                    try:
                        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        timestamps.append(timestamp)
                        agents.append(event.get('agent_id', 'unknown'))
                        statuses.append(event.get('status', 'unknown'))
                    except:
                        continue
            
            if not timestamps:
                self.logger.warning("No valid timestamps found in execution history")
                return None
            
            # Create scatter plot
            y_positions = range(len(timestamps))
            colors = [self.color_schemes["execution_status"].get(status, "#9E9E9E") for status in statuses]
            
            scatter = ax.scatter(timestamps, y_positions, c=colors, s=100, alpha=0.7)
            
            # Add agent labels
            for i, (timestamp, agent) in enumerate(zip(timestamps, agents)):
                ax.annotate(agent, (timestamp, i), xytext=(5, 0), 
                           textcoords='offset points', va='center', fontsize=8)
            
            # Format axes
            ax.set_xlabel('Time', fontsize=12)
            ax.set_ylabel('Execution Order', fontsize=12)
            ax.set_title(title, fontsize=16, fontweight='bold')
            
            # Add legend
            legend_elements = [
                mpatches.Patch(color=color, label=status.title()) 
                for status, color in self.color_schemes["execution_status"].items()
            ]
            ax.legend(handles=legend_elements, loc='upper right')
            
            # Format x-axis for better readability
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Save the timeline
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"execution_timeline_{timestamp}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"Execution timeline saved as {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error creating execution timeline: {e}")
            return None

    def analyze_execution_paths(
        self, 
        workflow_executions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Analyze common execution paths and patterns.
        
        Args:
            workflow_executions: List of workflow execution data
            
        Returns:
            Dictionary with path analysis results
        """
        try:
            if not workflow_executions:
                return {"message": "No execution data available for analysis"}
            
            # Extract execution paths
            paths = []
            for execution in workflow_executions:
                agent_history = execution.get("agent_history", [])
                if agent_history:
                    paths.append(" -> ".join(agent_history))
            
            if not paths:
                return {"message": "No agent transition paths found"}
            
            # Analyze path frequency
            path_frequency = {}
            for path in paths:
                path_frequency[path] = path_frequency.get(path, 0) + 1
            
            # Sort by frequency
            sorted_paths = sorted(path_frequency.items(), key=lambda x: x[1], reverse=True)
            
            # Calculate path statistics
            total_executions = len(paths)
            unique_paths = len(path_frequency)
            most_common_path = sorted_paths[0] if sorted_paths else ("", 0)
            
            # Analyze path lengths
            path_lengths = [len(path.split(" -> ")) for path in paths]
            avg_path_length = sum(path_lengths) / len(path_lengths) if path_lengths else 0
            
            return {
                "total_executions": total_executions,
                "unique_paths": unique_paths,
                "path_diversity": unique_paths / total_executions if total_executions > 0 else 0,
                "most_common_path": {
                    "path": most_common_path[0],
                    "frequency": most_common_path[1],
                    "percentage": (most_common_path[1] / total_executions * 100) if total_executions > 0 else 0
                },
                "avg_path_length": avg_path_length,
                "top_paths": sorted_paths[:10],  # Top 10 most common paths
                "path_length_distribution": {
                    "min": min(path_lengths) if path_lengths else 0,
                    "max": max(path_lengths) if path_lengths else 0,
                    "avg": avg_path_length
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing execution paths: {e}")
            return {"error": str(e)}

    def _get_node_colors(
        self, 
        graph: 'nx.DiGraph', 
        execution_data: Optional[Dict[str, Any]]
    ) -> List[str]:
        """Get colors for graph nodes based on execution data or node type."""
        colors = []
        
        for node in graph.nodes():
            if execution_data and node in execution_data:
                # Color based on execution status
                status = execution_data[node].get("status", "pending")
                colors.append(self.color_schemes["execution_status"].get(status, "#9E9E9E"))
            else:
                # Color based on node type
                node_data = graph.nodes[node]
                node_type = node_data.get("type", "unknown")
                colors.append(self.color_schemes["agent_type"].get(node_type, "#9E9E9E"))
        
        return colors

    def _add_legend(self, has_execution_data: bool):
        """Add legend to the graph visualization."""
        if has_execution_data:
            # Legend for execution status
            legend_elements = [
                mpatches.Patch(color=color, label=status.title()) 
                for status, color in self.color_schemes["execution_status"].items()
            ]
        else:
            # Legend for node types
            legend_elements = [
                mpatches.Patch(color=color, label=node_type.title()) 
                for node_type, color in self.color_schemes["agent_type"].items()
            ]
        
        plt.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1))

    def generate_workflow_report(
        self, 
        workflow_data: Dict[str, Any],
        output_format: str = "html"
    ) -> Optional[str]:
        """
        Generate a comprehensive workflow analysis report.
        
        Args:
            workflow_data: Complete workflow data including metrics and execution history
            output_format: Output format ("html", "json", "text")
            
        Returns:
            Path to the generated report file or None if generation failed
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if output_format == "html":
                return self._generate_html_report(workflow_data, timestamp)
            elif output_format == "json":
                return self._generate_json_report(workflow_data, timestamp)
            elif output_format == "text":
                return self._generate_text_report(workflow_data, timestamp)
            else:
                self.logger.error(f"Unsupported output format: {output_format}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error generating workflow report: {e}")
            return None

    def _generate_html_report(self, workflow_data: Dict[str, Any], timestamp: str) -> str:
        """Generate HTML report."""
        filename = f"workflow_report_{timestamp}.html"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Workflow Analysis Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #e8f4f8; border-radius: 3px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Workflow Analysis Report</h1>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Summary</h2>
                <div class="metric">Workflow ID: {workflow_data.get('workflow_id', 'N/A')}</div>
                <div class="metric">Status: {workflow_data.get('status', 'N/A')}</div>
                <div class="metric">Execution Time: {workflow_data.get('execution_time', 'N/A')}s</div>
                <div class="metric">Success Rate: {workflow_data.get('success_rate', 'N/A')}</div>
            </div>
            
            <div class="section">
                <h2>Performance Metrics</h2>
                <p>Detailed performance analysis and recommendations would be included here.</p>
            </div>
        </body>
        </html>
        """
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return filename

    def _generate_json_report(self, workflow_data: Dict[str, Any], timestamp: str) -> str:
        """Generate JSON report."""
        filename = f"workflow_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(workflow_data, f, indent=2, default=str)
        
        return filename

    def _generate_text_report(self, workflow_data: Dict[str, Any], timestamp: str) -> str:
        """Generate text report."""
        filename = f"workflow_report_{timestamp}.txt"
        
        report_content = f"""
Workflow Analysis Report
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

SUMMARY
=======
Workflow ID: {workflow_data.get('workflow_id', 'N/A')}
Status: {workflow_data.get('status', 'N/A')}
Execution Time: {workflow_data.get('execution_time', 'N/A')}s
Success Rate: {workflow_data.get('success_rate', 'N/A')}

PERFORMANCE METRICS
==================
{json.dumps(workflow_data.get('metrics', {}), indent=2)}

RECOMMENDATIONS
===============
{json.dumps(workflow_data.get('recommendations', []), indent=2)}
        """
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return filename
