#!/usr/bin/env python3
"""
Test script to verify the routing fix for selected agents.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.langgraph.core.workflow_manager import WorkflowManager
from agents.langgraph.states.unified_state import create_unified_state


async def test_routing_with_selected_agent():
    """Test that routing respects the user's selected agent."""
    print("🧪 Testing routing with selected agent...")
    
    try:
        # Initialize workflow manager
        wm = WorkflowManager()
        print(f"✅ WorkflowManager initialized with {len(wm.agent_nodes)} agents")
        print(f"📋 Available agents: {list(wm.agent_nodes.keys())}")
        
        # Create test state with current_persona set to marketing agent
        test_state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            initial_message={
                "id": "test_message",
                "content": "who are you",
                "type": "user"
            }
        )
        
        # Set current_persona to marketing agent
        test_state["current_persona"] = "composable-marketing-ai"
        print(f"🎯 Set current_persona to: {test_state['current_persona']}")
        
        # Test routing decision
        routing_target = wm._route_to_agent(test_state)
        print(f"🎯 Routing target: {routing_target}")
        
        # Verify routing respects user selection
        if routing_target == "composable-marketing-ai":
            print("✅ SUCCESS: Routing correctly respects current_persona")
            return True
        else:
            print(f"❌ FAILED: Expected 'composable-marketing-ai', got '{routing_target}'")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_routing_node_behavior():
    """Test that routing node respects selected agents."""
    print("\n🧪 Testing routing node behavior...")
    
    try:
        from agents.langgraph.nodes.routing_node import RoutingNode
        
        # Initialize workflow manager to get agent nodes
        wm = WorkflowManager()
        
        # Create routing node
        routing_node = RoutingNode(wm.agent_nodes)
        print(f"✅ RoutingNode initialized")
        
        # Create test state with current_persona
        test_state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            initial_message={
                "id": "test_message",
                "content": "who are you",
                "type": "user"
            }
        )
        test_state["current_persona"] = "composable-marketing-ai"
        
        # Execute routing node
        result_state = await routing_node.execute(test_state)
        
        # Check if routing_analysis was set (it shouldn't be when current_persona is set)
        routing_analysis = result_state.get("routing_analysis")
        if routing_analysis:
            target_agent = routing_analysis.get("target_agent")
            print(f"⚠️ RoutingNode set routing_analysis with target_agent: {target_agent}")
            if target_agent == "composable-marketing-ai":
                print("✅ At least the target matches current_persona")
                return True
            else:
                print(f"❌ FAILED: RoutingNode overrode current_persona with: {target_agent}")
                return False
        else:
            print("✅ SUCCESS: RoutingNode correctly deferred to workflow manager")
            return True
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all routing tests."""
    print("🚀 Starting routing fix tests...\n")
    
    test1_passed = await test_routing_with_selected_agent()
    test2_passed = await test_routing_node_behavior()
    
    print(f"\n📊 Test Results:")
    print(f"   Routing Manager Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Routing Node Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! Routing fix is working correctly.")
        return 0
    else:
        print("\n💥 Some tests failed. Routing fix needs more work.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
