"""
Task Repository Implementation.

Provides specialized repository operations for Task entities,
replacing the task-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import Task
from ..models.schemas import TaskCreate, TaskUpdate, TaskResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class TaskRepository(BaseRepository[Task]):
    """Repository for Task entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, Task)
        self.logger = logger
    
    def create_task(
        self,
        user_id: int,
        task_type: str,
        input_file_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Task:
        """
        Create a new task.
        
        Args:
            user_id: ID of the user who owns the task
            task_type: Type of the task
            input_file_id: Optional ID of the input file
            config: Optional task configuration
            
        Returns:
            Created task instance
            
        Raises:
            RepositoryError: If task creation fails
        """
        try:
            task_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'task_type': task_type,
                'input_file_id': input_file_id,
                'config': config or {},
                'status': 'pending'  # Default status
            }
            
            task = Task(**task_data)
            
            self.session.add(task)
            self.session.commit()
            self.session.refresh(task)
            
            self.logger.info(f"Created task {task.id} for user {user_id}: {task_type}")
            return task
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create task: {e}")
            raise RepositoryError(
                f"Failed to create task: {str(e)}",
                entity_type="Task",
                operation="create"
            )
    
    def get_user_tasks(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        task_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[Task]:
        """
        Get all tasks for a user with optional filtering.
        
        Args:
            user_id: ID of the user
            skip: Number of records to skip
            limit: Maximum number of records to return
            task_type: Optional task type filter
            status: Optional status filter
            
        Returns:
            List of tasks owned by the user
        """
        try:
            query = self.session.query(Task).filter(Task.user_id == user_id)
            
            if task_type:
                query = query.filter(Task.task_type == task_type)
            
            if status:
                query = query.filter(Task.status == status)
            
            return query.order_by(Task.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get user tasks: {e}")
            raise RepositoryError(
                f"Failed to get user tasks: {str(e)}",
                entity_type="Task",
                operation="get_user_tasks"
            )
    
    def update_task_status(
        self,
        task_id: str,
        status: str,
        message: Optional[str] = None,
        result_file_path: Optional[str] = None
    ) -> Optional[Task]:
        """
        Update a task's status and related fields.
        
        Args:
            task_id: ID of the task to update
            status: New status
            message: Optional status message
            result_file_path: Optional path to result file
            
        Returns:
            Updated task or None if not found
        """
        try:
            task = self.get_by_id(task_id)
            if not task:
                self.logger.warning(f"Task {task_id} not found for status update")
                return None
            
            task.status = status
            
            if message is not None:
                task.message = message
            
            if result_file_path is not None:
                task.result_file_path = result_file_path
            
            self.session.commit()
            self.session.refresh(task)
            
            self.logger.info(f"Updated task {task_id} status to {status}")
            return task
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update task status: {e}")
            raise RepositoryError(
                f"Failed to update task status: {str(e)}",
                entity_type="Task",
                operation="update_status"
            )
    
    def get_tasks_by_status(
        self,
        status: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Task]:
        """
        Get tasks by status across all users.
        
        Args:
            status: Status to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of tasks with the specified status
        """
        try:
            return self.session.query(Task).filter(
                Task.status == status
            ).order_by(Task.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get tasks by status: {e}")
            raise RepositoryError(
                f"Failed to get tasks by status: {str(e)}",
                entity_type="Task",
                operation="get_tasks_by_status"
            )
    
    def get_tasks_by_type(
        self,
        task_type: str,
        user_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Task]:
        """
        Get tasks by type, optionally filtered by user.
        
        Args:
            task_type: Type of tasks to retrieve
            user_id: Optional user ID filter
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of tasks with the specified type
        """
        try:
            query = self.session.query(Task).filter(Task.task_type == task_type)
            
            if user_id:
                query = query.filter(Task.user_id == user_id)
            
            return query.order_by(Task.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get tasks by type: {e}")
            raise RepositoryError(
                f"Failed to get tasks by type: {str(e)}",
                entity_type="Task",
                operation="get_tasks_by_type"
            )
    
    def update_task_config(
        self,
        task_id: str,
        config: Dict[str, Any]
    ) -> Optional[Task]:
        """
        Update a task's configuration.
        
        Args:
            task_id: ID of the task to update
            config: New configuration data
            
        Returns:
            Updated task or None if not found
        """
        try:
            task = self.get_by_id(task_id)
            if not task:
                self.logger.warning(f"Task {task_id} not found for config update")
                return None
            
            # Merge with existing config
            existing_config = task.config or {}
            existing_config.update(config)
            task.config = existing_config
            
            self.session.commit()
            self.session.refresh(task)
            
            self.logger.info(f"Updated config for task {task_id}")
            return task
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update task config: {e}")
            raise RepositoryError(
                f"Failed to update task config: {str(e)}",
                entity_type="Task",
                operation="update_config"
            )
    
    def get_task_count_by_user(self, user_id: int) -> int:
        """
        Get the count of tasks for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Number of tasks owned by the user
        """
        try:
            return self.session.query(Task).filter(
                Task.user_id == user_id
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get task count: {e}")
            raise RepositoryError(
                f"Failed to get task count: {str(e)}",
                entity_type="Task",
                operation="get_task_count_by_user"
            )
    
    def get_task_count_by_status(self, status: str) -> int:
        """
        Get the count of tasks by status.
        
        Args:
            status: Status to count
            
        Returns:
            Number of tasks with the specified status
        """
        try:
            return self.session.query(Task).filter(
                Task.status == status
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get task count by status: {e}")
            raise RepositoryError(
                f"Failed to get task count by status: {str(e)}",
                entity_type="Task",
                operation="get_task_count_by_status"
            )
    
    def delete_task(self, task_id: str) -> bool:
        """
        Delete a task.
        
        Args:
            task_id: ID of the task to delete
            
        Returns:
            True if task was deleted, False if not found
        """
        try:
            task = self.get_by_id(task_id)
            if not task:
                self.logger.warning(f"Task {task_id} not found for deletion")
                return False
            
            self.session.delete(task)
            self.session.commit()
            
            self.logger.info(f"Deleted task {task_id}")
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to delete task: {e}")
            raise RepositoryError(
                f"Failed to delete task: {str(e)}",
                entity_type="Task",
                operation="delete"
            )
