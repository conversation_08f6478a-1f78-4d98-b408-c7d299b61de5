"""
Business Profile API endpoints.

This module provides REST API endpoints for managing business profiles
and their data source associations.
"""

import logging
import uuid
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Path, Request
from pydantic import ValidationError

from ..dependencies import get_business_profile_repository, get_db_service
from ..repositories.business_profile_repository import BusinessProfileRepository
from ..repositories.database_service import DatabaseService
from ..database import get_db
from sqlalchemy.orm import Session
from ..models.auth import User
from ..models.business_profile import (
    BusinessProfileCreate,
    BusinessProfileUpdate,
    BusinessProfileResponse,
    BusinessProfileWithDataSources,
    BusinessProfileListResponse,
    BusinessProfileSwitchRequest,
    BusinessProfileDataSourceAssignmentCreate,
    BusinessProfileDataSourceAssignmentUpdate,
    BusinessProfileDataSourceAssignment
)
from ..services.business_profile_service import BusinessProfileService
from ..services.business_profile_template_service import get_business_profile_template_service
from ..auth import get_current_active_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/business-profiles", tags=["business-profiles"])


def validate_uuid(uuid_string: str, field_name: str = "ID") -> str:
    """
    Validate that a string is a valid UUID.

    Args:
        uuid_string: String to validate
        field_name: Name of the field for error messages

    Returns:
        The validated UUID string

    Raises:
        HTTPException: If the UUID is invalid
    """
    try:
        # This will raise ValueError if invalid
        uuid.UUID(uuid_string)
        return uuid_string
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid {field_name} format. Must be a valid UUID."
        )


def get_business_profile_service(db: Session = Depends(get_db)) -> BusinessProfileService:
    """Dependency to get business profile service."""
    return BusinessProfileService(db)


@router.post("/debug", response_model=dict)
async def debug_business_profile_data(
    request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """Debug endpoint to see raw request data."""
    try:
        body = await request.body()
        import json
        raw_data = json.loads(body.decode())
        logger.info(f"Raw request data: {raw_data}")

        # Try to validate with BusinessProfileCreate
        try:
            profile_data = BusinessProfileCreate(**raw_data)
            logger.info(f"Validation successful: {profile_data}")
            return {"status": "valid", "data": profile_data.model_dump()}
        except ValidationError as e:
            logger.error(f"Validation failed: {e}")
            return {"status": "invalid", "errors": e.errors(), "raw_data": raw_data}

    except Exception as e:
        logger.error(f"Debug endpoint error: {e}")
        return {"status": "error", "message": str(e)}

@router.post("/", response_model=BusinessProfileResponse)
async def create_business_profile(
    profile_data: BusinessProfileCreate,
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Create a new business profile."""
    try:
        logger.info(f"Received profile data: {profile_data.model_dump()}")

        # Validate input data
        if not profile_data.name or len(profile_data.name.strip()) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Profile name cannot be empty"
            )

        profile = await service.create_profile(current_user.id, profile_data)
        logger.info(f"Created business profile for user {current_user.id}")
        return profile
    except HTTPException:
        raise
    except ValidationError as e:
        logger.warning(f"Validation error creating business profile for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid profile data provided: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error creating business profile for user {current_user.id}: {type(e).__name__}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create business profile"
        )


@router.get("/", response_model=BusinessProfileListResponse)
async def list_business_profiles(
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """List all business profiles for the current user."""
    try:
        profiles = service.list_profiles(current_user.id)
        return profiles
    except Exception as e:
        logger.error(f"Error listing business profiles: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list business profiles"
        )


@router.get("/active", response_model=BusinessProfileResponse)
async def get_active_business_profile(
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Get the active business profile for the current user."""
    try:
        profile = service.get_active_profile(current_user.id)
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active business profile found"
            )
        return profile
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting active business profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get active business profile"
        )


@router.post("/switch", response_model=dict)
async def switch_active_profile(
    switch_request: BusinessProfileSwitchRequest,
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Switch the active business profile."""
    try:
        # UUID validation is handled by the Pydantic model
        # Import security functions
        from ..security.business_profile_security import security_audit_log

        success = service.switch_active_profile(current_user.id, switch_request.profile_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Business profile not found or access denied"
            )

        # Security audit log
        security_audit_log(
            "profile_switched",
            current_user.id,
            switch_request.profile_id
        )

        logger.info(f"Switched active profile to {switch_request.profile_id} for user {current_user.id}")
        return {"success": True, "message": "Active profile switched successfully"}
    except HTTPException:
        raise
    except ValidationError as e:
        logger.warning(f"Validation error switching profile for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid profile ID provided"
        )
    except Exception as e:
        logger.error(f"Unexpected error switching active profile for user {current_user.id}: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to switch active profile"
        )


@router.get("/{profile_id}", response_model=BusinessProfileResponse)
async def get_business_profile(
    profile_id: str = Path(..., description="Business profile UUID"),
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Get a specific business profile."""
    try:
        # Validate UUID format
        validated_profile_id = validate_uuid(profile_id, "Profile ID")

        profile = service.get_profile(validated_profile_id, current_user.id)
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Business profile not found or access denied"
            )
        return profile
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting business profile for user {current_user.id}: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get business profile"
        )


@router.get("/{profile_id}/with-data-sources", response_model=BusinessProfileWithDataSources)
async def get_business_profile_with_data_sources(
    profile_id: str = Path(..., description="Business profile UUID"),
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Get a business profile with its data source assignments."""
    try:
        # Validate UUID format
        validated_profile_id = validate_uuid(profile_id, "Profile ID")

        profile = service.get_profile_with_data_sources(validated_profile_id, current_user.id)
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Business profile not found or access denied"
            )
        return profile
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting business profile with data sources for user {current_user.id}: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get business profile with data sources"
        )


@router.put("/{profile_id}", response_model=BusinessProfileResponse)
async def update_business_profile(
    update_data: BusinessProfileUpdate,
    profile_id: str = Path(..., description="Business profile UUID"),
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Update a business profile."""
    try:
        # Validate UUID format
        validated_profile_id = validate_uuid(profile_id, "Profile ID")

        # Import security functions
        from ..security.business_profile_security import (
            validate_text_input,
            validate_metadata,
            security_audit_log
        )

        # Validate update data
        if update_data.name is not None:
            update_data.name = validate_text_input(
                update_data.name, "Profile name", 255, required=True
            )

        if update_data.context_metadata is not None:
            update_data.context_metadata = validate_metadata(
                update_data.context_metadata, "context_metadata"
            )

        profile = service.update_profile(validated_profile_id, current_user.id, update_data)
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Business profile not found or access denied"
            )

        # Security audit log
        security_audit_log(
            "profile_updated",
            current_user.id,
            validated_profile_id,
            {"fields_updated": list(update_data.model_dump(exclude_unset=True).keys())}
        )

        logger.info(f"Updated business profile {validated_profile_id} for user {current_user.id}")
        return profile
    except HTTPException:
        raise
    except ValidationError as e:
        logger.warning(f"Validation error updating business profile for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid profile data provided"
        )
    except Exception as e:
        logger.error(f"Unexpected error updating business profile for user {current_user.id}: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update business profile"
        )


@router.delete("/{profile_id}")
async def delete_business_profile(
    profile_id: str = Path(..., description="Business profile UUID"),
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Delete a business profile and all associated data."""
    try:
        # Validate UUID format
        validated_profile_id = validate_uuid(profile_id, "Profile ID")

        # Import security functions
        from ..security.business_profile_security import security_audit_log

        success = await service.delete_profile(validated_profile_id, current_user.id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Business profile not found or access denied"
            )

        # Security audit log
        security_audit_log(
            "profile_deleted",
            current_user.id,
            validated_profile_id
        )

        logger.info(f"Deleted business profile {validated_profile_id} for user {current_user.id}")
        return {"success": True, "message": "Business profile deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting business profile for user {current_user.id}: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete business profile"
        )


@router.post("/{profile_id}/data-sources", response_model=BusinessProfileDataSourceAssignment)
async def assign_data_source_to_profile(
    assignment_data: BusinessProfileDataSourceAssignmentCreate,
    profile_id: str = Path(..., description="Business profile UUID"),
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Assign a data source to a business profile."""
    try:
        # Validate UUID format
        validated_profile_id = validate_uuid(profile_id, "Profile ID")

        # Import security functions
        from ..security.business_profile_security import (
            check_profile_data_source_limit,
            security_audit_log
        )

        # Check data source limit
        check_profile_data_source_limit(service.db, validated_profile_id)

        assignment = service.assign_data_source(validated_profile_id, current_user.id, assignment_data)
        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Business profile or data source not found or access denied"
            )

        # Security audit log
        security_audit_log(
            "data_source_assigned",
            current_user.id,
            validated_profile_id,
            {"data_source_id": assignment_data.data_source_id, "role": assignment_data.role}
        )

        logger.info(f"Assigned data source {assignment_data.data_source_id} to profile {validated_profile_id}")
        return assignment
    except HTTPException:
        raise
    except ValidationError as e:
        logger.warning(f"Validation error assigning data source for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid assignment data provided"
        )
    except Exception as e:
        logger.error(f"Unexpected error assigning data source for user {current_user.id}: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign data source to profile"
        )


@router.delete("/{profile_id}/data-sources/{data_source_id}")
async def unassign_data_source_from_profile(
    profile_id: str = Path(..., description="Business profile UUID"),
    data_source_id: str = Path(..., description="Data source UUID"),
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Remove a data source assignment from a business profile."""
    try:
        # Validate UUID formats
        validated_profile_id = validate_uuid(profile_id, "Profile ID")
        validated_data_source_id = validate_uuid(data_source_id, "Data Source ID")

        # Import security functions
        from ..security.business_profile_security import security_audit_log

        success = service.unassign_data_source(validated_profile_id, current_user.id, validated_data_source_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Business profile or data source assignment not found or access denied"
            )

        # Security audit log
        security_audit_log(
            "data_source_unassigned",
            current_user.id,
            validated_profile_id,
            {"data_source_id": validated_data_source_id}
        )

        logger.info(f"Unassigned data source {validated_data_source_id} from profile {validated_profile_id}")
        return {"success": True, "message": "Data source unassigned successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error unassigning data source for user {current_user.id}: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to unassign data source from profile"
        )


@router.put("/{profile_id}/data-sources/{data_source_id}", response_model=BusinessProfileDataSourceAssignment)
async def update_data_source_assignment(
    profile_id: str,
    data_source_id: str,
    update_data: BusinessProfileDataSourceAssignmentUpdate,
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Update a data source assignment."""
    try:
        assignment = service.update_data_source_assignment(profile_id, current_user.id, data_source_id, update_data)
        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Business profile or data source assignment not found"
            )
        return assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating data source assignment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update data source assignment"
        )


@router.get("/templates/{industry}")
async def get_business_profile_template(
    industry: str = Path(..., description="Industry name"),
    current_user: User = Depends(get_current_active_user)
):
    """Get business profile template for a specific industry."""
    try:
        template_service = get_business_profile_template_service()
        template = template_service.get_template(industry)

        return {
            "industry": industry,
            "template": template,
            "available_industries": template_service.get_available_industries()
        }
    except Exception as e:
        logger.error(f"Error getting template for industry {industry}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get business profile template"
        )


@router.get("/templates")
async def get_available_templates(
    current_user: User = Depends(get_current_active_user)
):
    """Get list of available business profile templates."""
    try:
        template_service = get_business_profile_template_service()
        industries = template_service.get_available_industries()

        return {
            "industries": industries,
            "total": len(industries)
        }
    except Exception as e:
        logger.error(f"Error getting available templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get available templates"
        )


@router.post("/default", response_model=BusinessProfileResponse)
async def create_default_profile(
    industry: str = "General",
    current_user: User = Depends(get_current_active_user),
    service: BusinessProfileService = Depends(get_business_profile_service)
):
    """Create a default business profile for the user."""
    try:
        # Check if user already has profiles
        existing_profiles = service.list_profiles(current_user.id)
        if existing_profiles.profiles:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User already has business profiles"
            )

        profile = await service.create_default_profile(current_user.id, industry)
        logger.info(f"Created default profile for user {current_user.id}")
        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating default profile for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create default profile"
        )
