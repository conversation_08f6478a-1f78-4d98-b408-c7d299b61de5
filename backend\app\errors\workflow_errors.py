"""
Workflow-specific Error Classes.

Specialized error classes for workflow-related operations and failures.
"""

from typing import Optional, Dict, Any, List
from .base_errors import DatageniusError, ErrorCategory, ErrorSeverity


class WorkflowError(DatageniusError):
    """Base class for workflow-related errors."""
    
    def __init__(
        self,
        message: str,
        workflow_id: Optional[str] = None,
        workflow_name: Optional[str] = None,
        step_id: Optional[str] = None,
        error_code: str = "WORKFLOW_ERROR",
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        user_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        **kwargs
    ):
        details = details or {}
        if workflow_id:
            details["workflow_id"] = workflow_id
        if workflow_name:
            details["workflow_name"] = workflow_name
        if step_id:
            details["step_id"] = step_id
        
        super().__init__(
            message=message,
            error_code=error_code,
            category=ErrorCategory.INTERNAL_ERROR,
            severity=severity,
            user_message=user_message or "Workflow operation failed",
            details=details,
            cause=cause,
            **kwargs
        )


class WorkflowValidationError(WorkflowError):
    """Error in workflow definition or validation."""
    
    def __init__(
        self,
        message: str,
        validation_errors: Optional[List[str]] = None,
        invalid_field: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if validation_errors:
            details["validation_errors"] = validation_errors
        if invalid_field:
            details["invalid_field"] = invalid_field
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_VALIDATION_ERROR')
        kwargs.setdefault('category', ErrorCategory.VALIDATION)
        kwargs.setdefault('severity', ErrorSeverity.LOW)
        kwargs.setdefault('user_message', 'Workflow definition is invalid')
        
        super().__init__(message, **kwargs)


class WorkflowExecutionError(WorkflowError):
    """Error during workflow execution."""
    
    def __init__(
        self,
        message: str,
        execution_id: Optional[str] = None,
        failed_step: Optional[str] = None,
        step_error: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if execution_id:
            details["execution_id"] = execution_id
        if failed_step:
            details["failed_step"] = failed_step
        if step_error:
            details["step_error"] = step_error
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_EXECUTION_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Workflow execution failed')
        
        super().__init__(message, **kwargs)


class WorkflowTimeoutError(WorkflowError):
    """Error when workflow execution times out."""
    
    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[float] = None,
        elapsed_seconds: Optional[float] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
        if elapsed_seconds:
            details["elapsed_seconds"] = elapsed_seconds
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_TIMEOUT_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Workflow execution timed out')
        
        super().__init__(message, **kwargs)


class WorkflowStateError(WorkflowError):
    """Error when workflow is in invalid state for operation."""
    
    def __init__(
        self,
        message: str,
        current_state: Optional[str] = None,
        required_state: Optional[str] = None,
        allowed_states: Optional[List[str]] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if current_state:
            details["current_state"] = current_state
        if required_state:
            details["required_state"] = required_state
        if allowed_states:
            details["allowed_states"] = allowed_states
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_STATE_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Workflow is in invalid state for this operation')
        
        super().__init__(message, **kwargs)


class WorkflowNotFoundError(WorkflowError):
    """Error when workflow is not found."""
    
    def __init__(
        self,
        message: str,
        workflow_id: Optional[str] = None,
        **kwargs
    ):
        kwargs.setdefault('error_code', 'WORKFLOW_NOT_FOUND')
        kwargs.setdefault('category', ErrorCategory.NOT_FOUND)
        kwargs.setdefault('severity', ErrorSeverity.LOW)
        kwargs.setdefault('user_message', 'Workflow not found')
        
        super().__init__(message, workflow_id=workflow_id, **kwargs)


class WorkflowPermissionError(WorkflowError):
    """Error when user lacks permission to access workflow."""
    
    def __init__(
        self,
        message: str,
        required_permission: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if required_permission:
            details["required_permission"] = required_permission
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_PERMISSION_ERROR')
        kwargs.setdefault('category', ErrorCategory.AUTHORIZATION)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Access denied to workflow')
        
        super().__init__(message, **kwargs)


class WorkflowDependencyError(WorkflowError):
    """Error in workflow dependencies or prerequisites."""
    
    def __init__(
        self,
        message: str,
        missing_dependencies: Optional[List[str]] = None,
        dependency_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if missing_dependencies:
            details["missing_dependencies"] = missing_dependencies
        if dependency_type:
            details["dependency_type"] = dependency_type
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_DEPENDENCY_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Workflow dependencies not satisfied')
        
        super().__init__(message, **kwargs)


class WorkflowConcurrencyError(WorkflowError):
    """Error related to workflow concurrency limits."""
    
    def __init__(
        self,
        message: str,
        max_concurrent: Optional[int] = None,
        current_running: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if max_concurrent:
            details["max_concurrent"] = max_concurrent
        if current_running:
            details["current_running"] = current_running
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_CONCURRENCY_ERROR')
        kwargs.setdefault('category', ErrorCategory.RATE_LIMIT)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Workflow concurrency limit exceeded')
        
        super().__init__(message, **kwargs)


class WorkflowResourceError(WorkflowError):
    """Error related to workflow resource constraints."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        resource_limit: Optional[str] = None,
        current_usage: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if resource_type:
            details["resource_type"] = resource_type
        if resource_limit:
            details["resource_limit"] = resource_limit
        if current_usage:
            details["current_usage"] = current_usage
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_RESOURCE_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Workflow resource limit exceeded')
        
        super().__init__(message, **kwargs)


class WorkflowConfigurationError(WorkflowError):
    """Error in workflow configuration."""
    
    def __init__(
        self,
        message: str,
        config_field: Optional[str] = None,
        config_value: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if config_field:
            details["config_field"] = config_field
        if config_value:
            details["config_value"] = config_value
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_CONFIGURATION_ERROR')
        kwargs.setdefault('category', ErrorCategory.CONFIGURATION)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Workflow configuration is invalid')
        
        super().__init__(message, **kwargs)


class WorkflowVersionError(WorkflowError):
    """Error related to workflow versioning."""
    
    def __init__(
        self,
        message: str,
        current_version: Optional[str] = None,
        required_version: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if current_version:
            details["current_version"] = current_version
        if required_version:
            details["required_version"] = required_version
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'WORKFLOW_VERSION_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Workflow version mismatch')
        
        super().__init__(message, **kwargs)
