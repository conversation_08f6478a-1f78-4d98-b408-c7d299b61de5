"""
Comprehensive Audit Trail Service for Phase 4 Privacy & Compliance.

This module provides comprehensive audit logging, data access monitoring,
and compliance reporting tools for regulatory compliance.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from enum import Enum
from dataclasses import dataclass, field
from app.security.business_profile_security import sanitize_log_data
from app.utils.ip_utils import anonymize_ip_for_logging

logger = logging.getLogger(__name__)


class AuditEventType(str, Enum):
    """Types of audit events."""
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    DATA_DELETION = "data_deletion"
    DATA_EXPORT = "data_export"
    USER_AUTHENTICATION = "user_authentication"
    PERMISSION_CHANGE = "permission_change"
    SYSTEM_CONFIGURATION = "system_configuration"
    SECURITY_EVENT = "security_event"
    PRIVACY_EVENT = "privacy_event"
    COMPLIANCE_EVENT = "compliance_event"


class AuditSeverity(str, Enum):
    """Severity levels for audit events."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Comprehensive audit event record."""
    event_id: str
    event_type: AuditEventType
    severity: AuditSeverity
    timestamp: datetime = field(default_factory=datetime.now)
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    action: Optional[str] = None
    outcome: str = "success"  # success, failure, partial
    details: Dict[str, Any] = field(default_factory=dict)
    compliance_tags: Set[str] = field(default_factory=set)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert audit event to dictionary."""
        return {
            "event_id": self.event_id,
            "event_type": self.event_type.value,
            "severity": self.severity.value,
            "timestamp": self.timestamp.isoformat(),
            "user_id": self.user_id,
            "session_id": self.session_id,
            "ip_address": anonymize_ip_for_logging(self.ip_address) if self.ip_address else None,
            "user_agent": self.user_agent,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "action": self.action,
            "outcome": self.outcome,
            "details": sanitize_log_data(self.details),
            "compliance_tags": list(self.compliance_tags)
        }


class DataAccessMonitor:
    """Monitor and log data access patterns."""
    
    def __init__(self):
        """Initialize the data access monitor."""
        self.access_patterns: Dict[str, List[Dict[str, Any]]] = {}
        self.suspicious_patterns: List[Dict[str, Any]] = []
        
    def log_data_access(
        self,
        user_id: str,
        resource_type: str,
        resource_id: str,
        action: str,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log data access event.
        
        Args:
            user_id: User identifier
            resource_type: Type of resource accessed
            resource_id: Resource identifier
            action: Action performed
            ip_address: User's IP address
            details: Additional details
        """
        access_event = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "resource_type": resource_type,
            "resource_id": resource_id,
            "action": action,
            "ip_address": anonymize_ip_for_logging(ip_address) if ip_address else None,
            "details": details or {}
        }
        
        # Store access pattern
        if user_id not in self.access_patterns:
            self.access_patterns[user_id] = []
        
        self.access_patterns[user_id].append(access_event)
        
        # Keep only last 1000 events per user
        if len(self.access_patterns[user_id]) > 1000:
            self.access_patterns[user_id] = self.access_patterns[user_id][-1000:]
        
        # Check for suspicious patterns
        self._check_suspicious_access(user_id, access_event)
    
    def _check_suspicious_access(self, user_id: str, access_event: Dict[str, Any]) -> None:
        """Check for suspicious access patterns."""
        user_events = self.access_patterns.get(user_id, [])
        
        # Check for rapid access (more than 100 requests in 1 minute)
        recent_events = [
            event for event in user_events
            if datetime.fromisoformat(event["timestamp"]) > datetime.now() - timedelta(minutes=1)
        ]
        
        if len(recent_events) > 100:
            self.suspicious_patterns.append({
                "type": "rapid_access",
                "user_id": user_id,
                "event_count": len(recent_events),
                "timestamp": datetime.now().isoformat(),
                "details": access_event
            })
        
        # Check for unusual resource access
        resource_types = set(event["resource_type"] for event in user_events[-50:])
        if len(resource_types) > 10:  # Accessing many different resource types
            self.suspicious_patterns.append({
                "type": "broad_access",
                "user_id": user_id,
                "resource_types": list(resource_types),
                "timestamp": datetime.now().isoformat(),
                "details": access_event
            })
    
    def get_user_access_summary(self, user_id: str) -> Dict[str, Any]:
        """Get access summary for a user."""
        user_events = self.access_patterns.get(user_id, [])
        
        if not user_events:
            return {"user_id": user_id, "total_accesses": 0}
        
        resource_types = {}
        actions = {}
        
        for event in user_events:
            resource_type = event["resource_type"]
            action = event["action"]
            
            resource_types[resource_type] = resource_types.get(resource_type, 0) + 1
            actions[action] = actions.get(action, 0) + 1
        
        return {
            "user_id": user_id,
            "total_accesses": len(user_events),
            "resource_types": resource_types,
            "actions": actions,
            "first_access": user_events[0]["timestamp"],
            "last_access": user_events[-1]["timestamp"]
        }


class ComprehensiveAuditService:
    """Comprehensive audit trail service."""
    
    def __init__(self):
        """Initialize the audit service."""
        self.audit_events: List[AuditEvent] = []
        self.data_access_monitor = DataAccessMonitor()
        self.max_events = 10000  # Keep last 10,000 events in memory
        
    def log_audit_event(
        self,
        event_type: AuditEventType,
        severity: AuditSeverity = AuditSeverity.MEDIUM,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        action: Optional[str] = None,
        outcome: str = "success",
        details: Optional[Dict[str, Any]] = None,
        compliance_tags: Optional[Set[str]] = None
    ) -> str:
        """
        Log a comprehensive audit event.
        
        Args:
            event_type: Type of audit event
            severity: Severity level
            user_id: User identifier
            session_id: Session identifier
            ip_address: User's IP address
            user_agent: User's user agent
            resource_type: Type of resource
            resource_id: Resource identifier
            action: Action performed
            outcome: Outcome of the action
            details: Additional details
            compliance_tags: Compliance-related tags
            
        Returns:
            Event ID
        """
        event_id = f"audit_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        audit_event = AuditEvent(
            event_id=event_id,
            event_type=event_type,
            severity=severity,
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            outcome=outcome,
            details=details or {},
            compliance_tags=compliance_tags or set()
        )
        
        self.audit_events.append(audit_event)
        
        # Trim events if needed
        if len(self.audit_events) > self.max_events:
            self.audit_events = self.audit_events[-self.max_events:]
        
        # Log data access if applicable
        if event_type == AuditEventType.DATA_ACCESS and user_id and resource_type and resource_id:
            self.data_access_monitor.log_data_access(
                user_id=user_id,
                resource_type=resource_type,
                resource_id=resource_id,
                action=action or "access",
                ip_address=ip_address,
                details=details
            )
        
        # Log to standard logger
        logger.info(f"AUDIT_EVENT: {json.dumps(audit_event.to_dict())}")
        
        return event_id
    
    def log_data_access(
        self,
        user_id: str,
        resource_type: str,
        resource_id: str,
        action: str = "read",
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Log data access event with monitoring.
        
        Args:
            user_id: User identifier
            resource_type: Type of resource
            resource_id: Resource identifier
            action: Action performed
            ip_address: User's IP address
            session_id: Session identifier
            details: Additional details
            
        Returns:
            Event ID
        """
        return self.log_audit_event(
            event_type=AuditEventType.DATA_ACCESS,
            severity=AuditSeverity.LOW,
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            details=details,
            compliance_tags={"data_access", "gdpr", "ccpa"}
        )
    
    def log_privacy_event(
        self,
        event_description: str,
        user_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        severity: AuditSeverity = AuditSeverity.MEDIUM
    ) -> str:
        """
        Log privacy-related event.
        
        Args:
            event_description: Description of the privacy event
            user_id: User identifier
            details: Additional details
            severity: Severity level
            
        Returns:
            Event ID
        """
        return self.log_audit_event(
            event_type=AuditEventType.PRIVACY_EVENT,
            severity=severity,
            user_id=user_id,
            action=event_description,
            details=details,
            compliance_tags={"privacy", "gdpr", "ccpa"}
        )
    
    def log_compliance_event(
        self,
        compliance_type: str,
        event_description: str,
        user_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        severity: AuditSeverity = AuditSeverity.HIGH
    ) -> str:
        """
        Log compliance-related event.
        
        Args:
            compliance_type: Type of compliance (gdpr, ccpa, sox, etc.)
            event_description: Description of the compliance event
            user_id: User identifier
            details: Additional details
            severity: Severity level
            
        Returns:
            Event ID
        """
        return self.log_audit_event(
            event_type=AuditEventType.COMPLIANCE_EVENT,
            severity=severity,
            user_id=user_id,
            action=event_description,
            details=details,
            compliance_tags={compliance_type.lower(), "compliance"}
        )
    
    def generate_compliance_report(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        compliance_tags: Optional[Set[str]] = None
    ) -> Dict[str, Any]:
        """
        Generate comprehensive compliance report.
        
        Args:
            start_date: Start date for report
            end_date: End date for report
            compliance_tags: Filter by compliance tags
            
        Returns:
            Compliance report
        """
        if not start_date:
            start_date = datetime.now() - timedelta(days=30)
        if not end_date:
            end_date = datetime.now()
        
        # Filter events by date range and compliance tags
        filtered_events = []
        for event in self.audit_events:
            if start_date <= event.timestamp <= end_date:
                if not compliance_tags or compliance_tags.intersection(event.compliance_tags):
                    filtered_events.append(event)
        
        # Generate report
        report = {
            "report_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "total_events": len(filtered_events),
            "events_by_type": {},
            "events_by_severity": {},
            "events_by_outcome": {},
            "compliance_tags": {},
            "suspicious_activities": len(self.data_access_monitor.suspicious_patterns),
            "top_users_by_activity": self._get_top_users_by_activity(filtered_events),
            "resource_access_summary": self._get_resource_access_summary(filtered_events)
        }
        
        # Count events by various dimensions
        for event in filtered_events:
            # By type
            event_type = event.event_type.value
            report["events_by_type"][event_type] = report["events_by_type"].get(event_type, 0) + 1
            
            # By severity
            severity = event.severity.value
            report["events_by_severity"][severity] = report["events_by_severity"].get(severity, 0) + 1
            
            # By outcome
            outcome = event.outcome
            report["events_by_outcome"][outcome] = report["events_by_outcome"].get(outcome, 0) + 1
            
            # By compliance tags
            for tag in event.compliance_tags:
                report["compliance_tags"][tag] = report["compliance_tags"].get(tag, 0) + 1
        
        return report
    
    def get_user_audit_trail(self, user_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get audit trail for a specific user.
        
        Args:
            user_id: User identifier
            limit: Maximum number of events to return
            
        Returns:
            List of audit events for the user
        """
        user_events = [
            event.to_dict() for event in self.audit_events
            if event.user_id == user_id
        ]
        
        # Sort by timestamp (most recent first)
        user_events.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return user_events[:limit]
    
    def _get_top_users_by_activity(self, events: List[AuditEvent]) -> List[Dict[str, Any]]:
        """Get top users by activity level."""
        user_activity = {}
        
        for event in events:
            if event.user_id:
                user_activity[event.user_id] = user_activity.get(event.user_id, 0) + 1
        
        # Sort by activity count
        sorted_users = sorted(user_activity.items(), key=lambda x: x[1], reverse=True)
        
        return [
            {"user_id": user_id, "event_count": count}
            for user_id, count in sorted_users[:10]
        ]
    
    def _get_resource_access_summary(self, events: List[AuditEvent]) -> Dict[str, Any]:
        """Get summary of resource access."""
        resource_access = {}
        
        for event in events:
            if event.resource_type and event.event_type == AuditEventType.DATA_ACCESS:
                resource_type = event.resource_type
                resource_access[resource_type] = resource_access.get(resource_type, 0) + 1
        
        return resource_access


# Global audit service instance
audit_service = ComprehensiveAuditService()
