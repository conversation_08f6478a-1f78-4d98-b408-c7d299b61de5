"""
Comprehensive Workflow Monitoring and Performance Optimization.

This module provides real-time monitoring, metrics collection, and
performance optimization for LangGraph workflows in the Datagenius system.
"""

import logging
import time
import psutil
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import asyncio

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from ..states.agent_state import DatageniusAgentState
from app.config import DATABASE_URL

logger = logging.getLogger(__name__)


@dataclass
class WorkflowMetrics:
    """Data class for workflow performance metrics."""
    workflow_id: str
    execution_time: float
    agent_transitions: int
    tool_executions: int
    success_rate: float
    error_count: int
    quality_score: Optional[float]
    memory_usage_mb: float
    cpu_usage_percent: float
    resource_usage: Dict[str, Any]
    timestamp: datetime


@dataclass
class AgentPerformanceMetrics:
    """Data class for individual agent performance metrics."""
    agent_id: str
    execution_count: int
    avg_execution_time: float
    success_rate: float
    error_rate: float
    quality_score: float
    last_execution: datetime


@dataclass
class OptimizationRecommendation:
    """Data class for optimization recommendations."""
    type: str  # 'routing', 'resource', 'workflow', 'agent'
    priority: str  # 'high', 'medium', 'low'
    description: str
    impact: str
    implementation: str
    estimated_improvement: float


class WorkflowMonitor:
    """
    Comprehensive workflow monitor and performance optimizer.
    
    This class provides:
    - Real-time performance monitoring
    - Metrics collection and analysis
    - Performance optimization recommendations
    - Resource usage tracking
    - Bottleneck identification
    """

    def __init__(self, database_url: str = None, monitoring_interval: int = 60):
        """
        Initialize the workflow monitor.
        
        Args:
            database_url: Database connection URL
            monitoring_interval: Monitoring interval in seconds
        """
        self.database_url = database_url or DATABASE_URL
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.monitoring_interval = monitoring_interval
        
        # In-memory metrics storage for real-time analysis
        self.metrics_history: deque = deque(maxlen=1000)
        self.agent_metrics: Dict[str, AgentPerformanceMetrics] = {}
        self.performance_cache: Dict[str, Dict[str, Any]] = {}
        
        # Performance thresholds
        self.performance_thresholds = {
            "max_execution_time": 300,  # 5 minutes
            "max_agent_transitions": 10,
            "min_success_rate": 0.95,
            "max_error_rate": 0.05,
            "max_memory_usage_mb": 1024,  # 1GB
            "max_cpu_usage_percent": 80.0
        }

        # Optimization rules
        self.optimization_rules = {
            "high_execution_time": self._optimize_execution_time,
            "excessive_transitions": self._optimize_agent_routing,
            "high_error_rate": self._optimize_error_handling,
            "resource_bottleneck": self._optimize_resource_usage
        }

        self.logger = logging.getLogger(__name__)

    async def monitor_workflow(self, workflow_id: str, state: DatageniusAgentState) -> WorkflowMetrics:
        """
        Monitor workflow execution and collect comprehensive metrics.
        
        Args:
            workflow_id: Workflow identifier
            state: Current workflow state
            
        Returns:
            Collected workflow metrics
        """
        try:
            # Collect system metrics
            memory_usage = psutil.virtual_memory().used / (1024 * 1024)  # MB
            cpu_usage = psutil.cpu_percent(interval=0.1)
            
            # Extract workflow metrics from state
            execution_metrics = state.get("execution_metrics", {})
            
            # Calculate execution time
            start_time_str = execution_metrics.get("start_time")
            execution_time = 0.0
            if start_time_str:
                try:
                    start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                    execution_time = (datetime.now(timezone.utc) - start_time).total_seconds()
                except:
                    pass
            
            # Calculate success rate
            agent_transitions = len(state.get("agent_history", []))
            tool_executions = execution_metrics.get("tool_executions", 0)
            error_count = len(state.get("error_history", []))
            total_operations = agent_transitions + tool_executions
            success_rate = max(0.0, 1.0 - (error_count / max(total_operations, 1)))
            
            # Calculate quality score
            quality_scores = state.get("quality_scores", {})
            avg_quality = sum(quality_scores.values()) / len(quality_scores) if quality_scores else None
            
            # Create metrics object
            metrics = WorkflowMetrics(
                workflow_id=workflow_id,
                execution_time=execution_time,
                agent_transitions=agent_transitions,
                tool_executions=tool_executions,
                success_rate=success_rate,
                error_count=error_count,
                quality_score=avg_quality,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage,
                resource_usage=execution_metrics,
                timestamp=datetime.now(timezone.utc)
            )
            
            # Store metrics
            self.metrics_history.append(metrics)
            
            # Save to database
            await self._save_metrics_to_database(metrics)
            
            # Check for performance issues
            await self._check_performance_alerts(metrics)
            
            # Update agent-specific metrics
            await self._update_agent_metrics(state)
            
            self.logger.debug(f"Workflow {workflow_id} metrics collected: {execution_time:.2f}s, {success_rate:.2f} success rate")
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error monitoring workflow {workflow_id}: {e}")
            # Return minimal metrics on error
            return WorkflowMetrics(
                workflow_id=workflow_id,
                execution_time=0.0,
                agent_transitions=0,
                tool_executions=0,
                success_rate=0.0,
                error_count=1,
                quality_score=None,
                memory_usage_mb=0.0,
                cpu_usage_percent=0.0,
                resource_usage={},
                timestamp=datetime.now(timezone.utc)
            )

    async def _save_metrics_to_database(self, metrics: WorkflowMetrics):
        """Save metrics to the database."""
        try:
            session = self.SessionLocal()
            
            insert_query = text("""
                INSERT INTO workflow_performance_metrics 
                (workflow_id, execution_time_ms, agent_transitions, tool_executions,
                 success_rate, error_count, quality_score, memory_usage_mb, 
                 cpu_usage_percent, resource_usage, recorded_at)
                VALUES (:workflow_id, :execution_time_ms, :agent_transitions, :tool_executions,
                        :success_rate, :error_count, :quality_score, :memory_usage_mb,
                        :cpu_usage_percent, :resource_usage, :recorded_at)
            """)
            
            session.execute(insert_query, {
                "workflow_id": metrics.workflow_id,
                "execution_time_ms": int(metrics.execution_time * 1000),
                "agent_transitions": metrics.agent_transitions,
                "tool_executions": metrics.tool_executions,
                "success_rate": metrics.success_rate,
                "error_count": metrics.error_count,
                "quality_score": metrics.quality_score,
                "memory_usage_mb": int(metrics.memory_usage_mb),
                "cpu_usage_percent": metrics.cpu_usage_percent,
                "resource_usage": str(metrics.resource_usage),
                "recorded_at": metrics.timestamp
            })
            
            session.commit()
            session.close()
            
        except Exception as e:
            self.logger.error(f"Error saving metrics to database: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()

    async def _check_performance_alerts(self, metrics: WorkflowMetrics):
        """Check metrics against thresholds and generate alerts."""
        alerts = []
        
        # Check execution time
        if metrics.execution_time > self.performance_thresholds["max_execution_time"]:
            alerts.append({
                "type": "execution_time",
                "severity": "high",
                "message": f"Workflow execution time ({metrics.execution_time:.2f}s) exceeds threshold",
                "threshold": self.performance_thresholds["max_execution_time"],
                "actual": metrics.execution_time
            })
        
        # Check agent transitions
        if metrics.agent_transitions > self.performance_thresholds["max_agent_transitions"]:
            alerts.append({
                "type": "agent_transitions",
                "severity": "medium",
                "message": f"Excessive agent transitions ({metrics.agent_transitions})",
                "threshold": self.performance_thresholds["max_agent_transitions"],
                "actual": metrics.agent_transitions
            })
        
        # Check success rate
        if metrics.success_rate < self.performance_thresholds["min_success_rate"]:
            alerts.append({
                "type": "success_rate",
                "severity": "high",
                "message": f"Low success rate ({metrics.success_rate:.2f})",
                "threshold": self.performance_thresholds["min_success_rate"],
                "actual": metrics.success_rate
            })
        
        # Check resource usage
        if metrics.memory_usage_mb > self.performance_thresholds["max_memory_usage_mb"]:
            alerts.append({
                "type": "memory_usage",
                "severity": "medium",
                "message": f"High memory usage ({metrics.memory_usage_mb:.0f}MB)",
                "threshold": self.performance_thresholds["max_memory_usage_mb"],
                "actual": metrics.memory_usage_mb
            })
        
        if metrics.cpu_usage_percent > self.performance_thresholds["max_cpu_usage_percent"]:
            alerts.append({
                "type": "cpu_usage",
                "severity": "medium",
                "message": f"High CPU usage ({metrics.cpu_usage_percent:.1f}%)",
                "threshold": self.performance_thresholds["max_cpu_usage_percent"],
                "actual": metrics.cpu_usage_percent
            })
        
        # Log alerts
        for alert in alerts:
            if alert["severity"] == "high":
                self.logger.warning(f"Performance alert: {alert['message']}")
            else:
                self.logger.info(f"Performance notice: {alert['message']}")
        
        # Store alerts for analysis
        if alerts:
            await self._store_performance_alerts(metrics.workflow_id, alerts)

    async def _store_performance_alerts(self, workflow_id: str, alerts: List[Dict[str, Any]]):
        """Store performance alerts for analysis."""
        # This could be extended to store alerts in a dedicated table
        # For now, we'll log them
        self.logger.info(f"Workflow {workflow_id} generated {len(alerts)} performance alerts")

    async def _update_agent_metrics(self, state: DatageniusAgentState):
        """Update agent-specific performance metrics."""
        try:
            current_agent = state.get("current_agent")
            if not current_agent:
                return
            
            # Get or create agent metrics
            if current_agent not in self.agent_metrics:
                self.agent_metrics[current_agent] = AgentPerformanceMetrics(
                    agent_id=current_agent,
                    execution_count=0,
                    avg_execution_time=0.0,
                    success_rate=1.0,
                    error_rate=0.0,
                    quality_score=1.0,
                    last_execution=datetime.now(timezone.utc)
                )
            
            agent_metrics = self.agent_metrics[current_agent]
            
            # Update metrics
            agent_metrics.execution_count += 1
            agent_metrics.last_execution = datetime.now(timezone.utc)
            
            # Update success/error rates
            error_count = len(state.get("error_history", []))
            total_operations = agent_metrics.execution_count
            agent_metrics.error_rate = error_count / total_operations
            agent_metrics.success_rate = 1.0 - agent_metrics.error_rate
            
            # Update quality score
            quality_scores = state.get("quality_scores", {})
            if current_agent in quality_scores:
                agent_metrics.quality_score = quality_scores[current_agent]
            
        except Exception as e:
            self.logger.error(f"Error updating agent metrics: {e}")

    async def optimize_routing(self) -> Dict[str, Any]:
        """Analyze routing patterns and suggest optimizations."""
        try:
            recent_metrics = self._get_recent_metrics(hours=24)
            
            if not recent_metrics:
                return {"message": "No recent metrics available for optimization"}
            
            # Analyze routing efficiency
            routing_analysis = {
                "most_efficient_paths": self._analyze_efficient_paths(recent_metrics),
                "bottleneck_agents": self._identify_bottlenecks(recent_metrics),
                "optimization_suggestions": self._generate_routing_optimizations(recent_metrics),
                "performance_trends": self._analyze_performance_trends(recent_metrics)
            }
            
            return routing_analysis
            
        except Exception as e:
            self.logger.error(f"Error optimizing routing: {e}")
            return {"error": str(e)}

    def _get_recent_metrics(self, hours: int = 24) -> List[WorkflowMetrics]:
        """Get recent metrics for analysis."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]

    def _analyze_efficient_paths(self, metrics: List[WorkflowMetrics]) -> Dict[str, Any]:
        """Analyze most efficient routing paths."""
        # Group metrics by execution patterns
        path_performance = defaultdict(list)

        for metric in metrics:
            # Create a simple path signature based on transitions and execution time
            path_key = f"transitions_{metric.agent_transitions}_tools_{metric.tool_executions}"
            path_performance[path_key].append({
                "execution_time": metric.execution_time,
                "success_rate": metric.success_rate,
                "quality_score": metric.quality_score or 0.0
            })

        # Calculate average performance for each path
        efficient_paths = {}
        for path, performances in path_performance.items():
            if len(performances) >= 3:  # Only consider paths with sufficient data
                avg_time = sum(p["execution_time"] for p in performances) / len(performances)
                avg_success = sum(p["success_rate"] for p in performances) / len(performances)
                avg_quality = sum(p["quality_score"] for p in performances) / len(performances)

                efficient_paths[path] = {
                    "avg_execution_time": avg_time,
                    "avg_success_rate": avg_success,
                    "avg_quality_score": avg_quality,
                    "sample_count": len(performances),
                    "efficiency_score": (avg_success * avg_quality) / max(avg_time, 1.0)
                }

        # Sort by efficiency score
        sorted_paths = sorted(efficient_paths.items(), key=lambda x: x[1]["efficiency_score"], reverse=True)

        return {
            "most_efficient": sorted_paths[:3],
            "least_efficient": sorted_paths[-3:],
            "total_paths_analyzed": len(efficient_paths)
        }

    def _identify_bottlenecks(self, metrics: List[WorkflowMetrics]) -> Dict[str, Any]:
        """Identify performance bottlenecks."""
        bottlenecks = {
            "execution_time": [],
            "agent_transitions": [],
            "resource_usage": [],
            "error_patterns": []
        }

        # Analyze execution time bottlenecks
        avg_execution_time = sum(m.execution_time for m in metrics) / len(metrics)
        slow_workflows = [m for m in metrics if m.execution_time > avg_execution_time * 1.5]

        if slow_workflows:
            bottlenecks["execution_time"] = {
                "count": len(slow_workflows),
                "avg_slow_time": sum(m.execution_time for m in slow_workflows) / len(slow_workflows),
                "threshold": avg_execution_time * 1.5,
                "recommendation": "Consider optimizing agent routing or tool execution"
            }

        # Analyze agent transition bottlenecks
        avg_transitions = sum(m.agent_transitions for m in metrics) / len(metrics)
        high_transition_workflows = [m for m in metrics if m.agent_transitions > avg_transitions * 1.5]

        if high_transition_workflows:
            bottlenecks["agent_transitions"] = {
                "count": len(high_transition_workflows),
                "avg_transitions": sum(m.agent_transitions for m in high_transition_workflows) / len(high_transition_workflows),
                "threshold": avg_transitions * 1.5,
                "recommendation": "Review routing logic to reduce unnecessary agent switches"
            }

        # Analyze resource usage bottlenecks
        avg_memory = sum(m.memory_usage_mb for m in metrics) / len(metrics)
        high_memory_workflows = [m for m in metrics if m.memory_usage_mb > avg_memory * 1.5]

        if high_memory_workflows:
            bottlenecks["resource_usage"] = {
                "count": len(high_memory_workflows),
                "avg_memory_mb": sum(m.memory_usage_mb for m in high_memory_workflows) / len(high_memory_workflows),
                "threshold": avg_memory * 1.5,
                "recommendation": "Optimize memory usage in agents and tools"
            }

        return bottlenecks

    def _generate_routing_optimizations(self, metrics: List[WorkflowMetrics]) -> List[OptimizationRecommendation]:
        """Generate routing optimization recommendations."""
        recommendations = []

        # Analyze success rates
        low_success_metrics = [m for m in metrics if m.success_rate < 0.9]
        if len(low_success_metrics) > len(metrics) * 0.1:  # More than 10% have low success
            recommendations.append(OptimizationRecommendation(
                type="routing",
                priority="high",
                description="High failure rate detected in routing decisions",
                impact="Improve overall workflow success rate",
                implementation="Review and update routing logic, add fallback strategies",
                estimated_improvement=0.15
            ))

        # Analyze execution times
        avg_execution_time = sum(m.execution_time for m in metrics) / len(metrics)
        if avg_execution_time > 60:  # More than 1 minute average
            recommendations.append(OptimizationRecommendation(
                type="workflow",
                priority="medium",
                description="Average execution time is high",
                impact="Reduce user wait times and improve responsiveness",
                implementation="Optimize agent selection, implement parallel processing",
                estimated_improvement=0.25
            ))

        # Analyze agent transitions
        avg_transitions = sum(m.agent_transitions for m in metrics) / len(metrics)
        if avg_transitions > 5:
            recommendations.append(OptimizationRecommendation(
                type="routing",
                priority="medium",
                description="Excessive agent transitions detected",
                impact="Reduce complexity and improve efficiency",
                implementation="Consolidate agent capabilities, improve initial routing",
                estimated_improvement=0.20
            ))

        return recommendations

    def _analyze_performance_trends(self, metrics: List[WorkflowMetrics]) -> Dict[str, Any]:
        """Analyze performance trends over time."""
        if len(metrics) < 10:
            return {"message": "Insufficient data for trend analysis"}

        # Sort metrics by timestamp
        sorted_metrics = sorted(metrics, key=lambda m: m.timestamp)

        # Calculate trends
        recent_half = sorted_metrics[len(sorted_metrics)//2:]
        older_half = sorted_metrics[:len(sorted_metrics)//2]

        trends = {
            "execution_time": {
                "recent_avg": sum(m.execution_time for m in recent_half) / len(recent_half),
                "older_avg": sum(m.execution_time for m in older_half) / len(older_half),
                "trend": "improving" if sum(m.execution_time for m in recent_half) < sum(m.execution_time for m in older_half) else "degrading"
            },
            "success_rate": {
                "recent_avg": sum(m.success_rate for m in recent_half) / len(recent_half),
                "older_avg": sum(m.success_rate for m in older_half) / len(older_half),
                "trend": "improving" if sum(m.success_rate for m in recent_half) > sum(m.success_rate for m in older_half) else "degrading"
            },
            "quality_score": {
                "recent_avg": sum(m.quality_score or 0 for m in recent_half) / len(recent_half),
                "older_avg": sum(m.quality_score or 0 for m in older_half) / len(older_half),
                "trend": "improving" if sum(m.quality_score or 0 for m in recent_half) > sum(m.quality_score or 0 for m in older_half) else "degrading"
            }
        }

        return trends

    async def get_performance_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive performance dashboard data."""
        try:
            recent_metrics = self._get_recent_metrics(hours=24)

            if not recent_metrics:
                return {"message": "No recent metrics available"}

            # Calculate summary statistics
            total_workflows = len(recent_metrics)
            avg_execution_time = sum(m.execution_time for m in recent_metrics) / total_workflows
            avg_success_rate = sum(m.success_rate for m in recent_metrics) / total_workflows
            total_errors = sum(m.error_count for m in recent_metrics)

            # Get quality scores (excluding None values)
            quality_scores = [m.quality_score for m in recent_metrics if m.quality_score is not None]
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0

            dashboard = {
                "summary": {
                    "total_workflows": total_workflows,
                    "avg_execution_time": avg_execution_time,
                    "avg_success_rate": avg_success_rate,
                    "avg_quality_score": avg_quality,
                    "total_errors": total_errors,
                    "period": "24 hours"
                },
                "agent_performance": dict(self.agent_metrics),
                "bottlenecks": self._identify_bottlenecks(recent_metrics),
                "trends": self._analyze_performance_trends(recent_metrics),
                "recommendations": self._generate_routing_optimizations(recent_metrics),
                "system_health": {
                    "current_memory_mb": psutil.virtual_memory().used / (1024 * 1024),
                    "current_cpu_percent": psutil.cpu_percent(interval=0.1),
                    "active_workflows": len([m for m in recent_metrics if m.timestamp > datetime.now(timezone.utc) - timedelta(minutes=5)])
                }
            }

            return dashboard

        except Exception as e:
            self.logger.error(f"Error generating performance dashboard: {e}")
            return {"error": str(e)}

    async def optimize_workflow_performance(self, workflow_id: str) -> Dict[str, Any]:
        """Provide specific optimization recommendations for a workflow."""
        try:
            # Get workflow-specific metrics
            workflow_metrics = [m for m in self.metrics_history if m.workflow_id == workflow_id]

            if not workflow_metrics:
                return {"message": f"No metrics found for workflow {workflow_id}"}

            latest_metric = max(workflow_metrics, key=lambda m: m.timestamp)

            optimizations = []

            # Check execution time
            if latest_metric.execution_time > 120:  # 2 minutes
                optimizations.append({
                    "type": "execution_time",
                    "issue": f"Long execution time: {latest_metric.execution_time:.2f}s",
                    "recommendation": "Consider breaking down into smaller steps or optimizing agent selection",
                    "priority": "high"
                })

            # Check agent transitions
            if latest_metric.agent_transitions > 7:
                optimizations.append({
                    "type": "routing",
                    "issue": f"Too many agent transitions: {latest_metric.agent_transitions}",
                    "recommendation": "Review routing logic to reduce unnecessary handoffs",
                    "priority": "medium"
                })

            # Check success rate
            if latest_metric.success_rate < 0.8:
                optimizations.append({
                    "type": "reliability",
                    "issue": f"Low success rate: {latest_metric.success_rate:.2f}",
                    "recommendation": "Add error handling and retry mechanisms",
                    "priority": "high"
                })

            # Check resource usage
            if latest_metric.memory_usage_mb > 512:  # 512MB
                optimizations.append({
                    "type": "resource",
                    "issue": f"High memory usage: {latest_metric.memory_usage_mb:.0f}MB",
                    "recommendation": "Optimize data structures and implement memory cleanup",
                    "priority": "medium"
                })

            return {
                "workflow_id": workflow_id,
                "current_performance": asdict(latest_metric),
                "optimizations": optimizations,
                "performance_score": self._calculate_performance_score(latest_metric)
            }

        except Exception as e:
            self.logger.error(f"Error optimizing workflow {workflow_id}: {e}")
            return {"error": str(e)}

    def _calculate_performance_score(self, metric: WorkflowMetrics) -> float:
        """Calculate overall performance score (0-100)."""
        try:
            # Normalize metrics to 0-1 scale
            time_score = max(0, 1 - (metric.execution_time / 300))  # 5 minutes max
            success_score = metric.success_rate
            quality_score = metric.quality_score or 0.5
            resource_score = max(0, 1 - (metric.memory_usage_mb / 1024))  # 1GB max

            # Weighted average
            overall_score = (
                time_score * 0.3 +
                success_score * 0.4 +
                quality_score * 0.2 +
                resource_score * 0.1
            ) * 100

            return round(overall_score, 2)

        except Exception as e:
            self.logger.error(f"Error calculating performance score: {e}")
            return 0.0

    async def _optimize_execution_time(self, metrics: List[WorkflowMetrics]) -> List[OptimizationRecommendation]:
        """
        Optimize workflow execution time based on metrics analysis.

        Args:
            metrics: List of workflow metrics to analyze

        Returns:
            List of optimization recommendations for execution time
        """
        recommendations = []

        if not metrics:
            return recommendations

        try:
            # Calculate average execution time
            avg_execution_time = sum(m.execution_time for m in metrics) / len(metrics)
            max_execution_time = max(m.execution_time for m in metrics)

            # Identify slow workflows
            slow_workflows = [m for m in metrics if m.execution_time > self.performance_thresholds["max_execution_time"]]

            if slow_workflows:
                recommendations.append(OptimizationRecommendation(
                    type="execution_time",
                    priority="high",
                    description=f"Found {len(slow_workflows)} workflows exceeding execution time threshold ({self.performance_thresholds['max_execution_time']}s)",
                    impact="Reduce user wait times and improve system responsiveness",
                    implementation="Implement parallel processing, optimize agent selection logic, add caching for repeated operations",
                    estimated_improvement=0.30
                ))

            # Check for consistently high execution times
            if avg_execution_time > self.performance_thresholds["max_execution_time"] * 0.7:
                recommendations.append(OptimizationRecommendation(
                    type="execution_time",
                    priority="medium",
                    description=f"Average execution time ({avg_execution_time:.2f}s) is approaching threshold",
                    impact="Prevent future performance degradation",
                    implementation="Profile bottleneck operations, optimize database queries, implement result caching",
                    estimated_improvement=0.20
                ))

            # Analyze execution time variance
            if len(metrics) > 1:
                execution_times = [m.execution_time for m in metrics]
                variance = sum((t - avg_execution_time) ** 2 for t in execution_times) / len(execution_times)
                std_dev = variance ** 0.5

                if std_dev > avg_execution_time * 0.5:  # High variance
                    recommendations.append(OptimizationRecommendation(
                        type="execution_time",
                        priority="medium",
                        description=f"High execution time variance detected (std dev: {std_dev:.2f}s)",
                        impact="Improve consistency and predictability",
                        implementation="Standardize processing paths, implement load balancing, optimize resource allocation",
                        estimated_improvement=0.15
                    ))

        except Exception as e:
            self.logger.error(f"Error in _optimize_execution_time: {e}")

        return recommendations

    async def _optimize_agent_routing(self, metrics: List[WorkflowMetrics]) -> List[OptimizationRecommendation]:
        """
        Optimize agent routing based on transition patterns and efficiency.

        Args:
            metrics: List of workflow metrics to analyze

        Returns:
            List of optimization recommendations for agent routing
        """
        recommendations = []

        if not metrics:
            return recommendations

        try:
            # Calculate average agent transitions
            avg_transitions = sum(m.agent_transitions for m in metrics) / len(metrics)
            max_transitions = max(m.agent_transitions for m in metrics)

            # Identify workflows with excessive transitions
            excessive_transition_workflows = [
                m for m in metrics
                if m.agent_transitions > self.performance_thresholds["max_agent_transitions"]
            ]

            if excessive_transition_workflows:
                recommendations.append(OptimizationRecommendation(
                    type="routing",
                    priority="high",
                    description=f"Found {len(excessive_transition_workflows)} workflows with excessive agent transitions (>{self.performance_thresholds['max_agent_transitions']})",
                    impact="Reduce complexity and improve workflow efficiency",
                    implementation="Consolidate agent capabilities, improve initial routing decisions, implement direct routing paths",
                    estimated_improvement=0.25
                ))

            # Check for high average transitions
            if avg_transitions > self.performance_thresholds["max_agent_transitions"] * 0.7:
                recommendations.append(OptimizationRecommendation(
                    type="routing",
                    priority="medium",
                    description=f"Average agent transitions ({avg_transitions:.1f}) approaching threshold",
                    impact="Prevent routing inefficiencies",
                    implementation="Review routing logic, add capability-based pre-filtering, implement smart agent selection",
                    estimated_improvement=0.20
                ))

            # Analyze routing patterns for optimization opportunities
            if len(metrics) > 5:
                # Look for patterns in successful vs failed workflows
                successful_workflows = [m for m in metrics if m.success_rate > 0.9]
                failed_workflows = [m for m in metrics if m.success_rate < 0.5]

                if successful_workflows and failed_workflows:
                    avg_successful_transitions = sum(m.agent_transitions for m in successful_workflows) / len(successful_workflows)
                    avg_failed_transitions = sum(m.agent_transitions for m in failed_workflows) / len(failed_workflows)

                    if avg_failed_transitions > avg_successful_transitions * 1.5:
                        recommendations.append(OptimizationRecommendation(
                            type="routing",
                            priority="medium",
                            description="Failed workflows show significantly more agent transitions than successful ones",
                            impact="Improve success rate by optimizing routing paths",
                            implementation="Analyze successful routing patterns, implement pattern-based routing, add early failure detection",
                            estimated_improvement=0.18
                        ))

        except Exception as e:
            self.logger.error(f"Error in _optimize_agent_routing: {e}")

        return recommendations

    async def _optimize_error_handling(self, metrics: List[WorkflowMetrics]) -> List[OptimizationRecommendation]:
        """
        Optimize error handling based on error patterns and rates.

        Args:
            metrics: List of workflow metrics to analyze

        Returns:
            List of optimization recommendations for error handling
        """
        recommendations = []

        if not metrics:
            return recommendations

        try:
            # Calculate error statistics
            total_workflows = len(metrics)
            total_errors = sum(m.error_count for m in metrics)
            avg_error_rate = (total_workflows - sum(1 for m in metrics if m.success_rate > 0.95)) / total_workflows

            # Identify high-error workflows
            high_error_workflows = [
                m for m in metrics
                if m.error_count > 0 and m.success_rate < (1 - self.performance_thresholds["max_error_rate"])
            ]

            if avg_error_rate > self.performance_thresholds["max_error_rate"]:
                recommendations.append(OptimizationRecommendation(
                    type="error_handling",
                    priority="high",
                    description=f"System error rate ({avg_error_rate:.2%}) exceeds threshold ({self.performance_thresholds['max_error_rate']:.2%})",
                    impact="Improve system reliability and user experience",
                    implementation="Implement comprehensive error handling, add retry mechanisms, improve input validation",
                    estimated_improvement=0.35
                ))

            if high_error_workflows:
                recommendations.append(OptimizationRecommendation(
                    type="error_handling",
                    priority="high",
                    description=f"Found {len(high_error_workflows)} workflows with significant error rates",
                    impact="Reduce workflow failures and improve success rates",
                    implementation="Add specific error handlers for common failure patterns, implement graceful degradation",
                    estimated_improvement=0.25
                ))

            # Analyze error patterns
            if total_errors > 0:
                workflows_with_errors = [m for m in metrics if m.error_count > 0]
                if len(workflows_with_errors) > total_workflows * 0.3:  # More than 30% have errors
                    recommendations.append(OptimizationRecommendation(
                        type="error_handling",
                        priority="medium",
                        description=f"High percentage of workflows ({len(workflows_with_errors)/total_workflows:.1%}) experiencing errors",
                        impact="Improve overall system stability",
                        implementation="Implement proactive error detection, add circuit breakers, improve monitoring",
                        estimated_improvement=0.20
                    ))

                # Check for correlation between errors and execution time
                error_workflows = [m for m in metrics if m.error_count > 0]
                no_error_workflows = [m for m in metrics if m.error_count == 0]

                if error_workflows and no_error_workflows:
                    avg_error_time = sum(m.execution_time for m in error_workflows) / len(error_workflows)
                    avg_success_time = sum(m.execution_time for m in no_error_workflows) / len(no_error_workflows)

                    if avg_error_time > avg_success_time * 1.5:
                        recommendations.append(OptimizationRecommendation(
                            type="error_handling",
                            priority="medium",
                            description="Workflows with errors take significantly longer to execute",
                            impact="Reduce time spent on failing operations",
                            implementation="Implement early error detection, add timeout mechanisms, optimize error recovery",
                            estimated_improvement=0.15
                        ))

        except Exception as e:
            self.logger.error(f"Error in _optimize_error_handling: {e}")

        return recommendations

    async def _optimize_resource_usage(self, metrics: List[WorkflowMetrics]) -> List[OptimizationRecommendation]:
        """
        Optimize resource usage based on memory and CPU patterns.

        Args:
            metrics: List of workflow metrics to analyze

        Returns:
            List of optimization recommendations for resource usage
        """
        recommendations = []

        if not metrics:
            return recommendations

        try:
            # Calculate resource usage statistics
            avg_memory = sum(m.memory_usage_mb for m in metrics) / len(metrics)
            max_memory = max(m.memory_usage_mb for m in metrics)
            avg_cpu = sum(m.cpu_usage_percent for m in metrics) / len(metrics)
            max_cpu = max(m.cpu_usage_percent for m in metrics)

            # Identify high memory usage workflows
            high_memory_workflows = [
                m for m in metrics
                if m.memory_usage_mb > self.performance_thresholds["max_memory_usage_mb"]
            ]

            if high_memory_workflows:
                recommendations.append(OptimizationRecommendation(
                    type="resource",
                    priority="high",
                    description=f"Found {len(high_memory_workflows)} workflows exceeding memory threshold ({self.performance_thresholds['max_memory_usage_mb']}MB)",
                    impact="Prevent out-of-memory errors and improve system stability",
                    implementation="Implement memory pooling, optimize data structures, add garbage collection triggers",
                    estimated_improvement=0.30
                ))

            # Check for high CPU usage
            high_cpu_workflows = [
                m for m in metrics
                if m.cpu_usage_percent > self.performance_thresholds["max_cpu_usage_percent"]
            ]

            if high_cpu_workflows:
                recommendations.append(OptimizationRecommendation(
                    type="resource",
                    priority="high",
                    description=f"Found {len(high_cpu_workflows)} workflows exceeding CPU threshold ({self.performance_thresholds['max_cpu_usage_percent']}%)",
                    impact="Reduce system load and improve concurrent processing",
                    implementation="Optimize algorithms, implement async processing, add CPU throttling",
                    estimated_improvement=0.25
                ))

            # Check average resource usage trends
            if avg_memory > self.performance_thresholds["max_memory_usage_mb"] * 0.8:
                recommendations.append(OptimizationRecommendation(
                    type="resource",
                    priority="medium",
                    description=f"Average memory usage ({avg_memory:.0f}MB) approaching threshold",
                    impact="Prevent future memory issues",
                    implementation="Implement memory monitoring, optimize caching strategies, add memory cleanup routines",
                    estimated_improvement=0.20
                ))

            if avg_cpu > self.performance_thresholds["max_cpu_usage_percent"] * 0.8:
                recommendations.append(OptimizationRecommendation(
                    type="resource",
                    priority="medium",
                    description=f"Average CPU usage ({avg_cpu:.1f}%) approaching threshold",
                    impact="Maintain system responsiveness",
                    implementation="Optimize processing algorithms, implement load balancing, add CPU monitoring",
                    estimated_improvement=0.18
                ))

            # Analyze resource usage correlation with performance
            if len(metrics) > 5:
                # Check correlation between resource usage and execution time
                high_resource_workflows = [
                    m for m in metrics
                    if m.memory_usage_mb > avg_memory * 1.5 or m.cpu_usage_percent > avg_cpu * 1.5
                ]

                if high_resource_workflows:
                    avg_high_resource_time = sum(m.execution_time for m in high_resource_workflows) / len(high_resource_workflows)
                    avg_normal_time = sum(m.execution_time for m in metrics if m not in high_resource_workflows) / max(1, len(metrics) - len(high_resource_workflows))

                    if avg_high_resource_time > avg_normal_time * 1.3:
                        recommendations.append(OptimizationRecommendation(
                            type="resource",
                            priority="medium",
                            description="High resource usage correlates with longer execution times",
                            impact="Improve both performance and resource efficiency",
                            implementation="Profile resource-intensive operations, implement resource pooling, optimize data processing",
                            estimated_improvement=0.22
                        ))

        except Exception as e:
            self.logger.error(f"Error in _optimize_resource_usage: {e}")

        return recommendations
