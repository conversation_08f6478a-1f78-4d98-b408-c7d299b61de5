#!/usr/bin/env python3
"""
Test script to verify agent routing and context persistence fixes.

This script tests the LangGraph Command pattern implementation to ensure:
1. Agents can be properly selected and switched
2. Selected agents maintain context throughout conversations
3. Follow-up messages are routed to the correct agent (not fallback to Concierge)
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Test the state management functions directly without full workflow
from agents.langgraph.states.unified_state import (
    create_initial_state,
    set_selected_agent,
    get_routing_target,
    should_route_to_selected_agent,
    ConversationMode,
    MessageType
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_agent_routing():
    """Test agent routing and context persistence state management functions."""

    print("🧪 Testing Agent Routing and Context Persistence")
    print("=" * 60)

    try:
        # Test 1: Initial state creation
        print("\n📋 Test 1: Initial State Creation")
        print("-" * 40)

        initial_state = create_initial_state(
            workflow_id="test_routing_001",
            conversation_id="conv_001",
            user_id="test_user",
            workflow_type="agent_conversation"
        )

        print(f"✅ Initial state created")
        print(f"📊 Current Agent: {initial_state.get('current_agent', 'None')}")
        print(f"🎯 Selected Agent: {initial_state.get('selected_agent', 'None')}")
        print(f"🔄 Conversation Mode: {initial_state.get('conversation_mode', 'Unknown')}")

        # Test 2: Set selected agent (Marketing)
        print("\n📋 Test 2: Set Selected Agent to Marketing")
        print("-" * 40)

        # Set marketing agent as selected
        marketing_state = set_selected_agent(
            initial_state.copy(),
            "marketing",
            ConversationMode.CONVERSATION,
            {
                "persona": "marketing_specialist",
                "context": "marketing_conversation"
            }
        )

        print(f"✅ Marketing agent selected")
        print(f"📊 Current Agent: {marketing_state.get('current_agent', 'None')}")
        print(f"🎯 Selected Agent: {marketing_state.get('selected_agent', 'None')}")
        print(f"🔄 Conversation Mode: {marketing_state.get('conversation_mode', 'Unknown')}")
        print(f"🧠 Agent Context: {marketing_state.get('agent_context', {})}")

        # Test 3: Get routing target
        print("\n📋 Test 3: Get Routing Target")
        print("-" * 40)

        routing_target = get_routing_target(marketing_state)
        print(f"✅ Routing target: {routing_target}")

        # Test 4: Check if should route to selected agent
        print("\n📋 Test 4: Should Route to Selected Agent")
        print("-" * 40)

        should_route = should_route_to_selected_agent(marketing_state)
        print(f"✅ Should route to selected agent: {should_route}")

        # Test 5: Test conversation mode persistence
        print("\n📋 Test 5: Conversation Mode Persistence")
        print("-" * 40)

        # Simulate follow-up message state
        followup_state = marketing_state.copy()
        followup_state["conversation_mode"] = ConversationMode.CONVERSATION

        routing_target_followup = get_routing_target(followup_state)
        should_route_followup = should_route_to_selected_agent(followup_state)

        print(f"✅ Follow-up routing target: {routing_target_followup}")
        print(f"✅ Should route to selected agent (follow-up): {should_route_followup}")

        # Test 6: Test agent switch scenario
        print("\n📋 Test 6: Agent Switch Scenario")
        print("-" * 40)

        # Switch to different agent
        analyst_state = set_selected_agent(
            followup_state.copy(),
            "analyst",
            ConversationMode.AGENT_SWITCH,
            {
                "persona": "data_analyst",
                "context": "analysis_conversation"
            }
        )

        routing_target_analyst = get_routing_target(analyst_state)
        should_route_analyst = should_route_to_selected_agent(analyst_state)

        print(f"✅ Analyst routing target: {routing_target_analyst}")
        print(f"✅ Should route to analyst: {should_route_analyst}")
        print(f"🎯 Selected Agent: {analyst_state.get('selected_agent', 'None')}")

        # Verification
        print("\n🔍 Verification Results")
        print("-" * 40)

        # Check if routing functions work correctly
        if routing_target == "marketing" and should_route:
            print("✅ SUCCESS: Marketing agent routing works correctly!")
        else:
            print("❌ FAILURE: Marketing agent routing failed")

        if routing_target_followup == "marketing" and should_route_followup:
            print("✅ SUCCESS: Follow-up routing maintains selected agent!")
        else:
            print("❌ FAILURE: Follow-up routing failed")

        if routing_target_analyst == "analyst" and should_route_analyst:
            print("✅ SUCCESS: Agent switching works correctly!")
        else:
            print("❌ FAILURE: Agent switching failed")

        print("\n🎉 State Management Test Complete!")

    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(test_agent_routing())
