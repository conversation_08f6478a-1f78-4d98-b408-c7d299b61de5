"""
Event-driven integration for the Intelligent Router.

This module provides an enhanced intelligent router that subscribes to events
and updates its routing decisions based on real-time agent and workflow information.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta

from ....langgraph.events.event_bus import event_bus, LangGraphEvent
from ....langgraph.events.types import (
    WorkflowStartedEvent,
    WorkflowCompletedEvent,
    WorkflowFailedEvent,
    AgentPerformanceEvent
)
from ....langgraph.graphs.intelligent_router import DynamicIntelligentRouter
from ....langgraph.states.agent_state import DatageniusAgentState

logger = logging.getLogger(__name__)


class EventDrivenIntelligentRouter:
    """
    Enhanced intelligent router with event-driven capabilities.
    
    This class extends the existing DynamicIntelligentRouter with event
    subscriptions to automatically update routing decisions based on
    real-time system events.
    """
    
    def __init__(self, enable_events: bool = True):
        self.enable_events = enable_events
        self.router = DynamicIntelligentRouter()
        self.performance_cache: Dict[str, Dict[str, Any]] = {}
        self.workflow_history: List[Dict[str, Any]] = []
        self.agent_load_tracking: Dict[str, int] = {}
        
        # Initialize event handlers if events are enabled
        if self.enable_events:
            self._setup_event_handlers()
    
    def _setup_event_handlers(self):
        """Set up event handlers for the router."""
        try:
            # Subscribe to workflow events
            event_bus.subscribe("workflow.started", self._handle_workflow_started)
            event_bus.subscribe("workflow.completed", self._handle_workflow_completed)
            event_bus.subscribe("workflow.failed", self._handle_workflow_failed)
            event_bus.subscribe("agent.performance_updated", self._handle_performance_update)
            event_bus.subscribe("agent.registered", self._handle_agent_registration)
            event_bus.subscribe("agent.capability_updated", self._handle_capability_update)
            
            logger.info("Event handlers set up for EventDrivenIntelligentRouter")
            
        except Exception as e:
            logger.error(f"Error setting up event handlers: {e}")
            self.enable_events = False
    
    async def _handle_workflow_started(self, event: LangGraphEvent):
        """Handle workflow started event."""
        try:
            data = event.data
            workflow_id = data.get("workflow_id")
            agents_involved = data.get("agents_involved", [])
            
            # Track agent load
            for agent_id in agents_involved:
                self.agent_load_tracking[agent_id] = self.agent_load_tracking.get(agent_id, 0) + 1
            
            # Add to workflow history
            workflow_record = {
                "workflow_id": workflow_id,
                "start_time": event.timestamp,
                "agents_involved": agents_involved,
                "status": "started"
            }
            self.workflow_history.append(workflow_record)
            
            # Keep only last 1000 workflow records
            if len(self.workflow_history) > 1000:
                self.workflow_history = self.workflow_history[-1000:]
            
            logger.debug(f"Tracked workflow start: {workflow_id} with agents {agents_involved}")
            
        except Exception as e:
            logger.error(f"Error handling workflow started event: {e}")
    
    async def _handle_workflow_completed(self, event: LangGraphEvent):
        """Handle workflow completed event."""
        try:
            data = event.data
            workflow_id = data.get("workflow_id")
            execution_time = data.get("execution_time", 0.0)
            success = data.get("success", True)
            
            # Update workflow history
            for record in self.workflow_history:
                if record.get("workflow_id") == workflow_id:
                    record.update({
                        "completion_time": event.timestamp,
                        "execution_time": execution_time,
                        "success": success,
                        "status": "completed"
                    })
                    
                    # Update agent load tracking
                    agents_involved = record.get("agents_involved", [])
                    for agent_id in agents_involved:
                        if agent_id in self.agent_load_tracking:
                            self.agent_load_tracking[agent_id] = max(0, self.agent_load_tracking[agent_id] - 1)
                    
                    break
            
            # Update router performance metrics if available
            if hasattr(self.router, 'performance_optimizer'):
                await self.router.performance_optimizer.record_routing_performance(
                    routing_context={"workflow_id": workflow_id},
                    outcome={"execution_time": execution_time, "success": success},
                    success=success
                )
            
            logger.debug(f"Tracked workflow completion: {workflow_id} (success: {success}, time: {execution_time}s)")
            
        except Exception as e:
            logger.error(f"Error handling workflow completed event: {e}")
    
    async def _handle_workflow_failed(self, event: LangGraphEvent):
        """Handle workflow failed event."""
        try:
            data = event.data
            workflow_id = data.get("workflow_id")
            execution_time = data.get("execution_time", 0.0)
            error_message = data.get("error_message", "")
            
            # Update workflow history
            for record in self.workflow_history:
                if record.get("workflow_id") == workflow_id:
                    record.update({
                        "failure_time": event.timestamp,
                        "execution_time": execution_time,
                        "error_message": error_message,
                        "success": False,
                        "status": "failed"
                    })
                    
                    # Update agent load tracking
                    agents_involved = record.get("agents_involved", [])
                    for agent_id in agents_involved:
                        if agent_id in self.agent_load_tracking:
                            self.agent_load_tracking[agent_id] = max(0, self.agent_load_tracking[agent_id] - 1)
                    
                    break
            
            # Update router performance metrics if available
            if hasattr(self.router, 'performance_optimizer'):
                await self.router.performance_optimizer.record_routing_performance(
                    routing_context={"workflow_id": workflow_id},
                    outcome={"execution_time": execution_time, "success": False, "error": error_message},
                    success=False
                )
            
            logger.debug(f"Tracked workflow failure: {workflow_id} (error: {error_message})")
            
        except Exception as e:
            logger.error(f"Error handling workflow failed event: {e}")
    
    async def _handle_performance_update(self, event: LangGraphEvent):
        """Handle agent performance update event."""
        try:
            data = event.data
            agent_id = data.get("agent_id")
            performance_metrics = data.get("performance_metrics", {})
            
            # Update performance cache
            if agent_id not in self.performance_cache:
                self.performance_cache[agent_id] = {
                    "metrics_history": [],
                    "average_response_time": 0.0,
                    "success_rate": 1.0,
                    "last_updated": event.timestamp
                }
            
            cache_entry = self.performance_cache[agent_id]
            cache_entry["metrics_history"].append({
                "timestamp": event.timestamp,
                "metrics": performance_metrics
            })
            
            # Keep only last 50 performance records per agent
            if len(cache_entry["metrics_history"]) > 50:
                cache_entry["metrics_history"] = cache_entry["metrics_history"][-50:]
            
            # Update aggregated metrics
            response_time = performance_metrics.get("response_time", 0.0)
            success = performance_metrics.get("success", True)
            
            if response_time > 0:
                # Update average response time (exponential moving average)
                alpha = 0.1
                cache_entry["average_response_time"] = (
                    alpha * response_time + (1 - alpha) * cache_entry["average_response_time"]
                )
            
            # Update success rate (exponential moving average)
            success_value = 1.0 if success else 0.0
            cache_entry["success_rate"] = (
                alpha * success_value + (1 - alpha) * cache_entry["success_rate"]
            )
            
            cache_entry["last_updated"] = event.timestamp
            
            # Update router's agent scoring if available
            if hasattr(self.router, 'agent_scoring_system'):
                self.router.agent_scoring_system.update_performance_metrics(agent_id, performance_metrics)
            
            logger.debug(f"Updated performance cache for agent {agent_id}")
            
        except Exception as e:
            logger.error(f"Error handling performance update event: {e}")
    
    async def _handle_agent_registration(self, event: LangGraphEvent):
        """Handle agent registration event."""
        try:
            data = event.data
            agent_id = data.get("agent_id")
            capabilities = data.get("capabilities", [])
            
            # Initialize performance tracking for new agent
            if agent_id not in self.performance_cache:
                self.performance_cache[agent_id] = {
                    "metrics_history": [],
                    "average_response_time": 0.0,
                    "success_rate": 1.0,
                    "last_updated": event.timestamp
                }
            
            # Initialize load tracking
            self.agent_load_tracking[agent_id] = 0
            
            # Trigger router capability refresh if available
            if hasattr(self.router, 'capability_registry'):
                for capability in capabilities:
                    self.router.capability_registry.register_capability(capability)
            
            logger.debug(f"Initialized tracking for new agent {agent_id}")
            
        except Exception as e:
            logger.error(f"Error handling agent registration event: {e}")
    
    async def _handle_capability_update(self, event: LangGraphEvent):
        """Handle capability update event."""
        try:
            data = event.data
            agent_id = data.get("agent_id")
            new_capabilities = data.get("new_capabilities", [])
            changes = data.get("changes", {})
            
            # Update router capability registry if available
            if hasattr(self.router, 'capability_registry'):
                # Add new capabilities
                for capability in changes.get("added", []):
                    self.router.capability_registry.register_capability(capability)
                
                # Note: We don't remove capabilities as other agents might still have them
            
            logger.debug(f"Updated capabilities for agent {agent_id}: {changes}")
            
        except Exception as e:
            logger.error(f"Error handling capability update event: {e}")
    
    async def make_routing_decision(self, state: DatageniusAgentState) -> Any:
        """
        Make routing decision with event-driven enhancements.
        
        This method uses the original router but enhances it with real-time
        performance data and load balancing information from events.
        """
        try:
            # Enhance state with real-time information
            enhanced_state = state.copy()
            
            # Add performance information to routing context
            if "routing_context" not in enhanced_state:
                enhanced_state["routing_context"] = {}
            
            enhanced_state["routing_context"]["agent_performance"] = self.performance_cache
            enhanced_state["routing_context"]["agent_load"] = self.agent_load_tracking
            enhanced_state["routing_context"]["recent_workflows"] = self.workflow_history[-10:]  # Last 10 workflows
            
            # Use the original router with enhanced context
            routing_decision = await self.router.make_routing_decision(enhanced_state)
            
            # Publish workflow started event if a workflow is being initiated
            if routing_decision and hasattr(routing_decision, 'primary_agent'):
                await self._publish_workflow_started_event(enhanced_state, routing_decision)
            
            return routing_decision
            
        except Exception as e:
            logger.error(f"Error making routing decision: {e}")
            # Fallback to original router
            return await self.router.make_routing_decision(state)
    
    async def _publish_workflow_started_event(self, state: DatageniusAgentState, routing_decision: Any):
        """Publish workflow started event."""
        try:
            workflow_id = state.get("workflow_id", f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            user_id = state.get("user_id", "unknown")
            
            agents_involved = [routing_decision.primary_agent] if routing_decision.primary_agent else []
            if hasattr(routing_decision, 'collaborating_agents'):
                agents_involved.extend(routing_decision.collaborating_agents or [])
            
            event = WorkflowStartedEvent(
                workflow_id=workflow_id,
                workflow_type=routing_decision.strategy.value if hasattr(routing_decision, 'strategy') else "unknown",
                user_id=user_id,
                agents_involved=agents_involved,
                estimated_duration=routing_decision.estimated_duration if hasattr(routing_decision, 'estimated_duration') else 30.0
            )
            
            await event_bus.publish(event)
            logger.debug(f"Published workflow started event for {workflow_id}")
            
        except Exception as e:
            logger.error(f"Error publishing workflow started event: {e}")
    
    def get_agent_performance_summary(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get performance summary for an agent."""
        return self.performance_cache.get(agent_id)
    
    def get_agent_load(self, agent_id: str) -> int:
        """Get current load for an agent."""
        return self.agent_load_tracking.get(agent_id, 0)
    
    def get_workflow_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get workflow history."""
        if limit:
            return self.workflow_history[-limit:]
        return self.workflow_history.copy()
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system-wide metrics."""
        total_workflows = len(self.workflow_history)
        successful_workflows = sum(1 for w in self.workflow_history if w.get("success", False))
        failed_workflows = sum(1 for w in self.workflow_history if w.get("success") is False)
        
        return {
            "total_workflows": total_workflows,
            "successful_workflows": successful_workflows,
            "failed_workflows": failed_workflows,
            "success_rate": successful_workflows / total_workflows if total_workflows > 0 else 0.0,
            "active_agents": len([agent for agent, load in self.agent_load_tracking.items() if load > 0]),
            "total_tracked_agents": len(self.performance_cache),
            "average_agent_load": sum(self.agent_load_tracking.values()) / len(self.agent_load_tracking) if self.agent_load_tracking else 0.0
        }


# Global instance for easy access
event_driven_router = EventDrivenIntelligentRouter()
