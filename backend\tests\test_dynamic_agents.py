#!/usr/bin/env python3
"""
Test script to verify dynamic agent configuration and management.

This script tests the dynamic agent system to ensure:
1. Agents are loaded dynamically from configuration
2. Agent discovery works properly
3. Runtime agent management functions correctly
4. No hardcoded agent lists are used
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_dynamic_agent_configuration():
    """Test dynamic agent configuration and management."""
    
    print("🧪 Testing Dynamic Agent Configuration")
    print("=" * 60)
    
    try:
        # Test 1: Agent Factory Discovery
        print("\n📋 Test 1: Agent Factory Discovery")
        print("-" * 40)
        
        from agents.langgraph.core.agent_factory import agent_factory
        
        # Get available agents from factory
        available_agents = agent_factory.get_available_agents()
        print(f"✅ Found {len(available_agents)} agents in factory:")
        for agent_id in available_agents:
            print(f"   • {agent_id}")
        
        # Test 2: Dynamic Agent Manager
        print("\n📋 Test 2: Dynamic Agent Manager")
        print("-" * 40)
        
        from agents.langgraph.core.dynamic_agent_manager import dynamic_agent_manager
        
        # Discover and load agents
        discovery_results = await dynamic_agent_manager.discover_and_load_agents()
        
        print(f"✅ Discovery Results:")
        print(f"   • Discovered: {discovery_results['discovered']}")
        print(f"   • Loaded: {discovery_results['loaded']}")
        print(f"   • Failed: {discovery_results['failed']}")
        print(f"   • Discovery Time: {discovery_results.get('discovery_time', 0):.2f}s")
        
        if discovery_results['errors']:
            print(f"   • Errors: {discovery_results['errors']}")
        
        # Test 3: Workflow Manager Integration
        print("\n📋 Test 3: Workflow Manager Integration")
        print("-" * 40)
        
        from agents.langgraph.core.workflow_manager import WorkflowManager
        
        workflow_manager = WorkflowManager()
        
        # Get available agents from workflow manager
        wm_agents = workflow_manager.get_available_agents()
        print(f"✅ Workflow Manager has {len(wm_agents)} agents:")
        for agent_id in wm_agents:
            print(f"   • {agent_id}")
        
        # Test 4: Agent Information
        print("\n📋 Test 4: Agent Information")
        print("-" * 40)
        
        for agent_id in wm_agents[:3]:  # Test first 3 agents
            agent_info = workflow_manager.get_agent_info(agent_id)
            if agent_info:
                print(f"✅ Agent: {agent_id}")
                print(f"   • Type: {agent_info.get('agent_type', 'unknown')}")
                print(f"   • Capabilities: {len(agent_info.get('capabilities', []))}")
                print(f"   • Intents: {len(agent_info.get('supported_intents', []))}")
                print(f"   • Tools: {len(agent_info.get('tools', []))}")
        
        # Test 5: Agent Status
        print("\n📋 Test 5: Agent Status")
        print("-" * 40)
        
        status_info = dynamic_agent_manager.get_agent_status()
        summary = status_info.get("summary", {})
        
        print(f"✅ Agent Status Summary:")
        print(f"   • Total: {summary.get('total', 0)}")
        print(f"   • Active: {summary.get('active', 0)}")
        print(f"   • Inactive: {summary.get('inactive', 0)}")
        print(f"   • Error: {summary.get('error', 0)}")
        print(f"   • Loading: {summary.get('loading', 0)}")
        
        # Test 6: Dynamic Operations
        print("\n📋 Test 6: Dynamic Operations")
        print("-" * 40)
        
        # Test refresh
        print("🔄 Testing agent refresh...")
        refresh_results = await dynamic_agent_manager.refresh_all_agents()
        
        print(f"✅ Refresh Results:")
        print(f"   • Discovered: {refresh_results['discovered']}")
        print(f"   • Loaded: {refresh_results['loaded']}")
        print(f"   • Failed: {refresh_results['failed']}")
        
        # Verification
        print("\n🔍 Verification Results")
        print("-" * 40)
        
        # Check if agents are loaded dynamically (not hardcoded)
        if len(available_agents) > 0:
            print("✅ SUCCESS: Agents are loaded dynamically from configuration!")
        else:
            print("❌ FAILURE: No agents found in dynamic discovery")
        
        # Check if workflow manager has agents
        if len(wm_agents) > 0:
            print("✅ SUCCESS: Workflow manager has dynamically loaded agents!")
        else:
            print("❌ FAILURE: Workflow manager has no agents")
        
        # Check if discovery worked
        if discovery_results['loaded'] > 0:
            print("✅ SUCCESS: Dynamic agent discovery and loading works!")
        else:
            print("❌ FAILURE: No agents were loaded through discovery")
        
        # Check if agents match between systems
        factory_set = set(available_agents)
        wm_set = set(wm_agents)
        
        if factory_set == wm_set:
            print("✅ SUCCESS: Agent factory and workflow manager are synchronized!")
        else:
            print("⚠️ WARNING: Agent factory and workflow manager have different agents")
            print(f"   Factory only: {factory_set - wm_set}")
            print(f"   Workflow only: {wm_set - factory_set}")
        
        print("\n🎉 Dynamic Agent Configuration Test Complete!")
        
        return {
            "factory_agents": len(available_agents),
            "workflow_agents": len(wm_agents),
            "discovery_loaded": discovery_results['loaded'],
            "discovery_failed": discovery_results['failed'],
            "status_summary": summary
        }
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)
        print(f"❌ Test failed: {e}")
        return None


async def test_agent_configuration_loading():
    """Test loading agents from configuration files."""
    
    print("\n🧪 Testing Agent Configuration Loading")
    print("=" * 60)
    
    try:
        from agents.langgraph.core.agent_factory import agent_factory
        
        # Test configuration loading
        print("\n📋 Testing Configuration Loading")
        print("-" * 40)
        
        # Check if configuration is loaded
        agent_configs = agent_factory.agent_configs
        print(f"✅ Loaded {len(agent_configs)} agent configurations:")
        
        for agent_id, config in agent_configs.items():
            print(f"   • {agent_id}:")
            print(f"     - Type: {config.get('agent_type', 'unknown')}")
            print(f"     - Name: {config.get('name', 'Unknown')}")
            print(f"     - Capabilities: {len(config.get('capabilities', []))}")
            print(f"     - Priority: {config.get('priority', 'N/A')}")
        
        # Test specific agent configuration
        if "concierge-agent" in agent_configs:
            concierge_config = agent_configs["concierge-agent"]
            print(f"\n✅ Concierge Agent Configuration:")
            print(f"   • Fallback: {concierge_config.get('fallback', False)}")
            print(f"   • Tools: {concierge_config.get('tools', [])}")
            print(f"   • Intents: {concierge_config.get('supported_intents', [])}")
        
        print("\n🎉 Configuration Loading Test Complete!")
        
    except Exception as e:
        logger.error(f"Configuration test failed: {e}", exc_info=True)
        print(f"❌ Configuration test failed: {e}")


if __name__ == "__main__":
    async def main():
        # Run dynamic agent tests
        results = await test_dynamic_agent_configuration()
        
        # Run configuration tests
        await test_agent_configuration_loading()
        
        # Summary
        if results:
            print(f"\n📊 Test Summary:")
            print(f"   • Factory Agents: {results['factory_agents']}")
            print(f"   • Workflow Agents: {results['workflow_agents']}")
            print(f"   • Successfully Loaded: {results['discovery_loaded']}")
            print(f"   • Failed to Load: {results['discovery_failed']}")
            
            if results['discovery_loaded'] > 0 and results['discovery_failed'] == 0:
                print("🎉 ALL TESTS PASSED: Dynamic agent configuration is working!")
            else:
                print("⚠️ SOME ISSUES FOUND: Check the logs above for details")
    
    asyncio.run(main())
