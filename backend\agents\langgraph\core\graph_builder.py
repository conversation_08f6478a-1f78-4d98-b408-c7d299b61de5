"""
LangGraph Graph Builder for Datagenius.

This module provides utilities for dynamically constructing
LangGraph workflows based on agent configurations and requirements.
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Set
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from ..states.unified_state import UnifiedDatageniusState
from ..nodes.base_agent_node import BaseAgentNode
from ..nodes.routing_node import RoutingNode
from ..nodes.tool_execution_node import ToolExecutionNode, MCPToolExecutionNode

logger = logging.getLogger(__name__)


class GraphBuilder:
    """
    Dynamic graph builder for LangGraph workflows.
    
    This builder creates workflow graphs based on available agents,
    tools, and workflow requirements, replacing static graph definitions
    with dynamic construction.
    """
    
    def __init__(self):
        """Initialize the graph builder."""
        self.logger = logging.getLogger(__name__)
        
        # Registry of available components
        self.agent_nodes: Dict[str, BaseAgentNode] = {}
        self.tool_nodes: Dict[str, ToolExecutionNode] = {}
        self.routing_nodes: Dict[str, RoutingNode] = {}
        
        # Graph templates
        self.graph_templates: Dict[str, Dict[str, Any]] = {}
        
        # Built graphs cache
        self.graph_cache: Dict[str, StateGraph] = {}
        
        self.logger.info("GraphBuilder initialized")
    
    def register_agent_node(self, agent_id: str, agent_node: BaseAgentNode) -> None:
        """
        Register an agent node.
        
        Args:
            agent_id: Agent identifier
            agent_node: Agent node instance
        """
        self.agent_nodes[agent_id] = agent_node
        self.logger.info(f"Registered agent node: {agent_id}")
    
    def register_tool_node(self, tool_name: str, tool_node: ToolExecutionNode) -> None:
        """
        Register a tool node.
        
        Args:
            tool_name: Tool name
            tool_node: Tool node instance
        """
        self.tool_nodes[tool_name] = tool_node
        self.logger.info(f"Registered tool node: {tool_name}")
    
    def register_routing_node(self, routing_id: str, routing_node: RoutingNode) -> None:
        """
        Register a routing node.
        
        Args:
            routing_id: Routing identifier
            routing_node: Routing node instance
        """
        self.routing_nodes[routing_id] = routing_node
        self.logger.info(f"Registered routing node: {routing_id}")
    
    def add_graph_template(self, template_name: str, template_config: Dict[str, Any]) -> None:
        """
        Add a graph template for reuse.
        
        Args:
            template_name: Template name
            template_config: Template configuration
        """
        self.graph_templates[template_name] = template_config
        self.logger.info(f"Added graph template: {template_name}")
    
    def build_workflow_graph(
        self,
        workflow_type: str = "default",
        agents: Optional[List[str]] = None,
        tools: Optional[List[str]] = None,
        routing_strategy: str = "intelligent",
        checkpointer: Optional[Any] = None
    ) -> StateGraph:
        """
        Build a workflow graph based on requirements.
        
        Args:
            workflow_type: Type of workflow to build
            agents: List of agent IDs to include
            tools: List of tool names to include
            routing_strategy: Routing strategy to use
            checkpointer: Optional checkpointer for state persistence
            
        Returns:
            Compiled StateGraph
        """
        try:
            # Create cache key
            cache_key = self._create_cache_key(workflow_type, agents, tools, routing_strategy)
            
            # Check cache
            if cache_key in self.graph_cache:
                self.logger.debug(f"Retrieved graph from cache: {cache_key}")
                return self.graph_cache[cache_key]
            
            self.logger.info(f"Building workflow graph: {workflow_type}")
            
            # Create the graph
            workflow = StateGraph(UnifiedDatageniusState)
            
            # Add nodes based on requirements
            self._add_routing_nodes(workflow, routing_strategy)
            self._add_agent_nodes(workflow, agents)
            self._add_tool_nodes(workflow, tools)
            
            # Add edges
            self._add_routing_edges(workflow, agents, tools)
            self._add_agent_edges(workflow, agents, tools)
            self._add_tool_edges(workflow, tools)
            
            # Set entry point
            workflow.set_entry_point("routing")
            
            # Compile the graph
            compiled_graph = workflow.compile(
                checkpointer=checkpointer or MemorySaver()
            )
            
            # Cache the graph
            self.graph_cache[cache_key] = compiled_graph
            
            self.logger.info(f"Built and cached workflow graph: {workflow_type}")
            
            return compiled_graph
            
        except Exception as e:
            self.logger.error(f"Error building workflow graph: {e}", exc_info=True)
            raise
    
    def build_from_template(
        self,
        template_name: str,
        parameters: Optional[Dict[str, Any]] = None,
        checkpointer: Optional[Any] = None
    ) -> StateGraph:
        """
        Build a workflow graph from a template.
        
        Args:
            template_name: Name of the template to use
            parameters: Optional parameters to customize the template
            checkpointer: Optional checkpointer for state persistence
            
        Returns:
            Compiled StateGraph
        """
        if template_name not in self.graph_templates:
            raise ValueError(f"Template '{template_name}' not found")
        
        template = self.graph_templates[template_name].copy()
        
        # Apply parameters
        if parameters:
            template.update(parameters)
        
        return self.build_workflow_graph(
            workflow_type=template.get("workflow_type", "template"),
            agents=template.get("agents"),
            tools=template.get("tools"),
            routing_strategy=template.get("routing_strategy", "intelligent"),
            checkpointer=checkpointer
        )
    
    def _add_routing_nodes(self, workflow: StateGraph, routing_strategy: str) -> None:
        """Add routing nodes to the workflow."""
        if routing_strategy == "intelligent" and "default" in self.routing_nodes:
            workflow.add_node("routing", self.routing_nodes["default"].execute)
        else:
            # Create a simple routing node
            def simple_routing(state: UnifiedDatageniusState) -> UnifiedDatageniusState:
                # Default to concierge agent
                if not state.get("routing_analysis"):
                    state["routing_analysis"] = {
                        "target_agent": "concierge",
                        "confidence": 0.5,
                        "routing_strategy": "simple"
                    }
                return state
            
            workflow.add_node("routing", simple_routing)
    
    def _add_agent_nodes(self, workflow: StateGraph, agents: Optional[List[str]]) -> None:
        """Add agent nodes to the workflow."""
        agents_to_add = agents or list(self.agent_nodes.keys())
        
        for agent_id in agents_to_add:
            if agent_id in self.agent_nodes:
                workflow.add_node(f"agent_{agent_id}", self.agent_nodes[agent_id].execute)
                self.logger.debug(f"Added agent node: {agent_id}")
    
    def _add_tool_nodes(self, workflow: StateGraph, tools: Optional[List[str]]) -> None:
        """Add tool nodes to the workflow."""
        tools_to_add = tools or list(self.tool_nodes.keys())
        
        for tool_name in tools_to_add:
            if tool_name in self.tool_nodes:
                workflow.add_node(f"tool_{tool_name}", self.tool_nodes[tool_name].execute)
                self.logger.debug(f"Added tool node: {tool_name}")
    
    def _add_routing_edges(
        self, 
        workflow: StateGraph, 
        agents: Optional[List[str]], 
        tools: Optional[List[str]]
    ) -> None:
        """Add routing edges from routing node to agents."""
        agents_to_route = agents or list(self.agent_nodes.keys())
        
        # Create routing function
        def route_to_agent(state: UnifiedDatageniusState) -> str:
            routing_analysis = state.get("routing_analysis", {})
            target_agent = routing_analysis.get("target_agent", "concierge")
            
            # Ensure target agent is available
            if target_agent in agents_to_route:
                return f"agent_{target_agent}"
            
            # Fallback to first available agent
            if agents_to_route:
                return f"agent_{agents_to_route[0]}"
            
            return END
        
        # Create routing map
        routing_map = {f"agent_{agent}": f"agent_{agent}" for agent in agents_to_route}
        routing_map["END"] = END
        
        workflow.add_conditional_edges("routing", route_to_agent, routing_map)
    
    def _add_agent_edges(
        self, 
        workflow: StateGraph, 
        agents: Optional[List[str]], 
        tools: Optional[List[str]]
    ) -> None:
        """Add edges from agent nodes to tools or end."""
        agents_to_connect = agents or list(self.agent_nodes.keys())
        tools_to_connect = tools or list(self.tool_nodes.keys())
        
        def route_from_agent(state: UnifiedDatageniusState) -> str:
            # Check if any tools need to be executed
            pending_tools = [
                tool for tool, status in state.get("tool_status", {}).items()
                if status == "pending" and tool in tools_to_connect
            ]
            
            if pending_tools:
                return f"tool_{pending_tools[0]}"
            
            # Check if collaboration is needed
            if state.get("collaboration_opportunities"):
                return "routing"  # Route back for collaboration
            
            return END
        
        # Create routing map for agent to tool/end
        tool_routing_map = {f"tool_{tool}": f"tool_{tool}" for tool in tools_to_connect}
        tool_routing_map["routing"] = "routing"
        tool_routing_map["END"] = END
        
        for agent_id in agents_to_connect:
            workflow.add_conditional_edges(
                f"agent_{agent_id}",
                route_from_agent,
                tool_routing_map
            )
    
    def _add_tool_edges(self, workflow: StateGraph, tools: Optional[List[str]]) -> None:
        """Add edges from tool nodes back to routing."""
        tools_to_connect = tools or list(self.tool_nodes.keys())
        
        for tool_name in tools_to_connect:
            # Tools typically route back to routing for next agent decision
            workflow.add_edge(f"tool_{tool_name}", "routing")
    
    def _create_cache_key(
        self,
        workflow_type: str,
        agents: Optional[List[str]],
        tools: Optional[List[str]],
        routing_strategy: str
    ) -> str:
        """Create a cache key for the graph configuration."""
        agents_str = ",".join(sorted(agents)) if agents else "all"
        tools_str = ",".join(sorted(tools)) if tools else "all"
        
        return f"{workflow_type}:{agents_str}:{tools_str}:{routing_strategy}"
    
    def get_available_agents(self) -> List[str]:
        """Get list of available agent IDs."""
        return list(self.agent_nodes.keys())
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names."""
        return list(self.tool_nodes.keys())
    
    def get_graph_templates(self) -> List[str]:
        """Get list of available graph templates."""
        return list(self.graph_templates.keys())
    
    def clear_cache(self) -> None:
        """Clear the graph cache."""
        self.graph_cache.clear()
        self.logger.info("Cleared graph cache")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_graphs": len(self.graph_cache),
            "available_agents": len(self.agent_nodes),
            "available_tools": len(self.tool_nodes),
            "available_templates": len(self.graph_templates)
        }
