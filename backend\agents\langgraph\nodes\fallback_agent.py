"""
Fallback Agent for LangGraph-based Datagenius System.

This module provides a robust fallback agent that can handle basic interactions
when specific agent implementations fail to load or are unavailable.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)


class FallbackAgent:
    """
    Fallback agent that provides basic functionality when specific agents fail.
    
    This agent ensures the system remains functional even when specialized
    agents cannot be loaded or are experiencing issues.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the fallback agent.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.agent_id = self.config.get("agent_id", "fallback")
        self.agent_type = self.config.get("agent_type", "fallback")
        self.capabilities = self.config.get("capabilities", ["basic_response", "error_handling"])
        self.fallback_mode = self.config.get("fallback_mode", True)
        
        # Fallback responses for different agent types
        self.agent_type_responses = {
            "concierge": {
                "greeting": "Hello! I'm your Datagenius assistant. I'm currently running in fallback mode, but I can still help you with basic questions and guide you to the right resources.",
                "default": "I'm here to help! While I'm running in a simplified mode, I can still assist you with general questions about Datagenius and help connect you with the right tools."
            },
            "marketing": {
                "greeting": "Hi! I'm your Marketing assistant. I'm currently in fallback mode, but I can still provide basic marketing guidance and help you understand our marketing tools.",
                "default": "I can help with basic marketing questions and guide you to our marketing resources, though I'm currently running in simplified mode."
            },
            "analysis": {
                "greeting": "Hello! I'm your Data Analysis assistant. While I'm in fallback mode, I can still help you understand our data analysis capabilities and guide you through basic processes.",
                "default": "I can provide basic guidance on data analysis and help you understand our analytical tools, though I'm currently in simplified mode."
            },
            "classification": {
                "greeting": "Hi! I'm your Classification assistant. I'm running in fallback mode but can still help you understand content classification and organization concepts.",
                "default": "I can provide basic guidance on content classification and help you understand our classification tools."
            },
            "visualization": {
                "greeting": "Hello! I'm your Visualization assistant. While in fallback mode, I can still help you understand data visualization concepts and guide you to our visualization tools.",
                "default": "I can provide basic guidance on data visualization and help you understand our charting capabilities."
            }
        }
        
        logger.info(f"FallbackAgent initialized for {self.agent_id} ({self.agent_type})")

    async def process_message(
        self,
        message: str,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a message using fallback logic.

        Args:
            message: The user's message
            user_id: User identifier
            conversation_id: Conversation identifier
            context: Additional context information

        Returns:
            Dict containing the agent's response and metadata
        """
        try:
            logger.debug(f"Fallback agent processing message: {message[:50]}...")
            
            # Determine response based on message content and agent type
            response_message = self._generate_response(message, context)
            
            return {
                "message": response_message,
                "metadata": {
                    "agent_type": self.agent_type,
                    "agent_id": self.agent_id,
                    "fallback_mode": True,
                    "capabilities_used": ["basic_response"],
                    "timestamp": datetime.now().isoformat(),
                    "workflow_complete": True,
                    "next_action": "END"
                },
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error in fallback agent process_message: {e}")
            return {
                "message": "I apologize, but I'm experiencing technical difficulties. Please try again later or contact support.",
                "metadata": {
                    "error": str(e),
                    "fallback_response": True,
                    "agent_type": self.agent_type,
                    "timestamp": datetime.now().isoformat(),
                    "workflow_complete": True,
                    "next_action": "END"
                },
                "success": False
            }

    def _generate_response(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate an appropriate response based on the message and agent type.

        Args:
            message: The user's message
            context: Additional context

        Returns:
            Response message string
        """
        try:
            message_lower = message.lower()
            
            # Check if this is a greeting or initial message
            if (message == "generate_greeting" or 
                (context and context.get("is_initial_greeting")) or
                any(greeting in message_lower for greeting in ["hello", "hi", "hey", "start"])):
                return self._get_greeting_response()
            
            # Check for specific intents and provide appropriate responses
            if any(word in message_lower for word in ["help", "what can you do", "capabilities"]):
                return self._get_capabilities_response()
            
            if any(word in message_lower for word in ["error", "problem", "issue", "not working"]):
                return self._get_error_help_response()
            
            # Default response based on agent type
            return self._get_default_response()
            
        except Exception as e:
            logger.error(f"Error generating fallback response: {e}")
            return "I'm here to help, but I'm currently experiencing some technical difficulties. Please try again or contact support if the issue persists."

    def _get_greeting_response(self) -> str:
        """Get a greeting response for this agent type."""
        responses = self.agent_type_responses.get(self.agent_type, {})
        return responses.get("greeting", "Hello! I'm your Datagenius assistant running in fallback mode. How can I help you today?")

    def _get_capabilities_response(self) -> str:
        """Get a capabilities response for this agent type."""
        base_response = f"I'm a {self.agent_type} assistant currently running in fallback mode. "
        
        if self.agent_type == "concierge":
            return base_response + "I can help you understand Datagenius features, guide you to the right tools, and provide general assistance with the platform."
        elif self.agent_type == "marketing":
            return base_response + "I can provide basic marketing guidance, explain our marketing tools, and help you understand marketing concepts."
        elif self.agent_type == "analysis":
            return base_response + "I can explain data analysis concepts, guide you through our analytical tools, and provide basic insights about data processing."
        elif self.agent_type == "classification":
            return base_response + "I can help you understand content classification, explain our categorization tools, and provide guidance on organizing information."
        elif self.agent_type == "visualization":
            return base_response + "I can explain data visualization concepts, guide you through our charting tools, and help you understand different visualization types."
        else:
            return base_response + "I can provide general assistance and help you understand the available tools and features."

    def _get_error_help_response(self) -> str:
        """Get an error help response."""
        return ("I understand you're experiencing an issue. Since I'm running in fallback mode, my troubleshooting capabilities are limited. "
                "Here are some general steps you can try:\n\n"
                "1. Refresh the page and try again\n"
                "2. Check your internet connection\n"
                "3. Clear your browser cache\n"
                "4. Contact support if the issue persists\n\n"
                "I apologize for any inconvenience!")

    def _get_default_response(self) -> str:
        """Get a default response for this agent type."""
        responses = self.agent_type_responses.get(self.agent_type, {})
        return responses.get("default", "I'm here to help! While I'm running in fallback mode, I'll do my best to assist you with your request.")

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this fallback agent."""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "capabilities": self.capabilities,
            "fallback_mode": True,
            "status": "active",
            "description": f"Fallback {self.agent_type} assistant providing basic functionality"
        }

    async def handle_with_own_capabilities(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle message with fallback capabilities (compatibility method).
        
        Args:
            message: The user's message
            context: Additional context
            
        Returns:
            Dict containing the response
        """
        return await self.process_message(message, context=context)
