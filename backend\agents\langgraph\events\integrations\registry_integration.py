"""
Event-driven integration for the Agent Registry.

This module provides an enhanced agent registry that publishes events
when agents are registered, unregistered, or have their capabilities updated.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Type
from datetime import datetime

from ....langgraph.events.event_bus import event_bus
from ....langgraph.events.types import (
    AgentRegistrationEvent,
    AgentUnregistrationEvent,
    CapabilityUpdateEvent
)
from ...core.agent_factory import agent_factory
from ....base import BaseAgent

logger = logging.getLogger(__name__)


class EventDrivenAgentRegistry:
    """
    Enhanced agent registry with event publishing capabilities.
    
    This class wraps the existing AgentRegistry and adds event publishing
    for registration, unregistration, and capability updates while maintaining
    full backward compatibility.
    """
    
    def __init__(self, enable_events: bool = True):
        self.enable_events = enable_events
        self._capability_cache: Dict[str, List[str]] = {}
        self._metadata_cache: Dict[str, Dict[str, Any]] = {}
        
        # Initialize event handlers if events are enabled
        if self.enable_events:
            self._setup_event_handlers()
    
    def _setup_event_handlers(self):
        """Set up event handlers for the registry."""
        try:
            # Import and initialize event handlers
            from ....langgraph.events.handlers import AgentEventHandler
            self.agent_handler = AgentEventHandler()
            
            # Subscribe to agent events
            event_bus.subscribe("agent.registered", self.agent_handler.handle_agent_registration)
            event_bus.subscribe("agent.unregistered", self.agent_handler.handle_agent_unregistration)
            event_bus.subscribe("agent.capability_updated", self.agent_handler.handle_capability_update)
            
            logger.info("Event handlers set up for EventDrivenAgentRegistry")
            
        except Exception as e:
            logger.error(f"Error setting up event handlers: {e}")
            self.enable_events = False
    
    async def register_agent(self, persona_id: str, agent_class: Type[BaseAgent], 
                           capabilities: Optional[List[str]] = None,
                           metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Register an agent class with event publishing.
        
        Args:
            persona_id: The ID of the persona
            agent_class: The agent class to register
            capabilities: Optional list of agent capabilities
            metadata: Optional metadata about the agent
        """
        try:
            # Use the agent factory for actual registration
            agent_factory.register_agent_class(persona_id, agent_class)
            
            # Extract capabilities and metadata
            if capabilities is None:
                capabilities = self._extract_capabilities_from_class(agent_class)
            
            if metadata is None:
                metadata = self._extract_metadata_from_class(agent_class)
            
            # Cache the information
            self._capability_cache[persona_id] = capabilities
            self._metadata_cache[persona_id] = metadata
            
            # Publish registration event if events are enabled
            if self.enable_events:
                await self._publish_registration_event(persona_id, capabilities, metadata)
            
            logger.info(f"Successfully registered agent {persona_id} with event publishing")
            
        except Exception as e:
            logger.error(f"Error registering agent {persona_id}: {e}")
            raise
    
    async def unregister_agent(self, persona_id: str, reason: str = "manual") -> bool:
        """
        Unregister an agent with event publishing.
        
        Args:
            persona_id: The ID of the persona to unregister
            reason: Reason for unregistration
            
        Returns:
            True if successfully unregistered, False otherwise
        """
        try:
            # Check if agent exists
            if persona_id not in agent_factory.agent_classes:
                logger.warning(f"Agent {persona_id} not found in factory")
                return False

            # Remove from the agent factory
            agent_factory.remove_agent(persona_id)
            
            # Publish unregistration event if events are enabled
            if self.enable_events:
                await self._publish_unregistration_event(persona_id, reason)
            
            # Clean up local caches
            self._capability_cache.pop(persona_id, None)
            self._metadata_cache.pop(persona_id, None)
            
            logger.info(f"Successfully unregistered agent {persona_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error unregistering agent {persona_id}: {e}")
            return False
    
    async def update_agent_capabilities(self, persona_id: str, new_capabilities: List[str],
                                      update_reason: str = "manual") -> bool:
        """
        Update agent capabilities with event publishing.
        
        Args:
            persona_id: The ID of the persona
            new_capabilities: New list of capabilities
            update_reason: Reason for the update
            
        Returns:
            True if successfully updated, False otherwise
        """
        try:
            # Check if agent exists
            if persona_id not in agent_factory.agent_classes:
                logger.warning(f"Agent {persona_id} not found in factory")
                return False

            # Get old capabilities
            old_capabilities = self._capability_cache.get(persona_id, [])

            # Update capability cache
            self._capability_cache[persona_id] = new_capabilities

            # Update configuration if it exists
            if persona_id in agent_factory.agent_configs:
                agent_factory.agent_configs[persona_id]["capabilities"] = new_capabilities
            
            # Publish capability update event if events are enabled
            if self.enable_events:
                await self._publish_capability_update_event(
                    persona_id, old_capabilities, new_capabilities, update_reason
                )
            
            logger.info(f"Successfully updated capabilities for agent {persona_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating capabilities for agent {persona_id}: {e}")
            return False
    
    async def _publish_registration_event(self, persona_id: str, capabilities: List[str],
                                        metadata: Dict[str, Any]):
        """Publish agent registration event."""
        try:
            event = AgentRegistrationEvent(
                agent_id=persona_id,
                capabilities=capabilities,
                metadata=metadata
            )
            await event_bus.publish(event)
            logger.debug(f"Published registration event for {persona_id}")
            
        except Exception as e:
            logger.error(f"Error publishing registration event for {persona_id}: {e}")
    
    async def _publish_unregistration_event(self, persona_id: str, reason: str):
        """Publish agent unregistration event."""
        try:
            event = AgentUnregistrationEvent(
                agent_id=persona_id,
                reason=reason
            )
            await event_bus.publish(event)
            logger.debug(f"Published unregistration event for {persona_id}")
            
        except Exception as e:
            logger.error(f"Error publishing unregistration event for {persona_id}: {e}")
    
    async def _publish_capability_update_event(self, persona_id: str, old_capabilities: List[str],
                                             new_capabilities: List[str], update_reason: str):
        """Publish capability update event."""
        try:
            event = CapabilityUpdateEvent(
                agent_id=persona_id,
                old_capabilities=old_capabilities,
                new_capabilities=new_capabilities,
                update_reason=update_reason
            )
            await event_bus.publish(event)
            logger.debug(f"Published capability update event for {persona_id}")
            
        except Exception as e:
            logger.error(f"Error publishing capability update event for {persona_id}: {e}")
    
    def _extract_capabilities_from_class(self, agent_class: Type[BaseAgent]) -> List[str]:
        """Extract capabilities from agent class."""
        try:
            # Try to get capabilities from class attributes
            if hasattr(agent_class, 'capabilities'):
                return list(agent_class.capabilities)
            
            # Try to get from class docstring or other metadata
            if hasattr(agent_class, '__doc__') and agent_class.__doc__:
                # Simple capability extraction from docstring
                doc = agent_class.__doc__.lower()
                capabilities = []
                
                # Look for common capability keywords
                capability_keywords = [
                    'analysis', 'research', 'reporting', 'visualization',
                    'data processing', 'machine learning', 'nlp', 'classification',
                    'summarization', 'translation', 'generation'
                ]
                
                for keyword in capability_keywords:
                    if keyword in doc:
                        capabilities.append(keyword)
                
                return capabilities
            
            # Default capabilities based on class name
            class_name = agent_class.__name__.lower()
            if 'analyst' in class_name:
                return ['analysis', 'data processing']
            elif 'research' in class_name:
                return ['research', 'information gathering']
            elif 'report' in class_name:
                return ['reporting', 'document generation']
            else:
                return ['general']
                
        except Exception as e:
            logger.warning(f"Error extracting capabilities from {agent_class.__name__}: {e}")
            return ['general']
    
    def _extract_metadata_from_class(self, agent_class: Type[BaseAgent]) -> Dict[str, Any]:
        """Extract metadata from agent class."""
        try:
            metadata = {
                'class_name': agent_class.__name__,
                'module': agent_class.__module__,
                'registration_time': datetime.now().isoformat()
            }
            
            # Add docstring if available
            if hasattr(agent_class, '__doc__') and agent_class.__doc__:
                metadata['description'] = agent_class.__doc__.strip()
            
            # Add version if available
            if hasattr(agent_class, '__version__'):
                metadata['version'] = agent_class.__version__
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting metadata from {agent_class.__name__}: {e}")
            return {
                'class_name': agent_class.__name__,
                'registration_time': datetime.now().isoformat()
            }
    
    # Delegate other methods to the agent factory for backward compatibility
    def get_agent_class(self, persona_id: str) -> Optional[Type[BaseAgent]]:
        """Get agent class (delegates to agent factory)."""
        # Use backward compatibility attribute
        return agent_factory.agent_classes.get(persona_id)

    def list_registered_personas(self) -> List[str]:
        """List registered personas (delegates to agent factory)."""
        return agent_factory.get_available_agents()

    def get_configuration(self, persona_id: str) -> Optional[Dict[str, Any]]:
        """Get agent configuration (delegates to agent factory)."""
        return agent_factory.agent_configs.get(persona_id)
    
    def get_cached_capabilities(self, persona_id: str) -> Optional[List[str]]:
        """Get cached capabilities for an agent."""
        return self._capability_cache.get(persona_id)
    
    def get_cached_metadata(self, persona_id: str) -> Optional[Dict[str, Any]]:
        """Get cached metadata for an agent."""
        return self._metadata_cache.get(persona_id)
    
    async def load_configurations_with_events(self, config_dir: str) -> None:
        """
        Load configurations and register agents with event publishing.
        
        Args:
            config_dir: Directory containing agent configuration files
        """
        try:
            # Use the agent factory to discover agents from configuration directory
            # Note: The agent factory doesn't have a direct load_configurations method,
            # so we'll skip this for now as it's part of the legacy system
            
            # If events are enabled, publish registration events for all loaded agents
            if self.enable_events:
                await self._publish_bulk_registration_events()
            
        except Exception as e:
            logger.error(f"Error loading configurations with events: {e}")
            raise
    
    async def _publish_bulk_registration_events(self):
        """Publish registration events for all currently registered agents."""
        try:
            for persona_id in agent_factory.get_available_agents():
                # Get configuration if available
                config = agent_factory.agent_configs.get(persona_id)
                capabilities = config.get('capabilities', []) if config else []

                # Extract metadata
                agent_class = agent_factory.agent_classes.get(persona_id)
                metadata = self._extract_metadata_from_class(agent_class) if agent_class else {}

                # Update caches
                self._capability_cache[persona_id] = capabilities
                self._metadata_cache[persona_id] = metadata

                # Publish event
                await self._publish_registration_event(persona_id, capabilities, metadata)

            logger.info(f"Published bulk registration events for {len(agent_factory.agent_classes)} agents")
            
        except Exception as e:
            logger.error(f"Error publishing bulk registration events: {e}")


# Global instance for easy access
event_driven_registry = EventDrivenAgentRegistry()
