/**
 * Business Profile Header Dropdown Component
 * 
 * Compact dropdown component designed for the unified header that displays
 * the active business profile and allows switching between profiles.
 */

import React, { useState, useEffect } from 'react';
import { Check, ChevronDown, Building2, Loader2, Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useBusinessProfileStore } from '@/stores/business-profile-store';
import { getBusinessTypeLabel } from '@/lib/businessProfileApi';
import { useToast } from '@/hooks/use-toast';

interface BusinessProfileHeaderDropdownProps {
  className?: string;
  onCreateNew?: () => void;
  showCreateButton?: boolean;
}

export const BusinessProfileHeaderDropdown: React.FC<BusinessProfileHeaderDropdownProps> = ({
  className,
  onCreateNew,
  showCreateButton = true,
}) => {
  const [open, setOpen] = useState(false);
  const [isSwitching, setIsSwitching] = useState(false);
  const { toast } = useToast();

  const {
    profiles,
    activeProfile,
    isLoading,
    error,
    loadProfiles,
    switchProfile,
    clearError,
  } = useBusinessProfileStore();

  // Load profiles on mount - only if not already loaded
  useEffect(() => {
    if (!isLoading && profiles.length === 0 && !error) {
      loadProfiles();
    }
  }, []); // Empty dependency array - only run once on mount

  // Clear error when dropdown opens
  useEffect(() => {
    if (open && error) {
      clearError();
    }
  }, [open, error]); // Remove clearError from dependencies to prevent infinite loop

  const handleProfileSelect = async (profileId: string) => {
    if (profileId === activeProfile?.id) {
      setOpen(false);
      return;
    }

    try {
      setIsSwitching(true);
      await switchProfile(profileId);
      setOpen(false);
      
      toast({
        title: 'Profile Switched',
        description: 'Business profile switched successfully',
      });
    } catch (error) {
      console.error('Error switching profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to switch business profile',
        variant: 'destructive',
      });
    } finally {
      setIsSwitching(false);
    }
  };

  const handleCreateNew = () => {
    setOpen(false);
    if (onCreateNew) {
      onCreateNew();
    }
  };

  // Show loading state
  if (isLoading && !activeProfile) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
        <span className="text-sm text-muted-foreground">Loading...</span>
      </div>
    );
  }

  // Show no profile state
  if (!activeProfile && !isLoading) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <Building2 className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">No Profile</span>
        {showCreateButton && onCreateNew && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onCreateNew}
            className="h-6 px-2 text-xs"
          >
            <Plus className="h-3 w-3 mr-1" />
            Create
          </Button>
        )}
      </div>
    );
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-8 px-3 justify-between min-w-[140px] max-w-[200px]",
            "hover:bg-slate-100 focus:bg-slate-100",
            className
          )}
          disabled={isSwitching}
        >
          <div className="flex items-center space-x-2 min-w-0">
            <Building2 className="h-4 w-4 text-slate-600 flex-shrink-0" />
            <span className="text-sm font-medium truncate">
              {activeProfile?.name || 'Select Profile'}
            </span>
          </div>
          {isSwitching ? (
            <Loader2 className="h-3 w-3 animate-spin flex-shrink-0" />
          ) : (
            <ChevronDown className="h-3 w-3 text-slate-500 flex-shrink-0" />
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start" className="w-64">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Business Profiles</span>
          <Badge variant="secondary" className="text-xs">
            {profiles.length}
          </Badge>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />

        {/* Profile List */}
        <div className="max-h-64 overflow-y-auto">
          {profiles.length === 0 ? (
            <div className="px-2 py-3 text-center text-sm text-muted-foreground">
              No profiles available
            </div>
          ) : (
            profiles.map((profile) => (
              <DropdownMenuItem
                key={profile.id}
                className="cursor-pointer px-3 py-2"
                onClick={() => handleProfileSelect(profile.id)}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-2 min-w-0">
                    <Building2 className="h-4 w-4 text-slate-500 flex-shrink-0" />
                    <div className="min-w-0">
                      <div className="font-medium text-sm truncate">
                        {profile.name}
                      </div>
                      {profile.industry && (
                        <div className="text-xs text-muted-foreground truncate">
                          {profile.industry}
                        </div>
                      )}
                    </div>
                  </div>
                  {profile.is_active && (
                    <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                  )}
                </div>
              </DropdownMenuItem>
            ))
          )}
        </div>

        {/* Create New Profile Button */}
        {showCreateButton && onCreateNew && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="cursor-pointer px-3 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
              onClick={handleCreateNew}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create New Profile
            </DropdownMenuItem>
          </>
        )}

        {/* Error State */}
        {error && (
          <>
            <DropdownMenuSeparator />
            <div className="px-3 py-2 text-xs text-red-600 bg-red-50">
              {error}
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
