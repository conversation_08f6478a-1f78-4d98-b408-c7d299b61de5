# LangGraph Development Environment Configuration
# This file contains LangGraph-specific development overrides
# Inherits from main app development.yaml configuration

# LangGraph Development Settings
langgraph:
  enabled: true  # Enable for development testing
  max_workflow_duration: 1800  # 30 minutes for development
  rollout_percentage: 100.0  # Full rollout in development
  migration_phase: "testing"

  # Development-specific LangGraph settings
  enable_checkpointing: true
  checkpoint_interval: 30
  max_checkpoints_per_workflow: 100
  enable_monitoring: true
  monitoring_interval: 60
  performance_cache_ttl: 300
  enable_auto_recovery: true
  max_recovery_attempts: 3
  recovery_timeout: 300
  enable_legacy_fallback: true

# LangGraph-specific logging overrides
logging:
  level: "DEBUG"
  enable_file_logging: true
  log_file_path: "logs/langgraph_dev.log"

# Development-specific Agent Settings
agents:
  default_timeout: 120  # Shorter timeouts for faster development
  max_concurrent_agents: 3
  
  # Enable detailed logging for all agents
  concierge:
    timeout: 30
    enable_debug_logging: true
  
  analysis:
    timeout: 300
    enable_debug_logging: true
    enable_caching: false  # Disable caching for development
  
  marketing:
    timeout: 120
    enable_debug_logging: true
  
  classification:
    timeout: 60
    enable_debug_logging: true

# Tool Configuration for Development
tools:
  default_timeout: 60  # Shorter timeouts
  max_concurrent_tools: 5
  
  # Enable debugging for tools
  data_access:
    timeout: 120
    max_results: 100  # Smaller result sets for development
    enable_query_logging: true
  
  code_execution:
    timeout: 90
    max_memory_mb: 256  # Lower memory limit for development
    enable_debug_output: true
  
  visualization:
    timeout: 60
    enable_debug_mode: true

# Workflow Configuration for Development
workflows:
  default_timeout: 600  # 10 minutes
  max_concurrent_workflows: 5
  enable_detailed_metrics: true
  
  # Enable debugging for all workflow types
  simple:
    timeout: 120
    enable_step_logging: true
  
  complex:
    timeout: 600
    enable_step_logging: true
    enable_state_logging: true
  
  collaborative:
    timeout: 900
    enable_agent_communication_logging: true

# Security Configuration for Development
security:
  enable_input_validation: true
  enable_output_sanitization: true  # Enable for security-first development
  max_input_size: 2097152  # 2MB for development testing

  # Enable rate limiting in development for security testing
  rate_limiting:
    enabled: true  # Enable rate limiting in development
    requests_per_minute: 1000  # Higher limit for development
    burst_size: 100

  # Security-first development defaults
  security_first_defaults:
    enable_sql_injection_protection: true
    enable_xss_protection: true
    enable_csrf_protection: true
    enable_audit_logging: true
    validate_all_inputs: true

# Monitoring Configuration for Development
monitoring:
  enable_metrics_collection: true
  metrics_retention_days: 7  # Shorter retention for development
  enable_performance_alerts: false  # Disable alerts in development
  
  # Development-friendly alert thresholds
  alerts:
    high_execution_time: 600  # More lenient thresholds
    high_error_rate: 0.1  # 10%
    low_success_rate: 0.8  # 80%

# Feature Flags for Development
feature_flags:
  # Enable most features for development testing
  langgraph_enabled: true
  intelligent_routing: true
  multi_agent_collaboration: true
  state_persistence: true
  performance_monitoring: true
  legacy_fallback: true
  
  # Advanced features enabled for testing
  advanced_analytics: true
  custom_workflows: true
  external_integrations: false  # Keep disabled to avoid external dependencies
  
  # Experimental features enabled for development
  ai_optimization: true
  predictive_routing: true
  auto_scaling: false  # Keep disabled for development

  # Phase 4: Platform Evolution Features (enabled for development)
  phase4_platform_evolution: true
  capability_marketplace: true
  ai_workflow_composer: true
  pattern_recognition: true
  trading_engine: true
  certification_system: true

# Integration Settings for Development
integrations:
  external_apis:
    timeout: 10  # Shorter timeout for development
    max_retries: 1
    enable_caching: false  # Disable caching for development
  
  business_profiles:
    enable_auto_context: true
    context_refresh_interval: 60  # More frequent refresh for development
  
  cross_agent_intelligence:
    enabled: true
    insight_retention_hours: 2  # Shorter retention for development
    max_insights_per_profile: 20

# Phase 4: Platform Evolution Development Settings
phase4:
  enabled: true
  initialization_timeout: 60  # Shorter timeout for development

  # Development-friendly marketplace settings
  marketplace:
    enabled: true
    matching_threshold: 0.6  # Lower threshold for more matches
    auto_pricing: false  # Disable for predictable testing
    enable_debug_logging: true

  # Development workflow composer settings
  workflow_composer:
    enabled: true
    max_workflow_nodes: 10  # Smaller workflows for development
    default_timeout: 300.0  # 5 minutes
    enable_pattern_learning: false  # Disable for faster startup
    enable_debug_mode: true

  # Development pattern recognition settings
  pattern_recognition:
    enabled: true
    learning_window_hours: 24  # 1 day for development
    min_pattern_occurrences: 2  # Lower threshold for testing
    max_patterns: 100  # Smaller limit for development
    enable_debug_logging: true

  # Development trading engine settings
  trading_engine:
    enabled: true
    auction_duration_minutes: 5  # Shorter auctions for development
    bid_expiry_hours: 0.5  # 30 minutes
    enable_debug_logging: true

  # Development certification system settings
  certification_system:
    enabled: true
    validity_period_days: 30  # Shorter validity for development
    test_duration_hours: 1  # 1 hour for development
    min_test_executions: 3  # Lower threshold for testing
    auto_certification: true
    enable_debug_logging: true

  # Faster background services for development
  background_services:
    pattern_learning_interval: 300  # 5 minutes
    workflow_optimization_interval: 180  # 3 minutes
    marketplace_maintenance_interval: 60  # 1 minute
    price_optimization_interval: 300  # 5 minutes

# Development-specific Settings
development:
  debug_mode: true
  enable_detailed_logging: true
  mock_external_services: true  # Mock external services in development
  test_data_enabled: true
  
  # Development tools
  enable_hot_reload: true
  enable_config_reload: true
  enable_state_inspection: true
  
  # Testing settings
  enable_test_endpoints: true
  enable_mock_agents: true
  enable_workflow_simulation: true
  
  # Performance settings for development
  disable_performance_optimizations: true  # For easier debugging
  enable_memory_profiling: true
  enable_execution_tracing: true

# Development Database Settings
dev_database:
  enable_query_logging: true
  enable_slow_query_logging: true
  slow_query_threshold: 1.0  # 1 second
  enable_connection_pooling_debug: true

# Development Caching Settings
dev_caching:
  disable_all_caches: false
  cache_debug_logging: true
  short_cache_ttl: 30  # 30 seconds for development

# Development Error Handling
dev_error_handling:
  enable_detailed_error_responses: true
  enable_stack_traces: true
  enable_error_context: true
  disable_error_suppression: true
