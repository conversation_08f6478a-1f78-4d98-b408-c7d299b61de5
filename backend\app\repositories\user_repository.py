"""
User Repository Implementation.

Specialized repository for User entity operations with enhanced security
and user-specific business logic.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from ..models.database_models import User
from ..models.schemas import UserCreate, UserUpdate, UserFilterParams, UserStats, UserWithStats
from .base_repository import BaseRepository, RepositoryError

logger = logging.getLogger(__name__)


class UserRepository(BaseRepository[User]):
    """
    Specialized repository for User entity operations.
    
    Provides user-specific methods beyond basic CRUD operations.
    """
    
    def __init__(self, db: Session):
        super().__init__(db, User)
    
    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        try:
            return self.find_by(email=email)
        except Exception as e:
            self.logger.error(f"Error getting user by email {email}: {e}")
            raise RepositoryError(
                "Failed to retrieve user by email",
                entity_type="User"
            )
    
    def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        try:
            return self.find_by(username=username)
        except Exception as e:
            self.logger.error(f"Error getting user by username {username}: {e}")
            raise RepositoryError(
                "Failed to retrieve user by username",
                entity_type="User"
            )
    
    def get_active_users(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None
    ) -> List[User]:
        """Get active users with optional search."""
        try:
            filters = {"is_active": True}
            
            if search:
                # Use custom query for search functionality
                query = self._build_query(is_active=True)
                query = query.filter(
                    or_(
                        User.email.ilike(f"%{search}%"),
                        User.username.ilike(f"%{search}%"),
                        User.first_name.ilike(f"%{search}%"),
                        User.last_name.ilike(f"%{search}%")
                    )
                )
                query = self._apply_pagination(query, skip, limit)
                return query.all()
            else:
                return self.get_multi(skip=skip, limit=limit, **filters)
                
        except Exception as e:
            self.logger.error(f"Error getting active users: {e}")
            raise RepositoryError(
                "Failed to retrieve active users",
                entity_type="User"
            )
    
    def get_superusers(self) -> List[User]:
        """Get all superusers."""
        try:
            return self.find_all_by(is_superuser=True, is_active=True)
        except Exception as e:
            self.logger.error(f"Error getting superusers: {e}")
            raise RepositoryError(
                "Failed to retrieve superusers",
                entity_type="User"
            )
    
    def create_user(self, user_data: UserCreate, **kwargs) -> User:
        """Create a new user with validation."""
        try:
            # Check if email already exists
            existing_user = self.get_by_email(user_data.email)
            if existing_user:
                raise RepositoryError(
                    "User with this email already exists",
                    entity_type="User"
                )
            
            # Check if username already exists (if provided)
            if user_data.username:
                existing_username = self.get_by_username(user_data.username)
                if existing_username:
                    raise RepositoryError(
                        "User with this username already exists",
                        entity_type="User"
                    )
            
            return self.create(user_data, **kwargs)
            
        except RepositoryError:
            raise
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            raise RepositoryError(
                "Failed to create user",
                entity_type="User"
            )
    
    def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """Update user with validation."""
        try:
            # If email is being updated, check for conflicts
            if hasattr(user_data, 'email') and user_data.email:
                existing_user = self.get_by_email(user_data.email)
                if existing_user and existing_user.id != user_id:
                    raise RepositoryError(
                        "Another user with this email already exists",
                        entity_type="User"
                    )
            
            # If username is being updated, check for conflicts
            if hasattr(user_data, 'username') and user_data.username:
                existing_username = self.get_by_username(user_data.username)
                if existing_username and existing_username.id != user_id:
                    raise RepositoryError(
                        "Another user with this username already exists",
                        entity_type="User"
                    )
            
            return self.update(user_id, user_data)
            
        except RepositoryError:
            raise
        except Exception as e:
            self.logger.error(f"Error updating user {user_id}: {e}")
            raise RepositoryError(
                "Failed to update user",
                entity_type="User"
            )
    
    def deactivate_user(self, user_id: int) -> Optional[User]:
        """Deactivate a user (soft delete)."""
        try:
            return self.update(user_id, {"is_active": False})
        except Exception as e:
            self.logger.error(f"Error deactivating user {user_id}: {e}")
            raise RepositoryError(
                "Failed to deactivate user",
                entity_type="User"
            )
    
    def activate_user(self, user_id: int) -> Optional[User]:
        """Activate a user."""
        try:
            return self.update(user_id, {"is_active": True})
        except Exception as e:
            self.logger.error(f"Error activating user {user_id}: {e}")
            raise RepositoryError(
                "Failed to activate user",
                entity_type="User"
            )
    
    def verify_user(self, user_id: int) -> Optional[User]:
        """Mark user as verified."""
        try:
            return self.update(user_id, {"is_verified": True})
        except Exception as e:
            self.logger.error(f"Error verifying user {user_id}: {e}")
            raise RepositoryError(
                "Failed to verify user",
                entity_type="User"
            )
    
    def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics."""
        try:
            total_users = self.count()
            active_users = self.count(is_active=True)
            verified_users = self.count(is_verified=True)
            superusers = self.count(is_superuser=True)
            
            # Get recent registrations (last 30 days)
            thirty_days_ago = datetime.now(timezone.utc).replace(
                day=datetime.now(timezone.utc).day - 30
            )
            
            recent_registrations = self.db.query(func.count(User.id)).filter(
                User.created_at >= thirty_days_ago
            ).scalar()
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "verified_users": verified_users,
                "superusers": superusers,
                "recent_registrations": recent_registrations,
                "inactive_users": total_users - active_users,
                "unverified_users": total_users - verified_users
            }
            
        except Exception as e:
            self.logger.error(f"Error getting user statistics: {e}")
            raise RepositoryError(
                "Failed to retrieve user statistics",
                entity_type="User"
            )
    
    def search_users(
        self,
        query: str,
        skip: int = 0,
        limit: int = 100,
        include_inactive: bool = False
    ) -> List[User]:
        """Search users by various fields."""
        try:
            base_query = self.db.query(User)
            
            # Apply activity filter
            if not include_inactive:
                base_query = base_query.filter(User.is_active == True)
            
            # Apply search filters
            search_filter = or_(
                User.email.ilike(f"%{query}%"),
                User.username.ilike(f"%{query}%"),
                User.first_name.ilike(f"%{query}%"),
                User.last_name.ilike(f"%{query}%"),
                func.concat(User.first_name, ' ', User.last_name).ilike(f"%{query}%")
            )
            
            base_query = base_query.filter(search_filter)
            base_query = self._apply_pagination(base_query, skip, limit)
            base_query = base_query.order_by(User.created_at.desc())
            
            return base_query.all()
            
        except Exception as e:
            self.logger.error(f"Error searching users with query '{query}': {e}")
            raise RepositoryError(
                "Failed to search users",
                entity_type="User"
            )
    
    def get_users_by_role(
        self,
        is_superuser: Optional[bool] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """Get users filtered by role and status."""
        try:
            filters = {}

            if is_superuser is not None:
                filters["is_superuser"] = is_superuser

            if is_active is not None:
                filters["is_active"] = is_active

            return self.get_multi(
                skip=skip,
                limit=limit,
                order_by="created_at",
                desc_order=True,
                **filters
            )

        except Exception as e:
            self.logger.error(f"Error getting users by role: {e}")
            raise RepositoryError(
                "Failed to retrieve users by role",
                entity_type="User"
            )

    def get_users_with_pagination(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_superuser: Optional[bool] = None
    ) -> tuple[List[User], int]:
        """
        Get users with filtering and pagination, returning both users and total count.

        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return
            search: Search term for email or username
            is_active: Filter by active status
            is_superuser: Filter by superuser status

        Returns:
            Tuple of (users list, total count)
        """
        try:
            query = self.db.query(User)

            # Apply filters
            if search:
                query = query.filter(
                    or_(
                        User.email.ilike(f"%{search}%"),
                        User.username.ilike(f"%{search}%")
                    )
                )
            if is_active is not None:
                query = query.filter(User.is_active == is_active)
            if is_superuser is not None:
                query = query.filter(User.is_superuser == is_superuser)

            # Get total count
            total = query.count()

            # Apply pagination
            users = query.order_by(User.created_at.desc()).offset(skip).limit(limit).all()

            return users, total

        except Exception as e:
            self.logger.error(f"Error getting users with pagination: {e}")
            raise RepositoryError(
                "Failed to retrieve users with pagination",
                entity_type="User"
            )

    def update_user_data(self, user_id: int, user_data: Dict[str, Any]) -> Optional[User]:
        """
        Update a user with the provided data.

        Args:
            user_id: ID of the user to update
            user_data: Dictionary of fields to update

        Returns:
            Updated user or None if not found
        """
        try:
            user = self.get(user_id)
            if not user:
                return None

            for key, value in user_data.items():
                if hasattr(user, key):
                    setattr(user, key, value)

            self.db.commit()
            self.db.refresh(user)
            return user

        except Exception as e:
            self.logger.error(f"Error updating user {user_id}: {e}")
            self.db.rollback()
            raise RepositoryError(
                f"Failed to update user {user_id}",
                entity_type="User"
            )
    
    def update_last_login(self, user_id: int) -> Optional[User]:
        """Update user's last login timestamp."""
        try:
            # Note: This assumes a last_login field exists on the User model
            # If it doesn't exist, this method should be removed or the field added
            current_time = datetime.now(timezone.utc)
            return self.update(user_id, {"last_login": current_time})
            
        except Exception as e:
            self.logger.error(f"Error updating last login for user {user_id}: {e}")
            # Don't raise error for this non-critical operation
            return None
    
    def bulk_update_status(
        self,
        user_ids: List[int],
        is_active: Optional[bool] = None,
        is_verified: Optional[bool] = None
    ) -> int:
        """Bulk update user status."""
        try:
            update_data = {}
            
            if is_active is not None:
                update_data["is_active"] = is_active
            
            if is_verified is not None:
                update_data["is_verified"] = is_verified
            
            if not update_data:
                return 0
            
            # Add updated_at timestamp
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            updated_count = self.db.query(User).filter(
                User.id.in_(user_ids)
            ).update(update_data, synchronize_session=False)
            
            self.db.commit()
            
            self._log_audit("BULK_UPDATE_STATUS", details={
                "user_ids": user_ids,
                "update_data": update_data,
                "updated_count": updated_count
            })
            
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error bulk updating user status: {e}")
            raise RepositoryError(
                "Failed to bulk update user status",
                entity_type="User"
            )
