"""
Business Profile Repository Implementation.

Specialized repository for BusinessProfile entity operations with business-specific
logic and validation.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from ..models.database_models import BusinessProfile
from ..models.schemas import BusinessProfileCreate, BusinessProfileUpdate, BusinessProfileFilterParams
from .base_repository import BaseRepository, RepositoryError

logger = logging.getLogger(__name__)


class BusinessProfileRepository(BaseRepository[BusinessProfile]):
    """
    Specialized repository for BusinessProfile entity operations.
    
    Provides business profile-specific methods for managing company information,
    settings, and business-related data.
    """
    
    def __init__(self, db: Session):
        super().__init__(db, BusinessProfile)
    
    def get_by_user_id(self, user_id: str) -> Optional[BusinessProfile]:
        """Get business profile by user ID."""
        try:
            return self.find_by(user_id=user_id)
        except Exception as e:
            self.logger.error(f"Error getting business profile for user {user_id}: {e}")
            raise RepositoryError(
                "Failed to retrieve business profile by user ID",
                entity_type="BusinessProfile"
            )
    
    def get_by_company_name(self, company_name: str) -> Optional[BusinessProfile]:
        """Get business profile by company name."""
        try:
            return self.find_by(company_name=company_name)
        except Exception as e:
            self.logger.error(f"Error getting business profile by company name {company_name}: {e}")
            raise RepositoryError(
                "Failed to retrieve business profile by company name",
                entity_type="BusinessProfile"
            )
    
    def search_profiles(
        self,
        query: str,
        skip: int = 0,
        limit: int = 50,
        include_inactive: bool = False
    ) -> List[BusinessProfile]:
        """Search business profiles by various fields."""
        try:
            base_query = self.db.query(BusinessProfile)
            
            # Apply activity filter
            if not include_inactive:
                base_query = base_query.filter(BusinessProfile.is_active == True)
            
            # Apply search filters
            search_filter = or_(
                BusinessProfile.company_name.ilike(f"%{query}%"),
                BusinessProfile.industry.ilike(f"%{query}%"),
                BusinessProfile.description.ilike(f"%{query}%"),
                BusinessProfile.website.ilike(f"%{query}%")
            )
            
            base_query = base_query.filter(search_filter)
            base_query = self._apply_pagination(base_query, skip, limit)
            base_query = base_query.order_by(BusinessProfile.created_at.desc())
            
            return base_query.all()
            
        except Exception as e:
            self.logger.error(f"Error searching business profiles with query '{query}': {e}")
            raise RepositoryError(
                "Failed to search business profiles",
                entity_type="BusinessProfile"
            )
    
    def get_profiles_by_industry(
        self,
        industry: str,
        skip: int = 0,
        limit: int = 100,
        include_inactive: bool = False
    ) -> List[BusinessProfile]:
        """Get business profiles by industry."""
        try:
            filters = {"industry": industry}
            
            if not include_inactive:
                filters["is_active"] = True
            
            return self.get_multi(
                skip=skip,
                limit=limit,
                order_by="created_at",
                desc_order=True,
                **filters
            )
            
        except Exception as e:
            self.logger.error(f"Error getting profiles by industry {industry}: {e}")
            raise RepositoryError(
                "Failed to retrieve profiles by industry",
                entity_type="BusinessProfile"
            )
    
    def create_profile(
        self,
        profile_data: BusinessProfileCreate,
        user_id: str,
        **kwargs
    ) -> BusinessProfile:
        """Create a new business profile for a user."""
        try:
            # Check if user already has a profile
            existing_profile = self.get_by_user_id(user_id)
            if existing_profile:
                raise RepositoryError(
                    "User already has a business profile",
                    entity_type="BusinessProfile"
                )
            
            # Check if company name is already taken
            if profile_data.company_name:
                existing_company = self.get_by_company_name(profile_data.company_name)
                if existing_company:
                    raise RepositoryError(
                        "Company name already exists",
                        entity_type="BusinessProfile"
                    )
            
            # Add user_id to profile data
            create_data = profile_data.model_dump()
            create_data["user_id"] = user_id
            create_data.setdefault("is_active", True)
            
            return self.create(create_data, **kwargs)
            
        except RepositoryError:
            raise
        except Exception as e:
            self.logger.error(f"Error creating business profile for user {user_id}: {e}")
            raise RepositoryError(
                "Failed to create business profile",
                entity_type="BusinessProfile"
            )
    
    def update_profile(
        self,
        profile_id: str,
        profile_data: BusinessProfileUpdate,
        user_id: str
    ) -> Optional[BusinessProfile]:
        """Update business profile with ownership verification."""
        try:
            # Verify ownership
            profile = self.get(profile_id)
            if not profile or profile.user_id != user_id:
                return None
            
            # Check company name conflicts if being updated
            if hasattr(profile_data, 'company_name') and profile_data.company_name:
                existing_company = self.get_by_company_name(profile_data.company_name)
                if existing_company and existing_company.id != profile_id:
                    raise RepositoryError(
                        "Company name already exists",
                        entity_type="BusinessProfile"
                    )
            
            return self.update(profile_id, profile_data)
            
        except RepositoryError:
            raise
        except Exception as e:
            self.logger.error(f"Error updating business profile {profile_id}: {e}")
            raise RepositoryError(
                "Failed to update business profile",
                entity_type="BusinessProfile"
            )
    
    def deactivate_profile(self, profile_id: str, user_id: str) -> Optional[BusinessProfile]:
        """Deactivate a business profile."""
        try:
            # Verify ownership
            profile = self.get(profile_id)
            if not profile or profile.user_id != user_id:
                return None
            
            return self.update(profile_id, {"is_active": False})
            
        except Exception as e:
            self.logger.error(f"Error deactivating business profile {profile_id}: {e}")
            raise RepositoryError(
                "Failed to deactivate business profile",
                entity_type="BusinessProfile"
            )
    
    def activate_profile(self, profile_id: str, user_id: str) -> Optional[BusinessProfile]:
        """Activate a business profile."""
        try:
            # Verify ownership
            profile = self.get(profile_id)
            if not profile or profile.user_id != user_id:
                return None
            
            return self.update(profile_id, {"is_active": True})
            
        except Exception as e:
            self.logger.error(f"Error activating business profile {profile_id}: {e}")
            raise RepositoryError(
                "Failed to activate business profile",
                entity_type="BusinessProfile"
            )
    
    def get_profile_stats(self) -> Dict[str, Any]:
        """Get business profile statistics."""
        try:
            total_profiles = self.count()
            active_profiles = self.count(is_active=True)
            inactive_profiles = total_profiles - active_profiles
            
            # Get industry distribution
            industry_stats = self.db.query(
                BusinessProfile.industry,
                func.count(BusinessProfile.id).label('count')
            ).filter(
                BusinessProfile.is_active == True
            ).group_by(
                BusinessProfile.industry
            ).all()
            
            industry_distribution = {
                industry: count for industry, count in industry_stats
                if industry is not None
            }
            
            # Get recent profiles (last 30 days)
            thirty_days_ago = datetime.now(timezone.utc).replace(
                day=datetime.now(timezone.utc).day - 30
            )
            
            recent_profiles = self.db.query(func.count(BusinessProfile.id)).filter(
                BusinessProfile.created_at >= thirty_days_ago
            ).scalar()
            
            return {
                "total_profiles": total_profiles,
                "active_profiles": active_profiles,
                "inactive_profiles": inactive_profiles,
                "recent_profiles": recent_profiles,
                "industry_distribution": industry_distribution,
                "top_industries": sorted(
                    industry_distribution.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5]
            }
            
        except Exception as e:
            self.logger.error(f"Error getting business profile statistics: {e}")
            raise RepositoryError(
                "Failed to retrieve business profile statistics",
                entity_type="BusinessProfile"
            )
    
    def get_profiles_by_size(
        self,
        min_employees: Optional[int] = None,
        max_employees: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[BusinessProfile]:
        """Get business profiles by company size."""
        try:
            query = self.db.query(BusinessProfile).filter(
                BusinessProfile.is_active == True
            )
            
            if min_employees is not None:
                query = query.filter(BusinessProfile.employee_count >= min_employees)
            
            if max_employees is not None:
                query = query.filter(BusinessProfile.employee_count <= max_employees)
            
            query = self._apply_pagination(query, skip, limit)
            query = query.order_by(BusinessProfile.employee_count.desc())
            
            return query.all()
            
        except Exception as e:
            self.logger.error(f"Error getting profiles by size: {e}")
            raise RepositoryError(
                "Failed to retrieve profiles by size",
                entity_type="BusinessProfile"
            )
    
    def update_profile_settings(
        self,
        profile_id: str,
        settings: Dict[str, Any],
        user_id: str
    ) -> Optional[BusinessProfile]:
        """Update business profile settings."""
        try:
            # Verify ownership
            profile = self.get(profile_id)
            if not profile or profile.user_id != user_id:
                return None
            
            # Merge with existing settings
            current_settings = profile.settings or {}
            current_settings.update(settings)
            
            return self.update(profile_id, {"settings": current_settings})
            
        except Exception as e:
            self.logger.error(f"Error updating profile settings {profile_id}: {e}")
            raise RepositoryError(
                "Failed to update profile settings",
                entity_type="BusinessProfile"
            )
    
    def get_profiles_with_website(
        self,
        skip: int = 0,
        limit: int = 100
    ) -> List[BusinessProfile]:
        """Get business profiles that have a website."""
        try:
            query = self.db.query(BusinessProfile).filter(
                and_(
                    BusinessProfile.is_active == True,
                    BusinessProfile.website.isnot(None),
                    BusinessProfile.website != ""
                )
            )
            
            query = self._apply_pagination(query, skip, limit)
            query = query.order_by(BusinessProfile.created_at.desc())
            
            return query.all()
            
        except Exception as e:
            self.logger.error(f"Error getting profiles with website: {e}")
            raise RepositoryError(
                "Failed to retrieve profiles with website",
                entity_type="BusinessProfile"
            )
    
    def bulk_update_industry(
        self,
        profile_ids: List[str],
        new_industry: str,
        user_id: str
    ) -> int:
        """Bulk update industry for multiple profiles owned by user."""
        try:
            updated_count = self.db.query(BusinessProfile).filter(
                and_(
                    BusinessProfile.id.in_(profile_ids),
                    BusinessProfile.user_id == user_id
                )
            ).update(
                {
                    "industry": new_industry,
                    "updated_at": datetime.now(timezone.utc)
                },
                synchronize_session=False
            )
            
            self.db.commit()
            
            self._log_audit("BULK_UPDATE_INDUSTRY", details={
                "user_id": user_id,
                "profile_ids": profile_ids,
                "new_industry": new_industry,
                "updated_count": updated_count
            })
            
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error bulk updating industry: {e}")
            raise RepositoryError(
                "Failed to bulk update industry",
                entity_type="BusinessProfile"
            )
