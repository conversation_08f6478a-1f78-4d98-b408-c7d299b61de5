"""
Workflow event handler for LangGraph system.

This module handles workflow-related events including workflow start, completion,
and failure events.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from ..event_bus import LangGraphEvent

logger = logging.getLogger(__name__)


class WorkflowEventHandler:
    """Handler for workflow-related events."""
    
    def __init__(self):
        self.processed_events = 0
        self.active_workflows = {}
        self.completed_workflows = {}
        self.failed_workflows = {}
        self.workflow_metrics = {
            "total_started": 0,
            "total_completed": 0,
            "total_failed": 0,
            "average_execution_time": 0.0,
            "success_rate": 0.0
        }
    
    async def handle_workflow_started(self, event: LangGraphEvent):
        """Handle workflow started event."""
        try:
            data = event.data
            workflow_id = data.get("workflow_id")
            workflow_type = data.get("workflow_type")
            user_id = data.get("user_id")
            agents_involved = data.get("agents_involved", [])
            estimated_duration = data.get("estimated_duration", 30.0)
            
            logger.info(f"Processing workflow start for {workflow_id} (type: {workflow_type})")
            
            # Track active workflow
            self.active_workflows[workflow_id] = {
                "workflow_type": workflow_type,
                "user_id": user_id,
                "agents_involved": agents_involved,
                "estimated_duration": estimated_duration,
                "start_time": event.timestamp,
                "status": "running"
            }
            
            # Update metrics
            self.workflow_metrics["total_started"] += 1
            
            self.processed_events += 1
            logger.debug(f"Successfully processed workflow start for {workflow_id}")
            
        except Exception as e:
            logger.error(f"Error handling workflow started event: {e}")
            raise
    
    async def handle_workflow_completed(self, event: LangGraphEvent):
        """Handle workflow completed event."""
        try:
            data = event.data
            workflow_id = data.get("workflow_id")
            execution_time = data.get("execution_time", 0.0)
            success = data.get("success", True)
            results = data.get("results", {})
            
            logger.info(f"Processing workflow completion for {workflow_id} (success: {success})")
            
            # Move from active to completed
            workflow_info = self.active_workflows.pop(workflow_id, {})
            workflow_info.update({
                "execution_time": execution_time,
                "success": success,
                "results": results,
                "completion_time": event.timestamp,
                "status": "completed"
            })
            
            self.completed_workflows[workflow_id] = workflow_info
            
            # Update metrics
            self.workflow_metrics["total_completed"] += 1
            await self._update_execution_time_metrics(execution_time)
            await self._update_success_rate()
            
            # Trigger post-completion analysis
            await self._analyze_workflow_completion(workflow_id, workflow_info)
            
            self.processed_events += 1
            logger.debug(f"Successfully processed workflow completion for {workflow_id}")
            
        except Exception as e:
            logger.error(f"Error handling workflow completed event: {e}")
            raise
    
    async def handle_workflow_failed(self, event: LangGraphEvent):
        """Handle workflow failed event."""
        try:
            data = event.data
            workflow_id = data.get("workflow_id")
            error_message = data.get("error_message")
            error_details = data.get("error_details", {})
            execution_time = data.get("execution_time", 0.0)
            recovery_possible = data.get("recovery_possible", True)
            
            logger.warning(f"Processing workflow failure for {workflow_id}: {error_message}")
            
            # Move from active to failed
            workflow_info = self.active_workflows.pop(workflow_id, {})
            workflow_info.update({
                "error_message": error_message,
                "error_details": error_details,
                "execution_time": execution_time,
                "recovery_possible": recovery_possible,
                "failure_time": event.timestamp,
                "status": "failed"
            })
            
            self.failed_workflows[workflow_id] = workflow_info
            
            # Update metrics
            self.workflow_metrics["total_failed"] += 1
            await self._update_execution_time_metrics(execution_time)
            await self._update_success_rate()
            
            # Trigger failure analysis
            await self._analyze_workflow_failure(workflow_id, workflow_info)
            
            self.processed_events += 1
            logger.debug(f"Successfully processed workflow failure for {workflow_id}")
            
        except Exception as e:
            logger.error(f"Error handling workflow failed event: {e}")
            raise
    
    async def _update_execution_time_metrics(self, execution_time: float):
        """Update average execution time metrics."""
        try:
            total_workflows = (self.workflow_metrics["total_completed"] + 
                             self.workflow_metrics["total_failed"])
            
            if total_workflows > 0:
                current_avg = self.workflow_metrics["average_execution_time"]
                self.workflow_metrics["average_execution_time"] = (
                    (current_avg * (total_workflows - 1) + execution_time) / total_workflows
                )
            
        except Exception as e:
            logger.error(f"Error updating execution time metrics: {e}")
    
    async def _update_success_rate(self):
        """Update success rate metrics."""
        try:
            total_completed = self.workflow_metrics["total_completed"]
            total_failed = self.workflow_metrics["total_failed"]
            total_finished = total_completed + total_failed
            
            if total_finished > 0:
                self.workflow_metrics["success_rate"] = total_completed / total_finished
            
        except Exception as e:
            logger.error(f"Error updating success rate: {e}")
    
    async def _analyze_workflow_completion(self, workflow_id: str, workflow_info: Dict[str, Any]):
        """Analyze completed workflow for insights."""
        try:
            execution_time = workflow_info.get("execution_time", 0.0)
            estimated_duration = workflow_info.get("estimated_duration", 30.0)
            agents_involved = workflow_info.get("agents_involved", [])
            
            # Check if workflow took significantly longer than estimated
            if execution_time > estimated_duration * 1.5:
                logger.warning(f"Workflow {workflow_id} took {execution_time:.2f}s, "
                             f"estimated {estimated_duration:.2f}s")
            
            # Log agent performance
            if agents_involved:
                avg_time_per_agent = execution_time / len(agents_involved)
                logger.debug(f"Workflow {workflow_id} average time per agent: {avg_time_per_agent:.2f}s")
            
        except Exception as e:
            logger.error(f"Error analyzing workflow completion for {workflow_id}: {e}")
    
    async def _analyze_workflow_failure(self, workflow_id: str, workflow_info: Dict[str, Any]):
        """Analyze failed workflow for patterns."""
        try:
            error_message = workflow_info.get("error_message", "")
            error_details = workflow_info.get("error_details", {})
            agents_involved = workflow_info.get("agents_involved", [])
            
            # Log failure patterns
            logger.warning(f"Workflow {workflow_id} failed with agents {agents_involved}")
            logger.debug(f"Error details: {error_details}")
            
            # Could implement failure pattern detection here
            # For example, track which agents are involved in failures
            
        except Exception as e:
            logger.error(f"Error analyzing workflow failure for {workflow_id}: {e}")
    
    def get_active_workflows(self) -> Dict[str, Any]:
        """Get currently active workflows."""
        return self.active_workflows.copy()
    
    def get_workflow_info(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific workflow."""
        # Check active workflows first
        if workflow_id in self.active_workflows:
            return self.active_workflows[workflow_id]
        
        # Check completed workflows
        if workflow_id in self.completed_workflows:
            return self.completed_workflows[workflow_id]
        
        # Check failed workflows
        if workflow_id in self.failed_workflows:
            return self.failed_workflows[workflow_id]
        
        return None
    
    def get_workflow_history(self, user_id: Optional[str] = None, 
                           workflow_type: Optional[str] = None,
                           limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get workflow history with optional filtering."""
        all_workflows = []
        
        # Combine completed and failed workflows
        for workflows in [self.completed_workflows, self.failed_workflows]:
            for wf_id, wf_info in workflows.items():
                wf_data = wf_info.copy()
                wf_data["workflow_id"] = wf_id
                all_workflows.append(wf_data)
        
        # Apply filters
        if user_id:
            all_workflows = [wf for wf in all_workflows if wf.get("user_id") == user_id]
        
        if workflow_type:
            all_workflows = [wf for wf in all_workflows if wf.get("workflow_type") == workflow_type]
        
        # Sort by completion/failure time (most recent first)
        all_workflows.sort(
            key=lambda x: x.get("completion_time") or x.get("failure_time"),
            reverse=True
        )
        
        # Apply limit
        if limit:
            all_workflows = all_workflows[:limit]
        
        return all_workflows
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get workflow handler metrics."""
        return {
            "processed_events": self.processed_events,
            "active_workflows": len(self.active_workflows),
            "completed_workflows": len(self.completed_workflows),
            "failed_workflows": len(self.failed_workflows),
            **self.workflow_metrics
        }
