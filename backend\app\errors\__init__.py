"""
Unified Error Handling Framework for Datagenius.

This package provides a comprehensive error management system that consolidates
all error handling across the application with structured logging, correlation IDs,
and security-aware error sanitization.

Phase 1 Implementation:
- Standard error hierarchy with DatageniusError base class
- ErrorHandlerRegistry for type-specific error handling
- Structured logging with correlation IDs
- Error sanitization for security (removing sensitive data from error messages)
- Integration with FastAPI exception handling
- Error metrics and monitoring
"""

from .base_errors import (
    DatageniusError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    ConflictError,
    RateLimitError,
    ServiceUnavailableError
)

from .agent_errors import (
    AgentError,
    AgentConfigurationError,
    AgentExecutionError,
    AgentTimeoutError,
    AgentResourceError
)

from .workflow_errors import (
    WorkflowError,
    WorkflowValidationError,
    WorkflowExecutionError,
    WorkflowTimeoutError,
    WorkflowStateError
)

from .database_errors import (
    DatabaseError,
    DatabaseConnectionError,
    DatabaseQueryError,
    DatabaseIntegrityError,
    DatabaseTimeoutError
)

from .error_handler_registry import <PERSON><PERSON>r<PERSON><PERSON>lerRegistry, <PERSON>rrorHandler
from .error_sanitizer import <PERSON>rrorSanitizer
from .correlation_context import CorrelationContext, get_correlation_id

__all__ = [
    # Base errors
    "DatageniusError",
    "ValidationError", 
    "AuthenticationError",
    "AuthorizationError",
    "NotFoundError",
    "ConflictError",
    "RateLimitError",
    "ServiceUnavailableError",
    
    # Agent errors
    "AgentError",
    "AgentConfigurationError",
    "AgentExecutionError", 
    "AgentTimeoutError",
    "AgentResourceError",
    
    # Workflow errors
    "WorkflowError",
    "WorkflowValidationError",
    "WorkflowExecutionError",
    "WorkflowTimeoutError",
    "WorkflowStateError",
    
    # Database errors
    "DatabaseError",
    "DatabaseConnectionError",
    "DatabaseQueryError",
    "DatabaseIntegrityError",
    "DatabaseTimeoutError",
    
    # Error handling infrastructure
    "ErrorHandlerRegistry",
    "ErrorHandler",
    "ErrorSanitizer",
    "CorrelationContext",
    "get_correlation_id"
]
