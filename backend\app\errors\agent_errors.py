"""
Agent-specific Error Classes.

Specialized error classes for agent-related operations and failures.
"""

from typing import Optional, Dict, Any
from .base_errors import DatageniusError, ErrorCategory, ErrorSeverity


class AgentError(DatageniusError):
    """Base class for agent-related errors."""
    
    def __init__(
        self,
        message: str,
        agent_id: Optional[str] = None,
        agent_type: Optional[str] = None,
        error_code: str = "AGENT_ERROR",
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        user_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        **kwargs
    ):
        details = details or {}
        if agent_id:
            details["agent_id"] = agent_id
        if agent_type:
            details["agent_type"] = agent_type
        
        super().__init__(
            message=message,
            error_code=error_code,
            category=ErrorCategory.INTERNAL_ERROR,
            severity=severity,
            user_message=user_message or "Agent operation failed",
            details=details,
            cause=cause,
            **kwargs
        )


class AgentConfigurationError(AgentError):
    """Error in agent configuration or setup."""
    
    def __init__(
        self,
        message: str,
        config_field: Optional[str] = None,
        config_value: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if config_field:
            details["config_field"] = config_field
        if config_value:
            details["config_value"] = config_value
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'AGENT_CONFIGURATION_ERROR')
        kwargs.setdefault('category', ErrorCategory.CONFIGURATION)
        kwargs.setdefault('user_message', 'Agent configuration is invalid')
        
        super().__init__(message, **kwargs)


class AgentExecutionError(AgentError):
    """Error during agent execution."""
    
    def __init__(
        self,
        message: str,
        execution_step: Optional[str] = None,
        execution_context: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if execution_step:
            details["execution_step"] = execution_step
        if execution_context:
            details["execution_context"] = execution_context
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'AGENT_EXECUTION_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Agent execution failed')
        
        super().__init__(message, **kwargs)


class AgentTimeoutError(AgentError):
    """Error when agent operation times out."""
    
    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[float] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
        if operation:
            details["operation"] = operation
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'AGENT_TIMEOUT_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Agent operation timed out')
        
        super().__init__(message, **kwargs)


class AgentResourceError(AgentError):
    """Error related to agent resource constraints."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        resource_limit: Optional[str] = None,
        current_usage: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if resource_type:
            details["resource_type"] = resource_type
        if resource_limit:
            details["resource_limit"] = resource_limit
        if current_usage:
            details["current_usage"] = current_usage
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'AGENT_RESOURCE_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Agent resource limit exceeded')
        
        super().__init__(message, **kwargs)


class AgentNotFoundError(AgentError):
    """Error when agent is not found."""
    
    def __init__(
        self,
        message: str,
        agent_id: Optional[str] = None,
        **kwargs
    ):
        kwargs.setdefault('error_code', 'AGENT_NOT_FOUND')
        kwargs.setdefault('category', ErrorCategory.NOT_FOUND)
        kwargs.setdefault('severity', ErrorSeverity.LOW)
        kwargs.setdefault('user_message', 'Agent not found')
        
        super().__init__(message, agent_id=agent_id, **kwargs)


class AgentPermissionError(AgentError):
    """Error when user lacks permission to access agent."""
    
    def __init__(
        self,
        message: str,
        required_permission: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if required_permission:
            details["required_permission"] = required_permission
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'AGENT_PERMISSION_ERROR')
        kwargs.setdefault('category', ErrorCategory.AUTHORIZATION)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Access denied to agent')
        
        super().__init__(message, **kwargs)


class AgentStateError(AgentError):
    """Error when agent is in invalid state for operation."""
    
    def __init__(
        self,
        message: str,
        current_state: Optional[str] = None,
        required_state: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if current_state:
            details["current_state"] = current_state
        if required_state:
            details["required_state"] = required_state
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'AGENT_STATE_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Agent is in invalid state for this operation')
        
        super().__init__(message, **kwargs)


class AgentCommunicationError(AgentError):
    """Error in agent communication or messaging."""
    
    def __init__(
        self,
        message: str,
        communication_type: Optional[str] = None,
        target_agent: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if communication_type:
            details["communication_type"] = communication_type
        if target_agent:
            details["target_agent"] = target_agent
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'AGENT_COMMUNICATION_ERROR')
        kwargs.setdefault('category', ErrorCategory.NETWORK)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Agent communication failed')
        
        super().__init__(message, **kwargs)


class AgentValidationError(AgentError):
    """Error in agent input or parameter validation."""
    
    def __init__(
        self,
        message: str,
        validation_field: Optional[str] = None,
        validation_rule: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if validation_field:
            details["validation_field"] = validation_field
        if validation_rule:
            details["validation_rule"] = validation_rule
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'AGENT_VALIDATION_ERROR')
        kwargs.setdefault('category', ErrorCategory.VALIDATION)
        kwargs.setdefault('severity', ErrorSeverity.LOW)
        kwargs.setdefault('user_message', 'Agent input validation failed')
        
        super().__init__(message, **kwargs)
