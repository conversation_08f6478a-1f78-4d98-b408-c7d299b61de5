# LangGraph Production Environment Configuration
# This file contains LangGraph-specific production overrides
# Inherits from main app production.yaml configuration

# LangGraph Production Settings
langgraph:
  enabled: false  # Start disabled in production, enable via feature flags
  max_workflow_duration: 3600  # 1 hour maximum
  max_agent_transitions: 15  # Stricter limits in production
  max_tool_executions: 30
  rollout_percentage: 0.0  # Start with 0% rollout
  migration_phase: "preparation"

  # Enhanced monitoring for production
  enable_monitoring: true
  monitoring_interval: 30  # More frequent monitoring
  performance_cache_ttl: 600  # Longer cache TTL for performance
  enable_checkpointing: true
  checkpoint_interval: 60  # Less frequent checkpointing in production
  max_checkpoints_per_workflow: 50  # Fewer checkpoints to save space
  enable_auto_recovery: true
  max_recovery_attempts: 2  # Fewer recovery attempts in production
  recovery_timeout: 600  # Longer recovery timeout
  enable_legacy_fallback: true

# LangGraph-specific logging overrides
logging:
  level: "WARNING"  # Less verbose logging in production
  format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
  enable_file_logging: true
  log_file_path: "/var/log/datagenius/langgraph.log"
  max_file_size: 52428800  # 50MB
  backup_count: 10

# Production Agent Settings
agents:
  default_timeout: 300  # 5 minutes
  max_concurrent_agents: 10  # Higher concurrency for production
  enable_agent_metrics: true
  
  # Production-optimized timeouts
  concierge:
    timeout: 60
    max_retries: 3
    enable_caching: true
  
  analysis:
    timeout: 900  # 15 minutes for complex analysis
    max_retries: 2
    enable_caching: true
    cache_ttl: 1800  # 30 minutes
  
  marketing:
    timeout: 300
    max_retries: 2
    enable_content_validation: true
    enable_caching: true
  
  classification:
    timeout: 120
    max_retries: 2
    confidence_threshold: 0.85  # Higher threshold for production

# Tool Configuration for Production
tools:
  default_timeout: 180  # 3 minutes
  max_concurrent_tools: 20  # Higher concurrency
  enable_tool_metrics: true
  
  # MCP Tool Settings for Production
  mcp_tools:
    enable_caching: true
    cache_ttl: 1800  # 30 minutes
    max_cache_size: 5000  # Larger cache
  
  data_access:
    timeout: 600  # 10 minutes for large datasets
    max_results: 5000
    enable_query_optimization: true
    enable_result_caching: true
  
  code_execution:
    timeout: 300  # 5 minutes
    max_memory_mb: 1024  # 1GB
    enable_sandboxing: true
    enable_resource_monitoring: true
  
  visualization:
    timeout: 180
    max_chart_size: "1920x1080"
    supported_formats: ["png", "svg"]  # Limit formats for security
    enable_caching: true

# Workflow Configuration for Production
workflows:
  default_timeout: 3600  # 1 hour
  max_concurrent_workflows: 50  # Higher concurrency
  enable_workflow_metrics: true
  
  simple:
    timeout: 600  # 10 minutes
    max_steps: 5
    enable_caching: true
  
  complex:
    timeout: 3600  # 1 hour
    max_steps: 15  # Stricter limit
    enable_checkpointing: true
    checkpoint_interval: 60  # More frequent checkpoints
  
  collaborative:
    timeout: 7200  # 2 hours
    max_agents: 3  # Limit for production stability
    enable_cross_agent_communication: true

# Security Configuration for Production
security:
  enable_input_validation: true
  enable_output_sanitization: true
  max_input_size: 1048576  # 1MB strict limit
  allowed_file_types: ["txt", "csv", "json", "pdf"]  # Restricted file types
  
  # Strict rate limiting for production
  rate_limiting:
    enabled: true
    requests_per_minute: 30  # Conservative limit
    burst_size: 5
  
  # Additional security measures
  enable_request_logging: true
  enable_ip_filtering: true
  enable_user_agent_validation: true
  max_concurrent_sessions_per_user: 3

# Monitoring and Metrics for Production
monitoring:
  enable_metrics_collection: true
  metrics_retention_days: 90  # Longer retention for production
  enable_performance_alerts: true
  
  # Production alert thresholds
  alerts:
    high_execution_time: 180  # 3 minutes
    high_error_rate: 0.01  # 1%
    low_success_rate: 0.99  # 99%
    high_memory_usage: 2048  # 2GB
    high_cpu_usage: 80  # 80%
  
  # Additional production monitoring
  enable_health_checks: true
  health_check_interval: 30  # seconds
  enable_uptime_monitoring: true
  enable_performance_profiling: false  # Disable for performance

# Feature Flags for Production
feature_flags:
  # Conservative feature enablement for production
  langgraph_enabled: false  # Start disabled
  intelligent_routing: false
  multi_agent_collaboration: false
  state_persistence: false
  performance_monitoring: true  # Always enabled
  legacy_fallback: true  # Always enabled for safety
  
  # Advanced features disabled initially
  advanced_analytics: false
  custom_workflows: false
  external_integrations: false
  
  # Experimental features disabled in production
  ai_optimization: false
  predictive_routing: false
  auto_scaling: false

# Integration Settings for Production
integrations:
  external_apis:
    timeout: 30
    max_retries: 3
    enable_caching: true
    cache_ttl: 3600  # 1 hour
    enable_circuit_breaker: true
  
  business_profiles:
    enable_auto_context: true
    context_refresh_interval: 600  # 10 minutes
    enable_context_caching: true
  
  cross_agent_intelligence:
    enabled: true
    insight_retention_hours: 72  # 3 days
    max_insights_per_profile: 500
    enable_insight_compression: true

# Production-specific Settings
production:
  # Performance optimizations
  enable_performance_optimizations: true
  enable_connection_pooling: true
  enable_query_optimization: true
  enable_response_compression: true
  
  # Reliability settings
  enable_graceful_shutdown: true
  shutdown_timeout: 30  # seconds
  enable_health_checks: true
  
  # Scaling settings
  enable_auto_scaling: false  # Manual scaling initially
  min_instances: 2
  max_instances: 10
  scale_up_threshold: 80  # CPU percentage
  scale_down_threshold: 30
  
  # Backup and recovery
  enable_automated_backups: true
  backup_interval_hours: 6
  backup_retention_days: 30
  enable_point_in_time_recovery: true

# Production Database Settings
prod_database:
  enable_connection_pooling: true
  enable_read_replicas: true
  enable_query_optimization: true
  enable_slow_query_monitoring: true
  slow_query_threshold: 5.0  # 5 seconds
  enable_automated_maintenance: true

# Production Caching Settings
prod_caching:
  enable_distributed_caching: true
  cache_cluster_size: 3
  cache_replication_factor: 2
  enable_cache_monitoring: true
  cache_eviction_policy: "lru"

# Production Error Handling
prod_error_handling:
  enable_detailed_error_responses: false  # Security measure
  enable_stack_traces: false  # Security measure
  enable_error_aggregation: true
  enable_error_alerting: true
  error_alert_threshold: 10  # errors per minute

# Compliance and Auditing
compliance:
  enable_audit_logging: true
  audit_log_retention_days: 365  # 1 year
  enable_data_encryption: true
  enable_pii_detection: true
  enable_gdpr_compliance: true
  enable_access_logging: true
