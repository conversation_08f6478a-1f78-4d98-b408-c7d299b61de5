"""
Comprehensive Integration Test for Marketplace Components.

This test verifies the complete integration between frontend marketplace
and backend LangGraph components, including purchase flow, agent registration,
and cross-agent intelligence.
"""

import asyncio
import pytest
import logging
from datetime import datetime
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestMarketplaceIntegration:
    """Test suite for marketplace integration."""
    
    @pytest.fixture
    async def setup_test_environment(self):
        """Set up test environment."""
        logger.info("Setting up test environment...")
        
        # Initialize test data
        test_data = {
            "user_id": "test_user_123",
            "persona_id": "marketing_specialist",
            "purchase_id": "purchase_456",
            "agent_id": None
        }
        
        yield test_data
        
        # Cleanup
        logger.info("Cleaning up test environment...")
    
    async def test_database_manager_integration(self, setup_test_environment):
        """Test database manager integration."""
        logger.info("Testing database manager integration...")
        
        try:
            from agents.langgraph.core.marketplace_database_manager import marketplace_db_manager
            
            # Test health check
            health_result = await marketplace_db_manager.health_check()
            logger.info(f"Database health check: {health_result}")
            
            assert health_result["database_available"] or health_result["status"] == "unavailable"
            
            # Test persona configuration save/retrieve
            test_config = {
                "name": "Test Marketing Specialist",
                "description": "AI assistant for marketing tasks",
                "capabilities": ["content_creation", "market_analysis"],
                "industry_specialization": "marketing"
            }
            
            if health_result["database_available"]:
                save_result = await marketplace_db_manager.save_persona_configuration(
                    "test_persona", test_config
                )
                logger.info(f"Save persona config result: {save_result}")
                
                if save_result:
                    retrieved_config = await marketplace_db_manager.get_persona_configuration("test_persona")
                    logger.info(f"Retrieved config: {retrieved_config}")
                    assert retrieved_config is not None
            
            logger.info("✓ Database manager integration test passed")
            
        except ImportError as e:
            logger.warning(f"Database manager not available: {e}")
            pytest.skip("Database manager not available")
        except Exception as e:
            logger.error(f"Database manager test failed: {e}")
            raise
    
    async def test_persona_configuration_manager(self, setup_test_environment):
        """Test persona configuration manager."""
        logger.info("Testing persona configuration manager...")
        
        try:
            from agents.langgraph.core.persona_configuration_manager import persona_config_manager
            
            test_data = setup_test_environment
            
            # Test configuration creation
            config_data = {
                "name": "Test Marketing Specialist",
                "description": "AI assistant for marketing tasks",
                "capabilities": ["content_creation", "market_analysis", "campaign_optimization"],
                "skills": ["copywriting", "data_analysis", "strategy"],
                "industry_specialization": "marketing",
                "methodology_framework": "DESIGN_THINKING",
                "specialized_tools": ["content_generation", "social_media_analysis"],
                "compliance_requirements": ["gdpr", "advertising_standards"]
            }
            
            # Test validation
            validation_result = await persona_config_manager.validate_configuration(config_data)
            logger.info(f"Validation result: {validation_result}")
            assert validation_result["is_valid"]
            
            # Test configuration creation
            create_result = await persona_config_manager.create_persona_configuration(
                test_data["persona_id"], config_data
            )
            logger.info(f"Create config result: {create_result}")
            
            # Test configuration retrieval
            retrieved_config = await persona_config_manager.get_persona_configuration(
                test_data["persona_id"]
            )
            logger.info(f"Retrieved config: {retrieved_config is not None}")
            
            logger.info("✓ Persona configuration manager test passed")
            
        except ImportError as e:
            logger.warning(f"Persona configuration manager not available: {e}")
            pytest.skip("Persona configuration manager not available")
        except Exception as e:
            logger.error(f"Persona configuration manager test failed: {e}")
            raise
    
    async def test_capability_marketplace(self, setup_test_environment):
        """Test capability marketplace."""
        logger.info("Testing capability marketplace...")
        
        try:
            from agents.langgraph.marketplace.capability_marketplace import CapabilityMarketplace
            
            capability_marketplace = CapabilityMarketplace()
            test_data = setup_test_environment
            
            # Test capability registration
            test_capabilities = [
                {
                    "name": "content_creation",
                    "type": "generation",
                    "description": "Create marketing content",
                    "pricing_model": "usage",
                    "base_price": 5.0
                },
                {
                    "name": "market_analysis",
                    "type": "analysis",
                    "description": "Analyze market trends",
                    "pricing_model": "fixed",
                    "base_price": 10.0
                }
            ]
            
            registration_result = await capability_marketplace.register_agent_capabilities(
                test_data["user_id"], test_capabilities
            )
            logger.info(f"Capability registration result: {registration_result}")
            
            # Test capability matching
            requirements = {
                "type": "generation",
                "budget": 20.0,
                "performance_requirements": {
                    "min_success_rate": 0.9
                }
            }
            
            matches = await capability_marketplace.find_capability_matches(requirements)
            logger.info(f"Found {len(matches)} capability matches")
            
            logger.info("✓ Capability marketplace test passed")
            
        except ImportError as e:
            logger.warning(f"Capability marketplace not available: {e}")
            pytest.skip("Capability marketplace not available")
        except Exception as e:
            logger.error(f"Capability marketplace test failed: {e}")
            raise
    
    async def test_purchase_to_agent_flow(self, setup_test_environment):
        """Test purchase-to-agent flow."""
        logger.info("Testing purchase-to-agent flow...")
        
        try:
            from agents.langgraph.core.purchase_to_agent_flow import (
                purchase_to_agent_flow_manager,
                PurchaseEvent
            )
            
            test_data = setup_test_environment
            
            # Create purchase event
            purchase_event = PurchaseEvent(
                purchase_id=test_data["purchase_id"],
                user_id=test_data["user_id"],
                persona_id=test_data["persona_id"],
                purchase_timestamp=datetime.utcnow(),
                payment_status="completed",
                metadata={
                    "quantity": 1,
                    "price": 10.0,
                    "payment_method": "credit_card"
                }
            )
            
            # Process purchase event
            registration_result = await purchase_to_agent_flow_manager.process_purchase_event(
                purchase_event
            )
            
            logger.info(f"Registration result: {registration_result}")
            logger.info(f"Success: {registration_result.success}")
            
            if registration_result.success:
                test_data["agent_id"] = registration_result.agent_id
                logger.info(f"Agent registered with ID: {registration_result.agent_id}")
            else:
                logger.warning(f"Registration failed: {registration_result.errors}")
            
            logger.info("✓ Purchase-to-agent flow test passed")
            
        except ImportError as e:
            logger.warning(f"Purchase-to-agent flow manager not available: {e}")
            pytest.skip("Purchase-to-agent flow manager not available")
        except Exception as e:
            logger.error(f"Purchase-to-agent flow test failed: {e}")
            raise
    
    async def test_marketplace_event_system(self, setup_test_environment):
        """Test marketplace event system."""
        logger.info("Testing marketplace event system...")
        
        try:
            from agents.langgraph.core.marketplace_event_system import (
                marketplace_event_system,
                EventType
            )
            
            test_data = setup_test_environment
            
            # Test event publishing
            event_id = await marketplace_event_system.publish_event(
                event_type=EventType.PERSONA_PURCHASED,
                data={
                    "persona_id": test_data["persona_id"],
                    "user_id": test_data["user_id"],
                    "purchase_id": test_data["purchase_id"]
                },
                source="test_suite",
                user_id=test_data["user_id"],
                persona_id=test_data["persona_id"]
            )
            
            logger.info(f"Published event with ID: {event_id}")
            assert event_id is not None
            
            # Test event subscription
            received_events = []
            
            def event_handler(event):
                received_events.append(event)
                logger.info(f"Received event: {event.event_type.value}")
            
            subscription_id = await marketplace_event_system.subscribe(
                event_types=[EventType.AGENT_REGISTERED],
                handler=event_handler
            )
            
            logger.info(f"Created subscription: {subscription_id}")
            
            # Publish another event
            await marketplace_event_system.publish_event(
                event_type=EventType.AGENT_REGISTERED,
                data={
                    "agent_id": "test_agent_123",
                    "persona_id": test_data["persona_id"]
                },
                source="test_suite",
                agent_id="test_agent_123"
            )
            
            # Wait for event processing
            await asyncio.sleep(1)
            
            # Check if event was received
            logger.info(f"Received {len(received_events)} events")
            
            # Get metrics
            metrics = await marketplace_event_system.get_event_metrics()
            logger.info(f"Event system metrics: {metrics}")
            
            logger.info("✓ Marketplace event system test passed")
            
        except ImportError as e:
            logger.warning(f"Marketplace event system not available: {e}")
            pytest.skip("Marketplace event system not available")
        except Exception as e:
            logger.error(f"Marketplace event system test failed: {e}")
            raise
    
    async def test_end_to_end_integration(self, setup_test_environment):
        """Test complete end-to-end integration."""
        logger.info("Testing end-to-end integration...")
        
        test_data = setup_test_environment
        
        try:
            # Test the complete flow from purchase to agent availability
            logger.info("1. Testing persona configuration creation...")
            await self.test_persona_configuration_manager(setup_test_environment)
            
            logger.info("2. Testing purchase-to-agent flow...")
            await self.test_purchase_to_agent_flow(setup_test_environment)
            
            logger.info("3. Testing capability marketplace...")
            await self.test_capability_marketplace(setup_test_environment)
            
            logger.info("4. Testing event system...")
            await self.test_marketplace_event_system(setup_test_environment)
            
            logger.info("✓ End-to-end integration test passed")
            
        except Exception as e:
            logger.error(f"End-to-end integration test failed: {e}")
            raise


async def run_integration_tests():
    """Run all integration tests."""
    logger.info("Starting marketplace integration tests...")
    
    test_suite = TestMarketplaceIntegration()
    
    # Create test environment
    test_data = {
        "user_id": "test_user_123",
        "persona_id": "marketing_specialist",
        "purchase_id": "purchase_456",
        "agent_id": None
    }
    
    try:
        # Run individual tests
        await test_suite.test_database_manager_integration(test_data)
        await test_suite.test_persona_configuration_manager(test_data)
        await test_suite.test_capability_marketplace(test_data)
        await test_suite.test_purchase_to_agent_flow(test_data)
        await test_suite.test_marketplace_event_system(test_data)
        
        # Run end-to-end test
        await test_suite.test_end_to_end_integration(test_data)
        
        logger.info("✓ All marketplace integration tests passed!")
        
    except Exception as e:
        logger.error(f"Integration tests failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(run_integration_tests())
