#!/usr/bin/env python3
"""
Test script to verify agent discovery and initialization fixes.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_template_manager():
    """Test TemplateManager initialization."""
    try:
        logger.info("Testing TemplateManager initialization...")

        # Test if the initialize method exists
        from agents.langgraph.templates.template_manager import TemplateManager

        # Create template manager
        template_manager = TemplateManager(enable_caching=False)  # Disable caching to avoid dependencies

        # Check if initialize method exists
        if hasattr(template_manager, 'initialize'):
            logger.info("✅ TemplateManager has initialize method")
            return True
        else:
            logger.error("❌ TemplateManager missing initialize method")
            return False

    except Exception as e:
        logger.error(f"❌ TemplateManager test failed: {e}")
        return False

async def test_persona_registry():
    """Test PersonaRegistry persona discovery."""
    try:
        logger.info("Testing PersonaRegistry persona discovery...")

        # Test direct file discovery without importing complex dependencies
        from pathlib import Path

        # Get the personas directory path
        current_dir = Path(__file__).parent
        personas_dir = current_dir / "agents" / "langgraph" / "config" / "personas"

        if not personas_dir.exists():
            logger.error(f"❌ Personas directory does not exist: {personas_dir}")
            return False

        # Scan for YAML and JSON files
        yaml_files = list(personas_dir.glob("*.yaml"))
        json_files = list(personas_dir.glob("*.json"))

        total_files = len(yaml_files) + len(json_files)

        logger.info(f"✅ Found {len(yaml_files)} YAML files and {len(json_files)} JSON files")
        logger.info(f"✅ Total persona configuration files: {total_files}")

        # List the files
        for file_path in yaml_files + json_files:
            logger.info(f"  - {file_path.name}")

        return total_files > 0

    except Exception as e:
        logger.error(f"❌ PersonaRegistry test failed: {e}")
        return False

async def test_marketplace_agent_factory():
    """Test MarketplaceAgentFactory agent discovery."""
    try:
        logger.info("Testing MarketplaceAgentFactory agent discovery...")

        # Test YAML loading directly
        import yaml
        from pathlib import Path

        current_dir = Path(__file__).parent
        personas_dir = current_dir / "agents" / "langgraph" / "config" / "personas"

        if not personas_dir.exists():
            logger.error(f"❌ Personas directory does not exist: {personas_dir}")
            return False

        loaded_configs = 0

        # Try to load each YAML file
        for yaml_file in personas_dir.glob("*.yaml"):
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                if config:
                    persona_id = config.get("persona_id") or config.get("id")
                    name = config.get("name", "Unknown")
                    logger.info(f"  ✅ Loaded {yaml_file.name}: {persona_id} - {name}")
                    loaded_configs += 1
                else:
                    logger.warning(f"  ⚠️ Empty config in {yaml_file.name}")

            except Exception as e:
                logger.error(f"  ❌ Failed to load {yaml_file.name}: {e}")

        logger.info(f"✅ Successfully loaded {loaded_configs} persona configurations")
        return loaded_configs > 0

    except Exception as e:
        logger.error(f"❌ MarketplaceAgentFactory test failed: {e}")
        return False

def test_workflow_manager_initialization():
    """Test WorkflowManager agent initialization."""
    try:
        logger.info("Testing WorkflowManager agent initialization...")

        # Test if the workflow manager can be imported and has the right methods
        try:
            from agents.langgraph.core.workflow_manager import WorkflowManager
            logger.info("✅ WorkflowManager can be imported")

            # Check if it has the _initialize_agents method
            if hasattr(WorkflowManager, '_initialize_agents'):
                logger.info("✅ WorkflowManager has _initialize_agents method")
                return True
            else:
                logger.error("❌ WorkflowManager missing _initialize_agents method")
                return False

        except ImportError as e:
            logger.error(f"❌ Cannot import WorkflowManager: {e}")
            return False

    except Exception as e:
        logger.error(f"❌ WorkflowManager test failed: {e}")
        return False

async def main():
    """Run all tests."""
    logger.info("🚀 Starting agent discovery tests...")
    
    results = {}
    
    # Test 1: TemplateManager initialization
    results['template_manager'] = await test_template_manager()
    
    # Test 2: PersonaRegistry discovery
    results['persona_registry'] = await test_persona_registry()
    
    # Test 3: MarketplaceAgentFactory discovery
    results['marketplace_factory'] = await test_marketplace_agent_factory()
    
    # Test 4: WorkflowManager initialization
    results['workflow_manager'] = test_workflow_manager_initialization()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Agent discovery is working correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
