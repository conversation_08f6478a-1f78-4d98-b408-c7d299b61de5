#!/usr/bin/env python3
"""
Test script to validate the marketing form consolidation into business profile workflow.

This script tests:
1. Database schema changes (new marketing fields)
2. Business profile model validation
3. AI field mapping service with marketing fields
4. Document query endpoint using concierge agent configuration
5. Business profile autofill with marketing fields
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.models.business_profile import BusinessProfileResponse, BusinessProfileCreate, BusinessProfileUpdate
from app.services.ai_field_mapping_service import AIFieldMappingService
from app.database import get_db, BusinessProfile as DBBusinessProfile
from sqlalchemy.orm import Session
from sqlalchemy import inspect
from pydantic import ValidationError

async def test_database_schema():
    """Test that the new marketing fields exist in the database schema."""
    print("🔍 Testing database schema...")
    
    try:
        # Get database session
        db = next(get_db())
        
        # Inspect the business_profiles table
        inspector = inspect(db.bind)
        columns = inspector.get_columns('business_profiles')
        column_names = [col['name'] for col in columns]
        
        # Check for new marketing fields
        marketing_fields = ['budget', 'timeline', 'platforms']
        missing_fields = []
        
        for field in marketing_fields:
            if field in column_names:
                print(f"  ✅ Field '{field}' exists in database")
            else:
                missing_fields.append(field)
                print(f"  ❌ Field '{field}' missing from database")
        
        if missing_fields:
            print(f"❌ Database schema test failed. Missing fields: {missing_fields}")
            return False
        else:
            print("✅ Database schema test passed")
            return True
            
    except Exception as e:
        print(f"❌ Database schema test failed with error: {e}")
        return False

def test_pydantic_models():
    """Test that the Pydantic models include the new marketing fields."""
    print("\n🔍 Testing Pydantic models...")
    
    try:
        # Test BusinessProfile model
        test_data = {
            "name": "Test Company",
            "industry": "Technology",
            "description": "A test company",
            "budget": "Monthly budget of $10,000 for digital marketing",
            "timeline": "Launch campaign by Q2 2024",
            "platforms": "Facebook, Instagram, LinkedIn, Email marketing"
        }
        
        # Test BusinessProfileCreate
        create_model = BusinessProfileCreate(**test_data)
        print(f"  ✅ BusinessProfileCreate model validation passed")
        print(f"    - Budget: {create_model.budget}")
        print(f"    - Timeline: {create_model.timeline}")
        print(f"    - Platforms: {create_model.platforms}")
        
        # Test BusinessProfileUpdate
        update_data = {
            "budget": "Updated budget constraints",
            "timeline": "Updated timeline requirements",
            "platforms": "Updated platform preferences"
        }
        update_model = BusinessProfileUpdate(**update_data)
        print(f"  ✅ BusinessProfileUpdate model validation passed")
        
        # Test BusinessProfile (response model)
        full_data = {
            **test_data,
            "id": "test-id",
            "user_id": 123,  # Use integer for user_id
            "is_active": True,
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-01T00:00:00"
        }
        response_model = BusinessProfileResponse(**full_data)
        print(f"  ✅ BusinessProfileResponse model validation passed")
        
        print("✅ Pydantic models test passed")
        return True
        
    except ValidationError as e:
        print(f"❌ Pydantic models test failed with validation error: {e}")
        return False
    except Exception as e:
        print(f"❌ Pydantic models test failed with error: {e}")
        return False

async def test_ai_field_mapping_service():
    """Test that the AI field mapping service includes marketing fields."""
    print("\n🔍 Testing AI field mapping service...")
    
    try:
        service = AIFieldMappingService()
        
        # Check that marketing fields are in the field definitions
        field_definitions = service.field_definitions
        marketing_fields = ['budget', 'timeline', 'platforms']
        
        missing_fields = []
        for field in marketing_fields:
            if field in field_definitions:
                print(f"  ✅ Field '{field}' found in AI field mapping service")
                field_def = field_definitions[field]
                print(f"    - Description: {field_def.get('description', 'N/A')}")
                print(f"    - Keywords: {field_def.get('keywords', [])}")
            else:
                missing_fields.append(field)
                print(f"  ❌ Field '{field}' missing from AI field mapping service")
        
        if missing_fields:
            print(f"❌ AI field mapping service test failed. Missing fields: {missing_fields}")
            return False
        else:
            print("✅ AI field mapping service test passed")
            return True
            
    except Exception as e:
        print(f"❌ AI field mapping service test failed with error: {e}")
        return False

def test_document_query_request():
    """Test that the document query request no longer requires provider/model parameters."""
    print("\n🔍 Testing document query request model...")
    
    try:
        from app.api.document_query import DocumentQueryRequest
        
        # Test that we can create a request without provider/model/temperature
        request_data = {
            "file_id": "test-file-id",
            "query_type": "marketing_fields"
        }
        
        request = DocumentQueryRequest(**request_data)
        print(f"  ✅ DocumentQueryRequest created without provider/model parameters")
        print(f"    - File ID: {request.file_id}")
        print(f"    - Query Type: {request.query_type}")
        
        # Verify that provider/model/temperature fields don't exist
        request_dict = request.model_dump()
        removed_fields = ['provider', 'model', 'temperature']
        
        for field in removed_fields:
            if field not in request_dict:
                print(f"  ✅ Field '{field}' successfully removed from DocumentQueryRequest")
            else:
                print(f"  ❌ Field '{field}' still exists in DocumentQueryRequest")
                return False
        
        print("✅ Document query request test passed")
        return True
        
    except Exception as e:
        print(f"❌ Document query request test failed with error: {e}")
        return False

async def test_business_profile_crud():
    """Test CRUD operations with the new marketing fields."""
    print("\n🔍 Testing business profile CRUD operations...")
    
    try:
        # Get database session
        db = next(get_db())
        
        # Create a test business profile with marketing fields
        import uuid
        test_profile = DBBusinessProfile(
            id=str(uuid.uuid4()),  # Generate UUID for id field
            name="Test Marketing Company",
            industry="Digital Marketing",
            description="A company for testing marketing field consolidation",
            user_id=123,  # Use integer for user_id
            budget="$50,000 annual marketing budget with focus on digital channels",
            timeline="6-month campaign timeline with quarterly reviews",
            platforms="Facebook Ads, Google Ads, LinkedIn, Email marketing, SEO",
            competitive_landscape="Competing with established agencies in the digital space",
            is_active=True
        )
        
        # Add to database
        db.add(test_profile)
        db.commit()
        db.refresh(test_profile)
        
        print(f"  ✅ Created business profile with ID: {test_profile.id}")
        print(f"    - Budget: {test_profile.budget}")
        print(f"    - Timeline: {test_profile.timeline}")
        print(f"    - Platforms: {test_profile.platforms}")
        
        # Update the marketing fields
        test_profile.budget = "Updated budget: $75,000 with increased digital focus"
        test_profile.timeline = "Updated timeline: 8-month campaign with monthly reviews"
        test_profile.platforms = "Updated platforms: TikTok, Instagram, YouTube added"
        
        db.commit()
        db.refresh(test_profile)
        
        print(f"  ✅ Updated business profile marketing fields")
        print(f"    - New Budget: {test_profile.budget}")
        print(f"    - New Timeline: {test_profile.timeline}")
        print(f"    - New Platforms: {test_profile.platforms}")
        
        # Clean up
        db.delete(test_profile)
        db.commit()
        
        print("  ✅ Cleaned up test data")
        print("✅ Business profile CRUD test passed")
        return True
        
    except Exception as e:
        print(f"❌ Business profile CRUD test failed with error: {e}")
        return False

async def run_all_tests():
    """Run all consolidation tests."""
    print("🚀 Starting Marketing Form Consolidation Tests")
    print("=" * 60)
    
    test_results = []
    
    # Run tests
    test_results.append(await test_database_schema())
    test_results.append(test_pydantic_models())
    test_results.append(await test_ai_field_mapping_service())
    test_results.append(test_document_query_request())
    test_results.append(await test_business_profile_crud())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    if passed_tests == total_tests:
        print(f"🎉 All tests passed! ({passed_tests}/{total_tests})")
        print("\n✅ Marketing form consolidation is working correctly!")
        print("✅ New marketing fields are properly integrated into business profiles")
        print("✅ AI provider configuration now uses concierge agent settings")
        print("✅ Database schema has been successfully updated")
        return True
    else:
        print(f"❌ Some tests failed. ({passed_tests}/{total_tests} passed)")
        print("\n⚠️  Please review the failed tests above and fix any issues.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
