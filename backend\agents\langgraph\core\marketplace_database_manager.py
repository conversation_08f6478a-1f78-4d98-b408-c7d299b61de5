"""
Marketplace Database Manager for LangGraph Integration.

This module provides database management and session handling for marketplace
components, ensuring proper connection management and transaction handling.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, AsyncContextManager
from contextlib import asynccontextmanager
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime

logger = logging.getLogger(__name__)

# Import database components
try:
    from ....app.database import SessionLocal, get_db
    from ....app.models.enhanced_marketplace import (
        PersonaConfiguration,
        AgentPlugin,
        WorkflowAdaptation,
        MessageThread
    )
    from ....app.services.enhanced_marketplace_service import (
        persona_config_service,
        agent_plugin_service,
        workflow_adaptation_service,
        hierarchical_message_service
    )
    DATABASE_AVAILABLE = True
except ImportError as e:
    DATABASE_AVAILABLE = False
    logger.warning(f"Database components not available: {e}")


class MarketplaceDatabaseManager:
    """
    Database manager for marketplace operations with proper session handling,
    connection pooling, and transaction management.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._session_factory = SessionLocal if DATABASE_AVAILABLE else None
        self._connection_pool_size = 10
        self._max_overflow = 20
        self._pool_timeout = 30
        
    @asynccontextmanager
    async def get_session(self) -> AsyncContextManager[Session]:
        """
        Get database session with proper error handling and cleanup.
        
        Yields:
            Database session
        """
        if not DATABASE_AVAILABLE:
            raise RuntimeError("Database not available")
            
        session = self._session_factory()
        try:
            yield session
            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            self.logger.error(f"Database error: {e}")
            raise
        except Exception as e:
            session.rollback()
            self.logger.error(f"Unexpected error: {e}")
            raise
        finally:
            session.close()
    
    async def save_persona_configuration(
        self,
        persona_id: str,
        configuration: Dict[str, Any],
        industry_specialization: Optional[str] = None,
        methodology_framework: Optional[str] = None,
        **kwargs
    ) -> bool:
        """
        Save persona configuration to database.
        
        Args:
            persona_id: Persona identifier
            configuration: Persona configuration data
            industry_specialization: Industry specialization
            methodology_framework: Methodology framework
            **kwargs: Additional configuration parameters
            
        Returns:
            True if saved successfully
        """
        if not DATABASE_AVAILABLE:
            self.logger.warning("Database not available, skipping persona configuration save")
            return False
            
        try:
            async with self.get_session() as session:
                # Check if configuration already exists
                existing_config = session.query(PersonaConfiguration).filter(
                    PersonaConfiguration.persona_id == persona_id
                ).first()
                
                if existing_config:
                    # Update existing configuration
                    existing_config.configuration = configuration
                    existing_config.industry_specialization = industry_specialization
                    existing_config.methodology_framework = methodology_framework
                    existing_config.updated_at = datetime.utcnow()
                    
                    # Update additional fields
                    for key, value in kwargs.items():
                        if hasattr(existing_config, key):
                            setattr(existing_config, key, value)
                    
                    self.logger.info(f"Updated persona configuration for {persona_id}")
                else:
                    # Create new configuration
                    config = PersonaConfiguration(
                        persona_id=persona_id,
                        configuration=configuration,
                        industry_specialization=industry_specialization,
                        methodology_framework=methodology_framework,
                        **kwargs
                    )
                    session.add(config)
                    self.logger.info(f"Created persona configuration for {persona_id}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving persona configuration for {persona_id}: {e}")
            return False
    
    async def get_persona_configuration(
        self,
        persona_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get persona configuration from database.
        
        Args:
            persona_id: Persona identifier
            
        Returns:
            Persona configuration or None if not found
        """
        if not DATABASE_AVAILABLE:
            self.logger.warning("Database not available, returning None for persona configuration")
            return None
            
        try:
            async with self.get_session() as session:
                config = session.query(PersonaConfiguration).filter(
                    PersonaConfiguration.persona_id == persona_id
                ).first()
                
                if config:
                    return {
                        "persona_id": config.persona_id,
                        "configuration": config.configuration,
                        "industry_specialization": config.industry_specialization,
                        "methodology_framework": config.methodology_framework,
                        "enable_cross_agent_intelligence": config.enable_cross_agent_intelligence,
                        "specialized_tools": config.specialized_tools,
                        "compliance_requirements": config.compliance_requirements,
                        "workflow_patterns": config.workflow_patterns,
                        "performance_optimization": config.performance_optimization,
                        "created_at": config.created_at,
                        "updated_at": config.updated_at
                    }
                
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting persona configuration for {persona_id}: {e}")
            return None
    
    async def register_agent_plugin(
        self,
        plugin_id: str,
        name: str,
        version: str,
        plugin_package: Dict[str, Any],
        author: Optional[str] = None,
        description: Optional[str] = None,
        **kwargs
    ) -> bool:
        """
        Register agent plugin in marketplace.
        
        Args:
            plugin_id: Plugin identifier
            name: Plugin name
            version: Plugin version
            plugin_package: Plugin package data
            author: Plugin author
            description: Plugin description
            **kwargs: Additional plugin parameters
            
        Returns:
            True if registered successfully
        """
        if not DATABASE_AVAILABLE:
            self.logger.warning("Database not available, skipping plugin registration")
            return False
            
        try:
            async with self.get_session() as session:
                # Check if plugin already exists
                existing_plugin = session.query(AgentPlugin).filter(
                    AgentPlugin.plugin_id == plugin_id
                ).first()
                
                if existing_plugin:
                    # Update existing plugin
                    existing_plugin.name = name
                    existing_plugin.version = version
                    existing_plugin.plugin_package = plugin_package
                    existing_plugin.author = author
                    existing_plugin.description = description
                    existing_plugin.updated_at = datetime.utcnow()
                    
                    # Update additional fields
                    for key, value in kwargs.items():
                        if hasattr(existing_plugin, key):
                            setattr(existing_plugin, key, value)
                    
                    self.logger.info(f"Updated agent plugin {plugin_id}")
                else:
                    # Create new plugin
                    plugin = AgentPlugin(
                        plugin_id=plugin_id,
                        name=name,
                        version=version,
                        plugin_package=plugin_package,
                        author=author,
                        description=description,
                        **kwargs
                    )
                    session.add(plugin)
                    self.logger.info(f"Registered agent plugin {plugin_id}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error registering agent plugin {plugin_id}: {e}")
            return False
    
    async def get_user_purchased_personas(self, user_id: str) -> List[str]:
        """
        Get list of personas purchased by user.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of purchased persona IDs
        """
        if not DATABASE_AVAILABLE:
            self.logger.warning("Database not available, returning empty list for purchased personas")
            return []
            
        try:
            async with self.get_session() as session:
                # Query purchased items for user
                from ....app.database import PurchasedItem, Purchase
                
                purchased_personas = session.query(PurchasedItem.persona_id).join(
                    Purchase, PurchasedItem.purchase_id == Purchase.id
                ).filter(
                    Purchase.user_id == int(user_id),
                    Purchase.payment_status == "completed"
                ).distinct().all()
                
                return [persona[0] for persona in purchased_personas]
                
        except Exception as e:
            self.logger.error(f"Error getting purchased personas for user {user_id}: {e}")
            return []
    
    async def check_persona_access(self, user_id: str, persona_id: str) -> bool:
        """
        Check if user has access to persona.
        
        Args:
            user_id: User identifier
            persona_id: Persona identifier
            
        Returns:
            True if user has access
        """
        purchased_personas = await self.get_user_purchased_personas(user_id)
        return persona_id in purchased_personas
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform database health check.
        
        Returns:
            Health check results
        """
        if not DATABASE_AVAILABLE:
            return {
                "status": "unavailable",
                "database_available": False,
                "error": "Database components not imported"
            }
        
        try:
            async with self.get_session() as session:
                # Simple query to test connection
                session.execute("SELECT 1")
                
                return {
                    "status": "healthy",
                    "database_available": True,
                    "connection_pool_size": self._connection_pool_size,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "database_available": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }


# Create global instance
marketplace_db_manager = MarketplaceDatabaseManager()
