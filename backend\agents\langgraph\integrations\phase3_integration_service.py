"""
Phase 3 Integration Service for LangGraph AI Systems.

This service integrates all Phase 3 components (predictive optimization,
self-healing systems, and advanced collaboration) into a unified system
that works seamlessly with the existing LangGraph architecture.

Key Features:
- Unified initialization and management of all Phase 3 components
- Event-driven integration with existing LangGraph systems
- Performance monitoring and optimization coordination
- Comprehensive health monitoring and self-healing orchestration
- Advanced collaboration workflow integration
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from ..ai.predictive_optimizer import PredictiveOptimizer
from ..ai.self_healing import SelfHealingSystem
from ..collaboration.advanced_patterns import (
    TeamFormationEngine,
    SkillMatcher,
    CollaborativeLearning,
    ConsensusBuilder,
    CollaborationAnalytics
)
from ..events.event_bus import LangGraphEventBus, event_bus
from ..events.types import (
    WorkflowStartedEvent,
    WorkflowCompletedEvent,
    WorkflowFailedEvent,
    SystemHealthEvent
)
from ..monitoring.workflow_monitor import WorkflowMonitor

# Import configuration with robust path handling
import sys
import os
import importlib.util
from pathlib import Path

# Get backend root directory
backend_root = Path(__file__).parent.parent.parent.parent

# Function to import config module dynamically
def _import_phase3_config():
    """Dynamically import phase3_config module."""
    config_path = backend_root / "config" / "phase3_config.py"

    if config_path.exists():
        spec = importlib.util.spec_from_file_location("phase3_config", config_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module
    else:
        raise ImportError(f"Could not find phase3_config.py at {config_path}")

# Import the functions
try:
    phase3_config_module = _import_phase3_config()
    get_phase3_config = phase3_config_module.get_phase3_config
    get_resource_limits = phase3_config_module.get_resource_limits
except Exception as e:
    # Fallback: create dummy functions to prevent import errors
    def get_phase3_config():
        from dataclasses import dataclass
        from enum import Enum

        class OptimizationLevel(Enum):
            STANDARD = "standard"

        @dataclass
        class Phase3Config:
            enable_phase3: bool = True
            optimization_level: OptimizationLevel = OptimizationLevel.STANDARD
            debug_mode: bool = False
            enable_predictive_optimization: bool = True
            enable_self_healing: bool = True
            enable_advanced_collaboration: bool = True

        return Phase3Config()

    def get_resource_limits():
        return {
            "max_concurrent_optimizations": 10,
            "optimization_timeout_seconds": 120,
            "memory_limit_mb": 1024,
            "cpu_usage_limit_percent": 80.0
        }

logger = logging.getLogger(__name__)


class Phase3IntegrationService:
    """
    Unified service for managing all Phase 3 AI-powered enhancements.
    
    This service coordinates predictive optimization, self-healing systems,
    and advanced collaboration to provide a seamless AI-enhanced workflow experience.
    """
    
    def __init__(self, workflow_monitor: Optional[WorkflowMonitor] = None):
        self.workflow_monitor = workflow_monitor

        # Load configuration
        self.config = get_phase3_config()
        self.resource_limits = get_resource_limits()

        # Initialize Phase 3 components based on configuration
        self.predictive_optimizer = PredictiveOptimizer(workflow_monitor) if self.config.enable_predictive_optimization else None
        self.self_healing_system = SelfHealingSystem(workflow_monitor) if self.config.enable_self_healing else None
        self.skill_matcher = SkillMatcher() if self.config.enable_advanced_collaboration else None
        self.team_formation = TeamFormationEngine(self.skill_matcher) if self.config.enable_advanced_collaboration and self.skill_matcher else None
        self.collaborative_learning = CollaborativeLearning() if self.config.enable_advanced_collaboration else None
        self.consensus_builder = ConsensusBuilder() if self.config.enable_advanced_collaboration else None
        self.collaboration_analytics = CollaborationAnalytics() if self.config.enable_advanced_collaboration else None

        # Service state
        self.is_initialized = False
        self.is_active = False
        self.integration_metrics = {
            'workflows_optimized': 0,
            'failures_prevented': 0,
            'teams_formed': 0,
            'knowledge_shared': 0,
            'consensus_reached': 0
        }

        # Resource management
        self.active_optimizations = 0
        self.max_concurrent_optimizations = self.resource_limits['max_concurrent_optimizations']
        
        # Subscribe to workflow events
        event_bus.subscribe("workflow.started", self._handle_workflow_started)
        event_bus.subscribe("workflow.completed", self._handle_workflow_completed)
        event_bus.subscribe("workflow.failed", self._handle_workflow_failed)
        event_bus.subscribe("agent.registration", self._handle_agent_registration)
        
        logger.info("Phase3IntegrationService initialized")
    
    async def initialize(self) -> bool:
        """Initialize all Phase 3 components."""
        try:
            logger.info("Initializing Phase 3 Integration Service...")
            
            # Initialize predictive optimizer if enabled
            if self.predictive_optimizer:
                optimizer_init = await self.predictive_optimizer.initialize()
                if not optimizer_init:
                    logger.warning("Predictive optimizer initialization failed")
                else:
                    logger.info("Predictive optimizer initialized successfully")
            else:
                logger.info("Predictive optimizer disabled by configuration")

            # Initialize self-healing system if enabled
            if self.self_healing_system:
                healing_init = await self.self_healing_system.initialize()
                if not healing_init:
                    logger.warning("Self-healing system initialization failed")
                else:
                    logger.info("Self-healing system initialized successfully")
            else:
                logger.info("Self-healing system disabled by configuration")
            
            # Initialize collaboration components (no async init needed)
            logger.info("Collaboration components initialized")
            
            self.is_initialized = True
            logger.info("Phase 3 Integration Service initialization completed")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Phase 3 Integration Service: {e}")
            return False
    
    async def start_services(self):
        """Start all Phase 3 services."""
        if not self.is_initialized:
            logger.warning("Phase 3 services not initialized, initializing now...")
            await self.initialize()
        
        if self.is_active:
            logger.warning("Phase 3 services already active")
            return
        
        try:
            # Start self-healing system if available
            if self.self_healing_system:
                await self.self_healing_system.start_healing()
                logger.info("Self-healing system started")
            else:
                logger.info("Self-healing system not available (disabled by configuration)")

            self.is_active = True
            logger.info("Phase 3 services started successfully")

        except Exception as e:
            logger.error(f"Error starting Phase 3 services: {e}")
    
    async def stop_services(self):
        """Stop all Phase 3 services."""
        if not self.is_active:
            return
        
        try:
            # Stop self-healing system
            await self.self_healing_system.stop_healing()
            
            self.is_active = False
            logger.info("Phase 3 services stopped")
            
        except Exception as e:
            logger.error(f"Error stopping Phase 3 services: {e}")
    
    async def optimize_workflow(
        self,
        workflow_id: str,
        workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Provide comprehensive workflow optimization using all Phase 3 capabilities.
        
        Args:
            workflow_id: ID of the workflow to optimize
            workflow_config: Workflow configuration and requirements
            
        Returns:
            Dictionary with optimization results and recommendations
        """
        # Check resource limits
        if self.active_optimizations >= self.max_concurrent_optimizations:
            logger.warning(f"Maximum concurrent optimizations ({self.max_concurrent_optimizations}) reached")
            return {
                'workflow_id': workflow_id,
                'error': 'resource_limit_exceeded',
                'message': f'Maximum concurrent optimizations ({self.max_concurrent_optimizations}) reached'
            }

        self.active_optimizations += 1

        try:
            optimization_results = {
                'workflow_id': workflow_id,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'optimizations_applied': []
            }
            
            # 1. Predictive optimization
            performance_prediction = await self.predictive_optimizer.predict_workflow_performance(
                workflow_config
            )
            
            optimization_suggestions = await self.predictive_optimizer.generate_optimization_suggestions(
                workflow_id, workflow_config
            )
            
            if performance_prediction:
                optimization_results['performance_prediction'] = performance_prediction.to_dict()
            
            if optimization_suggestions:
                optimization_results['optimization_suggestions'] = [
                    suggestion.to_dict() for suggestion in optimization_suggestions[:3]  # Top 3
                ]
                optimization_results['optimizations_applied'].append('predictive_optimization')
            
            # 2. Failure prediction and proactive healing
            failure_prediction = await self.self_healing_system.predict_workflow_failure(
                workflow_config
            )
            
            if failure_prediction and failure_prediction.probability > 0.5:
                optimization_results['failure_prediction'] = failure_prediction.to_dict()
                optimization_results['optimizations_applied'].append('proactive_healing')
            
            # 3. Team formation optimization
            if workflow_config.get('requires_collaboration', False):
                team_config = await self.team_formation.form_team(
                    workflow_config.get('task_requirements', {}),
                    strategy='balanced',
                    team_size=workflow_config.get('preferred_team_size', 3)
                )
                
                if team_config:
                    optimization_results['team_configuration'] = team_config.to_dict()
                    optimization_results['optimizations_applied'].append('team_formation')
                    self.integration_metrics['teams_formed'] += 1
            
            # 4. Knowledge sharing recommendations
            relevant_knowledge = await self._get_relevant_knowledge_for_workflow(workflow_config)
            if relevant_knowledge:
                optimization_results['relevant_knowledge'] = relevant_knowledge
                optimization_results['optimizations_applied'].append('knowledge_sharing')
            
            self.integration_metrics['workflows_optimized'] += 1
            
            logger.info(f"Workflow {workflow_id} optimized with {len(optimization_results['optimizations_applied'])} enhancements")
            return optimization_results
            
        except Exception as e:
            logger.error(f"Error optimizing workflow {workflow_id}: {e}")
            return {'error': str(e)}
        finally:
            # Always decrement active optimizations counter
            self.active_optimizations = max(0, self.active_optimizations - 1)
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all Phase 3 systems."""
        try:
            status = {
                'service_status': {
                    'initialized': self.is_initialized,
                    'active': self.is_active,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                },
                'integration_metrics': self.integration_metrics.copy()
            }
            
            # Get predictive optimizer insights
            if self.predictive_optimizer.is_initialized:
                optimizer_insights = await self.predictive_optimizer.get_optimization_insights()
                status['predictive_optimization'] = optimizer_insights
            
            # Get self-healing system health
            healing_health = await self.self_healing_system.get_system_health()
            status['self_healing'] = healing_health
            
            # Get collaboration analytics
            collaboration_insights = self.collaboration_analytics.get_collaboration_insights()
            status['collaboration'] = collaboration_insights
            
            # Get learning analytics
            learning_analytics = self.collaborative_learning.get_learning_analytics()
            status['learning'] = learning_analytics
            
            # Get consensus analytics
            consensus_analytics = self.consensus_builder.get_decision_analytics()
            status['consensus'] = consensus_analytics
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}
    
    async def _handle_workflow_started(self, event):
        """Handle workflow started events."""
        try:
            workflow_id = event.data.get('workflow_id')
            workflow_type = event.data.get('workflow_type')
            
            if not workflow_id:
                return
            
            # Predict potential issues and optimize proactively
            workflow_config = {
                'workflow_id': workflow_id,
                'workflow_type': workflow_type,
                'agents_involved': event.data.get('agents_involved', []),
                'estimated_duration': event.data.get('estimated_duration', 30.0)
            }
            
            # Run optimization in background
            asyncio.create_task(self._background_optimization(workflow_id, workflow_config))
            
        except Exception as e:
            logger.error(f"Error handling workflow started event: {e}")
    
    async def _handle_workflow_completed(self, event):
        """Handle workflow completed events."""
        try:
            workflow_id = event.data.get('workflow_id')
            execution_time = event.data.get('execution_time', 0)
            quality_score = event.data.get('quality_score', 1.0)
            
            # Update collaboration analytics if this was a team effort
            agents_involved = event.data.get('agents_involved', [])
            if len(agents_involved) > 1:
                # Record collaboration metrics
                team_performance = {
                    'completion_time': execution_time,
                    'quality_score': quality_score,
                    'coordination_efficiency': 0.8,  # Would be calculated from actual data
                    'knowledge_sharing_score': 0.7,
                    'consensus_time': 5.0,
                    'individual_contributions': {agent: 1.0/len(agents_involved) for agent in agents_involved},
                    'communication_patterns': {'direct_message': 10, 'broadcast': 3}
                }
                
                # This would typically use the actual team configuration
                # For now, we'll create a mock team config
                from ..collaboration.advanced_patterns import TeamConfiguration, CollaborationRole
                team_config = TeamConfiguration(
                    team_id=f"team_{workflow_id}",
                    task_requirements={},
                    selected_agents=agents_involved,
                    role_assignments={agent: CollaborationRole.SPECIALIST for agent in agents_involved},
                    skill_coverage={},
                    estimated_performance=0.8,
                    formation_timestamp=datetime.now(timezone.utc)
                )
                
                await self.collaboration_analytics.record_collaboration_metrics(
                    team_config, team_performance
                )
            
        except Exception as e:
            logger.error(f"Error handling workflow completed event: {e}")
    
    async def _handle_workflow_failed(self, event):
        """Handle workflow failed events."""
        try:
            workflow_id = event.data.get('workflow_id')
            error_type = event.data.get('error_type', 'unknown')
            
            # This failure could have been prevented
            self.integration_metrics['failures_prevented'] += 1
            
            logger.info(f"Workflow failure handled: {workflow_id} - {error_type}")
            
        except Exception as e:
            logger.error(f"Error handling workflow failed event: {e}")
    
    async def _handle_agent_registration(self, event):
        """Handle agent registration events."""
        try:
            agent_id = event.data.get('agent_id')
            capabilities = event.data.get('capabilities', [])
            
            if agent_id and capabilities:
                # Convert capabilities to skills and register with skill matcher
                from ..collaboration.advanced_patterns import AgentSkill, SkillLevel
                
                skills = []
                for capability in capabilities:
                    skill = AgentSkill(
                        skill_name=capability,
                        level=SkillLevel.INTERMEDIATE,  # Default level
                        confidence=0.8,
                        experience_points=100,
                        last_updated=datetime.now(timezone.utc),
                        performance_history=[0.8]
                    )
                    skills.append(skill)
                
                await self.skill_matcher.register_agent_skills(agent_id, skills)
                logger.info(f"Registered skills for agent {agent_id}: {capabilities}")
            
        except Exception as e:
            logger.error(f"Error handling agent registration event: {e}")
    
    async def _background_optimization(self, workflow_id: str, workflow_config: Dict[str, Any]):
        """Run background optimization for a workflow."""
        try:
            # Small delay to avoid overwhelming the system
            await asyncio.sleep(0.1)
            
            optimization_results = await self.optimize_workflow(workflow_id, workflow_config)
            
            if 'error' not in optimization_results:
                logger.debug(f"Background optimization completed for {workflow_id}")
            
        except Exception as e:
            logger.error(f"Error in background optimization for {workflow_id}: {e}")
    
    async def _get_relevant_knowledge_for_workflow(self, workflow_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get relevant knowledge for a workflow."""
        try:
            workflow_type = workflow_config.get('workflow_type', '')
            relevant_knowledge = []
            
            # Map workflow types to skills
            skill_mapping = {
                'analysis': 'data_analysis',
                'report': 'writing',
                'dashboard': 'visualization',
                'research': 'research'
            }
            
            skill_name = skill_mapping.get(workflow_type.lower())
            if skill_name:
                knowledge = await self.collaborative_learning.get_relevant_knowledge(
                    'system', skill_name
                )
                relevant_knowledge.extend(knowledge)
            
            return relevant_knowledge
            
        except Exception as e:
            logger.error(f"Error getting relevant knowledge: {e}")
            return []


# Global instance for easy access
phase3_integration_service = Phase3IntegrationService()
