"""
Unified Real-time Service for LangGraph and Dashboard Integration.

This module provides a unified real-time service that handles both dashboard updates
and LangGraph workflow state changes through WebSocket connections and event streaming.
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Optional, Callable, Set
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import weakref

from ..events.event_bus import event_bus, LangGraphEvent, EventPriority
from ..integrations.dashboard_bridge import WorkflowResult, WidgetUpdateType

logger = logging.getLogger(__name__)


class UpdateType(Enum):
    """Types of real-time updates."""
    WORKFLOW_RESULT = "workflow_result"
    WORKFLOW_ERROR = "workflow_error"
    WORKFLOW_STATUS = "workflow_status"
    DASHBOARD_UPDATE = "dashboard_update"
    WIDGET_UPDATE = "widget_update"
    BUSINESS_PROFILE_UPDATE = "business_profile_update"
    AGENT_STATUS = "agent_status"
    SYSTEM_HEALTH = "system_health"


@dataclass
class RealtimeSubscription:
    """Real-time subscription configuration."""
    subscription_id: str
    client_id: str
    update_types: Set[UpdateType]
    filters: Dict[str, Any] = field(default_factory=dict)
    callback: Optional[Callable] = None
    websocket_connection: Optional[Any] = None
    last_activity: datetime = field(default_factory=datetime.now)
    is_active: bool = True


@dataclass
class RealtimeUpdate:
    """Real-time update message."""
    update_type: UpdateType
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    target_filters: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1  # 1=low, 2=medium, 3=high


class UnifiedRealtimeService:
    """
    Unified real-time service for LangGraph workflows and dashboard updates.
    
    This service provides a centralized hub for all real-time updates in the system,
    supporting WebSocket connections, event streaming, and intelligent routing.
    """
    
    def __init__(self):
        self.subscriptions: Dict[str, RealtimeSubscription] = {}
        self.client_connections: Dict[str, Set[str]] = {}  # client_id -> subscription_ids
        self.update_queue: asyncio.Queue = asyncio.Queue()
        self.is_running = False
        self.processing_task: Optional[asyncio.Task] = None
        
        # Metrics
        self.metrics = {
            "total_subscriptions": 0,
            "active_connections": 0,
            "updates_sent": 0,
            "updates_failed": 0,
            "average_latency": 0.0
        }
        
        # Setup event handlers
        self._setup_event_handlers()
        
        logger.info("Unified Real-time Service initialized")
    
    async def start(self):
        """Start the real-time service."""
        if self.is_running:
            return
        
        self.is_running = True
        self.processing_task = asyncio.create_task(self._process_updates())
        
        logger.info("Unified Real-time Service started")
    
    async def stop(self):
        """Stop the real-time service."""
        self.is_running = False
        
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        
        # Close all connections
        await self._close_all_connections()
        
        logger.info("Unified Real-time Service stopped")
    
    async def subscribe(
        self,
        client_id: str,
        update_types: List[UpdateType],
        filters: Dict[str, Any] = None,
        websocket_connection: Any = None,
        callback: Callable = None
    ) -> str:
        """
        Subscribe to real-time updates.
        
        Args:
            client_id: Unique client identifier
            update_types: Types of updates to subscribe to
            filters: Optional filters for updates
            websocket_connection: WebSocket connection for the client
            callback: Optional callback function for updates
            
        Returns:
            Subscription ID
        """
        try:
            subscription_id = f"{client_id}_{datetime.now().timestamp()}"
            
            subscription = RealtimeSubscription(
                subscription_id=subscription_id,
                client_id=client_id,
                update_types=set(update_types),
                filters=filters or {},
                callback=callback,
                websocket_connection=websocket_connection,
                last_activity=datetime.now(),
                is_active=True
            )
            
            # Store subscription
            self.subscriptions[subscription_id] = subscription
            
            # Track client connections
            if client_id not in self.client_connections:
                self.client_connections[client_id] = set()
            self.client_connections[client_id].add(subscription_id)
            
            # Update metrics
            self.metrics["total_subscriptions"] += 1
            self.metrics["active_connections"] = len(self.client_connections)
            
            logger.info(f"Client {client_id} subscribed to {len(update_types)} update types")
            return subscription_id
            
        except Exception as e:
            logger.error(f"Error creating subscription for client {client_id}: {e}")
            raise
    
    async def unsubscribe(self, subscription_id: str):
        """Unsubscribe from real-time updates."""
        try:
            subscription = self.subscriptions.get(subscription_id)
            if not subscription:
                return
            
            client_id = subscription.client_id
            
            # Remove subscription
            del self.subscriptions[subscription_id]
            
            # Update client connections
            if client_id in self.client_connections:
                self.client_connections[client_id].discard(subscription_id)
                if not self.client_connections[client_id]:
                    del self.client_connections[client_id]
            
            # Close WebSocket if present
            if subscription.websocket_connection:
                try:
                    await subscription.websocket_connection.close()
                except:
                    pass
            
            # Update metrics
            self.metrics["active_connections"] = len(self.client_connections)
            
            logger.info(f"Unsubscribed {subscription_id}")
            
        except Exception as e:
            logger.error(f"Error unsubscribing {subscription_id}: {e}")
    
    async def publish_update(self, update: RealtimeUpdate):
        """Publish a real-time update."""
        try:
            await self.update_queue.put(update)
            logger.debug(f"Published {update.update_type.value} update from {update.source}")
            
        except Exception as e:
            logger.error(f"Error publishing update: {e}")
    
    async def publish_workflow_result(
        self,
        widget_id: str,
        workflow_result: WorkflowResult,
        dashboard_id: str = None
    ):
        """Publish workflow result update."""
        update = RealtimeUpdate(
            update_type=UpdateType.WORKFLOW_RESULT,
            timestamp=datetime.now(),
            source="workflow_engine",
            data={
                "widget_id": widget_id,
                "dashboard_id": dashboard_id,
                "result": {
                    "execution_id": workflow_result.execution_id,
                    "data": workflow_result.data,
                    "metadata": workflow_result.metadata,
                    "success": workflow_result.success,
                    "execution_time": workflow_result.execution_time,
                    "timestamp": workflow_result.timestamp.isoformat()
                }
            },
            target_filters={"widget_id": widget_id, "dashboard_id": dashboard_id},
            priority=2
        )
        
        await self.publish_update(update)
    
    async def publish_workflow_error(
        self,
        widget_id: str,
        error_message: str,
        dashboard_id: str = None
    ):
        """Publish workflow error update."""
        update = RealtimeUpdate(
            update_type=UpdateType.WORKFLOW_ERROR,
            timestamp=datetime.now(),
            source="workflow_engine",
            data={
                "widget_id": widget_id,
                "dashboard_id": dashboard_id,
                "error_message": error_message,
                "timestamp": datetime.now().isoformat()
            },
            target_filters={"widget_id": widget_id, "dashboard_id": dashboard_id},
            priority=3
        )
        
        await self.publish_update(update)
    
    async def publish_dashboard_update(
        self,
        dashboard_id: str,
        update_data: Dict[str, Any],
        update_subtype: str = "general"
    ):
        """Publish dashboard update."""
        update = RealtimeUpdate(
            update_type=UpdateType.DASHBOARD_UPDATE,
            timestamp=datetime.now(),
            source="dashboard_service",
            data={
                "dashboard_id": dashboard_id,
                "update_subtype": update_subtype,
                "data": update_data,
                "timestamp": datetime.now().isoformat()
            },
            target_filters={"dashboard_id": dashboard_id},
            priority=2
        )
        
        await self.publish_update(update)
    
    async def publish_business_profile_update(
        self,
        profile_id: str,
        user_id: int,
        update_data: Dict[str, Any]
    ):
        """Publish business profile update."""
        update = RealtimeUpdate(
            update_type=UpdateType.BUSINESS_PROFILE_UPDATE,
            timestamp=datetime.now(),
            source="business_profile_service",
            data={
                "profile_id": profile_id,
                "user_id": user_id,
                "data": update_data,
                "timestamp": datetime.now().isoformat()
            },
            target_filters={"profile_id": profile_id, "user_id": user_id},
            priority=2
        )
        
        await self.publish_update(update)
    
    def get_subscription_status(self, client_id: str) -> Dict[str, Any]:
        """Get subscription status for a client."""
        client_subscriptions = self.client_connections.get(client_id, set())
        
        subscriptions_info = []
        for sub_id in client_subscriptions:
            subscription = self.subscriptions.get(sub_id)
            if subscription:
                subscriptions_info.append({
                    "subscription_id": sub_id,
                    "update_types": [ut.value for ut in subscription.update_types],
                    "filters": subscription.filters,
                    "last_activity": subscription.last_activity.isoformat(),
                    "is_active": subscription.is_active
                })
        
        return {
            "client_id": client_id,
            "total_subscriptions": len(client_subscriptions),
            "subscriptions": subscriptions_info,
            "metrics": self.metrics
        }
    
    async def _process_updates(self):
        """Process real-time updates from the queue."""
        while self.is_running:
            try:
                # Get update from queue with timeout
                try:
                    update = await asyncio.wait_for(self.update_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # Process the update
                await self._handle_update(update)
                
            except Exception as e:
                logger.error(f"Error processing real-time update: {e}")
                await asyncio.sleep(0.1)
    
    async def _handle_update(self, update: RealtimeUpdate):
        """Handle individual update by routing to subscribers."""
        try:
            start_time = datetime.now()
            
            # Find matching subscriptions
            matching_subscriptions = self._find_matching_subscriptions(update)
            
            if not matching_subscriptions:
                return
            
            # Send update to all matching subscribers
            tasks = []
            for subscription in matching_subscriptions:
                task = asyncio.create_task(self._send_update_to_subscriber(subscription, update))
                tasks.append(task)
            
            # Wait for all sends to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Count successes and failures
            successes = sum(1 for r in results if not isinstance(r, Exception))
            failures = len(results) - successes
            
            # Update metrics
            self.metrics["updates_sent"] += successes
            self.metrics["updates_failed"] += failures
            
            # Calculate latency
            latency = (datetime.now() - start_time).total_seconds() * 1000
            self._update_average_latency(latency)
            
            logger.debug(f"Sent {update.update_type.value} update to {successes} subscribers")
            
        except Exception as e:
            logger.error(f"Error handling update: {e}")
            self.metrics["updates_failed"] += 1

    def _find_matching_subscriptions(self, update: RealtimeUpdate) -> List[RealtimeSubscription]:
        """Find subscriptions that match the update."""
        matching = []

        for subscription in self.subscriptions.values():
            if not subscription.is_active:
                continue

            # Check if subscription is interested in this update type
            if update.update_type not in subscription.update_types:
                continue

            # Check filters
            if self._matches_filters(update, subscription.filters):
                matching.append(subscription)

        return matching

    def _matches_filters(self, update: RealtimeUpdate, filters: Dict[str, Any]) -> bool:
        """Check if update matches subscription filters."""
        if not filters:
            return True

        # Check target filters in update
        for key, value in filters.items():
            # Check in update's target filters
            if key in update.target_filters:
                if update.target_filters[key] != value:
                    return False
            # Check in update's data
            elif key in update.data:
                if update.data[key] != value:
                    return False
            else:
                # Filter key not found, doesn't match
                return False

        return True

    async def _send_update_to_subscriber(self, subscription: RealtimeSubscription, update: RealtimeUpdate):
        """Send update to a specific subscriber."""
        try:
            # Update last activity
            subscription.last_activity = datetime.now()

            # Prepare message
            message = {
                "type": update.update_type.value,
                "timestamp": update.timestamp.isoformat(),
                "source": update.source,
                "data": update.data,
                "priority": update.priority
            }

            # Send via WebSocket if available
            if subscription.websocket_connection:
                await self._send_websocket_message(subscription.websocket_connection, message)

            # Call callback if available
            if subscription.callback:
                await self._call_subscriber_callback(subscription.callback, message)

        except Exception as e:
            logger.error(f"Error sending update to subscriber {subscription.subscription_id}: {e}")
            # Mark subscription as inactive if connection failed
            subscription.is_active = False
            raise

    async def _send_websocket_message(self, websocket, message: Dict[str, Any]):
        """Send message via WebSocket."""
        try:
            if hasattr(websocket, 'send_text'):
                # FastAPI WebSocket
                await websocket.send_text(json.dumps(message))
            elif hasattr(websocket, 'send'):
                # Standard WebSocket
                await websocket.send(json.dumps(message))
            else:
                logger.warning("Unknown WebSocket type, cannot send message")

        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            raise

    async def _call_subscriber_callback(self, callback: Callable, message: Dict[str, Any]):
        """Call subscriber callback function."""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(message)
            else:
                callback(message)

        except Exception as e:
            logger.error(f"Error calling subscriber callback: {e}")
            raise

    def _update_average_latency(self, latency: float):
        """Update average latency metric."""
        current_avg = self.metrics["average_latency"]
        # Simple moving average
        self.metrics["average_latency"] = (current_avg * 0.9) + (latency * 0.1)

    async def _close_all_connections(self):
        """Close all WebSocket connections."""
        for subscription in self.subscriptions.values():
            if subscription.websocket_connection:
                try:
                    await subscription.websocket_connection.close()
                except:
                    pass

        self.subscriptions.clear()
        self.client_connections.clear()

    def _setup_event_handlers(self):
        """Setup event handlers for LangGraph events."""
        try:
            # Subscribe to workflow events
            event_bus.subscribe("workflow.completed", self._handle_workflow_completed)
            event_bus.subscribe("workflow.failed", self._handle_workflow_failed)
            event_bus.subscribe("workflow.started", self._handle_workflow_started)

            # Subscribe to dashboard events
            event_bus.subscribe("dashboard.widget_updated", self._handle_widget_updated)
            event_bus.subscribe("dashboard.dashboard_updated", self._handle_dashboard_updated)

            # Subscribe to business profile events
            event_bus.subscribe("business_profile.updated", self._handle_business_profile_updated)

            # Subscribe to agent events
            event_bus.subscribe("agent.status_changed", self._handle_agent_status_changed)

            logger.info("Event handlers setup for UnifiedRealtimeService")

        except Exception as e:
            logger.error(f"Error setting up event handlers: {e}")

    async def _handle_workflow_completed(self, event: LangGraphEvent):
        """Handle workflow completion events."""
        try:
            data = event.data
            widget_id = data.get("widget_id")
            dashboard_id = data.get("dashboard_id")

            if widget_id:
                # Create workflow result from event data
                update = RealtimeUpdate(
                    update_type=UpdateType.WORKFLOW_RESULT,
                    timestamp=event.timestamp,
                    source=event.source,
                    data={
                        "widget_id": widget_id,
                        "dashboard_id": dashboard_id,
                        "workflow_id": data.get("workflow_id"),
                        "execution_time": data.get("execution_time", 0),
                        "success": True,
                        "result_data": data.get("results", {})
                    },
                    target_filters={"widget_id": widget_id, "dashboard_id": dashboard_id},
                    priority=2
                )

                await self.publish_update(update)

        except Exception as e:
            logger.error(f"Error handling workflow completed event: {e}")

    async def _handle_workflow_failed(self, event: LangGraphEvent):
        """Handle workflow failure events."""
        try:
            data = event.data
            widget_id = data.get("widget_id")
            dashboard_id = data.get("dashboard_id")
            error_message = data.get("error_message", "Workflow execution failed")

            if widget_id:
                await self.publish_workflow_error(widget_id, error_message, dashboard_id)

        except Exception as e:
            logger.error(f"Error handling workflow failed event: {e}")

    async def _handle_workflow_started(self, event: LangGraphEvent):
        """Handle workflow start events."""
        try:
            data = event.data
            widget_id = data.get("widget_id")
            dashboard_id = data.get("dashboard_id")

            if widget_id:
                update = RealtimeUpdate(
                    update_type=UpdateType.WORKFLOW_STATUS,
                    timestamp=event.timestamp,
                    source=event.source,
                    data={
                        "widget_id": widget_id,
                        "dashboard_id": dashboard_id,
                        "status": "started",
                        "workflow_id": data.get("workflow_id"),
                        "timestamp": event.timestamp.isoformat()
                    },
                    target_filters={"widget_id": widget_id, "dashboard_id": dashboard_id},
                    priority=1
                )

                await self.publish_update(update)

        except Exception as e:
            logger.error(f"Error handling workflow started event: {e}")

    async def _handle_widget_updated(self, event: LangGraphEvent):
        """Handle widget update events."""
        try:
            data = event.data
            widget_id = data.get("widget_id")
            dashboard_id = data.get("dashboard_id")

            update = RealtimeUpdate(
                update_type=UpdateType.WIDGET_UPDATE,
                timestamp=event.timestamp,
                source=event.source,
                data=data,
                target_filters={"widget_id": widget_id, "dashboard_id": dashboard_id},
                priority=2
            )

            await self.publish_update(update)

        except Exception as e:
            logger.error(f"Error handling widget updated event: {e}")

    async def _handle_dashboard_updated(self, event: LangGraphEvent):
        """Handle dashboard update events."""
        try:
            data = event.data
            dashboard_id = data.get("dashboard_id")

            await self.publish_dashboard_update(
                dashboard_id,
                data,
                data.get("update_type", "general")
            )

        except Exception as e:
            logger.error(f"Error handling dashboard updated event: {e}")

    async def _handle_business_profile_updated(self, event: LangGraphEvent):
        """Handle business profile update events."""
        try:
            data = event.data
            profile_id = data.get("profile_id")
            user_id = data.get("user_id")

            if profile_id and user_id:
                await self.publish_business_profile_update(profile_id, user_id, data)

        except Exception as e:
            logger.error(f"Error handling business profile updated event: {e}")

    async def _handle_agent_status_changed(self, event: LangGraphEvent):
        """Handle agent status change events."""
        try:
            data = event.data

            update = RealtimeUpdate(
                update_type=UpdateType.AGENT_STATUS,
                timestamp=event.timestamp,
                source=event.source,
                data=data,
                target_filters={"agent_id": data.get("agent_id")},
                priority=1
            )

            await self.publish_update(update)

        except Exception as e:
            logger.error(f"Error handling agent status changed event: {e}")


# Create singleton instance
unified_realtime_service = UnifiedRealtimeService()
