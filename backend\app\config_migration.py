"""
Configuration Migration Script.

Migrates the existing configuration system to use the new ConfigurationService.
This provides backward compatibility while enabling the new unified configuration system.
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the new configuration service
from .services.configuration_service import get_configuration_service, ConfigurationError

logger = logging.getLogger(__name__)


class ConfigurationMigration:
    """Handles migration from old configuration system to new ConfigurationService."""
    
    def __init__(self):
        self.config_service = get_configuration_service()
        self._migrated_configs: Dict[str, Any] = {}
        self._migration_complete = False
    
    async def migrate_configuration(self) -> bool:
        """
        Migrate existing configuration to the new system.
        
        Returns:
            True if migration was successful, False otherwise
        """
        try:
            logger.info("Starting configuration migration...")
            
            # Migrate core application configuration
            await self._migrate_app_config()
            
            # Migrate database configuration
            await self._migrate_database_config()
            
            # Migrate security configuration
            await self._migrate_security_config()
            
            # Migrate LLM configuration
            await self._migrate_llm_config()
            
            # Cache migrated configurations
            self._migration_complete = True
            
            logger.info("Configuration migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Configuration migration failed: {e}")
            return False
    
    async def _migrate_app_config(self):
        """Migrate application configuration."""
        app_config = {
            "name": os.getenv("APP_NAME", "Datagenius"),
            "version": os.getenv("APP_VERSION", "1.0.0"),
            "environment": os.getenv("ENVIRONMENT", "development"),
            "debug": os.getenv("DEBUG", "false").lower() == "true",
            "host": os.getenv("HOST", "0.0.0.0"),
            "port": int(os.getenv("PORT", "8000")),
            "cors_origins": os.getenv("CORS_ORIGINS", "*").split(","),
            "cors_credentials": os.getenv("CORS_CREDENTIALS", "true").lower() == "true",
            "cors_methods": os.getenv("CORS_METHODS", "GET,POST,PUT,DELETE,OPTIONS").split(","),
            "cors_headers": os.getenv("CORS_HEADERS", "*").split(",")
        }
        
        await self.config_service.save_config("app", app_config)
        self._migrated_configs["app"] = app_config
    
    async def _migrate_database_config(self):
        """Migrate database configuration."""
        database_config = {
            "url": os.getenv("DATABASE_URL", "sqlite:///./datagenius.db"),
            "echo": os.getenv("DATABASE_ECHO", "false").lower() == "true",
            "pool_size": int(os.getenv("DATABASE_POOL_SIZE", "10")),
            "max_overflow": int(os.getenv("DATABASE_MAX_OVERFLOW", "20")),
            "pool_timeout": int(os.getenv("DATABASE_POOL_TIMEOUT", "30")),
            "pool_recycle": int(os.getenv("DATABASE_POOL_RECYCLE", "3600")),
            "pool_pre_ping": os.getenv("DATABASE_POOL_PRE_PING", "true").lower() == "true"
        }
        
        await self.config_service.save_config("database", database_config)
        self._migrated_configs["database"] = database_config
    
    async def _migrate_security_config(self):
        """Migrate security configuration."""
        security_config = {
            "jwt_secret_key": os.getenv("JWT_SECRET_KEY", "your-secret-key-here"),
            "jwt_algorithm": os.getenv("JWT_ALGORITHM", "HS256"),
            "access_token_expire_minutes": int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30")),
            "refresh_token_expire_days": int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7")),
            "max_refresh_count": int(os.getenv("MAX_REFRESH_COUNT", "5")),
            "max_concurrent_sessions": int(os.getenv("MAX_CONCURRENT_SESSIONS", "3")),
            "enforce_ip_validation": os.getenv("ENFORCE_IP_VALIDATION", "false").lower() == "true",
            "ip_change_lockout": int(os.getenv("IP_CHANGE_LOCKOUT", "300")),
            "password_min_length": int(os.getenv("PASSWORD_MIN_LENGTH", "8")),
            "password_require_uppercase": os.getenv("PASSWORD_REQUIRE_UPPERCASE", "true").lower() == "true",
            "password_require_lowercase": os.getenv("PASSWORD_REQUIRE_LOWERCASE", "true").lower() == "true",
            "password_require_numbers": os.getenv("PASSWORD_REQUIRE_NUMBERS", "true").lower() == "true",
            "password_require_special": os.getenv("PASSWORD_REQUIRE_SPECIAL", "true").lower() == "true"
        }
        
        await self.config_service.save_config("security", security_config)
        self._migrated_configs["security"] = security_config
    
    async def _migrate_llm_config(self):
        """Migrate LLM configuration."""
        llm_config = {
            "default_provider": os.getenv("DEFAULT_LLM_PROVIDER", "openai"),
            "default_model": os.getenv("DEFAULT_LLM_MODEL", "gpt-3.5-turbo"),
            "max_tokens": int(os.getenv("LLM_MAX_TOKENS", "4096")),
            "temperature": float(os.getenv("LLM_TEMPERATURE", "0.7")),
            "timeout": int(os.getenv("LLM_TIMEOUT", "30")),
            "retry_attempts": int(os.getenv("LLM_RETRY_ATTEMPTS", "3")),
            "providers": {
                "openai": {
                    "api_key": os.getenv("OPENAI_API_KEY", ""),
                    "base_url": os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
                    "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
                },
                "groq": {
                    "api_key": os.getenv("GROQ_API_KEY", ""),
                    "base_url": os.getenv("GROQ_BASE_URL", "https://api.groq.com/openai/v1"),
                    "models": ["llama2-70b-4096", "mixtral-8x7b-32768"]
                },
                "anthropic": {
                    "api_key": os.getenv("ANTHROPIC_API_KEY", ""),
                    "base_url": os.getenv("ANTHROPIC_BASE_URL", "https://api.anthropic.com"),
                    "models": ["claude-3-sonnet", "claude-3-opus"]
                }
            }
        }
        
        await self.config_service.save_config("llm", llm_config)
        self._migrated_configs["llm"] = llm_config
    
    def get_migrated_config(self, key: str) -> Optional[Dict[str, Any]]:
        """Get a migrated configuration by key."""
        return self._migrated_configs.get(key)
    
    def is_migration_complete(self) -> bool:
        """Check if migration is complete."""
        return self._migration_complete


# Global migration instance
_migration_instance: Optional[ConfigurationMigration] = None


def get_migration_instance() -> ConfigurationMigration:
    """Get the global migration instance."""
    global _migration_instance
    if _migration_instance is None:
        _migration_instance = ConfigurationMigration()
    return _migration_instance


async def ensure_migration_complete() -> bool:
    """Ensure configuration migration is complete."""
    migration = get_migration_instance()
    if not migration.is_migration_complete():
        return await migration.migrate_configuration()
    return True


# Backward compatibility layer
class BackwardCompatibilityConfig:
    """Provides backward compatibility for the old configuration system."""
    
    def __init__(self):
        self.migration = get_migration_instance()
        self._cached_values: Dict[str, Any] = {}
    
    async def _ensure_migrated(self):
        """Ensure migration is complete."""
        if not self.migration.is_migration_complete():
            await self.migration.migrate_configuration()
    
    async def get_database_url(self) -> str:
        """Get database URL."""
        await self._ensure_migrated()
        db_config = self.migration.get_migrated_config("database")
        return db_config.get("url", "sqlite:///./datagenius.db") if db_config else "sqlite:///./datagenius.db"
    
    async def get_jwt_secret_key(self) -> str:
        """Get JWT secret key."""
        await self._ensure_migrated()
        security_config = self.migration.get_migrated_config("security")
        return security_config.get("jwt_secret_key", "your-secret-key-here") if security_config else "your-secret-key-here"
    
    async def get_cors_origins(self) -> list:
        """Get CORS origins."""
        await self._ensure_migrated()
        app_config = self.migration.get_migrated_config("app")
        return app_config.get("cors_origins", ["*"]) if app_config else ["*"]
    
    def get_env_value(self, key: str, default: Any = None) -> Any:
        """Get environment value with fallback."""
        return os.getenv(key, default)


# Global backward compatibility instance
_compat_config: Optional[BackwardCompatibilityConfig] = None


def get_compat_config() -> BackwardCompatibilityConfig:
    """Get the backward compatibility configuration."""
    global _compat_config
    if _compat_config is None:
        _compat_config = BackwardCompatibilityConfig()
    return _compat_config


# Export migration utilities
__all__ = [
    "ConfigurationMigration",
    "get_migration_instance",
    "ensure_migration_complete",
    "BackwardCompatibilityConfig",
    "get_compat_config"
]
