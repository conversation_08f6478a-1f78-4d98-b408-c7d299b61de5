"""
Dynamic Intelligent Agent Router for Datagenius Multi-Agent System.

This module provides a fully dynamic, extensible routing system that automatically
discovers available agents, respects user access permissions, and enables
multi-agent collaboration workflows without hardcoded mappings.
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Set, Tuple
from datetime import datetime, timedelta
import asyncio
import yaml
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

try:
    from langgraph.graph import StateGraph, END
except ImportError:
    # Fallback for development without Lang<PERSON>raph installed
    StateGraph = None
    END = "END"

from ..states.agent_state import DatageniusAgentState, update_agent_transition, add_error
from ..optimization.profile_optimizer import BusinessProfileWorkflowOptimizer, BusinessProfile, BusinessType
from ..events.event_bus import event_bus, LangGraphEvent, EventPriority
from ..core.agent_factory import agent_factory
from ...components.shared.universal_context_injector import UniversalContextInjector
from ...tools.mcp.security.access_control import get_access_controller

logger = logging.getLogger(__name__)


class RoutingStrategy(str, Enum):
    """Routing strategy options."""
    SINGLE_AGENT = "single_agent"
    MULTI_AGENT = "multi_agent"
    COLLABORATIVE = "collaborative"
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"


@dataclass
class RoutingDecision:
    """Routing decision with reasoning."""
    strategy: RoutingStrategy
    primary_agent: Optional[str] = None
    collaborating_agents: List[str] = field(default_factory=list)
    reasoning: str = ""
    confidence_score: float = 0.0
    estimated_complexity: str = "medium"
    estimated_duration: float = 30.0  # seconds
    requires_user_confirmation: bool = False


class RoutingAlgorithm:
    """Base class for routing algorithms."""

    def __init__(self, name: str, priority: int = 0):
        self.name = name
        self.priority = priority
        self.success_rate = 0.0
        self.usage_count = 0

    async def can_handle(self, context: Dict[str, Any]) -> bool:
        """
        Determine if this algorithm can handle the routing request.

        Args:
            context: Routing context including user, message, available agents

        Returns:
            True if this algorithm can handle the request
        """
        raise NotImplementedError

    async def calculate_routing_decision(self, context: Dict[str, Any]) -> RoutingDecision:
        """
        Calculate the routing decision for the given context.

        Args:
            context: Routing context

        Returns:
            RoutingDecision object
        """
        raise NotImplementedError

    def update_performance(self, success: bool):
        """Update performance metrics."""
        self.usage_count += 1
        if self.usage_count == 1:
            self.success_rate = 1.0 if success else 0.0
        else:
            # Exponential moving average
            alpha = 0.1
            self.success_rate = alpha * (1.0 if success else 0.0) + (1 - alpha) * self.success_rate


class IntentBasedRoutingAlgorithm(RoutingAlgorithm):
    """Intent-based routing algorithm."""

    def __init__(self):
        super().__init__("intent_based", priority=1)

    async def can_handle(self, context: Dict[str, Any]) -> bool:
        """Can handle any request with detected intent."""
        return context.get("detected_intent") is not None

    async def calculate_routing_decision(self, context: Dict[str, Any]) -> RoutingDecision:
        """Route based on detected intent."""
        intent = context.get("detected_intent", "general_inquiry")
        complexity = context.get("complexity", "medium")
        available_agents = context.get("available_agents", [])

        # Find agents that support this intent
        matching_agents = []
        for agent in available_agents:
            if intent in agent.supported_intents:
                score = 10
                # Add capability overlap bonus
                intent_capabilities = context.get("intent_capabilities", set())
                capability_overlap = len(agent.capabilities.intersection(intent_capabilities))
                score += capability_overlap * 5
                # Add performance bonus
                score += agent.performance_score * 2
                matching_agents.append((agent, score))

        if not matching_agents:
            # Fallback to primary agents
            primary_agents = [agent for agent in available_agents if agent.is_primary_agent]
            if primary_agents:
                return RoutingDecision(
                    strategy=RoutingStrategy.SINGLE_AGENT,
                    primary_agent=primary_agents[0].persona_id,
                    reasoning=f"No specific match for intent '{intent}', using primary agent",
                    confidence_score=0.6
                )

        # Sort by score and select best
        matching_agents.sort(key=lambda x: x[1], reverse=True)
        best_agent = matching_agents[0][0]

        return RoutingDecision(
            strategy=RoutingStrategy.SINGLE_AGENT,
            primary_agent=best_agent.persona_id,
            reasoning=f"Best match for intent '{intent}' with {complexity} complexity",
            confidence_score=0.9,
            estimated_complexity=complexity
        )


class CapabilityBasedRoutingAlgorithm(RoutingAlgorithm):
    """Capability-based routing algorithm."""

    def __init__(self):
        super().__init__("capability_based", priority=2)

    async def can_handle(self, context: Dict[str, Any]) -> bool:
        """Can handle requests with identified required capabilities."""
        return bool(context.get("required_capabilities"))

    async def calculate_routing_decision(self, context: Dict[str, Any]) -> RoutingDecision:
        """Route based on required capabilities."""
        required_capabilities = context.get("required_capabilities", set())
        available_agents = context.get("available_agents", [])
        complexity = context.get("complexity", "medium")

        # Score agents based on capability match
        agent_scores = []
        for agent in available_agents:
            capability_match = len(agent.capabilities.intersection(required_capabilities))
            if capability_match > 0:
                score = capability_match * 10
                score += agent.performance_score * 5
                score += agent.priority_score * 3
                agent_scores.append((agent, score))

        if not agent_scores:
            # No capability match, use fallback
            primary_agents = [agent for agent in available_agents if agent.is_primary_agent]
            if primary_agents:
                return RoutingDecision(
                    strategy=RoutingStrategy.SINGLE_AGENT,
                    primary_agent=primary_agents[0].persona_id,
                    reasoning="No capability match found, using primary agent",
                    confidence_score=0.4
                )

        # Sort by score
        agent_scores.sort(key=lambda x: x[1], reverse=True)
        best_agent = agent_scores[0][0]

        return RoutingDecision(
            strategy=RoutingStrategy.SINGLE_AGENT,
            primary_agent=best_agent.persona_id,
            reasoning=f"Best capability match for {required_capabilities}",
            confidence_score=0.85,
            estimated_complexity=complexity
        )


class CollaborativeRoutingAlgorithm(RoutingAlgorithm):
    """Collaborative routing algorithm for multi-agent workflows."""

    def __init__(self):
        super().__init__("collaborative", priority=3)

    async def can_handle(self, context: Dict[str, Any]) -> bool:
        """Can handle requests that need collaboration."""
        return context.get("collaboration_needed", False)

    async def calculate_routing_decision(self, context: Dict[str, Any]) -> RoutingDecision:
        """Route for collaborative workflows."""
        available_agents = context.get("available_agents", [])
        required_capabilities = context.get("required_capabilities", set())
        complexity = context.get("complexity", "high")

        # Find complementary agents
        selected_agents = []
        covered_capabilities = set()

        # Sort agents by collaboration score and performance
        sorted_agents = sorted(
            available_agents,
            key=lambda x: (x.collaboration_score, x.performance_score),
            reverse=True
        )

        for agent in sorted_agents:
            # Check if this agent adds new capabilities
            new_capabilities = agent.capabilities - covered_capabilities
            if new_capabilities and len(selected_agents) < 3:  # Max 3 agents
                selected_agents.append(agent)
                covered_capabilities.update(agent.capabilities)

                # Stop if we have good coverage
                if len(covered_capabilities.intersection(required_capabilities)) >= len(required_capabilities) * 0.8:
                    break

        if len(selected_agents) < 2:
            # Not enough agents for collaboration, fallback to single agent
            if selected_agents:
                return RoutingDecision(
                    strategy=RoutingStrategy.SINGLE_AGENT,
                    primary_agent=selected_agents[0].persona_id,
                    reasoning="Insufficient agents for collaboration, using single agent",
                    confidence_score=0.6
                )

        return RoutingDecision(
            strategy=RoutingStrategy.COLLABORATIVE,
            primary_agent=selected_agents[0].persona_id,
            collaborating_agents=[agent.persona_id for agent in selected_agents[1:]],
            reasoning=f"Collaborative workflow with {len(selected_agents)} agents",
            confidence_score=0.8,
            estimated_complexity=complexity
        )


class PluginRoutingManager:
    """Manager for routing algorithm plugins."""

    def __init__(self):
        self.algorithms: List[RoutingAlgorithm] = []
        self.algorithm_registry: Dict[str, RoutingAlgorithm] = {}
        self._register_default_algorithms()

    def _register_default_algorithms(self):
        """Register default routing algorithms."""
        default_algorithms = [
            IntentBasedRoutingAlgorithm(),
            CapabilityBasedRoutingAlgorithm(),
            CollaborativeRoutingAlgorithm()
        ]

        for algorithm in default_algorithms:
            self.register_algorithm(algorithm)

    def register_algorithm(self, algorithm: RoutingAlgorithm):
        """Register a new routing algorithm."""
        self.algorithms.append(algorithm)
        self.algorithm_registry[algorithm.name] = algorithm
        # Sort by priority (higher priority first)
        self.algorithms.sort(key=lambda x: x.priority, reverse=True)
        logger.info(f"Registered routing algorithm: {algorithm.name}")

    def unregister_algorithm(self, name: str):
        """Unregister a routing algorithm."""
        if name in self.algorithm_registry:
            algorithm = self.algorithm_registry[name]
            self.algorithms.remove(algorithm)
            del self.algorithm_registry[name]
            logger.info(f"Unregistered routing algorithm: {name}")

    async def get_best_routing_decision(self, context: Dict[str, Any]) -> RoutingDecision:
        """Get the best routing decision using available algorithms."""
        for algorithm in self.algorithms:
            try:
                if await algorithm.can_handle(context):
                    decision = await algorithm.calculate_routing_decision(context)
                    logger.debug(f"Using routing algorithm: {algorithm.name}")
                    return decision
            except Exception as e:
                logger.error(f"Error in routing algorithm {algorithm.name}: {e}")
                continue

        # Fallback decision
        available_agents = context.get("available_agents", [])
        if available_agents:
            primary_agents = [agent for agent in available_agents if agent.is_primary_agent]
            fallback_agent = primary_agents[0] if primary_agents else available_agents[0]

            return RoutingDecision(
                strategy=RoutingStrategy.SINGLE_AGENT,
                primary_agent=fallback_agent.persona_id,
                reasoning="Fallback routing - no algorithm could handle request",
                confidence_score=0.3
            )

        # Last resort
        return RoutingDecision(
            strategy=RoutingStrategy.SINGLE_AGENT,
            primary_agent="datagenius-concierge",
            reasoning="Emergency fallback to concierge",
            confidence_score=0.2
        )

    def update_algorithm_performance(self, algorithm_name: str, success: bool):
        """Update algorithm performance metrics."""
        if algorithm_name in self.algorithm_registry:
            self.algorithm_registry[algorithm_name].update_performance(success)

    def get_algorithm_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all algorithms."""
        stats = {}
        for algorithm in self.algorithms:
            stats[algorithm.name] = {
                "priority": algorithm.priority,
                "success_rate": algorithm.success_rate,
                "usage_count": algorithm.usage_count
            }
        return stats


class LearningIntentDetector:
    """
    Learning-based intent detection system that adapts based on historical
    routing success patterns and user feedback.
    """

    def __init__(self):
        self.intent_patterns: Dict[str, List[str]] = {}
        self.learned_patterns: Dict[str, Dict[str, float]] = {}  # intent -> {pattern: confidence}
        self.success_history: Dict[str, List[bool]] = {}  # intent -> [success_results]
        self.pattern_weights: Dict[str, float] = {}
        self.user_intent_history: Dict[str, List[str]] = {}  # user_id -> [intents]
        self.context_patterns: Dict[str, List[Dict[str, Any]]] = {}  # intent -> [contexts]

    def load_base_patterns(self, patterns: Dict[str, List[str]]):
        """Load base intent patterns from configuration."""
        self.intent_patterns = patterns.copy()

        # Initialize learned patterns with base patterns
        for intent, pattern_list in patterns.items():
            if intent not in self.learned_patterns:
                self.learned_patterns[intent] = {}

            for pattern in pattern_list:
                self.learned_patterns[intent][pattern] = 1.0  # High confidence for base patterns

    async def detect_intent(self, message: str, user_id: str = None, context: Dict[str, Any] = None) -> Tuple[str, float]:
        """
        Detect intent from message using learning-based approach.

        Args:
            message: User message
            user_id: User ID for personalization
            context: Additional context

        Returns:
            Tuple of (intent, confidence_score)
        """
        message_lower = message.lower().strip()

        if not message_lower:
            return "general_inquiry", 0.5

        # Score all intents
        intent_scores = {}

        # Use learned patterns
        for intent, patterns in self.learned_patterns.items():
            score = 0.0

            for pattern, confidence in patterns.items():
                if pattern in message_lower:
                    # Weight by pattern confidence and success history
                    pattern_weight = self._get_pattern_weight(intent, pattern)
                    score += confidence * pattern_weight

            if score > 0:
                intent_scores[intent] = score

        # Apply user personalization
        if user_id and user_id in self.user_intent_history:
            user_intents = self.user_intent_history[user_id]
            for intent in intent_scores:
                # Boost score for intents this user has used before
                user_frequency = user_intents.count(intent) / len(user_intents)
                intent_scores[intent] *= (1.0 + user_frequency * 0.2)

        # Apply contextual boosting
        if context:
            for intent in intent_scores:
                context_boost = self._calculate_context_boost(intent, context)
                intent_scores[intent] *= (1.0 + context_boost)

        # Find best intent
        if intent_scores:
            best_intent = max(intent_scores.items(), key=lambda x: x[1])
            confidence = min(best_intent[1] / 10.0, 1.0)  # Normalize to 0-1
            return best_intent[0], confidence

        # Fallback to semantic analysis for unknown patterns
        semantic_intent, semantic_confidence = self._semantic_intent_detection(message_lower)

        # Learn from this new pattern if confidence is high enough
        if semantic_confidence > 0.7:
            self._learn_new_pattern(semantic_intent, message_lower, semantic_confidence)

        return semantic_intent, semantic_confidence

    def _semantic_intent_detection(self, message: str) -> Tuple[str, float]:
        """Fallback semantic intent detection for unknown patterns."""
        semantic_mappings = {
            "greeting": {
                "keywords": ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening"],
                "weight": 1.0
            },
            "data_analysis": {
                "keywords": ["analyze", "analysis", "data", "statistics", "metrics", "insights", "trends", "examine"],
                "weight": 1.0
            },
            "visualization": {
                "keywords": ["visualize", "chart", "graph", "plot", "dashboard", "display", "show"],
                "weight": 1.0
            },
            "marketing": {
                "keywords": ["marketing", "campaign", "promotion", "advertising", "brand", "content"],
                "weight": 1.0
            },
            "classification": {
                "keywords": ["classify", "categorize", "label", "tag", "organize", "sort"],
                "weight": 1.0
            },
            "research": {
                "keywords": ["research", "investigate", "study", "explore", "find", "search"],
                "weight": 0.9
            },
            "general_inquiry": {
                "keywords": ["what", "how", "why", "explain", "tell", "describe", "help"],
                "weight": 0.8
            }
        }

        best_intent = "general_inquiry"
        best_score = 0.0

        for intent, mapping in semantic_mappings.items():
            score = 0.0
            for keyword in mapping["keywords"]:
                if keyword in message:
                    score += mapping["weight"]

            if score > best_score:
                best_score = score
                best_intent = intent

        # Normalize confidence
        confidence = min(best_score / 3.0, 1.0) if best_score > 0 else 0.3
        return best_intent, confidence

    def _learn_new_pattern(self, intent: str, message: str, confidence: float):
        """Learn a new pattern from successful routing."""
        if intent not in self.learned_patterns:
            self.learned_patterns[intent] = {}

        # Extract meaningful phrases (2-4 words)
        words = message.split()
        for i in range(len(words)):
            for length in [2, 3, 4]:
                if i + length <= len(words):
                    phrase = " ".join(words[i:i+length])
                    if len(phrase) > 5:  # Minimum phrase length
                        if phrase not in self.learned_patterns[intent]:
                            self.learned_patterns[intent][phrase] = confidence * 0.5  # Lower confidence for learned patterns
                        else:
                            # Reinforce existing pattern
                            current_conf = self.learned_patterns[intent][phrase]
                            self.learned_patterns[intent][phrase] = min(current_conf + 0.1, 1.0)

    def _get_pattern_weight(self, intent: str, pattern: str) -> float:
        """Get weight for a pattern based on success history."""
        key = f"{intent}:{pattern}"
        if key not in self.success_history:
            return 1.0  # Default weight

        history = self.success_history[key]
        if not history:
            return 1.0

        # Calculate success rate with recency weighting
        total_weight = 0.0
        success_weight = 0.0

        for i, success in enumerate(history):
            weight = 1.0 + (i / len(history)) * 0.5  # More recent = higher weight
            total_weight += weight
            if success:
                success_weight += weight

        return success_weight / total_weight if total_weight > 0 else 1.0

    def _calculate_context_boost(self, intent: str, context: Dict[str, Any]) -> float:
        """Calculate context-based boost for intent."""
        boost = 0.0

        # Time-based patterns
        if "timestamp" in context:
            # Could implement time-based intent patterns here
            pass

        # User role/type patterns
        if "user_role" in context:
            # Could boost certain intents based on user role
            pass

        # Previous conversation context
        if "previous_intents" in context:
            prev_intents = context["previous_intents"]
            if intent in prev_intents:
                boost += 0.1  # Small boost for conversation continuity

        return boost

    def update_success_feedback(self, intent: str, pattern: str, success: bool, user_id: str = None):
        """Update success feedback for learning."""
        key = f"{intent}:{pattern}"
        if key not in self.success_history:
            self.success_history[key] = []

        self.success_history[key].append(success)

        # Keep only recent history
        if len(self.success_history[key]) > 100:
            self.success_history[key] = self.success_history[key][-100:]

        # Update user intent history
        if user_id and success:
            if user_id not in self.user_intent_history:
                self.user_intent_history[user_id] = []
            self.user_intent_history[user_id].append(intent)

            # Keep only recent user history
            if len(self.user_intent_history[user_id]) > 50:
                self.user_intent_history[user_id] = self.user_intent_history[user_id][-50:]

    def add_custom_pattern(self, intent: str, pattern: str, confidence: float = 0.8):
        """Add a custom pattern for an intent."""
        if intent not in self.learned_patterns:
            self.learned_patterns[intent] = {}

        self.learned_patterns[intent][pattern] = confidence
        logger.info(f"Added custom pattern '{pattern}' for intent '{intent}' with confidence {confidence}")

    def get_intent_statistics(self) -> Dict[str, Any]:
        """Get statistics about intent detection."""
        stats = {
            "total_intents": len(self.learned_patterns),
            "total_patterns": sum(len(patterns) for patterns in self.learned_patterns.values()),
            "success_history_size": len(self.success_history),
            "user_history_size": len(self.user_intent_history)
        }

        # Intent-specific stats
        intent_stats = {}
        for intent, patterns in self.learned_patterns.items():
            intent_stats[intent] = {
                "pattern_count": len(patterns),
                "avg_confidence": sum(patterns.values()) / len(patterns) if patterns else 0.0
            }

        stats["intent_details"] = intent_stats
        return stats


class AdaptiveAgentScoringSystem:
    """
    Self-adapting agent scoring system that learns from performance metrics,
    user feedback, and routing success patterns to improve agent selection.
    """

    def __init__(self):
        self.agent_scores: Dict[str, Dict[str, float]] = {}  # agent_id -> {metric: score}
        self.performance_history: Dict[str, List[Dict[str, Any]]] = {}  # agent_id -> [performance_records]
        self.user_feedback: Dict[str, List[Dict[str, Any]]] = {}  # agent_id -> [feedback_records]
        self.context_performance: Dict[str, Dict[str, List[float]]] = {}  # agent_id -> {context_type: [scores]}
        self.capability_performance: Dict[str, Dict[str, List[float]]] = {}  # agent_id -> {capability: [scores]}
        self.collaboration_scores: Dict[str, Dict[str, float]] = {}  # agent_id -> {partner_id: score}
        self.temporal_patterns: Dict[str, Dict[str, List[float]]] = {}  # agent_id -> {time_period: [scores]}

        # Scoring weights (can be learned over time)
        self.scoring_weights = {
            "performance_score": 0.3,
            "user_satisfaction": 0.25,
            "response_time": 0.15,
            "success_rate": 0.2,
            "collaboration_ability": 0.1
        }

    def initialize_agent_scoring(self, agent_metadata: "AgentMetadata"):
        """Initialize scoring for a new agent."""
        agent_id = agent_metadata.persona_id

        if agent_id not in self.agent_scores:
            self.agent_scores[agent_id] = {
                "performance_score": agent_metadata.performance_score,
                "user_satisfaction": agent_metadata.user_satisfaction_score,
                "response_time_score": self._normalize_response_time(agent_metadata.average_response_time),
                "success_rate": agent_metadata.success_rate,
                "collaboration_score": agent_metadata.collaboration_score,
                "priority_score": agent_metadata.priority_score,
                "adaptive_score": 0.5  # Will be learned
            }

        if agent_id not in self.performance_history:
            self.performance_history[agent_id] = []

        if agent_id not in self.user_feedback:
            self.user_feedback[agent_id] = []

    def _normalize_response_time(self, response_time: float) -> float:
        """Normalize response time to a 0-1 score (lower time = higher score)."""
        # Assume 10 seconds is maximum acceptable response time
        max_time = 10.0
        return max(0.0, 1.0 - (response_time / max_time))

    def update_performance_metrics(self, agent_id: str, metrics: Dict[str, Any]):
        """
        Update performance metrics for an agent.

        Args:
            agent_id: Agent identifier
            metrics: Performance metrics dictionary
        """
        if agent_id not in self.performance_history:
            self.performance_history[agent_id] = []

        # Add timestamp to metrics
        metrics["timestamp"] = datetime.now().isoformat()
        self.performance_history[agent_id].append(metrics)

        # Keep only recent history (last 100 records)
        if len(self.performance_history[agent_id]) > 100:
            self.performance_history[agent_id] = self.performance_history[agent_id][-100:]

        # Update current scores based on recent performance
        self._update_scores_from_performance(agent_id)

    def _update_scores_from_performance(self, agent_id: str):
        """Update agent scores based on recent performance history."""
        if agent_id not in self.performance_history or not self.performance_history[agent_id]:
            return

        recent_records = self.performance_history[agent_id][-20:]  # Last 20 records

        # Calculate weighted averages with recency bias
        total_weight = 0.0
        weighted_metrics = {
            "success_rate": 0.0,
            "response_time": 0.0,
            "user_satisfaction": 0.0,
            "error_rate": 0.0
        }

        for i, record in enumerate(recent_records):
            # More recent records have higher weight
            weight = 1.0 + (i / len(recent_records)) * 0.5
            total_weight += weight

            for metric in weighted_metrics:
                if metric in record:
                    weighted_metrics[metric] += record[metric] * weight

        # Update scores
        if total_weight > 0:
            for metric in weighted_metrics:
                weighted_metrics[metric] /= total_weight

            # Update agent scores
            if agent_id in self.agent_scores:
                self.agent_scores[agent_id]["success_rate"] = weighted_metrics["success_rate"]
                self.agent_scores[agent_id]["response_time_score"] = self._normalize_response_time(
                    weighted_metrics["response_time"]
                )
                self.agent_scores[agent_id]["user_satisfaction"] = weighted_metrics["user_satisfaction"]

                # Calculate adaptive score
                self._calculate_adaptive_score(agent_id)

    def add_user_feedback(self, agent_id: str, feedback: Dict[str, Any]):
        """
        Add user feedback for an agent.

        Args:
            agent_id: Agent identifier
            feedback: User feedback dictionary
        """
        if agent_id not in self.user_feedback:
            self.user_feedback[agent_id] = []

        feedback["timestamp"] = datetime.now().isoformat()
        self.user_feedback[agent_id].append(feedback)

        # Keep only recent feedback (last 50 records)
        if len(self.user_feedback[agent_id]) > 50:
            self.user_feedback[agent_id] = self.user_feedback[agent_id][-50:]

        # Update satisfaction score
        self._update_satisfaction_score(agent_id)

    def _update_satisfaction_score(self, agent_id: str):
        """Update user satisfaction score based on recent feedback."""
        if agent_id not in self.user_feedback or not self.user_feedback[agent_id]:
            return

        recent_feedback = self.user_feedback[agent_id][-10:]  # Last 10 feedback records

        total_weight = 0.0
        weighted_satisfaction = 0.0

        for i, feedback in enumerate(recent_feedback):
            weight = 1.0 + (i / len(recent_feedback)) * 0.3
            total_weight += weight

            # Extract satisfaction score (assuming 1-5 scale)
            satisfaction = feedback.get("satisfaction_score", 3.0)
            normalized_satisfaction = (satisfaction - 1.0) / 4.0  # Normalize to 0-1
            weighted_satisfaction += normalized_satisfaction * weight

        if total_weight > 0:
            new_satisfaction = weighted_satisfaction / total_weight
            if agent_id in self.agent_scores:
                self.agent_scores[agent_id]["user_satisfaction"] = new_satisfaction
                self._calculate_adaptive_score(agent_id)

    def update_context_performance(self, agent_id: str, context_type: str, performance_score: float):
        """
        Update performance for specific context types.

        Args:
            agent_id: Agent identifier
            context_type: Type of context (e.g., "data_analysis", "marketing")
            performance_score: Performance score for this context
        """
        if agent_id not in self.context_performance:
            self.context_performance[agent_id] = {}

        if context_type not in self.context_performance[agent_id]:
            self.context_performance[agent_id][context_type] = []

        self.context_performance[agent_id][context_type].append(performance_score)

        # Keep only recent scores
        if len(self.context_performance[agent_id][context_type]) > 20:
            self.context_performance[agent_id][context_type] = \
                self.context_performance[agent_id][context_type][-20:]

    def update_capability_performance(self, agent_id: str, capability: str, performance_score: float):
        """
        Update performance for specific capabilities.

        Args:
            agent_id: Agent identifier
            capability: Capability name
            performance_score: Performance score for this capability
        """
        if agent_id not in self.capability_performance:
            self.capability_performance[agent_id] = {}

        if capability not in self.capability_performance[agent_id]:
            self.capability_performance[agent_id][capability] = []

        self.capability_performance[agent_id][capability].append(performance_score)

        # Keep only recent scores
        if len(self.capability_performance[agent_id][capability]) > 20:
            self.capability_performance[agent_id][capability] = \
                self.capability_performance[agent_id][capability][-20:]

    def _calculate_adaptive_score(self, agent_id: str):
        """Calculate the adaptive score that learns from all metrics."""
        if agent_id not in self.agent_scores:
            return

        scores = self.agent_scores[agent_id]

        # Base adaptive score from weighted combination
        adaptive_score = 0.0
        total_weight = 0.0

        for metric, weight in self.scoring_weights.items():
            if metric in scores:
                adaptive_score += scores[metric] * weight
                total_weight += weight

        if total_weight > 0:
            adaptive_score /= total_weight

        # Apply context-specific adjustments
        context_bonus = self._calculate_context_bonus(agent_id)
        capability_bonus = self._calculate_capability_bonus(agent_id)
        collaboration_bonus = self._calculate_collaboration_bonus(agent_id)

        # Combine all factors
        final_score = adaptive_score + context_bonus + capability_bonus + collaboration_bonus
        final_score = max(0.0, min(1.0, final_score))  # Clamp to 0-1

        self.agent_scores[agent_id]["adaptive_score"] = final_score

    def _calculate_context_bonus(self, agent_id: str) -> float:
        """Calculate bonus based on context-specific performance."""
        if agent_id not in self.context_performance:
            return 0.0

        total_bonus = 0.0
        context_count = 0

        for context_type, scores in self.context_performance[agent_id].items():
            if scores:
                avg_score = sum(scores) / len(scores)
                if avg_score > 0.8:  # High performance threshold
                    total_bonus += 0.05
                context_count += 1

        return total_bonus / max(1, context_count)

    def _calculate_capability_bonus(self, agent_id: str) -> float:
        """Calculate bonus based on capability-specific performance."""
        if agent_id not in self.capability_performance:
            return 0.0

        total_bonus = 0.0
        capability_count = 0

        for capability, scores in self.capability_performance[agent_id].items():
            if scores:
                avg_score = sum(scores) / len(scores)
                if avg_score > 0.8:  # High performance threshold
                    total_bonus += 0.03
                capability_count += 1

        return total_bonus / max(1, capability_count)

    def _calculate_collaboration_bonus(self, agent_id: str) -> float:
        """Calculate bonus based on collaboration performance."""
        if agent_id not in self.collaboration_scores:
            return 0.0

        collaboration_scores = list(self.collaboration_scores[agent_id].values())
        if not collaboration_scores:
            return 0.0

        avg_collaboration = sum(collaboration_scores) / len(collaboration_scores)
        return (avg_collaboration - 0.5) * 0.1  # Bonus/penalty based on collaboration

    def get_agent_score(self, agent_id: str, context: Dict[str, Any] = None) -> float:
        """
        Get the current adaptive score for an agent.

        Args:
            agent_id: Agent identifier
            context: Optional context for context-specific scoring

        Returns:
            Adaptive score for the agent
        """
        if agent_id not in self.agent_scores:
            return 0.5  # Default score

        base_score = self.agent_scores[agent_id].get("adaptive_score", 0.5)

        # Apply context-specific adjustments if context is provided
        if context:
            context_adjustment = self._get_context_adjustment(agent_id, context)
            base_score += context_adjustment

        return max(0.0, min(1.0, base_score))

    def _get_context_adjustment(self, agent_id: str, context: Dict[str, Any]) -> float:
        """Get context-specific score adjustment."""
        adjustment = 0.0

        # Check for context type performance
        context_type = context.get("context_type")
        if context_type and agent_id in self.context_performance:
            if context_type in self.context_performance[agent_id]:
                scores = self.context_performance[agent_id][context_type]
                if scores:
                    avg_score = sum(scores) / len(scores)
                    adjustment += (avg_score - 0.5) * 0.1

        # Check for capability performance
        required_capabilities = context.get("required_capabilities", set())
        if required_capabilities and agent_id in self.capability_performance:
            capability_adjustments = []
            for capability in required_capabilities:
                if capability in self.capability_performance[agent_id]:
                    scores = self.capability_performance[agent_id][capability]
                    if scores:
                        avg_score = sum(scores) / len(scores)
                        capability_adjustments.append((avg_score - 0.5) * 0.05)

            if capability_adjustments:
                adjustment += sum(capability_adjustments) / len(capability_adjustments)

        return adjustment

    def get_top_agents(self, available_agents: List["AgentMetadata"],
                      context: Dict[str, Any] = None, limit: int = 5) -> List[Tuple["AgentMetadata", float]]:
        """
        Get top-scoring agents for a given context.

        Args:
            available_agents: List of available agent metadata
            context: Optional context for scoring
            limit: Maximum number of agents to return

        Returns:
            List of (agent_metadata, score) tuples sorted by score
        """
        scored_agents = []

        for agent in available_agents:
            score = self.get_agent_score(agent.persona_id, context)
            scored_agents.append((agent, score))

        # Sort by score (highest first)
        scored_agents.sort(key=lambda x: x[1], reverse=True)

        return scored_agents[:limit]

    def get_scoring_statistics(self) -> Dict[str, Any]:
        """Get statistics about the scoring system."""
        stats = {
            "total_agents": len(self.agent_scores),
            "total_performance_records": sum(len(records) for records in self.performance_history.values()),
            "total_feedback_records": sum(len(records) for records in self.user_feedback.values()),
            "scoring_weights": self.scoring_weights.copy()
        }

        # Agent-specific stats
        agent_stats = {}
        for agent_id, scores in self.agent_scores.items():
            agent_stats[agent_id] = {
                "adaptive_score": scores.get("adaptive_score", 0.5),
                "performance_records": len(self.performance_history.get(agent_id, [])),
                "feedback_records": len(self.user_feedback.get(agent_id, []))
            }

        stats["agent_details"] = agent_stats
        return stats


class ConfigurableBehaviorManager:
    """
    Comprehensive configuration system that allows all routing behavior
    to be modified through configuration files without code changes.
    """

    def __init__(self, config_path: Path):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.behavior_rules: Dict[str, Any] = {}
        self.routing_policies: Dict[str, Any] = {}
        self.scoring_weights: Dict[str, float] = {}
        self.thresholds: Dict[str, float] = {}
        self.feature_flags: Dict[str, bool] = {}
        self.custom_algorithms: Dict[str, Dict[str, Any]] = {}

        # Default configuration
        self._initialize_default_config()

        # Load configuration from file
        self.load_configuration()

    def _initialize_default_config(self):
        """Initialize default configuration values."""
        self.behavior_rules = {
            "intent_detection": {
                "use_learning_system": True,
                "fallback_to_static": True,
                "confidence_threshold": 0.6,
                "learning_rate": 0.1,
                "pattern_weight_decay": 0.95
            },
            "agent_scoring": {
                "use_adaptive_scoring": True,
                "performance_weight": 0.3,
                "user_satisfaction_weight": 0.25,
                "response_time_weight": 0.15,
                "success_rate_weight": 0.2,
                "collaboration_weight": 0.1,
                "context_bonus_enabled": True,
                "capability_bonus_enabled": True
            },
            "routing_strategy": {
                "prefer_single_agent": False,
                "collaboration_threshold": 0.7,
                "max_collaborating_agents": 3,
                "fallback_to_concierge": True,
                "use_user_preferences": True,
                "consider_agent_load": False
            },
            "capability_inference": {
                "use_dynamic_inference": True,
                "learn_from_descriptions": True,
                "confidence_threshold": 0.5,
                "pattern_reinforcement": True,
                "semantic_analysis_enabled": True
            }
        }

        self.routing_policies = {
            "default_strategy": "intent_based",
            "fallback_strategy": "capability_based",
            "emergency_fallback": "concierge",
            "collaboration_triggers": {
                "complexity_threshold": "high",
                "keyword_triggers": [
                    "comprehensive", "detailed", "multi-step",
                    "analyze and create", "research and develop"
                ],
                "capability_count_threshold": 3
            },
            "quality_control": {
                "minimum_confidence": 0.4,
                "retry_threshold": 0.6,
                "escalation_threshold": 0.3,
                "max_retries": 2
            }
        }

        self.thresholds = {
            "intent_confidence_min": 0.3,
            "agent_score_min": 0.2,
            "collaboration_score_min": 0.5,
            "performance_threshold": 0.7,
            "user_satisfaction_min": 0.6,
            "response_time_max": 10.0,
            "success_rate_min": 0.8
        }

        self.feature_flags = {
            "enable_learning": True,
            "enable_adaptive_scoring": True,
            "enable_collaboration": True,
            "enable_user_personalization": True,
            "enable_context_awareness": True,
            "enable_performance_tracking": True,
            "enable_hot_reload": True,
            "enable_custom_algorithms": True
        }

    def load_configuration(self):
        """Load configuration from YAML file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)

                # Merge with defaults
                self._merge_config(file_config)
                logger.info(f"Configuration loaded from {self.config_path}")
            else:
                logger.warning(f"Configuration file not found: {self.config_path}, using defaults")

        except Exception as e:
            logger.error(f"Error loading configuration: {e}, using defaults")

    def _merge_config(self, file_config: Dict[str, Any]):
        """Merge file configuration with defaults."""
        # Update behavior rules
        if "behavior_rules" in file_config:
            self._deep_merge(self.behavior_rules, file_config["behavior_rules"])

        # Update routing policies
        if "routing_policies" in file_config:
            self._deep_merge(self.routing_policies, file_config["routing_policies"])

        # Update thresholds
        if "thresholds" in file_config:
            self.thresholds.update(file_config["thresholds"])

        # Update feature flags
        if "feature_flags" in file_config:
            self.feature_flags.update(file_config["feature_flags"])

        # Load custom algorithms
        if "custom_algorithms" in file_config:
            self.custom_algorithms = file_config["custom_algorithms"]

    def _deep_merge(self, target: Dict[str, Any], source: Dict[str, Any]):
        """Deep merge two dictionaries."""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value

    def get_behavior_setting(self, category: str, setting: str, default: Any = None) -> Any:
        """Get a behavior setting value."""
        return self.behavior_rules.get(category, {}).get(setting, default)

    def get_routing_policy(self, policy: str, default: Any = None) -> Any:
        """Get a routing policy value."""
        return self.routing_policies.get(policy, default)

    def get_threshold(self, threshold: str, default: float = 0.5) -> float:
        """Get a threshold value."""
        return self.thresholds.get(threshold, default)

    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled."""
        return self.feature_flags.get(feature, False)

    def get_custom_algorithm_config(self, algorithm_name: str) -> Dict[str, Any]:
        """Get configuration for a custom algorithm."""
        return self.custom_algorithms.get(algorithm_name, {})

    def update_behavior_setting(self, category: str, setting: str, value: Any):
        """Update a behavior setting at runtime."""
        if category not in self.behavior_rules:
            self.behavior_rules[category] = {}
        self.behavior_rules[category][setting] = value
        logger.info(f"Updated behavior setting: {category}.{setting} = {value}")

    def update_threshold(self, threshold: str, value: float):
        """Update a threshold value at runtime."""
        self.thresholds[threshold] = value
        logger.info(f"Updated threshold: {threshold} = {value}")

    def toggle_feature(self, feature: str, enabled: bool = None):
        """Toggle a feature flag."""
        if enabled is None:
            enabled = not self.feature_flags.get(feature, False)
        self.feature_flags[feature] = enabled
        logger.info(f"Feature {feature} {'enabled' if enabled else 'disabled'}")

    def reload_configuration(self):
        """Reload configuration from file."""
        logger.info("Reloading configuration...")
        self._initialize_default_config()
        self.load_configuration()

    def get_intent_detection_config(self) -> Dict[str, Any]:
        """Get intent detection configuration."""
        return self.behavior_rules.get("intent_detection", {})

    def get_agent_scoring_config(self) -> Dict[str, Any]:
        """Get agent scoring configuration."""
        return self.behavior_rules.get("agent_scoring", {})

    def get_routing_strategy_config(self) -> Dict[str, Any]:
        """Get routing strategy configuration."""
        return self.behavior_rules.get("routing_strategy", {})

    def get_capability_inference_config(self) -> Dict[str, Any]:
        """Get capability inference configuration."""
        return self.behavior_rules.get("capability_inference", {})

    def should_use_learning_system(self) -> bool:
        """Check if learning system should be used for intent detection."""
        return self.get_behavior_setting("intent_detection", "use_learning_system", True)

    def should_use_adaptive_scoring(self) -> bool:
        """Check if adaptive scoring should be used."""
        return self.get_behavior_setting("agent_scoring", "use_adaptive_scoring", True)

    def should_enable_collaboration(self) -> bool:
        """Check if collaboration should be enabled."""
        return self.is_feature_enabled("enable_collaboration")

    def get_collaboration_threshold(self) -> float:
        """Get collaboration threshold."""
        return self.get_behavior_setting("routing_strategy", "collaboration_threshold", 0.7)

    def get_max_collaborating_agents(self) -> int:
        """Get maximum number of collaborating agents."""
        return self.get_behavior_setting("routing_strategy", "max_collaborating_agents", 3)

    def get_scoring_weights(self) -> Dict[str, float]:
        """Get scoring weights for agent evaluation."""
        scoring_config = self.get_agent_scoring_config()
        return {
            "performance_score": scoring_config.get("performance_weight", 0.3),
            "user_satisfaction": scoring_config.get("user_satisfaction_weight", 0.25),
            "response_time": scoring_config.get("response_time_weight", 0.15),
            "success_rate": scoring_config.get("success_rate_weight", 0.2),
            "collaboration_ability": scoring_config.get("collaboration_weight", 0.1)
        }

    def export_configuration(self) -> Dict[str, Any]:
        """Export current configuration."""
        return {
            "behavior_rules": self.behavior_rules,
            "routing_policies": self.routing_policies,
            "thresholds": self.thresholds,
            "feature_flags": self.feature_flags,
            "custom_algorithms": self.custom_algorithms
        }

    def save_configuration(self, file_path: Path = None):
        """Save current configuration to file."""
        save_path = file_path or self.config_path
        try:
            config_to_save = self.export_configuration()
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_to_save, f, default_flow_style=False, indent=2)
            logger.info(f"Configuration saved to {save_path}")
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")

    def validate_configuration(self) -> List[str]:
        """Validate current configuration and return any issues."""
        issues = []

        # Validate thresholds
        for threshold, value in self.thresholds.items():
            if not isinstance(value, (int, float)) or value < 0 or value > 1:
                issues.append(f"Invalid threshold value for {threshold}: {value}")

        # Validate scoring weights
        scoring_weights = self.get_scoring_weights()
        weight_sum = sum(scoring_weights.values())
        if abs(weight_sum - 1.0) > 0.1:
            issues.append(f"Scoring weights sum to {weight_sum}, should be close to 1.0")

        # Validate feature flags
        for flag, value in self.feature_flags.items():
            if not isinstance(value, bool):
                issues.append(f"Feature flag {flag} should be boolean, got {type(value)}")

        return issues


class PerformanceLearningOptimizer:
    """
    Performance learning and optimization system that tracks routing performance
    and automatically optimizes routing decisions based on success patterns.
    """

    def __init__(self):
        self.performance_history: List[Dict[str, Any]] = []
        self.success_patterns: Dict[str, List[Dict[str, Any]]] = {}  # pattern_type -> [patterns]
        self.failure_patterns: Dict[str, List[Dict[str, Any]]] = {}  # pattern_type -> [patterns]
        self.optimization_rules: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, Dict[str, float]] = {}  # metric_type -> {period: value}
        self.trend_analysis: Dict[str, List[float]] = {}  # metric -> [values over time]
        self.optimization_history: List[Dict[str, Any]] = []

        # Learning parameters
        self.learning_window_size = 100  # Number of recent records to consider
        self.pattern_confidence_threshold = 0.7
        self.optimization_trigger_threshold = 0.1  # Performance improvement threshold
        self.min_pattern_occurrences = 5

    def record_routing_performance(self, routing_context: Dict[str, Any],
                                 outcome: Dict[str, Any], success: bool):
        """
        Record routing performance for learning.

        Args:
            routing_context: Context used for routing decision
            outcome: Outcome of the routing
            success: Whether the routing was successful
        """
        performance_record = {
            "timestamp": datetime.now().isoformat(),
            "context": routing_context.copy(),
            "outcome": outcome.copy(),
            "success": success,
            "response_time": outcome.get("response_time", 0.0),
            "user_satisfaction": outcome.get("user_satisfaction", 0.5),
            "agent_used": outcome.get("agent_used"),
            "strategy_used": outcome.get("strategy_used"),
            "confidence_score": outcome.get("confidence_score", 0.5)
        }

        self.performance_history.append(performance_record)

        # Keep only recent history
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-1000:]

        # Extract patterns
        self._extract_patterns(performance_record)

        # Update metrics
        self._update_performance_metrics()

        # Check for optimization opportunities
        self._check_optimization_opportunities()

    def _extract_patterns(self, record: Dict[str, Any]):
        """Extract patterns from performance record."""
        context = record["context"]
        success = record["success"]

        # Intent-based patterns
        intent = context.get("detected_intent")
        if intent:
            pattern_key = f"intent_{intent}"
            pattern_data = {
                "intent": intent,
                "complexity": context.get("complexity"),
                "agent_used": record["outcome"].get("agent_used"),
                "strategy_used": record["outcome"].get("strategy_used"),
                "success": success,
                "response_time": record["response_time"],
                "confidence": record["confidence_score"],
                "timestamp": record["timestamp"]
            }

            if success:
                if pattern_key not in self.success_patterns:
                    self.success_patterns[pattern_key] = []
                self.success_patterns[pattern_key].append(pattern_data)
            else:
                if pattern_key not in self.failure_patterns:
                    self.failure_patterns[pattern_key] = []
                self.failure_patterns[pattern_key].append(pattern_data)

        # Agent-based patterns
        agent_used = record["outcome"].get("agent_used")
        if agent_used:
            pattern_key = f"agent_{agent_used}"
            pattern_data = {
                "agent": agent_used,
                "intent": context.get("detected_intent"),
                "complexity": context.get("complexity"),
                "success": success,
                "response_time": record["response_time"],
                "user_satisfaction": record["user_satisfaction"],
                "timestamp": record["timestamp"]
            }

            if success:
                if pattern_key not in self.success_patterns:
                    self.success_patterns[pattern_key] = []
                self.success_patterns[pattern_key].append(pattern_data)
            else:
                if pattern_key not in self.failure_patterns:
                    self.failure_patterns[pattern_key] = []
                self.failure_patterns[pattern_key].append(pattern_data)

        # Strategy-based patterns
        strategy_used = record["outcome"].get("strategy_used")
        if strategy_used:
            pattern_key = f"strategy_{strategy_used}"
            pattern_data = {
                "strategy": strategy_used,
                "intent": context.get("detected_intent"),
                "complexity": context.get("complexity"),
                "collaboration_needed": context.get("collaboration_needed"),
                "success": success,
                "response_time": record["response_time"],
                "timestamp": record["timestamp"]
            }

            if success:
                if pattern_key not in self.success_patterns:
                    self.success_patterns[pattern_key] = []
                self.success_patterns[pattern_key].append(pattern_data)
            else:
                if pattern_key not in self.failure_patterns:
                    self.failure_patterns[pattern_key] = []
                self.failure_patterns[pattern_key].append(pattern_data)

    def _update_performance_metrics(self):
        """Update performance metrics based on recent history."""
        if not self.performance_history:
            return

        recent_records = self.performance_history[-self.learning_window_size:]

        # Calculate overall metrics
        total_records = len(recent_records)
        successful_records = [r for r in recent_records if r["success"]]

        # Success rate
        success_rate = len(successful_records) / total_records if total_records > 0 else 0.0

        # Average response time
        response_times = [r["response_time"] for r in recent_records if r["response_time"] > 0]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0.0

        # Average user satisfaction
        satisfactions = [r["user_satisfaction"] for r in recent_records if r["user_satisfaction"] > 0]
        avg_satisfaction = sum(satisfactions) / len(satisfactions) if satisfactions else 0.5

        # Average confidence
        confidences = [r["confidence_score"] for r in recent_records if r["confidence_score"] > 0]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.5

        # Update metrics
        current_time = datetime.now().isoformat()
        self.performance_metrics[current_time] = {
            "success_rate": success_rate,
            "avg_response_time": avg_response_time,
            "avg_satisfaction": avg_satisfaction,
            "avg_confidence": avg_confidence,
            "total_records": total_records
        }

        # Update trend analysis
        for metric in ["success_rate", "avg_response_time", "avg_satisfaction", "avg_confidence"]:
            if metric not in self.trend_analysis:
                self.trend_analysis[metric] = []

            self.trend_analysis[metric].append(self.performance_metrics[current_time][metric])

            # Keep only recent trend data
            if len(self.trend_analysis[metric]) > 50:
                self.trend_analysis[metric] = self.trend_analysis[metric][-50:]

    def _check_optimization_opportunities(self):
        """Check for optimization opportunities based on patterns."""
        if len(self.performance_history) < 20:  # Need minimum data
            return

        # Analyze trends
        opportunities = []

        for metric, values in self.trend_analysis.items():
            if len(values) >= 10:
                recent_avg = sum(values[-5:]) / 5
                older_avg = sum(values[-10:-5]) / 5

                if metric in ["success_rate", "avg_satisfaction", "avg_confidence"]:
                    # Higher is better
                    if older_avg - recent_avg > self.optimization_trigger_threshold:
                        opportunities.append({
                            "type": "declining_performance",
                            "metric": metric,
                            "recent_avg": recent_avg,
                            "older_avg": older_avg,
                            "decline": older_avg - recent_avg
                        })
                elif metric == "avg_response_time":
                    # Lower is better
                    if recent_avg - older_avg > self.optimization_trigger_threshold:
                        opportunities.append({
                            "type": "increasing_response_time",
                            "metric": metric,
                            "recent_avg": recent_avg,
                            "older_avg": older_avg,
                            "increase": recent_avg - older_avg
                        })

        # Generate optimization rules
        for opportunity in opportunities:
            self._generate_optimization_rule(opportunity)

    def _generate_optimization_rule(self, opportunity: Dict[str, Any]):
        """Generate optimization rule based on opportunity."""
        rule = {
            "id": f"opt_{len(self.optimization_rules)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "type": opportunity["type"],
            "metric": opportunity["metric"],
            "created": datetime.now().isoformat(),
            "confidence": 0.7,  # Default confidence
            "applied": False
        }

        if opportunity["type"] == "declining_performance":
            # Analyze successful patterns for this metric
            successful_patterns = self._analyze_successful_patterns_for_metric(opportunity["metric"])
            if successful_patterns:
                rule["recommendation"] = {
                    "action": "prefer_successful_patterns",
                    "patterns": successful_patterns[:3],  # Top 3 patterns
                    "description": f"Prefer patterns that historically perform well for {opportunity['metric']}"
                }

        elif opportunity["type"] == "increasing_response_time":
            # Find fastest agents/strategies
            fast_patterns = self._find_fast_response_patterns()
            if fast_patterns:
                rule["recommendation"] = {
                    "action": "prefer_fast_patterns",
                    "patterns": fast_patterns[:3],
                    "description": "Prefer agents/strategies with faster response times"
                }

        self.optimization_rules.append(rule)
        logger.info(f"Generated optimization rule: {rule['id']} for {opportunity['type']}")

    def _analyze_successful_patterns_for_metric(self, metric: str) -> List[Dict[str, Any]]:
        """Analyze successful patterns for a specific metric."""
        successful_patterns = []

        for pattern_key, patterns in self.success_patterns.items():
            if len(patterns) >= self.min_pattern_occurrences:
                # Calculate average metric value for this pattern
                metric_values = []
                for pattern in patterns:
                    if metric == "success_rate":
                        metric_values.append(1.0 if pattern["success"] else 0.0)
                    elif metric == "avg_satisfaction":
                        metric_values.append(pattern.get("user_satisfaction", 0.5))
                    elif metric == "avg_confidence":
                        metric_values.append(pattern.get("confidence", 0.5))
                    elif metric == "avg_response_time":
                        metric_values.append(pattern.get("response_time", 0.0))

                if metric_values:
                    avg_value = sum(metric_values) / len(metric_values)
                    successful_patterns.append({
                        "pattern_key": pattern_key,
                        "avg_metric_value": avg_value,
                        "occurrences": len(patterns),
                        "pattern_data": patterns[0]  # Representative pattern
                    })

        # Sort by metric value (higher is better for most metrics)
        reverse_sort = metric != "avg_response_time"
        successful_patterns.sort(key=lambda x: x["avg_metric_value"], reverse=reverse_sort)

        return successful_patterns

    def _find_fast_response_patterns(self) -> List[Dict[str, Any]]:
        """Find patterns with fast response times."""
        fast_patterns = []

        for pattern_key, patterns in self.success_patterns.items():
            if len(patterns) >= self.min_pattern_occurrences:
                response_times = [p.get("response_time", 0.0) for p in patterns if p.get("response_time", 0.0) > 0]
                if response_times:
                    avg_response_time = sum(response_times) / len(response_times)
                    fast_patterns.append({
                        "pattern_key": pattern_key,
                        "avg_response_time": avg_response_time,
                        "occurrences": len(patterns),
                        "pattern_data": patterns[0]
                    })

        # Sort by response time (lower is better)
        fast_patterns.sort(key=lambda x: x["avg_response_time"])

        return fast_patterns

    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get current optimization recommendations."""
        return [rule for rule in self.optimization_rules if not rule["applied"]]

    def apply_optimization_rule(self, rule_id: str) -> bool:
        """
        Apply an optimization rule.

        Args:
            rule_id: ID of the rule to apply

        Returns:
            True if rule was applied successfully
        """
        for rule in self.optimization_rules:
            if rule["id"] == rule_id:
                rule["applied"] = True
                rule["applied_at"] = datetime.now().isoformat()

                self.optimization_history.append({
                    "rule_id": rule_id,
                    "applied_at": rule["applied_at"],
                    "rule": rule.copy()
                })

                logger.info(f"Applied optimization rule: {rule_id}")
                return True

        return False

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        if not self.performance_metrics:
            return {"status": "no_data"}

        latest_metrics = list(self.performance_metrics.values())[-1]

        # Calculate trends
        trends = {}
        for metric, values in self.trend_analysis.items():
            if len(values) >= 2:
                recent = values[-1]
                previous = values[-2]
                change = recent - previous
                trends[metric] = {
                    "current": recent,
                    "change": change,
                    "trend": "improving" if change > 0 else "declining" if change < 0 else "stable"
                }

        return {
            "latest_metrics": latest_metrics,
            "trends": trends,
            "total_records": len(self.performance_history),
            "success_patterns_count": sum(len(patterns) for patterns in self.success_patterns.values()),
            "failure_patterns_count": sum(len(patterns) for patterns in self.failure_patterns.values()),
            "optimization_rules_count": len(self.optimization_rules),
            "pending_optimizations": len(self.get_optimization_recommendations())
        }

    def get_pattern_analysis(self) -> Dict[str, Any]:
        """Get pattern analysis."""
        analysis = {
            "success_patterns": {},
            "failure_patterns": {},
            "pattern_insights": []
        }

        # Analyze success patterns
        for pattern_key, patterns in self.success_patterns.items():
            if len(patterns) >= self.min_pattern_occurrences:
                analysis["success_patterns"][pattern_key] = {
                    "count": len(patterns),
                    "avg_response_time": sum(p.get("response_time", 0) for p in patterns) / len(patterns),
                    "avg_satisfaction": sum(p.get("user_satisfaction", 0.5) for p in patterns) / len(patterns)
                }

        # Analyze failure patterns
        for pattern_key, patterns in self.failure_patterns.items():
            if len(patterns) >= self.min_pattern_occurrences:
                analysis["failure_patterns"][pattern_key] = {
                    "count": len(patterns),
                    "common_issues": self._analyze_failure_reasons(patterns)
                }

        return analysis

    def _analyze_failure_reasons(self, failure_patterns: List[Dict[str, Any]]) -> List[str]:
        """Analyze common failure reasons."""
        reasons = []

        # Analyze response times
        slow_responses = [p for p in failure_patterns if p.get("response_time", 0) > 5.0]
        if len(slow_responses) > len(failure_patterns) * 0.3:
            reasons.append("slow_response_time")

        # Analyze confidence scores
        low_confidence = [p for p in failure_patterns if p.get("confidence", 1.0) < 0.5]
        if len(low_confidence) > len(failure_patterns) * 0.3:
            reasons.append("low_confidence")

        return reasons


class RuntimeExtensibilityFramework:
    """
    Framework for hot-reload capabilities and runtime agent registration
    without system restarts.
    """

    def __init__(self, router_instance):
        self.router = router_instance
        self.watched_paths: Set[Path] = set()
        self.file_watchers: Dict[str, Any] = {}
        self.hot_reload_enabled = True
        self.reload_callbacks: Dict[str, List[Callable]] = {}
        self.runtime_agents: Dict[str, Dict[str, Any]] = {}
        self.extension_points: Dict[str, List[Callable]] = {}
        self.plugin_registry: Dict[str, Any] = {}

        # Initialize file watching
        self._initialize_file_watching()

    def _initialize_file_watching(self):
        """Initialize file watching for hot reload."""
        try:
            # Watch configuration files
            config_dir = Path(__file__).parent.parent / "config"
            if config_dir.exists():
                self.watch_directory(config_dir, self._on_config_change)

            # Watch agent configurations
            agents_dir = Path(__file__).parent.parent.parent
            if agents_dir.exists():
                self.watch_directory(agents_dir, self._on_agent_change, pattern="*.yaml")

            logger.info("File watching initialized for hot reload")

        except Exception as e:
            logger.error(f"Error initializing file watching: {e}")

    def watch_directory(self, path: Path, callback: Callable, pattern: str = "*"):
        """
        Watch a directory for changes.

        Args:
            path: Directory path to watch
            callback: Callback function for changes
            pattern: File pattern to watch
        """
        try:
            self.watched_paths.add(path)

            # Register callback
            path_str = str(path)
            if path_str not in self.reload_callbacks:
                self.reload_callbacks[path_str] = []
            self.reload_callbacks[path_str].append(callback)

            logger.info(f"Watching directory: {path} with pattern: {pattern}")

        except Exception as e:
            logger.error(f"Error watching directory {path}: {e}")

    def _on_config_change(self, file_path: Path):
        """Handle configuration file changes."""
        try:
            logger.info(f"Configuration file changed: {file_path}")

            if self.hot_reload_enabled:
                # Reload configuration
                self.router.reload_configuration()

                # Trigger configuration reload callbacks
                self._trigger_callbacks("config_reload", {"file_path": file_path})

                logger.info("Configuration hot-reloaded successfully")

        except Exception as e:
            logger.error(f"Error handling config change: {e}")

    def _on_agent_change(self, file_path: Path):
        """Handle agent configuration changes."""
        try:
            logger.info(f"Agent configuration changed: {file_path}")

            if self.hot_reload_enabled and file_path.suffix in ['.yaml', '.yml']:
                # Extract persona ID from file path
                persona_id = self._extract_persona_id_from_path(file_path)

                if persona_id:
                    # Schedule async reload
                    asyncio.create_task(self._reload_agent_configuration(persona_id, file_path))

                    # Trigger agent reload callbacks
                    self._trigger_callbacks("agent_reload", {
                        "persona_id": persona_id,
                        "file_path": file_path
                    })

                    logger.info(f"Agent {persona_id} hot-reload scheduled")

        except Exception as e:
            logger.error(f"Error handling agent change: {e}")

    def _extract_persona_id_from_path(self, file_path: Path) -> Optional[str]:
        """Extract persona ID from file path."""
        try:
            # Look for patterns like "datagenius-concierge.yaml" or "concierge_agent.yaml"
            filename = file_path.stem

            # Common patterns
            if filename.startswith("datagenius-"):
                return filename
            elif filename.endswith("_agent"):
                return f"datagenius-{filename[:-6]}"  # Remove "_agent" suffix
            elif "agent" in filename:
                return f"datagenius-{filename.replace('_agent', '').replace('-agent', '')}"

            return None

        except Exception as e:
            logger.error(f"Error extracting persona ID from {file_path}: {e}")
            return None

    async def _reload_agent_configuration(self, persona_id: str, file_path: Path):
        """Reload agent configuration from file."""
        try:
            # Load new configuration
            with open(file_path, 'r', encoding='utf-8') as f:
                new_config = yaml.safe_load(f)

            # Update agent factory configuration
            if hasattr(self.router.agent_factory, 'update_agent_config'):
                self.router.agent_factory.update_agent_config(persona_id, new_config)

            # Re-extract metadata
            new_metadata = await self.router._extract_agent_metadata(persona_id)
            if new_metadata:
                self.router.discovered_agents[persona_id] = new_metadata

                # Update scoring system
                self.router.scoring_system.initialize_agent_scoring(new_metadata)

                # Update declaration system
                self.router.declaration_system.register_agent_declaration(persona_id, new_config)

                # Rebuild mappings
                await self.router._build_intent_mappings()

        except Exception as e:
            logger.error(f"Error reloading agent configuration for {persona_id}: {e}")

    def register_runtime_agent(self, persona_id: str, config: Dict[str, Any]) -> bool:
        """
        Register a new agent at runtime.

        Args:
            persona_id: Agent persona ID
            config: Agent configuration

        Returns:
            True if registration was successful
        """
        try:
            # Validate configuration
            if not self._validate_agent_config(config):
                logger.error(f"Invalid configuration for runtime agent: {persona_id}")
                return False

            # Store runtime agent
            self.runtime_agents[persona_id] = config

            # Register with agent factory
            if hasattr(self.router.agent_factory, 'register_agent_class'):
                self.router.agent_factory.register_agent_class(persona_id, config)

            # Extract metadata and register with systems
            asyncio.create_task(self._register_runtime_agent_async(persona_id, config))

            logger.info(f"Runtime agent registered: {persona_id}")
            return True

        except Exception as e:
            logger.error(f"Error registering runtime agent {persona_id}: {e}")
            return False

    async def _register_runtime_agent_async(self, persona_id: str, config: Dict[str, Any]):
        """Async registration of runtime agent."""
        try:
            # Extract metadata
            metadata = await self.router._extract_agent_metadata(persona_id)
            if metadata:
                self.router.discovered_agents[persona_id] = metadata

                # Initialize scoring
                self.router.scoring_system.initialize_agent_scoring(metadata)

                # Register declaration
                self.router.declaration_system.register_agent_declaration(persona_id, config)

                # Update mappings
                self.router.agent_mappings = self.router._load_dynamic_agent_mappings()
                await self.router._build_intent_mappings()

                # Trigger callbacks
                self._trigger_callbacks("runtime_agent_registered", {
                    "persona_id": persona_id,
                    "config": config
                })

        except Exception as e:
            logger.error(f"Error in async runtime agent registration for {persona_id}: {e}")

    def unregister_runtime_agent(self, persona_id: str) -> bool:
        """
        Unregister a runtime agent.

        Args:
            persona_id: Agent persona ID

        Returns:
            True if unregistration was successful
        """
        try:
            # Remove from runtime agents
            if persona_id in self.runtime_agents:
                del self.runtime_agents[persona_id]

            # Remove from discovered agents
            if persona_id in self.router.discovered_agents:
                del self.router.discovered_agents[persona_id]

            # Remove from agent mappings
            agent_type = self.router._extract_agent_type_from_persona(persona_id)
            if agent_type and agent_type in self.router.agent_mappings:
                del self.router.agent_mappings[agent_type]

            # Trigger callbacks
            self._trigger_callbacks("runtime_agent_unregistered", {
                "persona_id": persona_id
            })

            logger.info(f"Runtime agent unregistered: {persona_id}")
            return True

        except Exception as e:
            logger.error(f"Error unregistering runtime agent {persona_id}: {e}")
            return False

    def _validate_agent_config(self, config: Dict[str, Any]) -> bool:
        """Validate agent configuration."""
        required_fields = ["name", "description"]

        for field in required_fields:
            if field not in config:
                logger.error(f"Missing required field in agent config: {field}")
                return False

        return True

    def register_extension_point(self, name: str, callback: Callable):
        """
        Register an extension point callback.

        Args:
            name: Extension point name
            callback: Callback function
        """
        if name not in self.extension_points:
            self.extension_points[name] = []

        self.extension_points[name].append(callback)
        logger.info(f"Registered extension point: {name}")

    def trigger_extension_point(self, name: str, *args, **kwargs):
        """
        Trigger an extension point.

        Args:
            name: Extension point name
            *args: Positional arguments
            **kwargs: Keyword arguments
        """
        if name in self.extension_points:
            for callback in self.extension_points[name]:
                try:
                    callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Error in extension point {name}: {e}")

    def register_plugin(self, plugin_name: str, plugin_instance: Any):
        """
        Register a plugin.

        Args:
            plugin_name: Plugin name
            plugin_instance: Plugin instance
        """
        self.plugin_registry[plugin_name] = plugin_instance

        # Initialize plugin if it has an init method
        if hasattr(plugin_instance, 'initialize'):
            try:
                plugin_instance.initialize(self.router)
                logger.info(f"Plugin initialized: {plugin_name}")
            except Exception as e:
                logger.error(f"Error initializing plugin {plugin_name}: {e}")

        logger.info(f"Plugin registered: {plugin_name}")

    def unregister_plugin(self, plugin_name: str):
        """
        Unregister a plugin.

        Args:
            plugin_name: Plugin name
        """
        if plugin_name in self.plugin_registry:
            plugin_instance = self.plugin_registry[plugin_name]

            # Cleanup plugin if it has a cleanup method
            if hasattr(plugin_instance, 'cleanup'):
                try:
                    plugin_instance.cleanup()
                    logger.info(f"Plugin cleaned up: {plugin_name}")
                except Exception as e:
                    logger.error(f"Error cleaning up plugin {plugin_name}: {e}")

            del self.plugin_registry[plugin_name]
            logger.info(f"Plugin unregistered: {plugin_name}")

    def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]):
        """Trigger callbacks for an event type."""
        for callback_list in self.reload_callbacks.values():
            for callback in callback_list:
                try:
                    if hasattr(callback, '__name__') and event_type in callback.__name__:
                        callback(data)
                except Exception as e:
                    logger.error(f"Error in callback for {event_type}: {e}")

    def enable_hot_reload(self):
        """Enable hot reload functionality."""
        self.hot_reload_enabled = True
        logger.info("Hot reload enabled")

    def disable_hot_reload(self):
        """Disable hot reload functionality."""
        self.hot_reload_enabled = False
        logger.info("Hot reload disabled")

    def get_runtime_agents(self) -> Dict[str, Dict[str, Any]]:
        """Get all runtime agents."""
        return self.runtime_agents.copy()

    def get_watched_paths(self) -> List[str]:
        """Get all watched paths."""
        return [str(path) for path in self.watched_paths]

    def get_extension_points(self) -> List[str]:
        """Get all registered extension points."""
        return list(self.extension_points.keys())

    def get_plugins(self) -> List[str]:
        """Get all registered plugins."""
        return list(self.plugin_registry.keys())

    def get_framework_status(self) -> Dict[str, Any]:
        """Get framework status."""
        return {
            "hot_reload_enabled": self.hot_reload_enabled,
            "watched_paths_count": len(self.watched_paths),
            "runtime_agents_count": len(self.runtime_agents),
            "extension_points_count": len(self.extension_points),
            "plugins_count": len(self.plugin_registry),
            "callbacks_count": sum(len(callbacks) for callbacks in self.reload_callbacks.values())
        }


class AgentSelfDeclarationSystem:
    """
    System that allows agents to declare their own capabilities, routing preferences,
    and inference patterns through their configuration files.
    """

    def __init__(self):
        self.agent_declarations: Dict[str, Dict[str, Any]] = {}
        self.capability_declarations: Dict[str, Dict[str, Any]] = {}
        self.routing_preferences: Dict[str, Dict[str, Any]] = {}
        self.inference_patterns: Dict[str, Dict[str, Any]] = {}
        self.performance_expectations: Dict[str, Dict[str, Any]] = {}

    def register_agent_declaration(self, persona_id: str, config: Dict[str, Any]):
        """
        Register an agent's self-declaration from its configuration.

        Args:
            persona_id: Agent persona ID
            config: Agent configuration dictionary
        """
        try:
            # Extract self-declaration section
            self_declaration = config.get("self_declaration", {})

            if not self_declaration:
                # Create basic declaration from existing config
                self_declaration = self._create_basic_declaration(config)

            self.agent_declarations[persona_id] = self_declaration

            # Process capability declarations
            self._process_capability_declarations(persona_id, self_declaration)

            # Process routing preferences
            self._process_routing_preferences(persona_id, self_declaration)

            # Process inference patterns
            self._process_inference_patterns(persona_id, self_declaration)

            # Process performance expectations
            self._process_performance_expectations(persona_id, self_declaration)

            logger.info(f"Registered self-declaration for agent: {persona_id}")

        except Exception as e:
            logger.error(f"Error registering agent declaration for {persona_id}: {e}")

    def _create_basic_declaration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Create basic declaration from existing configuration."""
        return {
            "capabilities": {
                "declared_capabilities": config.get("capabilities", []),
                "confidence_levels": {},
                "specialization_areas": config.get("specialization_areas", []),
                "supported_formats": config.get("output_formats", ["text"])
            },
            "routing_preferences": {
                "preferred_complexity_levels": config.get("complexity_levels", ["medium"]),
                "collaboration_preference": config.get("collaboration_score", 0.5),
                "primary_agent_capable": config.get("is_primary_agent", False),
                "fallback_agent": config.get("fallback_agent", False)
            },
            "performance_expectations": {
                "expected_response_time": config.get("average_response_time", 2.0),
                "expected_success_rate": config.get("success_rate", 0.95),
                "expected_satisfaction": config.get("user_satisfaction_score", 0.85)
            },
            "inference_patterns": {
                "intent_keywords": [],
                "capability_indicators": [],
                "context_preferences": []
            }
        }

    def _process_capability_declarations(self, persona_id: str, declaration: Dict[str, Any]):
        """Process capability declarations."""
        capabilities = declaration.get("capabilities", {})

        self.capability_declarations[persona_id] = {
            "declared_capabilities": capabilities.get("declared_capabilities", []),
            "confidence_levels": capabilities.get("confidence_levels", {}),
            "specialization_areas": capabilities.get("specialization_areas", []),
            "supported_formats": capabilities.get("supported_formats", ["text"]),
            "capability_descriptions": capabilities.get("capability_descriptions", {}),
            "capability_examples": capabilities.get("capability_examples", {}),
            "capability_limitations": capabilities.get("capability_limitations", {}),
            "required_tools": capabilities.get("required_tools", []),
            "optional_tools": capabilities.get("optional_tools", [])
        }

    def _process_routing_preferences(self, persona_id: str, declaration: Dict[str, Any]):
        """Process routing preferences."""
        routing = declaration.get("routing_preferences", {})

        self.routing_preferences[persona_id] = {
            "preferred_complexity_levels": routing.get("preferred_complexity_levels", ["medium"]),
            "collaboration_preference": routing.get("collaboration_preference", 0.5),
            "primary_agent_capable": routing.get("primary_agent_capable", False),
            "fallback_agent": routing.get("fallback_agent", False),
            "preferred_user_types": routing.get("preferred_user_types", []),
            "avoid_contexts": routing.get("avoid_contexts", []),
            "priority_intents": routing.get("priority_intents", []),
            "load_balancing_weight": routing.get("load_balancing_weight", 1.0),
            "concurrent_request_limit": routing.get("concurrent_request_limit", 10),
            "preferred_time_slots": routing.get("preferred_time_slots", [])
        }

    def _process_inference_patterns(self, persona_id: str, declaration: Dict[str, Any]):
        """Process inference patterns."""
        inference = declaration.get("inference_patterns", {})

        self.inference_patterns[persona_id] = {
            "intent_keywords": inference.get("intent_keywords", []),
            "capability_indicators": inference.get("capability_indicators", []),
            "context_preferences": inference.get("context_preferences", []),
            "negative_indicators": inference.get("negative_indicators", []),
            "confidence_boosters": inference.get("confidence_boosters", []),
            "pattern_weights": inference.get("pattern_weights", {}),
            "semantic_patterns": inference.get("semantic_patterns", []),
            "user_intent_mappings": inference.get("user_intent_mappings", {})
        }

    def _process_performance_expectations(self, persona_id: str, declaration: Dict[str, Any]):
        """Process performance expectations."""
        performance = declaration.get("performance_expectations", {})

        self.performance_expectations[persona_id] = {
            "expected_response_time": performance.get("expected_response_time", 2.0),
            "expected_success_rate": performance.get("expected_success_rate", 0.95),
            "expected_satisfaction": performance.get("expected_satisfaction", 0.85),
            "performance_metrics": performance.get("performance_metrics", {}),
            "quality_thresholds": performance.get("quality_thresholds", {}),
            "optimization_targets": performance.get("optimization_targets", []),
            "monitoring_preferences": performance.get("monitoring_preferences", {})
        }

    def get_agent_capabilities(self, persona_id: str) -> Dict[str, Any]:
        """Get declared capabilities for an agent."""
        return self.capability_declarations.get(persona_id, {})

    def get_routing_preferences(self, persona_id: str) -> Dict[str, Any]:
        """Get routing preferences for an agent."""
        return self.routing_preferences.get(persona_id, {})

    def get_inference_patterns(self, persona_id: str) -> Dict[str, Any]:
        """Get inference patterns for an agent."""
        return self.inference_patterns.get(persona_id, {})

    def get_performance_expectations(self, persona_id: str) -> Dict[str, Any]:
        """Get performance expectations for an agent."""
        return self.performance_expectations.get(persona_id, {})

    def should_route_to_agent(self, persona_id: str, context: Dict[str, Any]) -> Tuple[bool, float, str]:
        """
        Determine if an agent should handle a request based on its declarations.

        Args:
            persona_id: Agent persona ID
            context: Routing context

        Returns:
            Tuple of (should_route, confidence, reasoning)
        """
        if persona_id not in self.agent_declarations:
            return False, 0.0, "No declaration found"

        routing_prefs = self.routing_preferences.get(persona_id, {})
        inference_patterns = self.inference_patterns.get(persona_id, {})
        capabilities = self.capability_declarations.get(persona_id, {})

        confidence = 0.0
        reasons = []

        # Check complexity preference
        complexity = context.get("complexity", "medium")
        preferred_complexity = routing_prefs.get("preferred_complexity_levels", ["medium"])
        if complexity in preferred_complexity:
            confidence += 0.3
            reasons.append(f"complexity match ({complexity})")

        # Check intent keywords
        message = context.get("message_content", "").lower()
        intent_keywords = inference_patterns.get("intent_keywords", [])
        keyword_matches = sum(1 for keyword in intent_keywords if keyword.lower() in message)
        if keyword_matches > 0:
            keyword_confidence = min(keyword_matches * 0.1, 0.4)
            confidence += keyword_confidence
            reasons.append(f"{keyword_matches} keyword matches")

        # Check capability indicators
        capability_indicators = inference_patterns.get("capability_indicators", [])
        capability_matches = sum(1 for indicator in capability_indicators if indicator.lower() in message)
        if capability_matches > 0:
            capability_confidence = min(capability_matches * 0.15, 0.3)
            confidence += capability_confidence
            reasons.append(f"{capability_matches} capability indicators")

        # Check negative indicators
        negative_indicators = inference_patterns.get("negative_indicators", [])
        negative_matches = sum(1 for indicator in negative_indicators if indicator.lower() in message)
        if negative_matches > 0:
            confidence -= min(negative_matches * 0.2, 0.5)
            reasons.append(f"{negative_matches} negative indicators")

        # Check required capabilities
        required_capabilities = context.get("required_capabilities", set())
        declared_capabilities = set(capabilities.get("declared_capabilities", []))
        capability_overlap = len(required_capabilities.intersection(declared_capabilities))
        if required_capabilities and capability_overlap > 0:
            overlap_confidence = (capability_overlap / len(required_capabilities)) * 0.4
            confidence += overlap_confidence
            reasons.append(f"capability overlap: {capability_overlap}/{len(required_capabilities)}")

        # Apply confidence boosters
        confidence_boosters = inference_patterns.get("confidence_boosters", [])
        for booster in confidence_boosters:
            if booster.lower() in message:
                confidence += 0.1
                reasons.append(f"confidence booster: {booster}")

        # Check if agent can be primary
        if context.get("needs_primary_agent", False):
            if routing_prefs.get("primary_agent_capable", False):
                confidence += 0.2
                reasons.append("primary agent capable")
            else:
                confidence -= 0.3
                reasons.append("not primary agent capable")

        # Normalize confidence
        confidence = max(0.0, min(1.0, confidence))

        should_route = confidence > 0.3  # Threshold for routing
        reasoning = "; ".join(reasons) if reasons else "no specific indicators"

        return should_route, confidence, reasoning

    def get_agent_specialization_score(self, persona_id: str, context: Dict[str, Any]) -> float:
        """Get specialization score for an agent in a given context."""
        if persona_id not in self.capability_declarations:
            return 0.0

        capabilities = self.capability_declarations[persona_id]
        specialization_areas = capabilities.get("specialization_areas", [])

        if not specialization_areas:
            return 0.5  # General agent

        # Check context match with specialization areas
        context_type = context.get("context_type", "")
        intent = context.get("detected_intent", "")
        required_capabilities = context.get("required_capabilities", set())

        specialization_score = 0.0

        # Direct specialization match
        for area in specialization_areas:
            if area.lower() in context_type.lower() or area.lower() in intent.lower():
                specialization_score += 0.3

        # Capability-based specialization
        declared_capabilities = set(capabilities.get("declared_capabilities", []))
        if required_capabilities:
            overlap = len(required_capabilities.intersection(declared_capabilities))
            specialization_score += (overlap / len(required_capabilities)) * 0.4

        return min(1.0, specialization_score)

    def update_agent_declaration(self, persona_id: str, updates: Dict[str, Any]):
        """Update an agent's declaration."""
        if persona_id in self.agent_declarations:
            self.agent_declarations[persona_id].update(updates)

            # Reprocess declarations
            self._process_capability_declarations(persona_id, self.agent_declarations[persona_id])
            self._process_routing_preferences(persona_id, self.agent_declarations[persona_id])
            self._process_inference_patterns(persona_id, self.agent_declarations[persona_id])
            self._process_performance_expectations(persona_id, self.agent_declarations[persona_id])

            logger.info(f"Updated declaration for agent: {persona_id}")

    def get_all_declared_capabilities(self) -> Set[str]:
        """Get all capabilities declared by agents."""
        all_capabilities = set()
        for capabilities in self.capability_declarations.values():
            all_capabilities.update(capabilities.get("declared_capabilities", []))
        return all_capabilities

    def get_agents_by_capability(self, capability: str) -> List[str]:
        """Get agents that declare a specific capability."""
        agents = []
        for persona_id, capabilities in self.capability_declarations.items():
            if capability in capabilities.get("declared_capabilities", []):
                agents.append(persona_id)
        return agents

    def get_declaration_statistics(self) -> Dict[str, Any]:
        """Get statistics about agent declarations."""
        return {
            "total_agents": len(self.agent_declarations),
            "total_declared_capabilities": len(self.get_all_declared_capabilities()),
            "agents_with_routing_preferences": len(self.routing_preferences),
            "agents_with_inference_patterns": len(self.inference_patterns),
            "agents_with_performance_expectations": len(self.performance_expectations),
            "primary_capable_agents": len([
                p for p, r in self.routing_preferences.items()
                if r.get("primary_agent_capable", False)
            ]),
            "fallback_agents": len([
                p for p, r in self.routing_preferences.items()
                if r.get("fallback_agent", False)
            ])
        }


class DynamicCapabilityInferenceEngine:
    """
    Advanced capability inference engine that learns from agent configurations,
    descriptions, and historical performance to dynamically infer capabilities.
    """

    def __init__(self):
        self.inference_rules: Dict[str, Dict[str, Any]] = {}
        self.learned_patterns: Dict[str, List[str]] = {}
        self.capability_weights: Dict[str, float] = {}
        self.success_history: Dict[str, List[bool]] = {}

    def learn_from_agent_config(self, persona_id: str, config: Dict[str, Any]) -> Set[str]:
        """
        Learn capabilities from agent configuration using NLP and pattern analysis.

        Args:
            persona_id: Agent persona ID
            config: Agent configuration dictionary

        Returns:
            Set of inferred capabilities
        """
        inferred_capabilities = set()

        # Extract explicit capabilities
        explicit_caps = config.get("capabilities", [])
        for cap in explicit_caps:
            if isinstance(cap, str):
                normalized_cap = self._normalize_capability(cap)
                inferred_capabilities.add(normalized_cap)

        # Analyze description for capability hints
        description = config.get("description", "")
        name = config.get("name", "")

        # Use semantic analysis on description and name
        semantic_caps = self._extract_capabilities_from_text(description + " " + name)
        inferred_capabilities.update(semantic_caps)

        # Analyze persona ID for capability hints
        persona_caps = self._extract_capabilities_from_persona_id(persona_id)
        inferred_capabilities.update(persona_caps)

        # Learn patterns for future inference
        self._update_learned_patterns(persona_id, inferred_capabilities, config)

        return inferred_capabilities

    def _extract_capabilities_from_text(self, text: str) -> Set[str]:
        """Extract capabilities from text using semantic analysis."""
        if not text:
            return set()

        text_lower = text.lower()
        capabilities = set()

        # Semantic capability mapping with confidence scoring
        semantic_mappings = {
            "data_analysis": {
                "keywords": ["analyze", "analysis", "data", "statistics", "metrics", "insights", "trends", "patterns", "examine", "study"],
                "phrases": ["data analysis", "statistical analysis", "data insights", "analyze data", "data patterns"],
                "weight": 1.0
            },
            "visualization": {
                "keywords": ["visualize", "chart", "graph", "plot", "dashboard", "display", "visual", "diagram"],
                "phrases": ["data visualization", "create charts", "visual representation", "graphical display"],
                "weight": 1.0
            },
            "conversation": {
                "keywords": ["chat", "conversation", "dialogue", "talk", "communicate", "interact", "discuss"],
                "phrases": ["natural conversation", "chat interface", "conversational ai", "dialogue system"],
                "weight": 0.9
            },
            "greeting": {
                "keywords": ["greet", "welcome", "hello", "introduction", "onboard", "initial"],
                "phrases": ["user greeting", "welcome users", "initial interaction", "first contact"],
                "weight": 0.8
            },
            "routing": {
                "keywords": ["route", "direct", "dispatch", "forward", "delegate", "assign", "coordinate"],
                "phrases": ["route requests", "direct users", "coordinate agents", "dispatch tasks"],
                "weight": 0.9
            },
            "marketing": {
                "keywords": ["marketing", "campaign", "promotion", "advertising", "brand", "content", "social"],
                "phrases": ["marketing campaigns", "content creation", "social media", "brand promotion"],
                "weight": 1.0
            },
            "classification": {
                "keywords": ["classify", "categorize", "label", "tag", "organize", "sort", "group"],
                "phrases": ["data classification", "categorize information", "label content", "organize data"],
                "weight": 1.0
            },
            "research": {
                "keywords": ["research", "investigate", "explore", "study", "gather", "collect", "find"],
                "phrases": ["research information", "investigate topics", "gather data", "explore options"],
                "weight": 0.9
            },
            "content_creation": {
                "keywords": ["create", "generate", "write", "compose", "produce", "develop", "build"],
                "phrases": ["content creation", "generate content", "write articles", "create materials"],
                "weight": 0.9
            },
            "tool_execution": {
                "keywords": ["execute", "run", "perform", "operate", "use tools", "function", "action"],
                "phrases": ["execute tools", "run functions", "perform actions", "use utilities"],
                "weight": 0.8
            }
        }

        for capability, mapping in semantic_mappings.items():
            score = 0.0

            # Check keywords
            for keyword in mapping["keywords"]:
                if keyword in text_lower:
                    score += 0.1

            # Check phrases (higher weight)
            for phrase in mapping["phrases"]:
                if phrase in text_lower:
                    score += 0.3

            # Apply capability weight and threshold
            final_score = score * mapping["weight"]
            if final_score >= 0.2:  # Threshold for capability inference
                capabilities.add(capability)
                self.capability_weights[capability] = max(
                    self.capability_weights.get(capability, 0.0),
                    final_score
                )

        return capabilities

    def _extract_capabilities_from_persona_id(self, persona_id: str) -> Set[str]:
        """Extract capabilities from persona ID patterns."""
        persona_lower = persona_id.lower()
        capabilities = set()

        # Pattern-based inference from persona ID
        id_patterns = {
            "concierge": ["greeting", "routing", "conversation"],
            "analyst": ["data_analysis", "visualization"],
            "analysis": ["data_analysis", "visualization"],
            "marketing": ["marketing", "content_creation"],
            "classifier": ["classification"],
            "classification": ["classification"],
            "researcher": ["research"],
            "research": ["research"]
        }

        for pattern, caps in id_patterns.items():
            if pattern in persona_lower:
                capabilities.update(caps)

        return capabilities

    def _normalize_capability(self, capability: str) -> str:
        """Normalize capability string."""
        return capability.lower().strip().replace(" ", "_").replace("-", "_")

    def _update_learned_patterns(self, persona_id: str, capabilities: Set[str], config: Dict[str, Any]):
        """Update learned patterns for future inference."""
        # Store successful inference patterns
        for capability in capabilities:
            if capability not in self.learned_patterns:
                self.learned_patterns[capability] = []

            # Add context patterns that led to this capability inference
            description = config.get("description", "")
            name = config.get("name", "")

            # Extract key phrases that might be useful for future inference
            text = (description + " " + name).lower()
            words = text.split()

            # Store 2-3 word phrases that might be indicative
            for i in range(len(words) - 1):
                phrase = " ".join(words[i:i+2])
                if len(phrase) > 3 and phrase not in self.learned_patterns[capability]:
                    self.learned_patterns[capability].append(phrase)

    def update_success_feedback(self, persona_id: str, capability: str, success: bool):
        """Update success feedback for learning."""
        key = f"{persona_id}:{capability}"
        if key not in self.success_history:
            self.success_history[key] = []

        self.success_history[key].append(success)

        # Keep only recent history (last 50 entries)
        if len(self.success_history[key]) > 50:
            self.success_history[key] = self.success_history[key][-50:]

    def get_capability_confidence(self, persona_id: str, capability: str) -> float:
        """Get confidence score for a capability assignment."""
        key = f"{persona_id}:{capability}"
        if key not in self.success_history:
            return 0.5  # Default confidence

        history = self.success_history[key]
        if not history:
            return 0.5

        # Calculate success rate with recency weighting
        total_weight = 0.0
        success_weight = 0.0

        for i, success in enumerate(history):
            # More recent entries have higher weight
            weight = 1.0 + (i / len(history)) * 0.5
            total_weight += weight
            if success:
                success_weight += weight

        return success_weight / total_weight if total_weight > 0 else 0.5


class DynamicCapabilityRegistry:
    """Registry for dynamically discovered agent capabilities."""

    def __init__(self):
        self.capabilities: Set[str] = set()
        self.capability_synonyms: Dict[str, Set[str]] = {}
        self.capability_categories: Dict[str, str] = {}
        self.capability_descriptions: Dict[str, str] = {}
        self.inference_engine = DynamicCapabilityInferenceEngine()

    def register_capability(self, capability: str, synonyms: Set[str] = None,
                          category: str = "general", description: str = ""):
        """Register a new capability dynamically."""
        self.capabilities.add(capability)
        if synonyms:
            self.capability_synonyms[capability] = synonyms
        self.capability_categories[capability] = category
        self.capability_descriptions[capability] = description

    def normalize_capability(self, raw_capability: str) -> str:
        """Normalize a raw capability string to a standard capability."""
        raw_lower = raw_capability.lower().strip()

        # Direct match
        if raw_lower in self.capabilities:
            return raw_lower

        # Check synonyms
        for capability, synonyms in self.capability_synonyms.items():
            if raw_lower in synonyms or any(syn in raw_lower for syn in synonyms):
                return capability

        # Return normalized version
        return raw_lower.replace(" ", "_").replace("-", "_")

    def get_capabilities_by_category(self, category: str) -> Set[str]:
        """Get all capabilities in a specific category."""
        return {cap for cap, cat in self.capability_categories.items() if cat == category}

    def infer_capabilities_from_config(self, persona_id: str, config: Dict[str, Any]) -> Set[str]:
        """Use the inference engine to determine capabilities from configuration."""
        return self.inference_engine.learn_from_agent_config(persona_id, config)


@dataclass
class AgentMetadata:
    """Comprehensive agent metadata for dynamic routing."""
    persona_id: str
    name: str
    description: str
    capabilities: Set[str] = field(default_factory=set)  # Dynamic capabilities as strings
    supported_intents: Set[str] = field(default_factory=set)
    complexity_levels: Set[str] = field(default_factory=set)  # low, medium, high
    collaboration_score: float = 0.5  # How well it works with other agents
    performance_score: float = 0.8  # Historical performance
    specialization_areas: Set[str] = field(default_factory=set)
    required_tools: Set[str] = field(default_factory=set)
    output_formats: Set[str] = field(default_factory=set)
    max_context_length: int = 4000
    average_response_time: float = 2.0  # seconds
    success_rate: float = 0.95
    user_satisfaction_score: float = 0.85
    is_primary_agent: bool = False  # Can be a primary conversation agent
    requires_collaboration: bool = False  # Needs other agents to function
    agent_type: str = "general"  # Dynamic agent type
    priority_score: float = 0.5  # Priority for routing decisions
    tags: Set[str] = field(default_factory=set)  # Additional tags for categorization
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class UserAgentAccess:
    """User's agent access information."""
    user_id: str
    purchased_agents: Set[str] = field(default_factory=set)
    subscription_tier: Optional[str] = None
    access_level: str = "basic"  # basic, premium, enterprise
    concurrent_agent_limit: int = 1
    daily_usage_limit: int = 100
    current_usage: int = 0
    preferred_agents: List[str] = field(default_factory=list)
    blocked_agents: Set[str] = field(default_factory=set)
    last_updated: datetime = field(default_factory=datetime.now)


class DynamicIntelligentRouter:
    """
    Dynamic, extensible agent router for Datagenius multi-agent system.

    This router provides:
    - Dynamic agent discovery and capability mapping
    - User access control integration
    - Multi-agent collaboration workflows
    - Extensible plugin-style architecture
    - Real-time agent ecosystem adaptation
    """

    def __init__(self, agent_factory_instance = None, context_injector: UniversalContextInjector = None):
        """
        Initialize the dynamic intelligent router.

        Args:
            agent_factory_instance: Agent factory for agent management
            context_injector: Universal context injector for context management
        """
        self.agent_factory = agent_factory_instance or agent_factory
        self.context_injector = context_injector
        self.access_controller = get_access_controller()

        # Dynamic capability registry
        self.capability_registry = DynamicCapabilityRegistry()

        # Plugin routing manager
        self.routing_manager = PluginRoutingManager()

        # Learning intent detector
        self.intent_detector = LearningIntentDetector()

        # Adaptive agent scoring system
        self.scoring_system = AdaptiveAgentScoringSystem()

        # Configuration file path
        self.config_path = Path(__file__).parent.parent / "config" / "routing_config.yaml"

        # Configurable behavior manager with error handling - MUST BE INITIALIZED FIRST
        try:
            self.behavior_manager = ConfigurableBehaviorManager(self.config_path)
        except Exception as e:
            logger.warning(f"Failed to initialize ConfigurableBehaviorManager: {e}")
            # Create a minimal behavior manager as fallback
            self.behavior_manager = self._create_fallback_behavior_manager()

        # Business profile workflow optimizer
        self.profile_optimizer = BusinessProfileWorkflowOptimizer()

        # Update scoring system with configuration
        if self.behavior_manager.should_use_adaptive_scoring():
            scoring_weights = self.behavior_manager.get_scoring_weights()
            self.scoring_system.scoring_weights = scoring_weights

        # Performance learning optimizer
        self.performance_optimizer = PerformanceLearningOptimizer()

        # Agent self-declaration system
        self.declaration_system = AgentSelfDeclarationSystem()

        # Runtime extensibility framework
        self.extensibility_framework = RuntimeExtensibilityFramework(self)

        # Dynamic agent discovery and metadata
        self.discovered_agents: Dict[str, AgentMetadata] = {}
        self.agent_capabilities_cache: Dict[str, Set[str]] = {}
        self.user_access_cache: Dict[str, UserAgentAccess] = {}

        # Intent to capability mappings (dynamic)
        self.intent_capability_mappings: Dict[str, Set[str]] = {}
        self.capability_intent_mappings: Dict[str, Set[str]] = {}

        # Missing attributes that were referenced but not initialized
        self.agent_mappings: Dict[str, str] = {}
        self.intent_patterns: Dict[str, List[str]] = {}
        self.success_metrics: Dict[str, float] = {}

        # Routing history and analytics
        self.routing_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, Dict[str, float]] = {}
        self.collaboration_patterns: Dict[str, List[str]] = {}

        # Configuration and caching
        self.cache_ttl = timedelta(minutes=15)
        self.last_discovery_time = datetime.min
        self.discovery_interval = timedelta(minutes=5)

        # Load configuration and initialize mappings
        self._load_configuration()

        # Initialize dynamic discovery
        asyncio.create_task(self._initialize_dynamic_discovery())

    async def initialize(self):
        """
        Initialize the DynamicIntelligentRouter.

        This method performs any necessary setup operations for the router.
        """
        try:
            logger.info("Initializing DynamicIntelligentRouter...")

            # Ensure dynamic discovery is complete
            await self._initialize_dynamic_discovery()

            # Initialize any additional components
            await self._initialize_router_components()

            logger.info("DynamicIntelligentRouter initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize DynamicIntelligentRouter: {e}")
            raise

    async def _initialize_router_components(self):
        """Initialize additional router components."""
        try:
            # Initialize any additional components that need async setup
            logger.debug("Router components initialized")

        except Exception as e:
            logger.error(f"Error initializing router components: {e}")

    def _create_fallback_behavior_manager(self):
        """Create a minimal fallback behavior manager."""
        class FallbackBehaviorManager:
            def __init__(self):
                self.behavior_rules = {}
                self.routing_policies = {}
                self.scoring_weights = {}
                self.thresholds = {}
                self.feature_flags = {}

            def should_use_learning_system(self):
                return False

            def should_use_adaptive_scoring(self):
                return False

            def get_threshold(self, name, default=0.5):
                return self.thresholds.get(name, default)

            def get_behavior_setting(self, category, setting, default=None):
                return self.behavior_rules.get(category, {}).get(setting, default)

            def get_scoring_weights(self):
                return {
                    "performance_weight": 0.3,
                    "user_satisfaction_weight": 0.25,
                    "response_time_weight": 0.15,
                    "success_rate_weight": 0.2,
                    "collaboration_weight": 0.1
                }

            def update_behavior_setting(self, category, setting, value):
                if category not in self.behavior_rules:
                    self.behavior_rules[category] = {}
                self.behavior_rules[category][setting] = value

            def update_threshold(self, threshold, value):
                self.thresholds[threshold] = value

            def toggle_feature(self, feature, enabled=None):
                if enabled is None:
                    enabled = not self.feature_flags.get(feature, False)
                self.feature_flags[feature] = enabled

            def reload_configuration(self):
                pass

            def export_configuration(self):
                return {
                    "behavior_rules": self.behavior_rules,
                    "routing_policies": self.routing_policies,
                    "scoring_weights": self.scoring_weights,
                    "thresholds": self.thresholds,
                    "feature_flags": self.feature_flags
                }

        return FallbackBehaviorManager()

    def _load_configuration(self):
        """Load configuration from YAML file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                # Load intent patterns
                self.intent_patterns = config.get('intent_patterns', {})

                # Initialize learning intent detector with base patterns
                self.intent_detector.load_base_patterns(self.intent_patterns)

                # Load agent mappings from discovered agents
                self.agent_mappings = self._load_dynamic_agent_mappings()

                # Initialize success metrics
                self.success_metrics = {}

                logger.info(f"Configuration loaded from {self.config_path}")
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                self._initialize_default_configuration()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self._initialize_default_configuration()

    def _initialize_default_configuration(self):
        """Initialize default configuration when config file is not available."""
        self.intent_patterns = {
            "greeting": ["hello", "hi", "hey", "good morning", "good afternoon"],
            "general_inquiry": ["what", "how", "why", "explain", "tell me"],
            "data_analysis": ["analyze", "analysis", "statistics", "data", "insights"],
            "marketing": ["marketing", "campaign", "content", "social media"],
            "classification": ["classify", "categorize", "label", "tag"]
        }
        self.agent_mappings = {}
        self.success_metrics = {}
        logger.info("Default configuration initialized")

    def _load_dynamic_agent_mappings(self) -> Dict[str, str]:
        """
        Load dynamic agent mappings from the registry.

        Returns:
            Dictionary mapping agent types to persona IDs
        """
        try:
            mappings = {}
            registered_personas = self.agent_factory.get_available_agents()

            for persona_id in registered_personas:
                # Extract agent type from persona ID or configuration
                agent_type = self._extract_agent_type_from_persona(persona_id)
                if agent_type:
                    mappings[agent_type] = persona_id

            logger.debug(f"Loaded {len(mappings)} agent mappings")
            return mappings
        except Exception as e:
            logger.error(f"Error loading dynamic agent mappings: {e}")
            return {}

    def _extract_agent_type_from_persona(self, persona_id: str) -> Optional[str]:
        """
        Extract agent type from persona ID.

        Args:
            persona_id: The persona ID to extract type from

        Returns:
            Agent type string or None
        """
        try:
            # Try to get from configuration first
            config = self.agent_factory.agent_configs.get(persona_id)
            if config and "agent_type" in config:
                return config["agent_type"]

            # Fallback to extracting from persona ID
            if "concierge" in persona_id.lower():
                return "concierge"
            elif "analysis" in persona_id.lower() or "analyst" in persona_id.lower():
                return "analysis"
            elif "marketing" in persona_id.lower():
                return "marketing"
            elif "classification" in persona_id.lower() or "classifier" in persona_id.lower():
                return "classification"
            else:
                # Use the first part of the persona ID as agent type
                parts = persona_id.split("-")
                if len(parts) > 1:
                    return parts[1]  # Skip "datagenius" prefix
                return "general"
        except Exception as e:
            logger.error(f"Error extracting agent type from {persona_id}: {e}")
            return None

    async def _initialize_dynamic_discovery(self):
        """Initialize dynamic agent discovery system."""
        try:
            await self._discover_available_agents()
            await self._load_agent_capabilities()
            await self._build_intent_mappings()
            logger.info("Dynamic agent discovery initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing dynamic discovery: {e}")

    async def _discover_available_agents(self) -> Dict[str, AgentMetadata]:
        """
        Discover all available agents from the registry and build metadata.

        Returns:
            Dictionary mapping persona IDs to agent metadata
        """
        try:
            # Check if discovery is needed
            if (datetime.now() - self.last_discovery_time) < self.discovery_interval:
                return self.discovered_agents

            logger.info("Starting dynamic agent discovery...")
            discovered = {}

            # Get all registered personas
            registered_personas = self.agent_factory.get_available_agents()

            for persona_id in registered_personas:
                try:
                    metadata = await self._extract_agent_metadata(persona_id)
                    if metadata:
                        discovered[persona_id] = metadata
                        # Initialize adaptive scoring for the agent
                        self.scoring_system.initialize_agent_scoring(metadata)
                        # Register agent self-declaration
                        config = self.agent_factory.agent_configs.get(persona_id)
                        if config:
                            self.declaration_system.register_agent_declaration(persona_id, config)
                        logger.debug(f"Discovered agent: {persona_id} with {len(metadata.capabilities)} capabilities")
                except Exception as e:
                    logger.warning(f"Failed to extract metadata for {persona_id}: {e}")
                    continue

            self.discovered_agents = discovered
            self.last_discovery_time = datetime.now()

            logger.info(f"Discovered {len(discovered)} agents: {list(discovered.keys())}")
            return discovered

        except Exception as e:
            logger.error(f"Error in agent discovery: {e}")
            return self.discovered_agents

    async def _extract_agent_metadata(self, persona_id: str) -> Optional[AgentMetadata]:
        """
        Extract comprehensive metadata for an agent.

        Args:
            persona_id: The persona ID to extract metadata for

        Returns:
            AgentMetadata object or None if extraction fails
        """
        try:
            # Get agent configuration
            config = self.agent_factory.agent_configs.get(persona_id)
            if not config:
                logger.warning(f"No configuration found for {persona_id}")
                return None

            # Extract basic information
            name = config.get("name", persona_id)
            description = config.get("description", "")

            # Extract capabilities using dynamic inference engine
            capabilities = self.capability_registry.infer_capabilities_from_config(persona_id, config)

            # Register all inferred capabilities
            for capability in capabilities:
                self.capability_registry.register_capability(
                    capability,
                    category=self._get_capability_category(capability),
                    description=self._get_capability_description(capability)
                )

            # Extract agent type dynamically
            agent_type = config.get("agent_type", "")
            if not agent_type:
                # Infer agent type from capabilities and name
                if "greeting" in capabilities or "routing" in capabilities:
                    agent_type = "concierge"
                elif "data_analysis" in capabilities:
                    agent_type = "analyst"
                elif "marketing" in capabilities:
                    agent_type = "marketer"
                elif "classification" in capabilities:
                    agent_type = "classifier"
                elif "research" in capabilities:
                    agent_type = "researcher"
                else:
                    agent_type = "general"

            # Extract supported intents
            supported_intents = set(config.get("supported_intents", []))

            # Infer intents from capabilities dynamically
            capability_intent_mapping = {
                "greeting": ["greeting", "hello", "introduction", "welcome"],
                "data_analysis": ["data_analysis", "analyze", "statistics", "insights", "analytics"],
                "marketing": ["marketing", "campaign", "content_creation", "promotion"],
                "classification": ["classification", "categorize", "label", "classify"],
                "research": ["research", "investigate", "study", "explore"],
                "visualization": ["visualize", "chart", "graph", "plot"],
                "conversation": ["chat", "talk", "discuss", "converse"]
            }

            for capability in capabilities:
                if capability in capability_intent_mapping:
                    supported_intents.update(capability_intent_mapping[capability])

            # Extract other metadata
            complexity_levels = set(config.get("complexity_levels", ["low", "medium", "high"]))
            specialization_areas = set(config.get("specialization_areas", []))
            required_tools = set(config.get("required_tools", []))
            output_formats = set(config.get("output_formats", ["text", "json"]))

            # Performance metrics (would be loaded from historical data in production)
            performance_score = config.get("performance_score", 0.8)
            collaboration_score = config.get("collaboration_score", 0.5)
            success_rate = config.get("success_rate", 0.95)
            user_satisfaction_score = config.get("user_satisfaction_score", 0.85)

            # Determine if this is a primary agent (can handle conversations independently)
            persona_lower = persona_id.lower()
            is_primary_agent = (
                "conversation" in capabilities or
                "greeting" in capabilities or
                "concierge" in persona_lower or
                agent_type == "concierge"
            )

            # Determine if collaboration is required
            requires_collaboration = config.get("requires_collaboration", False)

            # Calculate priority score based on capabilities and performance
            priority_score = (performance_score * 0.4 +
                            collaboration_score * 0.3 +
                            success_rate * 0.3)

            # Extract tags for additional categorization
            tags = set(config.get("tags", []))
            tags.update([agent_type, persona_id.split("-")[0]])  # Add type and prefix as tags

            return AgentMetadata(
                persona_id=persona_id,
                name=name,
                description=description,
                capabilities=capabilities,
                supported_intents=supported_intents,
                complexity_levels=complexity_levels,
                collaboration_score=collaboration_score,
                performance_score=performance_score,
                specialization_areas=specialization_areas,
                required_tools=required_tools,
                output_formats=output_formats,
                max_context_length=config.get("max_context_length", 4000),
                average_response_time=config.get("average_response_time", 2.0),
                success_rate=success_rate,
                user_satisfaction_score=user_satisfaction_score,
                is_primary_agent=is_primary_agent,
                requires_collaboration=requires_collaboration,
                agent_type=agent_type,
                priority_score=priority_score,
                tags=tags,
                last_updated=datetime.now()
            )

        except Exception as e:
            logger.error(f"Error extracting metadata for {persona_id}: {e}")
            return None

    def _get_capability_category(self, capability: str) -> str:
        """Get the category for a capability."""
        category_mapping = {
            "greeting": "interaction",
            "routing": "coordination",
            "conversation": "interaction",
            "data_analysis": "analysis",
            "visualization": "analysis",
            "marketing": "content",
            "content_creation": "content",
            "classification": "processing",
            "research": "information",
            "tool_execution": "execution",
            "collaboration": "coordination"
        }
        return category_mapping.get(capability, "general")

    def _get_capability_description(self, capability: str) -> str:
        """Get a description for a capability."""
        descriptions = {
            "greeting": "Ability to greet users and provide initial interactions",
            "routing": "Ability to route requests to appropriate agents",
            "conversation": "Ability to maintain natural conversations",
            "data_analysis": "Ability to analyze and interpret data",
            "visualization": "Ability to create visual representations of data",
            "marketing": "Ability to create marketing strategies and content",
            "content_creation": "Ability to generate various types of content",
            "classification": "Ability to classify and categorize information",
            "research": "Ability to research and gather information",
            "tool_execution": "Ability to execute various tools and functions",
            "collaboration": "Ability to work with other agents"
        }
        return descriptions.get(capability, f"Capability: {capability}")

    async def _load_agent_capabilities(self):
        """Load and cache agent capabilities for quick access."""
        try:
            for persona_id, metadata in self.discovered_agents.items():
                self.agent_capabilities_cache[persona_id] = metadata.capabilities

            logger.info(f"Loaded capabilities for {len(self.agent_capabilities_cache)} agents")

        except Exception as e:
            logger.error(f"Error loading agent capabilities: {e}")

    async def _build_intent_mappings(self):
        """Build dynamic intent to capability and capability to intent mappings."""
        try:
            self.intent_capability_mappings.clear()
            self.capability_intent_mappings.clear()

            for persona_id, metadata in self.discovered_agents.items():
                # Build intent -> capabilities mapping
                for intent in metadata.supported_intents:
                    if intent not in self.intent_capability_mappings:
                        self.intent_capability_mappings[intent] = set()
                    self.intent_capability_mappings[intent].update(metadata.capabilities)

                # Build capability -> intents mapping
                for capability in metadata.capabilities:
                    if capability not in self.capability_intent_mappings:
                        self.capability_intent_mappings[capability] = set()
                    self.capability_intent_mappings[capability].update(metadata.supported_intents)

            logger.info(f"Built mappings for {len(self.intent_capability_mappings)} intents and {len(self.capability_intent_mappings)} capabilities")

        except Exception as e:
            logger.error(f"Error building intent mappings: {e}")

    async def get_user_agent_access(self, user_id: str) -> UserAgentAccess:
        """
        Get user's agent access information including purchased agents.

        Args:
            user_id: User ID to get access for

        Returns:
            UserAgentAccess object with user's permissions
        """
        try:
            # Check cache first
            if user_id in self.user_access_cache:
                cached_access = self.user_access_cache[user_id]
                if (datetime.now() - cached_access.last_updated) < self.cache_ttl:
                    return cached_access

            # Get user permissions from access controller
            user_permissions = await self.access_controller._get_or_create_user_permissions(user_id)

            if not user_permissions:
                # Return default access for unknown users
                default_access = UserAgentAccess(
                    user_id=user_id,
                    purchased_agents={"datagenius-concierge"},  # Everyone gets concierge
                    access_level="basic",
                    concurrent_agent_limit=1,
                    daily_usage_limit=10
                )
                self.user_access_cache[user_id] = default_access
                return default_access

            # Convert to UserAgentAccess format
            user_access = UserAgentAccess(
                user_id=user_id,
                purchased_agents=user_permissions.allowed_agents,
                access_level="premium" if len(user_permissions.allowed_agents) > 1 else "basic",
                concurrent_agent_limit=3 if len(user_permissions.allowed_agents) > 2 else 1,
                daily_usage_limit=100 if len(user_permissions.allowed_agents) > 1 else 20,
                current_usage=0,  # Would be loaded from usage tracking
                preferred_agents=[],  # Would be loaded from user preferences
                blocked_agents=set(),  # Would be loaded from user settings
                last_updated=datetime.now()
            )

            # Cache the access information
            self.user_access_cache[user_id] = user_access

            logger.debug(f"User {user_id} has access to {len(user_access.purchased_agents)} agents")
            return user_access

        except Exception as e:
            logger.error(f"Error getting user agent access for {user_id}: {e}")
            # Return minimal access on error
            return UserAgentAccess(
                user_id=user_id,
                purchased_agents={"datagenius-concierge"},
                access_level="basic"
            )

    async def get_available_agents_for_user(self, user_id: str) -> List[AgentMetadata]:
        """
        Get list of agents available to a specific user based on their purchases.

        Args:
            user_id: User ID to get available agents for

        Returns:
            List of AgentMetadata for agents the user can access
        """
        try:
            user_access = await self.get_user_agent_access(user_id)
            available_agents = []

            for persona_id in user_access.purchased_agents:
                if persona_id in self.discovered_agents:
                    available_agents.append(self.discovered_agents[persona_id])

            # Sort by priority score (highest first)
            available_agents.sort(key=lambda x: x.priority_score, reverse=True)

            logger.debug(f"User {user_id} has {len(available_agents)} available agents")
            return available_agents

        except Exception as e:
            logger.error(f"Error getting available agents for user {user_id}: {e}")
            return []

    async def can_user_access_agent(self, user_id: str, persona_id: str) -> bool:
        """
        Check if a user can access a specific agent.

        Args:
            user_id: User ID to check
            persona_id: Agent persona ID to check access for

        Returns:
            True if user can access the agent, False otherwise
        """
        try:
            user_access = await self.get_user_agent_access(user_id)
            return persona_id in user_access.purchased_agents

        except Exception as e:
            logger.error(f"Error checking agent access for user {user_id}, agent {persona_id}: {e}")
            return False

    async def make_routing_decision(self, state: DatageniusAgentState) -> RoutingDecision:
        """
        Make a dynamic routing decision using the plugin routing manager.

        Args:
            state: Current agent state

        Returns:
            RoutingDecision with selected strategy and agents
        """
        try:
            user_id = state.get("user_id", "")
            latest_message = state["messages"][-1] if state["messages"] else {}
            message_content = latest_message.get("content", "")

            # Get user's available agents
            available_agents = await self.get_available_agents_for_user(user_id)

            if not available_agents:
                logger.warning(f"No available agents for user {user_id}")
                return RoutingDecision(
                    strategy=RoutingStrategy.SINGLE_AGENT,
                    primary_agent="datagenius-concierge",
                    reasoning="No purchased agents found, using default concierge",
                    confidence_score=0.5
                )

            # Detect intent and complexity
            detected_intent = await self._detect_intent_dynamic(message_content, user_id)
            complexity = await self._assess_complexity_dynamic(message_content)
            collaboration_needed = await self._assess_collaboration_needs_dynamic(message_content, available_agents)

            # Determine required capabilities
            required_capabilities = self.intent_capability_mappings.get(detected_intent, set())

            # Build routing context
            routing_context = {
                "user_id": user_id,
                "message_content": message_content,
                "detected_intent": detected_intent,
                "complexity": complexity,
                "collaboration_needed": collaboration_needed,
                "available_agents": available_agents,
                "required_capabilities": required_capabilities,
                "intent_capabilities": required_capabilities,
                "state": state
            }

            # Use plugin routing manager to get decision
            decision = await self.routing_manager.get_best_routing_decision(routing_context)

            # Store routing decision for learning
            self.routing_history.append({
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "intent": detected_intent,
                "complexity": complexity,
                "decision": decision,
                "context": routing_context
            })

            return decision

        except Exception as e:
            logger.error(f"Error making routing decision: {e}")
            return RoutingDecision(
                strategy=RoutingStrategy.SINGLE_AGENT,
                primary_agent="datagenius-concierge",
                reasoning=f"Error in routing: {str(e)}",
                confidence_score=0.3
            )

    async def _detect_intent_dynamic(self, message: str, user_id: str = None) -> str:
        """Dynamically detect intent using learning-based approach."""
        try:
            # Check if learning system should be used
            if self.behavior_manager.should_use_learning_system():
                # Use learning intent detector
                intent, confidence = await self.intent_detector.detect_intent(
                    message,
                    user_id=user_id,
                    context={
                        "timestamp": datetime.now().isoformat(),
                        "available_intents": list(self.intent_capability_mappings.keys())
                    }
                )

                # Check confidence threshold
                confidence_threshold = self.behavior_manager.get_threshold("intent_confidence_min", 0.3)
                if confidence >= confidence_threshold:
                    logger.debug(f"Detected intent: {intent} (confidence: {confidence:.2f})")
                    return intent
                elif self.behavior_manager.get_behavior_setting("intent_detection", "fallback_to_static", True):
                    logger.debug(f"Low confidence ({confidence:.2f}), falling back to static detection")
                    return await self._fallback_intent_detection(message)
                else:
                    return intent
            else:
                # Use static detection
                return await self._fallback_intent_detection(message)

        except Exception as e:
            logger.error(f"Error in dynamic intent detection: {e}")
            # Fallback to simple detection
            return await self._fallback_intent_detection(message)

    async def _fallback_intent_detection(self, message: str) -> str:
        """Fallback intent detection method."""
        message_lower = message.lower()

        # Score intents based on keyword matches
        intent_scores = {}

        for intent, capabilities in self.intent_capability_mappings.items():
            score = 0
            intent_words = intent.lower().split("_")

            # Direct intent word matches
            for word in intent_words:
                if word in message_lower:
                    score += 2

            # Capability-based scoring
            for capability in capabilities:
                capability_words = capability.lower().split("_")
                for word in capability_words:
                    if word in message_lower:
                        score += 1

            if score > 0:
                intent_scores[intent] = score

        # Return highest scoring intent
        if intent_scores:
            best_intent = max(intent_scores.items(), key=lambda x: x[1])[0]
            logger.debug(f"Fallback detected intent: {best_intent} (score: {intent_scores[best_intent]})")
            return best_intent

        return "general_inquiry"

    async def _assess_complexity_dynamic(self, message: str) -> str:
        """Dynamically assess complexity based on message characteristics."""
        complexity_indicators = {
            "high": ["comprehensive", "detailed", "complex", "analyze", "compare", "synthesize", "multiple", "various"],
            "medium": ["explain", "describe", "show", "create", "generate", "help"],
            "low": ["what", "who", "when", "where", "hello", "hi", "yes", "no"]
        }

        message_lower = message.lower()
        scores = {"high": 0, "medium": 0, "low": 0}

        for level, indicators in complexity_indicators.items():
            for indicator in indicators:
                if indicator in message_lower:
                    scores[level] += 1

        # Consider message length
        if len(message) > 200:
            scores["high"] += 2
        elif len(message) > 50:
            scores["medium"] += 1
        else:
            scores["low"] += 1

        # Return highest scoring complexity
        best_complexity = max(scores.items(), key=lambda x: x[1])[0]
        logger.debug(f"Assessed complexity: {best_complexity}")
        return best_complexity

    async def _assess_collaboration_needs_dynamic(self, message: str, available_agents: List[AgentMetadata]) -> bool:
        """Assess if collaboration is needed based on message and available agents."""
        message_lower = message.lower()

        # Collaboration indicators
        collaboration_keywords = [
            "comprehensive", "multiple perspectives", "analyze and create",
            "research and develop", "compare and recommend", "end-to-end",
            "full analysis", "complete solution"
        ]

        # Check for collaboration keywords
        has_collaboration_keywords = any(keyword in message_lower for keyword in collaboration_keywords)

        # Check if multiple agent types would be beneficial
        required_capabilities = set()
        for agent in available_agents:
            for capability in agent.capabilities:
                if any(cap_word in message_lower for cap_word in capability.split("_")):
                    required_capabilities.add(capability)

        # If more than 2 different capability types are needed, suggest collaboration
        needs_multiple_capabilities = len(required_capabilities) > 2

        # Check if any agents specifically require collaboration
        has_collaborative_agents = any(agent.requires_collaboration for agent in available_agents)

        collaboration_needed = (has_collaboration_keywords or
                              needs_multiple_capabilities or
                              has_collaborative_agents)

        logger.debug(f"Collaboration needed: {collaboration_needed} (keywords: {has_collaboration_keywords}, capabilities: {needs_multiple_capabilities})")
        return collaboration_needed

    async def _find_matching_agents(self, intent: str, complexity: str, available_agents: List[AgentMetadata]) -> List[AgentMetadata]:
        """Find agents that best match the intent and complexity using adaptive scoring."""
        # Build context for adaptive scoring
        context = {
            "intent": intent,
            "complexity": complexity,
            "context_type": intent,
            "required_capabilities": self.intent_capability_mappings.get(intent, set())
        }

        # Use adaptive scoring system to get top agents
        scored_agents = self.scoring_system.get_top_agents(
            available_agents,
            context=context,
            limit=len(available_agents)
        )

        # Filter agents that have some relevance to the intent/complexity
        relevant_agents = []
        for agent, adaptive_score in scored_agents:
            relevance_score = 0

            # Check agent self-declaration
            should_route, declaration_confidence, declaration_reasoning = \
                self.declaration_system.should_route_to_agent(agent.persona_id, context)

            if should_route:
                relevance_score += declaration_confidence * 15  # High weight for self-declaration

            # Intent matching
            if intent in agent.supported_intents:
                relevance_score += 10

            # Capability matching
            intent_capabilities = self.intent_capability_mappings.get(intent, set())
            capability_overlap = len(agent.capabilities.intersection(intent_capabilities))
            relevance_score += capability_overlap * 5

            # Complexity matching
            if complexity in agent.complexity_levels:
                relevance_score += 3

            # Specialization score
            specialization_score = self.declaration_system.get_agent_specialization_score(
                agent.persona_id, context
            )
            relevance_score += specialization_score * 8

            # Combine relevance with adaptive score
            final_score = relevance_score + (adaptive_score * 20)  # Scale adaptive score

            if (relevance_score > 0 or adaptive_score > 0.6 or should_route):  # Include if relevant
                relevant_agents.append((agent, final_score))

        # Sort by final score (highest first) and return agents
        relevant_agents.sort(key=lambda x: x[1], reverse=True)
        result = [agent for agent, score in relevant_agents]

        logger.debug(f"Found {len(result)} matching agents for intent '{intent}' and complexity '{complexity}' using adaptive scoring")
        return result

    def create_routing_graph(self) -> Optional[Any]:
        """
        Create the intelligent routing graph.
        
        Returns:
            Compiled LangGraph workflow or None if LangGraph not available
        """
        if StateGraph is None:
            logger.warning("LangGraph not available, falling back to legacy routing")
            return None
            
        workflow = StateGraph(DatageniusAgentState)

        # Add routing nodes
        workflow.add_node("analyze_request", self.analyze_request)
        workflow.add_node("inject_context", self.inject_universal_context)
        workflow.add_node("route_decision", self.make_routing_decision)

        # Add agent execution nodes dynamically
        self._add_dynamic_agent_nodes(workflow)

        # Add utility nodes
        workflow.add_node("aggregate_results", self.aggregate_results)
        workflow.add_node("quality_check", self.quality_check)

        # Define routing flow
        workflow.set_entry_point("analyze_request")
        workflow.add_edge("analyze_request", "inject_context")
        workflow.add_edge("inject_context", "route_decision")

        # Conditional routing based on analysis - dynamically built
        routing_edges = self._build_dynamic_routing_edges()
        workflow.add_conditional_edges(
            "route_decision",
            self.routing_condition,
            routing_edges
        )

        # All agents flow to quality check - dynamically built
        agent_nodes = [f"{agent_type}_agent" for agent_type in self.agent_mappings.keys()]
        for agent_node in agent_nodes:
            workflow.add_edge(agent_node, "quality_check")

        workflow.add_edge("aggregate_results", "quality_check")

        # Quality check determines completion or retry
        workflow.add_conditional_edges(
            "quality_check",
            self.quality_condition,
            {
                "complete": END,
                "retry": "route_decision",
                "escalate": "concierge_agent"
            }
        )

        try:
            return workflow.compile()
        except Exception as e:
            logger.error(f"Failed to compile routing graph: {e}")
            return None

    def _add_dynamic_agent_nodes(self, workflow: StateGraph) -> None:
        """Add agent execution nodes dynamically based on registered agents."""
        for agent_type, persona_id in self.agent_mappings.items():
            node_name = f"{agent_type}_agent"
            node_function = self._create_agent_executor(persona_id)
            workflow.add_node(node_name, node_function)

    def _build_dynamic_routing_edges(self) -> Dict[str, str]:
        """Build routing edges dynamically based on available agents."""
        edges = {}

        # Add edges for each registered agent type
        for agent_type in self.agent_mappings.keys():
            edges[agent_type] = f"{agent_type}_agent"

        # Add special routing options
        edges["multi_agent"] = "aggregate_results"

        return edges

    def _create_agent_executor(self, persona_id: str) -> Callable:
        """Create an agent executor function for a specific persona."""
        async def execute_agent(state: DatageniusAgentState) -> DatageniusAgentState:
            return await self._execute_agent(state, persona_id)

        return execute_agent

    async def analyze_request(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """Analyze the incoming request to determine routing strategy."""
        try:
            latest_message = state["messages"][-1] if state["messages"] else {}
            message_content = latest_message.get("content", "")

            # Enhanced analysis using existing components
            analysis = {
                "intent": await self._detect_intent(message_content),
                "complexity": await self._assess_complexity(message_content),
                "data_requirements": await self._analyze_data_needs(message_content),
                "collaboration_needed": await self._assess_collaboration_needs(message_content),
                "user_context": await self._get_user_context(state["user_id"])
            }

            state["routing_analysis"] = analysis
            state["execution_metrics"]["analysis_time"] = datetime.now().isoformat()

            logger.info(f"Request analysis completed: {analysis['intent']}")
            return state
            
        except Exception as e:
            logger.error(f"Error in request analysis: {e}")
            return add_error(state, "analysis_error", str(e))

    async def inject_universal_context(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """Inject universal context using existing context injector."""
        try:
            if self.context_injector:
                # Leverage existing UniversalContextInjector
                enhanced_context = await self.context_injector.inject_context(
                    user_id=state["user_id"],
                    business_profile_id=state.get("business_profile_id"),
                    agent_id=state["current_agent"]
                )

                state["business_context"].update(enhanced_context.get("business_context", {}))
                state["shared_insights"].extend(enhanced_context.get("cross_agent_insights", []))
                state["collaboration_opportunities"] = enhanced_context.get("collaboration_opportunities", [])

            logger.info("Universal context injected successfully")
            return state
            
        except Exception as e:
            logger.error(f"Error injecting context: {e}")
            return add_error(state, "context_injection_error", str(e))

    def routing_condition(self, state: DatageniusAgentState) -> str:
        """Determine routing based on analysis using dynamic agent mappings."""
        try:
            analysis = state.get("routing_analysis", {})

            # Multi-agent workflow detection
            if analysis.get("collaboration_needed", False):
                return "multi_agent"

            # Single agent routing based on intent and complexity
            intent = analysis.get("intent", "")
            complexity = analysis.get("complexity", "low")

            # Dynamic routing based on intent patterns and available agents
            routing_decision = self._determine_agent_from_intent(intent, complexity)

            # Ensure the selected agent is available
            if routing_decision in self.agent_mappings:
                return routing_decision
            else:
                # Fallback to concierge if agent not available
                return "concierge" if "concierge" in self.agent_mappings else list(self.agent_mappings.keys())[0]

        except Exception as e:
            logger.error(f"Error in routing condition: {e}")
            # Safe fallback to first available agent
            return list(self.agent_mappings.keys())[0] if self.agent_mappings else "concierge"

    def _determine_agent_from_intent(self, intent: str, complexity: str) -> str:
        """
        Determine the best agent based on intent and complexity.

        Args:
            intent: Detected intent from the message
            complexity: Assessed complexity level

        Returns:
            Agent type identifier
        """
        # Intent to agent type mapping - extensible
        intent_mappings = {
            "greeting": "concierge",
            "general_inquiry": "concierge",
            "persona_recommendation": "concierge",
            "data_analysis": "analysis",
            "visualization": "analysis",
            "statistics": "analysis",
            "marketing": "marketing",
            "content_creation": "marketing",
            "campaign": "marketing",
            "classification": "classification",
            "categorization": "classification"
        }

        # Get base agent type from intent
        base_agent = intent_mappings.get(intent, "concierge")

        # Consider complexity for routing decisions
        if complexity == "high" and intent in ["data_analysis", "visualization"]:
            # High complexity analysis might benefit from specialized agents
            if "advanced_analysis" in self.agent_mappings:
                return "advanced_analysis"

        # Check if the base agent is available
        if base_agent in self.agent_mappings:
            return base_agent

        # Fallback logic based on available agents
        available_agents = list(self.agent_mappings.keys())

        # Try to find a suitable fallback
        if intent in ["data_analysis", "visualization", "statistics"] and "analysis" in available_agents:
            return "analysis"
        elif intent in ["marketing", "content_creation", "campaign"] and "marketing" in available_agents:
            return "marketing"
        elif "concierge" in available_agents:
            return "concierge"
        else:
            # Last resort - return first available agent
            return available_agents[0] if available_agents else "concierge"

    # Agent execution methods are now dynamically created by _create_agent_executor

    async def _execute_agent(self, state: DatageniusAgentState, agent_id: str) -> DatageniusAgentState:
        """Execute a specific agent."""
        try:
            # Update agent transition
            state = update_agent_transition(state, agent_id)
            
            # Get agent instance
            agent = self.agent_factory.create_agent_node(agent_id)
            if not agent:
                raise ValueError(f"Agent {agent_id} not found")

            # Execute agent
            latest_message = state["messages"][-1] if state["messages"] else {}
            
            # Prepare agent context
            agent_context = {
                "message": latest_message.get("content", ""),
                "user_id": state["user_id"],
                "conversation_id": state["conversation_id"],
                "business_context": state["business_context"],
                "attached_files": state["attached_files"]
            }

            # Execute agent (this would need to be adapted based on actual agent interface)
            result = await agent.process_message(agent_context)
            
            # Store result
            state["agent_outputs"][agent_id] = result
            
            # Add response message
            response_message = {
                "role": "assistant",
                "content": result.get("content", ""),
                "agent_id": agent_id,
                "timestamp": datetime.now().isoformat()
            }
            state["messages"].append(response_message)

            logger.info(f"Agent {agent_id} executed successfully")
            return state

        except Exception as e:
            logger.error(f"Error executing agent {agent_id}: {e}")
            return add_error(state, "agent_execution_error", str(e), {"agent_id": agent_id})

    async def aggregate_results(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """Aggregate results from multiple agents."""
        try:
            # Collect all agent outputs
            all_outputs = []
            for agent_id, output in state["agent_outputs"].items():
                all_outputs.append({
                    "agent_id": agent_id,
                    "output": output,
                    "timestamp": output.get("timestamp", datetime.now().isoformat())
                })

            # Create aggregated response
            aggregated_result = {
                "type": "multi_agent_response",
                "agents_involved": list(state["agent_outputs"].keys()),
                "individual_outputs": all_outputs,
                "synthesis": await self._synthesize_outputs(all_outputs),
                "timestamp": datetime.now().isoformat()
            }

            state["agent_outputs"]["aggregated"] = aggregated_result

            # Add aggregated response message
            response_message = {
                "role": "assistant",
                "content": aggregated_result["synthesis"],
                "agent_id": "multi_agent",
                "timestamp": datetime.now().isoformat()
            }
            state["messages"].append(response_message)

            logger.info("Multi-agent results aggregated successfully")
            return state

        except Exception as e:
            logger.error(f"Error aggregating results: {e}")
            return add_error(state, "aggregation_error", str(e))

    async def quality_check(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """Perform quality check on agent outputs."""
        try:
            # Calculate quality scores
            quality_scores = {}

            for agent_id, output in state["agent_outputs"].items():
                score = await self._calculate_quality_score(output)
                quality_scores[agent_id] = score

            state["quality_scores"] = quality_scores

            # Overall quality assessment
            avg_quality = sum(quality_scores.values()) / len(quality_scores) if quality_scores else 0.0
            state["execution_metrics"]["overall_quality"] = avg_quality

            logger.info(f"Quality check completed, average score: {avg_quality}")
            return state

        except Exception as e:
            logger.error(f"Error in quality check: {e}")
            return add_error(state, "quality_check_error", str(e))

    def quality_condition(self, state: DatageniusAgentState) -> str:
        """Determine next step based on quality check."""
        try:
            avg_quality = state["execution_metrics"].get("overall_quality", 0.0)
            error_count = len(state["error_history"])

            # Check for critical errors
            if error_count > 3:
                return "escalate"

            # Check quality threshold
            if avg_quality < 0.6:  # Quality threshold
                retry_count = state["execution_metrics"].get("retry_count", 0)
                if retry_count < 2:  # Max retries
                    state["execution_metrics"]["retry_count"] = retry_count + 1
                    return "retry"
                else:
                    return "escalate"

            return "complete"

        except Exception as e:
            logger.error(f"Error in quality condition: {e}")
            return "complete"  # Safe fallback

    # Helper methods for analysis and processing
    async def _detect_intent(self, message: str) -> str:
        """Detect intent from message content."""
        message_lower = message.lower()

        for intent, patterns in self.intent_patterns.items():
            if any(pattern in message_lower for pattern in patterns):
                return intent

        return "general_inquiry"

    async def _assess_complexity(self, message: str) -> str:
        """Assess complexity of the request."""
        # Simple heuristic based on message length and keywords
        complex_keywords = ["analyze", "compare", "synthesize", "comprehensive", "detailed"]

        if len(message) > 200 or any(keyword in message.lower() for keyword in complex_keywords):
            return "high"
        elif len(message) > 50:
            return "medium"
        else:
            return "low"

    async def _analyze_data_needs(self, message: str) -> List[str]:
        """Analyze data requirements from the message."""
        data_keywords = {
            "files": ["file", "document", "upload", "attachment"],
            "database": ["data", "database", "query", "table"],
            "external": ["api", "external", "fetch", "scrape"]
        }

        needs = []
        message_lower = message.lower()

        for need_type, keywords in data_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                needs.append(need_type)

        return needs

    async def _assess_collaboration_needs(self, message: str) -> bool:
        """Assess if collaboration between agents is needed."""
        collaboration_keywords = [
            "comprehensive analysis", "multiple perspectives", "cross-functional",
            "analyze and create", "research and develop", "compare and recommend"
        ]

        message_lower = message.lower()
        return any(keyword in message_lower for keyword in collaboration_keywords)

    async def _get_user_context(self, user_id: str) -> Dict[str, Any]:
        """Get user context for routing decisions."""
        # This would integrate with user preference system
        # For now, return basic context - can be extended to load from database
        try:
            # TODO: Load user preferences from database
            # TODO: Load interaction history for learning
            # TODO: Load user's preferred agents and complexity settings

            return {
                "user_id": user_id,  # Use the user_id parameter
                "preferred_agents": [],
                "interaction_history": [],
                "complexity_preference": "medium",
                "routing_preferences": {}
            }
        except Exception as e:
            logger.error(f"Error loading user context for {user_id}: {e}")
            return {
                "user_id": user_id,
                "preferred_agents": [],
                "interaction_history": [],
                "complexity_preference": "medium"
            }

    async def _synthesize_outputs(self, outputs: List[Dict[str, Any]]) -> str:
        """Synthesize multiple agent outputs into a coherent response."""
        if not outputs:
            return "No outputs to synthesize."

        if len(outputs) == 1:
            return outputs[0]["output"].get("content", "")

        # Simple synthesis - in production this would use an AI model
        synthesis = "Based on analysis from multiple agents:\n\n"

        for i, output in enumerate(outputs, 1):
            agent_id = output["agent_id"]
            content = output["output"].get("content", "")
            synthesis += f"{i}. {agent_id}: {content[:200]}...\n\n"

        return synthesis

    async def _calculate_quality_score(self, output: Dict[str, Any]) -> float:
        """Calculate quality score for agent output."""
        # Simple quality scoring - in production this would be more sophisticated
        score = 1.0

        content = output.get("content", "")
        if not content:
            score -= 0.5
        elif len(content) < 10:
            score -= 0.3

        if "error" in output:
            score -= 0.4

        return max(0.0, score)

    def refresh_agent_mappings(self) -> None:
        """
        Refresh agent mappings to pick up newly registered agents.

        This method allows for runtime extensibility - new agents can be
        registered and made available without restarting the router.
        """
        try:
            old_mappings = self.agent_mappings.copy()
            self.agent_mappings = self._load_dynamic_agent_mappings()

            # Log changes
            new_agents = set(self.agent_mappings.keys()) - set(old_mappings.keys())
            removed_agents = set(old_mappings.keys()) - set(self.agent_mappings.keys())

            if new_agents:
                logger.info(f"Added new agent mappings: {list(new_agents)}")
            if removed_agents:
                logger.info(f"Removed agent mappings: {list(removed_agents)}")

        except Exception as e:
            logger.error(f"Error refreshing agent mappings: {e}")

    # Profile-Aware Routing Methods

    async def route_with_business_profile(
        self,
        state: DatageniusAgentState,
        business_profile: Optional[BusinessProfile] = None
    ) -> DatageniusAgentState:
        """
        Route request with business profile optimization.

        Args:
            state: Current agent state
            business_profile: Business profile for context-aware routing

        Returns:
            Updated agent state with optimized routing
        """
        try:
            # Extract business profile from state if not provided
            if not business_profile:
                profile_data = state.metadata.get("business_profile")
                if profile_data:
                    business_profile = BusinessProfile(
                        profile_id=profile_data.get("id", ""),
                        name=profile_data.get("name", ""),
                        industry=profile_data.get("industry", ""),
                        business_type=BusinessType(profile_data.get("business_type", "smb")),
                        business_size=profile_data.get("business_size", "medium"),
                        target_audience=profile_data.get("target_audience", ""),
                        products_services=profile_data.get("products_services", ""),
                        marketing_goals=profile_data.get("marketing_goals", ""),
                        context_metadata=profile_data.get("context_metadata", {}),
                        performance_metrics=profile_data.get("performance_metrics", {}),
                        user_preferences=profile_data.get("user_preferences", {})
                    )

            if business_profile:
                # Get industry recommendations
                recommendations = await self.profile_optimizer.get_industry_recommendations(
                    business_profile.industry,
                    business_profile.business_type
                )

                # Apply profile-aware routing
                optimized_routing = await self._apply_profile_aware_routing(
                    state, business_profile, recommendations
                )

                # Update state with optimization context
                state.workflow_context["profile_optimization"] = {
                    "profile_id": business_profile.profile_id,
                    "industry": business_profile.industry,
                    "business_type": business_profile.business_type.value,
                    "recommendations": recommendations,
                    "optimized_routing": optimized_routing
                }

                # Publish profile routing event
                await event_bus.publish(LangGraphEvent(
                    event_type="routing.profile_aware_routing_applied",
                    timestamp=datetime.now(),
                    source="intelligent_router",
                    data={
                        "profile_id": business_profile.profile_id,
                        "industry": business_profile.industry,
                        "routing_decision": optimized_routing
                    },
                    priority=EventPriority.MEDIUM
                ))

                logger.info(f"Applied profile-aware routing for {business_profile.name}")

            return state

        except Exception as e:
            logger.error(f"Error in profile-aware routing: {e}")
            state.error_state = {"routing_error": str(e)}
            return state

    async def _apply_profile_aware_routing(
        self,
        state: DatageniusAgentState,
        business_profile: BusinessProfile,
        recommendations: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply profile-aware routing optimizations."""
        try:
            # Get preferred workflows for this profile
            preferred_workflows = recommendations.get("preferred_workflows", [])
            optimization_params = recommendations.get("optimization_params", {})

            # Analyze current request intent
            request_analysis = await self._analyze_request_with_profile_context(
                state.user_query, business_profile
            )

            # Select optimal agent based on profile and request
            optimal_agent = self._select_profile_optimized_agent(
                request_analysis, business_profile, preferred_workflows
            )

            # Generate routing decision
            routing_decision = {
                "selected_agent": optimal_agent,
                "routing_strategy": "profile_aware",
                "confidence_score": request_analysis.get("confidence", 0.7),
                "optimization_applied": True,
                "profile_context": {
                    "industry_focus": business_profile.industry,
                    "business_maturity": self._assess_business_maturity(business_profile),
                    "complexity_needs": request_analysis.get("complexity", "medium")
                }
            }

            return routing_decision

        except Exception as e:
            logger.error(f"Error applying profile-aware routing: {e}")
            return {
                "selected_agent": "concierge",
                "routing_strategy": "fallback",
                "error": str(e)
            }

    async def _analyze_request_with_profile_context(
        self,
        user_query: str,
        business_profile: BusinessProfile
    ) -> Dict[str, Any]:
        """Analyze request with business profile context."""
        try:
            # Basic intent analysis
            base_analysis = await self.analyze_request({"user_query": user_query})

            # Enhance with profile context
            profile_enhanced_analysis = {
                **base_analysis,
                "industry_relevance": self._calculate_industry_relevance(
                    user_query, business_profile.industry
                ),
                "business_goal_alignment": self._calculate_goal_alignment(
                    user_query, business_profile.marketing_goals
                ),
                "audience_consideration": self._analyze_audience_relevance(
                    user_query, business_profile.target_audience
                ),
                "business_context": {
                    "industry": business_profile.industry,
                    "business_type": business_profile.business_type.value,
                    "size": business_profile.business_size
                }
            }

            # Adjust confidence based on profile completeness
            profile_completeness = self._calculate_profile_completeness(business_profile)
            profile_enhanced_analysis["confidence"] = (
                base_analysis.get("confidence", 0.5) * 0.7 +
                profile_completeness * 0.3
            )

            return profile_enhanced_analysis

        except Exception as e:
            logger.error(f"Error analyzing request with profile context: {e}")
            return {"error": str(e), "confidence": 0.3}

    def _select_profile_optimized_agent(
        self,
        request_analysis: Dict[str, Any],
        business_profile: BusinessProfile,
        preferred_workflows: List[str]
    ) -> str:
        """Select optimal agent based on profile and request analysis."""
        try:
            # Get base intent
            intent = request_analysis.get("intent", "general_inquiry")
            complexity = request_analysis.get("complexity", "medium")

            # Industry-specific agent preferences
            industry_agent_preferences = {
                "technology": ["analysis", "data_analysis"],
                "healthcare": ["compliance", "analysis"],
                "finance": ["analysis", "risk_assessment"],
                "retail": ["marketing", "customer_analysis"],
                "manufacturing": ["operational_analysis", "efficiency"]
            }

            # Get industry preferences
            industry_preferences = industry_agent_preferences.get(
                business_profile.industry.lower(), []
            )

            # Business type considerations
            if business_profile.business_type == BusinessType.STARTUP:
                # Startups prefer quick, actionable insights
                if complexity == "high":
                    complexity = "medium"  # Simplify for startups
            elif business_profile.business_type == BusinessType.ENTERPRISE:
                # Enterprises can handle complex analysis
                if complexity == "low":
                    complexity = "medium"  # Enhance for enterprises

            # Select agent based on combined factors
            candidate_agents = []

            # Add agents based on intent
            base_agent = self._determine_agent_from_intent(intent, complexity)
            if base_agent:
                candidate_agents.append((base_agent, 10))

            # Add industry-preferred agents
            for pref in industry_preferences:
                if pref in self.agent_mappings:
                    candidate_agents.append((pref, 8))

            # Add workflow-preferred agents
            for workflow in preferred_workflows:
                # Map workflow to agent type
                workflow_agent = self._map_workflow_to_agent(workflow)
                if workflow_agent and workflow_agent in self.agent_mappings:
                    candidate_agents.append((workflow_agent, 6))

            # Select highest scoring available agent
            if candidate_agents:
                # Sort by score and return best available
                candidate_agents.sort(key=lambda x: x[1], reverse=True)
                for agent, score in candidate_agents:
                    if agent in self.agent_mappings:
                        return agent

            # Fallback to concierge
            return "concierge" if "concierge" in self.agent_mappings else list(self.agent_mappings.keys())[0]

        except Exception as e:
            logger.error(f"Error selecting profile-optimized agent: {e}")
            return "concierge"

    def _calculate_industry_relevance(self, user_query: str, industry: str) -> float:
        """Calculate how relevant the query is to the business industry."""
        if not user_query or not industry:
            return 0.5

        industry_keywords = {
            "technology": ["software", "digital", "tech", "innovation", "automation", "AI"],
            "healthcare": ["patient", "medical", "health", "treatment", "clinical", "care"],
            "finance": ["financial", "investment", "banking", "capital", "revenue", "profit"],
            "retail": ["customer", "sales", "product", "market", "shopping", "consumer"],
            "manufacturing": ["production", "manufacturing", "supply", "quality", "efficiency"]
        }

        keywords = industry_keywords.get(industry.lower(), [])
        query_lower = user_query.lower()

        matches = sum(1 for keyword in keywords if keyword in query_lower)
        return min(matches / max(len(keywords), 1), 1.0)

    def _calculate_goal_alignment(self, user_query: str, marketing_goals: str) -> float:
        """Calculate alignment between query and business goals."""
        if not user_query or not marketing_goals:
            return 0.5

        goal_keywords = ["growth", "revenue", "customer", "market", "brand", "sales"]
        query_lower = user_query.lower()
        goals_lower = marketing_goals.lower()

        # Check for goal keywords in both query and goals
        query_goal_matches = sum(1 for keyword in goal_keywords if keyword in query_lower)
        goals_keyword_matches = sum(1 for keyword in goal_keywords if keyword in goals_lower)

        if goals_keyword_matches == 0:
            return 0.5

        alignment = query_goal_matches / goals_keyword_matches
        return min(alignment, 1.0)

    def _analyze_audience_relevance(self, user_query: str, target_audience: str) -> Dict[str, Any]:
        """Analyze relevance to target audience."""
        if not user_query or not target_audience:
            return {"relevance": 0.5, "audience_type": "general"}

        query_lower = user_query.lower()
        audience_lower = target_audience.lower()

        audience_indicators = {
            "b2b": ["business", "enterprise", "company", "corporate"],
            "b2c": ["consumer", "customer", "individual", "personal"],
            "technical": ["technical", "developer", "engineer", "IT"],
            "executive": ["executive", "management", "strategic", "leadership"]
        }

        detected_audience = "general"
        max_relevance = 0.5

        for audience_type, indicators in audience_indicators.items():
            # Check if audience description matches this type
            audience_matches = sum(1 for indicator in indicators if indicator in audience_lower)
            # Check if query is relevant to this audience type
            query_matches = sum(1 for indicator in indicators if indicator in query_lower)

            if audience_matches > 0:
                relevance = query_matches / len(indicators)
                if relevance > max_relevance:
                    max_relevance = relevance
                    detected_audience = audience_type

        return {
            "relevance": max_relevance,
            "audience_type": detected_audience
        }

    def _calculate_profile_completeness(self, business_profile: BusinessProfile) -> float:
        """Calculate how complete a business profile is."""
        completeness = 0.0
        total_fields = 7

        if business_profile.name:
            completeness += 1
        if business_profile.industry:
            completeness += 1
        if business_profile.business_type:
            completeness += 1
        if business_profile.business_size:
            completeness += 1
        if business_profile.target_audience:
            completeness += 1
        if business_profile.products_services:
            completeness += 1
        if business_profile.marketing_goals:
            completeness += 1

        return completeness / total_fields

    def _assess_business_maturity(self, business_profile: BusinessProfile) -> str:
        """Assess business maturity level."""
        if business_profile.business_type == BusinessType.STARTUP:
            return "early"
        elif business_profile.business_type == BusinessType.ENTERPRISE:
            return "mature"
        elif business_profile.business_size in ["small", "medium"]:
            return "growing"
        else:
            return "established"

    def _map_workflow_to_agent(self, workflow: str) -> Optional[str]:
        """Map workflow template to appropriate agent type."""
        workflow_agent_mapping = {
            "analysis_template": "analysis",
            "dashboard_generation": "analysis",
            "business_analysis": "analysis",
            "market_research": "marketing",
            "competitive_analysis": "marketing",
            "customer_analysis": "marketing",
            "financial_analysis": "analysis",
            "operational_analysis": "analysis",
            "compliance_analysis": "analysis",
            "risk_analysis": "analysis"
        }

        return workflow_agent_mapping.get(workflow)

    async def get_profile_routing_insights(self, profile_id: str) -> Dict[str, Any]:
        """Get routing insights for a specific business profile."""
        try:
            # Get optimization insights from profile optimizer
            optimization_insights = await self.profile_optimizer.get_optimization_insights(profile_id)

            # Add routing-specific insights
            routing_insights = {
                **optimization_insights,
                "routing_patterns": self._analyze_routing_patterns(profile_id),
                "agent_preferences": self._get_agent_preferences(profile_id),
                "performance_metrics": self._get_routing_performance_metrics(profile_id)
            }

            return routing_insights

        except Exception as e:
            logger.error(f"Error getting profile routing insights: {e}")
            return {
                "profile_id": profile_id,
                "error": str(e)
            }

    def _analyze_routing_patterns(self, profile_id: str) -> Dict[str, Any]:
        """Analyze routing patterns for a profile."""
        # This would analyze historical routing data
        # For now, return placeholder data
        return {
            "most_used_agents": ["analysis", "marketing"],
            "routing_frequency": "high",
            "success_rate": 0.85,
            "average_response_time": 2.3
        }

    def _get_agent_preferences(self, profile_id: str) -> Dict[str, Any]:
        """Get agent preferences for a profile."""
        # This would analyze user preferences and performance
        # For now, return placeholder data
        return {
            "preferred_agents": ["analysis", "marketing"],
            "avoided_agents": [],
            "performance_rankings": {
                "analysis": 0.9,
                "marketing": 0.85,
                "concierge": 0.8
            }
        }

    def _get_routing_performance_metrics(self, profile_id: str) -> Dict[str, Any]:
        """Get routing performance metrics for a profile."""
        # This would calculate actual performance metrics
        # For now, return placeholder data
        return {
            "total_routes": 150,
            "successful_routes": 128,
            "success_rate": 0.853,
            "average_confidence": 0.78,
            "average_response_time": 2.1,
            "user_satisfaction": 0.82
        }

    def get_available_agents(self) -> List[str]:
        """
        Get list of currently available agent types.

        Returns:
            List of available agent type identifiers
        """
        return list(self.agent_mappings.keys())

    def add_custom_intent_pattern(self, intent: str, patterns: List[str]) -> None:
        """
        Add custom intent detection patterns for extensibility.

        Args:
            intent: Intent identifier
            patterns: List of keyword patterns for this intent
        """
        if intent not in self.intent_patterns:
            self.intent_patterns[intent] = []

        self.intent_patterns[intent].extend(patterns)
        logger.info(f"Added custom patterns for intent '{intent}': {patterns}")

    def get_routing_statistics(self) -> Dict[str, Any]:
        """
        Get routing statistics for monitoring and optimization.

        Returns:
            Dictionary containing routing statistics
        """
        return {
            "total_routes": len(self.routing_history),
            "agent_usage": self.success_metrics,
            "available_agents": list(self.agent_mappings.keys()),
            "intent_patterns": {intent: len(patterns) for intent, patterns in self.intent_patterns.items()},
            "routing_algorithms": self.routing_manager.get_algorithm_stats()
        }

    def register_custom_routing_algorithm(self, algorithm: RoutingAlgorithm):
        """
        Register a custom routing algorithm.

        Args:
            algorithm: Custom routing algorithm instance
        """
        self.routing_manager.register_algorithm(algorithm)
        logger.info(f"Registered custom routing algorithm: {algorithm.name}")

    def unregister_routing_algorithm(self, algorithm_name: str):
        """
        Unregister a routing algorithm.

        Args:
            algorithm_name: Name of the algorithm to unregister
        """
        self.routing_manager.unregister_algorithm(algorithm_name)
        logger.info(f"Unregistered routing algorithm: {algorithm_name}")

    def update_routing_performance(self, algorithm_name: str, success: bool):
        """
        Update routing algorithm performance.

        Args:
            algorithm_name: Name of the algorithm
            success: Whether the routing was successful
        """
        self.routing_manager.update_algorithm_performance(algorithm_name, success)

    def get_available_routing_algorithms(self) -> List[str]:
        """
        Get list of available routing algorithms.

        Returns:
            List of algorithm names
        """
        return list(self.routing_manager.algorithm_registry.keys())

    def update_intent_detection_feedback(self, intent: str, message: str, success: bool, user_id: str = None):
        """
        Update intent detection feedback for learning.

        Args:
            intent: The detected intent
            message: The original message
            success: Whether the routing was successful
            user_id: User ID for personalization
        """
        # Extract patterns from the message for feedback
        message_lower = message.lower()
        words = message_lower.split()

        # Update feedback for various patterns
        for i in range(len(words)):
            for length in [1, 2, 3]:
                if i + length <= len(words):
                    pattern = " ".join(words[i:i+length])
                    if len(pattern) > 2:  # Minimum pattern length
                        self.intent_detector.update_success_feedback(intent, pattern, success, user_id)

    def add_custom_intent_pattern(self, intent: str, pattern: str, confidence: float = 0.8):
        """
        Add a custom intent pattern.

        Args:
            intent: Intent name
            pattern: Pattern to match
            confidence: Confidence score for the pattern
        """
        self.intent_detector.add_custom_pattern(intent, pattern, confidence)

    def get_intent_detection_statistics(self) -> Dict[str, Any]:
        """
        Get intent detection statistics.

        Returns:
            Dictionary with intent detection statistics
        """
        return self.intent_detector.get_intent_statistics()

    def learn_from_routing_success(self, routing_context: Dict[str, Any], success: bool,
                                 outcome: Dict[str, Any] = None):
        """
        Learn from routing success/failure to improve future decisions.

        Args:
            routing_context: The context used for routing
            success: Whether the routing was successful
            outcome: Additional outcome information
        """
        try:
            intent = routing_context.get("detected_intent")
            message = routing_context.get("message_content", "")
            user_id = routing_context.get("user_id")

            if intent and message:
                self.update_intent_detection_feedback(intent, message, success, user_id)

            # Update routing algorithm performance
            algorithm_used = routing_context.get("algorithm_used")
            if algorithm_used:
                self.update_routing_performance(algorithm_used, success)

            # Update capability inference feedback
            if success and "available_agents" in routing_context:
                selected_agent = routing_context.get("selected_agent")
                if selected_agent:
                    # Find the agent metadata
                    for agent in routing_context["available_agents"]:
                        if agent.persona_id == selected_agent:
                            # Update capability confidence
                            for capability in agent.capabilities:
                                self.capability_registry.inference_engine.update_success_feedback(
                                    selected_agent, capability, success
                                )
                            break

            # Record performance for optimization
            if outcome is None:
                outcome = {
                    "agent_used": routing_context.get("selected_agent"),
                    "strategy_used": routing_context.get("strategy"),
                    "response_time": routing_context.get("response_time", 0.0),
                    "user_satisfaction": routing_context.get("user_satisfaction", 0.5),
                    "confidence_score": routing_context.get("confidence_score", 0.5)
                }

            self.performance_optimizer.record_routing_performance(routing_context, outcome, success)

            logger.debug(f"Updated learning from routing success: {success}")

        except Exception as e:
            logger.error(f"Error learning from routing success: {e}")

    def update_agent_performance_metrics(self, agent_id: str, metrics: Dict[str, Any]):
        """
        Update performance metrics for an agent.

        Args:
            agent_id: Agent identifier
            metrics: Performance metrics dictionary
        """
        self.scoring_system.update_performance_metrics(agent_id, metrics)

    def add_agent_user_feedback(self, agent_id: str, feedback: Dict[str, Any]):
        """
        Add user feedback for an agent.

        Args:
            agent_id: Agent identifier
            feedback: User feedback dictionary
        """
        self.scoring_system.add_user_feedback(agent_id, feedback)

    def update_agent_context_performance(self, agent_id: str, context_type: str, performance_score: float):
        """
        Update agent performance for a specific context.

        Args:
            agent_id: Agent identifier
            context_type: Context type
            performance_score: Performance score
        """
        self.scoring_system.update_context_performance(agent_id, context_type, performance_score)

    def update_agent_capability_performance(self, agent_id: str, capability: str, performance_score: float):
        """
        Update agent performance for a specific capability.

        Args:
            agent_id: Agent identifier
            capability: Capability name
            performance_score: Performance score
        """
        self.scoring_system.update_capability_performance(agent_id, capability, performance_score)

    def get_agent_adaptive_score(self, agent_id: str, context: Dict[str, Any] = None) -> float:
        """
        Get the adaptive score for an agent.

        Args:
            agent_id: Agent identifier
            context: Optional context for scoring

        Returns:
            Adaptive score for the agent
        """
        return self.scoring_system.get_agent_score(agent_id, context)

    def get_scoring_system_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the adaptive scoring system.

        Returns:
            Dictionary with scoring system statistics
        """
        return self.scoring_system.get_scoring_statistics()

    def update_behavior_setting(self, category: str, setting: str, value: Any):
        """
        Update a behavior setting at runtime.

        Args:
            category: Setting category
            setting: Setting name
            value: New value
        """
        self.behavior_manager.update_behavior_setting(category, setting, value)

        # Apply changes to relevant systems
        if category == "agent_scoring" and self.behavior_manager.should_use_adaptive_scoring():
            scoring_weights = self.behavior_manager.get_scoring_weights()
            self.scoring_system.scoring_weights = scoring_weights

    def update_threshold(self, threshold: str, value: float):
        """
        Update a threshold value at runtime.

        Args:
            threshold: Threshold name
            value: New threshold value
        """
        self.behavior_manager.update_threshold(threshold, value)

    def toggle_feature(self, feature: str, enabled: bool = None):
        """
        Toggle a feature flag.

        Args:
            feature: Feature name
            enabled: Enable/disable (None to toggle)
        """
        self.behavior_manager.toggle_feature(feature, enabled)

    def reload_configuration(self):
        """Reload configuration from file."""
        self.behavior_manager.reload_configuration()

        # Reinitialize systems with new configuration
        if self.behavior_manager.should_use_adaptive_scoring():
            scoring_weights = self.behavior_manager.get_scoring_weights()
            self.scoring_system.scoring_weights = scoring_weights

    def get_current_configuration(self) -> Dict[str, Any]:
        """
        Get current configuration.

        Returns:
            Dictionary with current configuration
        """
        return self.behavior_manager.export_configuration()

    def save_configuration(self, file_path: Path = None):
        """
        Save current configuration to file.

        Args:
            file_path: Optional custom file path
        """
        self.behavior_manager.save_configuration(file_path)

    def validate_configuration(self) -> List[str]:
        """
        Validate current configuration.

        Returns:
            List of validation issues (empty if valid)
        """
        return self.behavior_manager.validate_configuration()

    def get_behavior_setting(self, category: str, setting: str, default: Any = None) -> Any:
        """
        Get a behavior setting value.

        Args:
            category: Setting category
            setting: Setting name
            default: Default value if not found

        Returns:
            Setting value
        """
        return self.behavior_manager.get_behavior_setting(category, setting, default)

    def is_feature_enabled(self, feature: str) -> bool:
        """
        Check if a feature is enabled.

        Args:
            feature: Feature name

        Returns:
            True if feature is enabled
        """
        return self.behavior_manager.is_feature_enabled(feature)

    def get_configuration_summary(self) -> Dict[str, Any]:
        """
        Get a summary of current configuration.

        Returns:
            Configuration summary
        """
        return {
            "learning_enabled": self.behavior_manager.should_use_learning_system(),
            "adaptive_scoring_enabled": self.behavior_manager.should_use_adaptive_scoring(),
            "collaboration_enabled": self.behavior_manager.should_enable_collaboration(),
            "collaboration_threshold": self.behavior_manager.get_collaboration_threshold(),
            "max_collaborating_agents": self.behavior_manager.get_max_collaborating_agents(),
            "scoring_weights": self.behavior_manager.get_scoring_weights(),
            "active_features": [k for k, v in self.behavior_manager.feature_flags.items() if v],
            "validation_issues": self.validate_configuration()
        }

    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get performance summary from the optimizer.

        Returns:
            Performance summary dictionary
        """
        return self.performance_optimizer.get_performance_summary()

    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """
        Get current optimization recommendations.

        Returns:
            List of optimization recommendations
        """
        return self.performance_optimizer.get_optimization_recommendations()

    def apply_optimization_rule(self, rule_id: str) -> bool:
        """
        Apply an optimization rule.

        Args:
            rule_id: ID of the rule to apply

        Returns:
            True if rule was applied successfully
        """
        return self.performance_optimizer.apply_optimization_rule(rule_id)

    def get_pattern_analysis(self) -> Dict[str, Any]:
        """
        Get pattern analysis from the optimizer.

        Returns:
            Pattern analysis dictionary
        """
        return self.performance_optimizer.get_pattern_analysis()

    def record_routing_outcome(self, routing_context: Dict[str, Any],
                             outcome: Dict[str, Any], success: bool):
        """
        Record a routing outcome for performance tracking.

        Args:
            routing_context: Context used for routing
            outcome: Outcome information
            success: Whether the routing was successful
        """
        self.performance_optimizer.record_routing_performance(routing_context, outcome, success)

    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics about the routing system.

        Returns:
            Comprehensive statistics dictionary
        """
        return {
            "routing_statistics": self.get_routing_statistics(),
            "intent_detection_statistics": self.get_intent_detection_statistics(),
            "scoring_system_statistics": self.get_scoring_system_statistics(),
            "performance_summary": self.get_performance_summary(),
            "pattern_analysis": self.get_pattern_analysis(),
            "configuration_summary": self.get_configuration_summary(),
            "optimization_recommendations": self.get_optimization_recommendations(),
            "declaration_statistics": self.get_declaration_statistics()
        }

    def get_agent_declaration(self, persona_id: str) -> Dict[str, Any]:
        """
        Get agent's self-declaration.

        Args:
            persona_id: Agent persona ID

        Returns:
            Agent's declaration dictionary
        """
        return self.declaration_system.agent_declarations.get(persona_id, {})

    def update_agent_declaration(self, persona_id: str, updates: Dict[str, Any]):
        """
        Update an agent's self-declaration.

        Args:
            persona_id: Agent persona ID
            updates: Declaration updates
        """
        self.declaration_system.update_agent_declaration(persona_id, updates)

    def get_agents_by_capability(self, capability: str) -> List[str]:
        """
        Get agents that declare a specific capability.

        Args:
            capability: Capability name

        Returns:
            List of agent persona IDs
        """
        return self.declaration_system.get_agents_by_capability(capability)

    def get_all_declared_capabilities(self) -> Set[str]:
        """
        Get all capabilities declared by agents.

        Returns:
            Set of all declared capabilities
        """
        return self.declaration_system.get_all_declared_capabilities()

    def get_declaration_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about agent declarations.

        Returns:
            Declaration statistics dictionary
        """
        return self.declaration_system.get_declaration_statistics()

    def should_route_to_agent_by_declaration(self, persona_id: str,
                                           context: Dict[str, Any]) -> Tuple[bool, float, str]:
        """
        Check if agent should handle request based on its declaration.

        Args:
            persona_id: Agent persona ID
            context: Routing context

        Returns:
            Tuple of (should_route, confidence, reasoning)
        """
        return self.declaration_system.should_route_to_agent(persona_id, context)

    def get_agent_specialization_score(self, persona_id: str, context: Dict[str, Any]) -> float:
        """
        Get agent's specialization score for a context.

        Args:
            persona_id: Agent persona ID
            context: Routing context

        Returns:
            Specialization score (0.0 to 1.0)
        """
        return self.declaration_system.get_agent_specialization_score(persona_id, context)

    def register_new_agent_declaration(self, persona_id: str, config: Dict[str, Any]):
        """
        Register a new agent's self-declaration.

        Args:
            persona_id: Agent persona ID
            config: Agent configuration with self-declaration
        """
        self.declaration_system.register_agent_declaration(persona_id, config)

    def create_agent_declaration_template(self) -> Dict[str, Any]:
        """
        Create a template for agent self-declaration.

        Returns:
            Declaration template dictionary
        """
        return {
            "self_declaration": {
                "capabilities": {
                    "declared_capabilities": [],
                    "confidence_levels": {},
                    "specialization_areas": [],
                    "supported_formats": ["text"],
                    "capability_descriptions": {},
                    "capability_examples": {},
                    "capability_limitations": {},
                    "required_tools": [],
                    "optional_tools": []
                },
                "routing_preferences": {
                    "preferred_complexity_levels": ["medium"],
                    "collaboration_preference": 0.5,
                    "primary_agent_capable": False,
                    "fallback_agent": False,
                    "preferred_user_types": [],
                    "avoid_contexts": [],
                    "priority_intents": [],
                    "load_balancing_weight": 1.0,
                    "concurrent_request_limit": 10,
                    "preferred_time_slots": []
                },
                "performance_expectations": {
                    "expected_response_time": 2.0,
                    "expected_success_rate": 0.95,
                    "expected_satisfaction": 0.85,
                    "performance_metrics": {},
                    "quality_thresholds": {},
                    "optimization_targets": [],
                    "monitoring_preferences": {}
                },
                "inference_patterns": {
                    "intent_keywords": [],
                    "capability_indicators": [],
                    "context_preferences": [],
                    "negative_indicators": [],
                    "confidence_boosters": [],
                    "pattern_weights": {},
                    "semantic_patterns": [],
                    "user_intent_mappings": {}
                }
            }
        }

    def register_runtime_agent(self, persona_id: str, config: Dict[str, Any]) -> bool:
        """
        Register a new agent at runtime.

        Args:
            persona_id: Agent persona ID
            config: Agent configuration

        Returns:
            True if registration was successful
        """
        return self.extensibility_framework.register_runtime_agent(persona_id, config)

    def unregister_runtime_agent(self, persona_id: str) -> bool:
        """
        Unregister a runtime agent.

        Args:
            persona_id: Agent persona ID

        Returns:
            True if unregistration was successful
        """
        return self.extensibility_framework.unregister_runtime_agent(persona_id)

    def register_extension_point(self, name: str, callback: Callable):
        """
        Register an extension point callback.

        Args:
            name: Extension point name
            callback: Callback function
        """
        self.extensibility_framework.register_extension_point(name, callback)

    def trigger_extension_point(self, name: str, *args, **kwargs):
        """
        Trigger an extension point.

        Args:
            name: Extension point name
            *args: Positional arguments
            **kwargs: Keyword arguments
        """
        self.extensibility_framework.trigger_extension_point(name, *args, **kwargs)

    def register_plugin(self, plugin_name: str, plugin_instance: Any):
        """
        Register a plugin.

        Args:
            plugin_name: Plugin name
            plugin_instance: Plugin instance
        """
        self.extensibility_framework.register_plugin(plugin_name, plugin_instance)

    def unregister_plugin(self, plugin_name: str):
        """
        Unregister a plugin.

        Args:
            plugin_name: Plugin name
        """
        self.extensibility_framework.unregister_plugin(plugin_name)

    def enable_hot_reload(self):
        """Enable hot reload functionality."""
        self.extensibility_framework.enable_hot_reload()

    def disable_hot_reload(self):
        """Disable hot reload functionality."""
        self.extensibility_framework.disable_hot_reload()

    def get_runtime_agents(self) -> Dict[str, Dict[str, Any]]:
        """Get all runtime agents."""
        return self.extensibility_framework.get_runtime_agents()

    def get_extensibility_status(self) -> Dict[str, Any]:
        """Get extensibility framework status."""
        return self.extensibility_framework.get_framework_status()

    def watch_directory(self, path: Path, callback: Callable, pattern: str = "*"):
        """
        Watch a directory for changes.

        Args:
            path: Directory path to watch
            callback: Callback function for changes
            pattern: File pattern to watch
        """
        self.extensibility_framework.watch_directory(path, callback, pattern)

    def get_watched_paths(self) -> List[str]:
        """Get all watched paths."""
        return self.extensibility_framework.get_watched_paths()

    def get_extension_points(self) -> List[str]:
        """Get all registered extension points."""
        return self.extensibility_framework.get_extension_points()

    def get_plugins(self) -> List[str]:
        """Get all registered plugins."""
        return self.extensibility_framework.get_plugins()

    def run_extensibility_validation(self) -> Dict[str, Any]:
        """
        Run comprehensive validation of extensibility improvements.

        Returns:
            Validation results dictionary
        """
        validation_results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unknown",
            "tests": {},
            "issues": [],
            "recommendations": []
        }

        try:
            # Test 1: Dynamic Capability Inference
            validation_results["tests"]["dynamic_capability_inference"] = \
                self._test_dynamic_capability_inference()

            # Test 2: Plugin Architecture
            validation_results["tests"]["plugin_architecture"] = \
                self._test_plugin_architecture()

            # Test 3: Learning Intent Detection
            validation_results["tests"]["learning_intent_detection"] = \
                self._test_learning_intent_detection()

            # Test 4: Adaptive Scoring
            validation_results["tests"]["adaptive_scoring"] = \
                self._test_adaptive_scoring()

            # Test 5: Configuration System
            validation_results["tests"]["configuration_system"] = \
                self._test_configuration_system()

            # Test 6: Performance Optimization
            validation_results["tests"]["performance_optimization"] = \
                self._test_performance_optimization()

            # Test 7: Agent Self-Declaration
            validation_results["tests"]["agent_self_declaration"] = \
                self._test_agent_self_declaration()

            # Test 8: Runtime Extensibility
            validation_results["tests"]["runtime_extensibility"] = \
                self._test_runtime_extensibility()

            # Calculate overall status
            passed_tests = sum(1 for test in validation_results["tests"].values() if test["status"] == "passed")
            total_tests = len(validation_results["tests"])

            if passed_tests == total_tests:
                validation_results["overall_status"] = "passed"
            elif passed_tests >= total_tests * 0.8:
                validation_results["overall_status"] = "mostly_passed"
            elif passed_tests >= total_tests * 0.5:
                validation_results["overall_status"] = "partially_passed"
            else:
                validation_results["overall_status"] = "failed"

            # Generate recommendations
            validation_results["recommendations"] = self._generate_validation_recommendations(
                validation_results["tests"]
            )

            logger.info(f"Extensibility validation completed: {validation_results['overall_status']}")

        except Exception as e:
            validation_results["overall_status"] = "error"
            validation_results["issues"].append(f"Validation error: {str(e)}")
            logger.error(f"Error in extensibility validation: {e}")

        return validation_results

    def _test_dynamic_capability_inference(self) -> Dict[str, Any]:
        """Test dynamic capability inference system."""
        test_result = {"status": "unknown", "details": [], "issues": []}

        try:
            # Test capability inference from configuration
            test_config = {
                "name": "Test Agent",
                "description": "An agent for data analysis and visualization tasks",
                "capabilities": ["data_analysis"]
            }

            inferred_capabilities = self.capability_registry.infer_capabilities_from_config(
                "test-agent", test_config
            )

            # Check if inference worked
            if "data_analysis" in inferred_capabilities:
                test_result["details"].append("✓ Explicit capability inference working")
            else:
                test_result["issues"].append("✗ Explicit capability inference failed")

            if "visualization" in inferred_capabilities:
                test_result["details"].append("✓ Semantic capability inference working")
            else:
                test_result["issues"].append("✗ Semantic capability inference failed")

            # Test learning from success feedback
            self.capability_registry.inference_engine.update_success_feedback(
                "test-agent", "data_analysis", True
            )

            confidence = self.capability_registry.inference_engine.get_capability_confidence(
                "test-agent", "data_analysis"
            )

            if confidence > 0.5:
                test_result["details"].append("✓ Success feedback learning working")
            else:
                test_result["issues"].append("✗ Success feedback learning failed")

            test_result["status"] = "passed" if not test_result["issues"] else "failed"

        except Exception as e:
            test_result["status"] = "error"
            test_result["issues"].append(f"Test error: {str(e)}")

        return test_result

    def _test_plugin_architecture(self) -> Dict[str, Any]:
        """Test plugin architecture system."""
        test_result = {"status": "unknown", "details": [], "issues": []}

        try:
            # Test routing algorithm registration
            class TestRoutingAlgorithm(RoutingAlgorithm):
                def __init__(self):
                    super().__init__("test_algorithm", priority=5)

                async def can_handle(self, context):
                    return True

                async def calculate_routing_decision(self, context):
                    return RoutingDecision(
                        strategy=RoutingStrategy.SINGLE_AGENT,
                        primary_agent="test-agent",
                        reasoning="Test algorithm decision"
                    )

            test_algorithm = TestRoutingAlgorithm()
            self.register_custom_routing_algorithm(test_algorithm)

            if "test_algorithm" in self.get_available_routing_algorithms():
                test_result["details"].append("✓ Custom routing algorithm registration working")
            else:
                test_result["issues"].append("✗ Custom routing algorithm registration failed")

            # Test algorithm performance tracking
            self.update_routing_performance("test_algorithm", True)
            stats = self.routing_manager.get_algorithm_stats()

            if "test_algorithm" in stats:
                test_result["details"].append("✓ Algorithm performance tracking working")
            else:
                test_result["issues"].append("✗ Algorithm performance tracking failed")

            # Cleanup
            self.unregister_routing_algorithm("test_algorithm")

            test_result["status"] = "passed" if not test_result["issues"] else "failed"

        except Exception as e:
            test_result["status"] = "error"
            test_result["issues"].append(f"Test error: {str(e)}")

        return test_result

    def _test_learning_intent_detection(self) -> Dict[str, Any]:
        """Test learning intent detection system."""
        test_result = {"status": "unknown", "details": [], "issues": []}

        try:
            # Test intent detection
            test_message = "I need to analyze sales data and create charts"
            intent, confidence = asyncio.run(
                self.intent_detector.detect_intent(test_message, "test-user")
            )

            if intent and confidence > 0:
                test_result["details"].append(f"✓ Intent detection working: {intent} ({confidence:.2f})")
            else:
                test_result["issues"].append("✗ Intent detection failed")

            # Test pattern learning
            self.intent_detector.add_custom_pattern("test_intent", "test pattern", 0.8)

            if "test_intent" in self.intent_detector.learned_patterns:
                test_result["details"].append("✓ Custom pattern addition working")
            else:
                test_result["issues"].append("✗ Custom pattern addition failed")

            # Test success feedback
            self.update_intent_detection_feedback("test_intent", "test pattern", True, "test-user")

            stats = self.get_intent_detection_statistics()
            if stats.get("total_patterns", 0) > 0:
                test_result["details"].append("✓ Intent detection statistics working")
            else:
                test_result["issues"].append("✗ Intent detection statistics failed")

            test_result["status"] = "passed" if not test_result["issues"] else "failed"

        except Exception as e:
            test_result["status"] = "error"
            test_result["issues"].append(f"Test error: {str(e)}")

        return test_result

    def _test_adaptive_scoring(self) -> Dict[str, Any]:
        """Test adaptive scoring system."""
        test_result = {"status": "unknown", "details": [], "issues": []}

        try:
            # Test agent scoring initialization
            test_metadata = AgentMetadata(
                persona_id="test-agent",
                name="Test Agent",
                description="Test agent for validation",
                capabilities={"test_capability"},
                supported_intents={"test_intent"},
                complexity_levels={"medium"},
                performance_score=0.8,
                user_satisfaction_score=0.9,
                success_rate=0.95,
                average_response_time=2.0,
                collaboration_score=0.7,
                priority_score=0.6,
                is_primary_agent=True,
                last_updated=datetime.now()
            )

            self.scoring_system.initialize_agent_scoring(test_metadata)

            if "test-agent" in self.scoring_system.agent_scores:
                test_result["details"].append("✓ Agent scoring initialization working")
            else:
                test_result["issues"].append("✗ Agent scoring initialization failed")

            # Test performance metrics update
            test_metrics = {
                "success_rate": 0.9,
                "response_time": 1.5,
                "user_satisfaction": 0.85
            }

            self.update_agent_performance_metrics("test-agent", test_metrics)

            # Test adaptive score calculation
            score = self.get_agent_adaptive_score("test-agent")

            if 0 <= score <= 1:
                test_result["details"].append(f"✓ Adaptive scoring working: {score:.2f}")
            else:
                test_result["issues"].append("✗ Adaptive scoring failed")

            test_result["status"] = "passed" if not test_result["issues"] else "failed"

        except Exception as e:
            test_result["status"] = "error"
            test_result["issues"].append(f"Test error: {str(e)}")

        return test_result

    def _test_configuration_system(self) -> Dict[str, Any]:
        """Test configuration system."""
        test_result = {"status": "unknown", "details": [], "issues": []}

        try:
            # Test configuration loading
            config_summary = self.get_configuration_summary()

            if isinstance(config_summary, dict) and "learning_enabled" in config_summary:
                test_result["details"].append("✓ Configuration system working")
            else:
                test_result["issues"].append("✗ Configuration system failed")

            # Test behavior setting update
            original_value = self.get_behavior_setting("intent_detection", "confidence_threshold", 0.6)
            self.update_behavior_setting("intent_detection", "confidence_threshold", 0.8)
            new_value = self.get_behavior_setting("intent_detection", "confidence_threshold", 0.6)

            if new_value == 0.8:
                test_result["details"].append("✓ Behavior setting update working")
                # Restore original value
                self.update_behavior_setting("intent_detection", "confidence_threshold", original_value)
            else:
                test_result["issues"].append("✗ Behavior setting update failed")

            # Test feature toggle
            original_state = self.is_feature_enabled("enable_learning")
            self.toggle_feature("enable_learning", not original_state)
            new_state = self.is_feature_enabled("enable_learning")

            if new_state != original_state:
                test_result["details"].append("✓ Feature toggle working")
                # Restore original state
                self.toggle_feature("enable_learning", original_state)
            else:
                test_result["issues"].append("✗ Feature toggle failed")

            test_result["status"] = "passed" if not test_result["issues"] else "failed"

        except Exception as e:
            test_result["status"] = "error"
            test_result["issues"].append(f"Test error: {str(e)}")

        return test_result

    def _test_performance_optimization(self) -> Dict[str, Any]:
        """Test performance optimization system."""
        test_result = {"status": "unknown", "details": [], "issues": []}

        try:
            # Test performance recording
            test_context = {
                "detected_intent": "test_intent",
                "complexity": "medium",
                "user_id": "test-user"
            }

            test_outcome = {
                "agent_used": "test-agent",
                "strategy_used": "single_agent",
                "response_time": 2.0,
                "user_satisfaction": 0.8,
                "confidence_score": 0.9
            }

            self.record_routing_outcome(test_context, test_outcome, True)

            performance_summary = self.get_performance_summary()

            if performance_summary.get("status") != "no_data":
                test_result["details"].append("✓ Performance recording working")
            else:
                test_result["issues"].append("✗ Performance recording failed")

            # Test pattern analysis
            pattern_analysis = self.get_pattern_analysis()

            if isinstance(pattern_analysis, dict):
                test_result["details"].append("✓ Pattern analysis working")
            else:
                test_result["issues"].append("✗ Pattern analysis failed")

            # Test optimization recommendations
            recommendations = self.get_optimization_recommendations()

            if isinstance(recommendations, list):
                test_result["details"].append("✓ Optimization recommendations working")
            else:
                test_result["issues"].append("✗ Optimization recommendations failed")

            test_result["status"] = "passed" if not test_result["issues"] else "failed"

        except Exception as e:
            test_result["status"] = "error"
            test_result["issues"].append(f"Test error: {str(e)}")

        return test_result

    def _test_agent_self_declaration(self) -> Dict[str, Any]:
        """Test agent self-declaration system."""
        test_result = {"status": "unknown", "details": [], "issues": []}

        try:
            # Test declaration template creation
            template = self.create_agent_declaration_template()

            if "self_declaration" in template:
                test_result["details"].append("✓ Declaration template creation working")
            else:
                test_result["issues"].append("✗ Declaration template creation failed")

            # Test agent declaration registration
            test_config = {
                "name": "Test Agent",
                "description": "Test agent for validation",
                "self_declaration": {
                    "capabilities": {
                        "declared_capabilities": ["test_capability"],
                        "specialization_areas": ["testing"]
                    },
                    "routing_preferences": {
                        "preferred_complexity_levels": ["medium"],
                        "primary_agent_capable": True
                    }
                }
            }

            self.register_new_agent_declaration("test-declaration-agent", test_config)

            declaration = self.get_agent_declaration("test-declaration-agent")

            if declaration:
                test_result["details"].append("✓ Agent declaration registration working")
            else:
                test_result["issues"].append("✗ Agent declaration registration failed")

            # Test routing decision based on declaration
            test_context = {
                "message_content": "I need help with testing",
                "complexity": "medium",
                "required_capabilities": {"test_capability"}
            }

            should_route, confidence, reasoning = self.should_route_to_agent_by_declaration(
                "test-declaration-agent", test_context
            )

            if should_route and confidence > 0:
                test_result["details"].append(f"✓ Declaration-based routing working: {confidence:.2f}")
            else:
                test_result["issues"].append("✗ Declaration-based routing failed")

            test_result["status"] = "passed" if not test_result["issues"] else "failed"

        except Exception as e:
            test_result["status"] = "error"
            test_result["issues"].append(f"Test error: {str(e)}")

        return test_result

    def _test_runtime_extensibility(self) -> Dict[str, Any]:
        """Test runtime extensibility system."""
        test_result = {"status": "unknown", "details": [], "issues": []}

        try:
            # Test runtime agent registration
            test_runtime_config = {
                "name": "Runtime Test Agent",
                "description": "Agent registered at runtime for testing",
                "capabilities": ["runtime_testing"]
            }

            success = self.register_runtime_agent("runtime-test-agent", test_runtime_config)

            if success:
                test_result["details"].append("✓ Runtime agent registration working")
            else:
                test_result["issues"].append("✗ Runtime agent registration failed")

            # Test runtime agent listing
            runtime_agents = self.get_runtime_agents()

            if "runtime-test-agent" in runtime_agents:
                test_result["details"].append("✓ Runtime agent listing working")
            else:
                test_result["issues"].append("✗ Runtime agent listing failed")

            # Test extension point registration
            def test_callback(*args, **kwargs):
                pass

            self.register_extension_point("test_extension", test_callback)

            if "test_extension" in self.get_extension_points():
                test_result["details"].append("✓ Extension point registration working")
            else:
                test_result["issues"].append("✗ Extension point registration failed")

            # Test extensibility status
            status = self.get_extensibility_status()

            if isinstance(status, dict) and "hot_reload_enabled" in status:
                test_result["details"].append("✓ Extensibility status working")
            else:
                test_result["issues"].append("✗ Extensibility status failed")

            # Cleanup
            self.unregister_runtime_agent("runtime-test-agent")

            test_result["status"] = "passed" if not test_result["issues"] else "failed"

        except Exception as e:
            test_result["status"] = "error"
            test_result["issues"].append(f"Test error: {str(e)}")

        return test_result

    def _generate_validation_recommendations(self, test_results: Dict[str, Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []

        for test_name, result in test_results.items():
            if result["status"] == "failed":
                recommendations.append(
                    f"Fix issues in {test_name}: {'; '.join(result['issues'])}"
                )
            elif result["status"] == "error":
                recommendations.append(
                    f"Investigate errors in {test_name}: {'; '.join(result['issues'])}"
                )

        # General recommendations
        if len([r for r in test_results.values() if r["status"] == "passed"]) == len(test_results):
            recommendations.append("All extensibility features are working correctly!")
        else:
            recommendations.append("Consider running individual component tests for detailed debugging")
            recommendations.append("Check system logs for additional error information")
            recommendations.append("Verify configuration files are properly formatted")

        return recommendations
