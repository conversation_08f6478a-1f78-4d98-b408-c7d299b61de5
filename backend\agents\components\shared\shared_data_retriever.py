"""
Shared Data Retriever Component for Phase 2 Architecture Consolidation.

This component consolidates data retrieval logic that was previously duplicated
across different agents, providing unified access to vector databases, memory
services, and other data sources.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from ..base_component import BaseAgentComponent, AgentContext
from ...utils.vector_service import VectorService
from ...utils.memory_service import MemoryService

logger = logging.getLogger(__name__)


class SharedDataRetriever(BaseAgentComponent):
    """
    Shared data retriever component that consolidates data access logic
    across all agents with unified vector and memory search capabilities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("shared_data_retriever", config)
        self.vector_service = None
        self.memory_service = None
        
        # Configuration
        self.max_results = config.get("max_results", 10)
        self.similarity_threshold = config.get("similarity_threshold", 0.7)
        self.enable_vector_search = config.get("enable_vector_search", True)
        self.enable_memory_search = config.get("enable_memory_search", True)
        self.result_ranking_strategy = config.get("result_ranking_strategy", "relevance_score")
    
    async def _initialize_component(self) -> None:
        """Initialize the data retriever component."""
        self.logger.info("Initializing SharedDataRetriever")
        
        try:
            if self.enable_vector_search:
                self.vector_service = VectorService()
                await self.vector_service.initialize()
                self.logger.info("Vector service initialized")
            
            if self.enable_memory_search:
                self.memory_service = MemoryService()
                await self.memory_service.initialize()
                self.logger.info("Memory service initialized")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize data services: {e}")
            raise
    
    def get_required_fields(self) -> List[str]:
        """Return list of required context fields."""
        return ["query", "user_id"]
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process data retrieval request.
        
        Args:
            context: AgentContext containing query and user information
            
        Returns:
            Updated AgentContext with retrieved data
        """
        query = context.get_field("query")
        user_id = context.user_id
        
        # Optional parameters
        max_results = context.get_field("max_results", self.max_results)
        similarity_threshold = context.get_field("similarity_threshold", self.similarity_threshold)
        search_types = context.get_field("search_types", ["vector", "memory"])
        
        self.logger.info(f"Processing data retrieval for query: {query[:100]}...")
        
        # Collect results from different sources
        all_results = []
        
        # Vector database search
        if "vector" in search_types and self.enable_vector_search and self.vector_service:
            try:
                vector_results = await self._search_vector_database(
                    query, user_id, max_results, similarity_threshold
                )
                all_results.extend(vector_results)
                self.logger.info(f"Retrieved {len(vector_results)} results from vector database")
            except Exception as e:
                self.logger.warning(f"Vector search failed: {e}")
                context.add_error(self.name, f"Vector search error: {str(e)}")
        
        # Memory search
        if "memory" in search_types and self.enable_memory_search and self.memory_service:
            try:
                memory_results = await self._search_memory(
                    query, user_id, max_results, similarity_threshold
                )
                all_results.extend(memory_results)
                self.logger.info(f"Retrieved {len(memory_results)} results from memory")
            except Exception as e:
                self.logger.warning(f"Memory search failed: {e}")
                context.add_error(self.name, f"Memory search error: {str(e)}")
        
        # Rank and filter results
        ranked_results = self._rank_and_filter_results(all_results, max_results)
        
        # Update context with results
        context.set_field("retrieved_data", ranked_results)
        context.set_field("total_results_found", len(all_results))
        context.set_field("results_returned", len(ranked_results))
        context.set_status("success")
        
        self.logger.info(f"Data retrieval completed: {len(ranked_results)} results returned")
        
        return context
    
    async def _search_vector_database(
        self, 
        query: str, 
        user_id: str, 
        max_results: int,
        similarity_threshold: float
    ) -> List[Dict[str, Any]]:
        """
        Search the vector database.
        
        Args:
            query: Search query
            user_id: User identifier
            max_results: Maximum number of results
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of search results
        """
        try:
            results = await self.vector_service.search(
                query=query,
                user_id=user_id,
                limit=max_results,
                threshold=similarity_threshold
            )
            
            # Standardize result format
            standardized_results = []
            for result in results:
                standardized_result = {
                    "content": result.get("content", ""),
                    "score": result.get("score", 0.0),
                    "source": "vector_db",
                    "metadata": result.get("metadata", {}),
                    "document_id": result.get("document_id"),
                    "chunk_id": result.get("chunk_id")
                }
                standardized_results.append(standardized_result)
            
            return standardized_results
            
        except Exception as e:
            self.logger.error(f"Vector database search failed: {e}")
            raise
    
    async def _search_memory(
        self, 
        query: str, 
        user_id: str, 
        max_results: int,
        similarity_threshold: float
    ) -> List[Dict[str, Any]]:
        """
        Search the memory service.
        
        Args:
            query: Search query
            user_id: User identifier
            max_results: Maximum number of results
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of search results
        """
        try:
            results = await self.memory_service.search(
                query=query,
                user_id=user_id,
                limit=max_results,
                threshold=similarity_threshold
            )
            
            # Standardize result format
            standardized_results = []
            for result in results:
                standardized_result = {
                    "content": result.get("content", result.get("text", "")),
                    "score": result.get("score", result.get("relevance", 0.0)),
                    "source": "memory",
                    "metadata": result.get("metadata", {}),
                    "memory_id": result.get("memory_id", result.get("id")),
                    "timestamp": result.get("timestamp")
                }
                standardized_results.append(standardized_result)
            
            return standardized_results
            
        except Exception as e:
            self.logger.error(f"Memory search failed: {e}")
            raise
    
    def _rank_and_filter_results(
        self, 
        results: List[Dict[str, Any]], 
        max_results: int
    ) -> List[Dict[str, Any]]:
        """
        Rank and filter search results.
        
        Args:
            results: List of search results
            max_results: Maximum number of results to return
            
        Returns:
            Ranked and filtered results
        """
        if not results:
            return []
        
        # Remove duplicates based on content similarity
        unique_results = self._remove_duplicate_results(results)
        
        # Sort by ranking strategy
        if self.result_ranking_strategy == "relevance_score":
            unique_results.sort(key=lambda x: x.get("score", 0), reverse=True)
        elif self.result_ranking_strategy == "source_priority":
            # Prioritize vector results over memory results
            unique_results.sort(key=lambda x: (
                0 if x.get("source") == "vector_db" else 1,
                -x.get("score", 0)
            ))
        elif self.result_ranking_strategy == "timestamp":
            # Sort by timestamp if available
            unique_results.sort(
                key=lambda x: x.get("timestamp", ""), 
                reverse=True
            )
        
        # Return top results
        return unique_results[:max_results]
    
    def _remove_duplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Remove duplicate results based on content similarity.
        
        Args:
            results: List of search results
            
        Returns:
            Deduplicated results
        """
        if not results:
            return []
        
        unique_results = []
        seen_content = set()
        
        for result in results:
            content = result.get("content", "")
            # Simple deduplication based on content hash
            content_hash = hash(content.lower().strip())
            
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_results.append(result)
        
        return unique_results
    
    async def search_by_filters(self, context: AgentContext) -> AgentContext:
        """
        Search with additional filters.
        
        Args:
            context: AgentContext containing query and filter parameters
            
        Returns:
            Updated AgentContext with filtered search results
        """
        # Extract filter parameters
        filters = context.get_field("filters", {})
        date_range = filters.get("date_range")
        document_types = filters.get("document_types", [])
        tags = filters.get("tags", [])
        
        # Apply filters to the search
        # This would extend the basic search functionality
        self.logger.info(f"Applying filters: {filters}")
        
        # For now, delegate to the main process method
        # In a full implementation, this would modify the search parameters
        return await self.process(context)
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """Get search statistics."""
        return {
            "total_searches": self.metrics.success_count,
            "failed_searches": self.metrics.error_count,
            "average_search_time": self.metrics.get_average_execution_time(),
            "vector_search_enabled": self.enable_vector_search,
            "memory_search_enabled": self.enable_memory_search
        }
