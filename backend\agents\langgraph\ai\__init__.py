"""
AI-powered optimization and self-healing systems for LangGraph.

This module provides advanced AI capabilities including:
- Predictive workflow optimization
- Self-healing systems with automatic error recovery
- Machine learning-based performance prediction
- Automated remediation and optimization suggestions

Key Components:
- PredictiveOptimizer: AI-powered workflow optimization
- SelfHealingSystem: Automatic error recovery and health monitoring
- PatternLearningEngine: Machine learning for workflow pattern analysis
- OptimizationEngine: Automated optimization suggestions and A/B testing
"""

from .predictive_optimizer import (
    PredictiveOptimizer,
    WorkflowPattern,
    OptimizationSuggestion,
    PerformancePrediction
)

from .self_healing import (
    SelfHealingSystem,
    HealthMonitor,
    FailurePrediction,
    RemediationAction
)

__version__ = "1.0.0"

__all__ = [
    "PredictiveOptimizer",
    "WorkflowPattern", 
    "OptimizationSuggestion",
    "PerformancePrediction",
    "SelfHealingSystem",
    "HealthMonitor",
    "FailurePrediction",
    "RemediationAction"
]
