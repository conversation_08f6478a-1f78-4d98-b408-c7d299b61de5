# Base Configuration for LangGraph Integration
# This file contains default settings that apply to all environments

# LangGraph Core Settings
langgraph:
  enabled: false
  max_workflow_duration: 3600  # 1 hour in seconds
  max_agent_transitions: 20
  max_tool_executions: 50
  
  # State Management
  enable_checkpointing: true
  checkpoint_interval: 30  # seconds
  max_checkpoints_per_workflow: 100
  
  # Performance Settings
  enable_monitoring: true
  monitoring_interval: 60  # seconds
  performance_cache_ttl: 300  # 5 minutes
  
  # Recovery Settings
  enable_auto_recovery: true
  max_recovery_attempts: 3
  recovery_timeout: 300  # 5 minutes
  
  # Migration Settings
  migration_phase: "migration_completion"
  rollout_percentage: 100.0
  enable_legacy_fallback: false

# Database Configuration
database:
  host: "localhost"
  port: 5432
  database: "datagenius_db"
  username: "datagenius_user"
  password: ""  # Set via environment variable
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  enable_file_logging: true
  log_file_path: "logs/langgraph.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5

# Agent Configuration
agents:
  default_timeout: 300  # 5 minutes
  max_concurrent_agents: 5
  enable_agent_metrics: true
  
  # Agent-specific settings
  concierge:
    timeout: 60
    max_retries: 2
  
  analysis:
    timeout: 600  # 10 minutes for complex analysis
    max_retries: 1
    enable_caching: true
  
  marketing:
    timeout: 300
    max_retries: 2
    enable_content_validation: true
  
  classification:
    timeout: 120
    max_retries: 1
    confidence_threshold: 0.8

# Tool Configuration
tools:
  default_timeout: 120  # 2 minutes
  max_concurrent_tools: 10
  enable_tool_metrics: true
  
  # MCP Tool Settings
  mcp_tools:
    enable_caching: true
    cache_ttl: 600  # 10 minutes
    max_cache_size: 1000
  
  # Data Access Tool
  data_access:
    timeout: 300
    max_results: 1000
    enable_query_optimization: true
  
  # Code Execution Tool
  code_execution:
    timeout: 180
    max_memory_mb: 512
    enable_sandboxing: true
  
  # Visualization Tool
  visualization:
    timeout: 120
    max_chart_size: "1920x1080"
    supported_formats: ["png", "svg", "pdf"]

# Workflow Configuration
workflows:
  default_timeout: 1800  # 30 minutes
  max_concurrent_workflows: 20
  enable_workflow_metrics: true
  
  # Workflow Types
  simple:
    timeout: 300
    max_steps: 5
  
  complex:
    timeout: 1800
    max_steps: 20
    enable_checkpointing: true
  
  collaborative:
    timeout: 3600
    max_agents: 5
    enable_cross_agent_communication: true

# Security Configuration
security:
  enable_input_validation: true
  enable_output_sanitization: true
  max_input_size: 1048576  # 1MB
  allowed_file_types: ["txt", "csv", "json", "yaml", "pdf", "docx"]
  
  # Rate Limiting
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_size: 10

# Monitoring and Metrics
monitoring:
  enable_metrics_collection: true
  metrics_retention_days: 30
  enable_performance_alerts: true
  
  # Alert Thresholds
  alerts:
    high_execution_time: 300  # seconds
    high_error_rate: 0.05  # 5%
    low_success_rate: 0.95  # 95%
    high_memory_usage: 1024  # MB

# Feature Flags
feature_flags:
  # Core Features
  langgraph_enabled: true
  intelligent_routing: true
  multi_agent_collaboration: true
  state_persistence: true
  performance_monitoring: true
  legacy_fallback: false
  
  # Advanced Features
  advanced_analytics: false
  custom_workflows: false
  external_integrations: false
  
  # Experimental Features
  ai_optimization: false
  predictive_routing: false
  auto_scaling: false

  # Phase 4: Platform Evolution Features
  phase4_platform_evolution: true
  capability_marketplace: true
  ai_workflow_composer: true
  pattern_recognition: true
  trading_engine: true
  certification_system: true

  # Phase 5: Migration Completion Features
  phase5_migration_completion: true
  full_deployment_enabled: true
  performance_optimization_enabled: true
  production_monitoring_enabled: true
  legacy_code_removed: true

# Integration Settings
integrations:
  # External APIs
  external_apis:
    timeout: 30
    max_retries: 3
    enable_caching: true
  
  # Business Profile Integration
  business_profiles:
    enable_auto_context: true
    context_refresh_interval: 300  # 5 minutes
  
  # Cross-Agent Intelligence
  cross_agent_intelligence:
    enabled: true
    insight_retention_hours: 24
    max_insights_per_profile: 100

# Phase 4: Platform Evolution Configuration
phase4:
  # Core Platform Settings
  enabled: true
  initialization_timeout: 300  # 5 minutes

  # Capability Marketplace
  marketplace:
    enabled: true
    matching_threshold: 0.7
    price_adjustment_factor: 0.1
    performance_weight: 0.4
    certification_weight: 0.3
    usage_weight: 0.3
    auto_pricing: true

  # AI Workflow Composer
  workflow_composer:
    enabled: true
    max_workflow_nodes: 20
    default_timeout: 3600.0
    optimization_threshold: 0.8
    enable_pattern_learning: true

  # Pattern Recognition System
  pattern_recognition:
    enabled: true
    learning_window_hours: 168  # 1 week
    min_pattern_occurrences: 3
    pattern_evolution_threshold: 0.2
    max_patterns: 1000

  # Trading Engine
  trading_engine:
    enabled: true
    auction_duration_minutes: 30
    bid_expiry_hours: 1
    quality_weight: 0.4
    price_weight: 0.3
    speed_weight: 0.3

  # Certification System
  certification_system:
    enabled: true
    validity_period_days: 365
    test_duration_hours: 24
    min_test_executions: 10
    auto_certification: true

  # Background Services
  background_services:
    pattern_learning_interval: 3600  # 1 hour
    workflow_optimization_interval: 1800  # 30 minutes
    marketplace_maintenance_interval: 300  # 5 minutes
    price_optimization_interval: 3600  # 1 hour

# Phase 5: Migration Completion Configuration
phase5:
  # Core Migration Completion Settings
  enabled: true
  migration_completion_timeout: 600  # 10 minutes

  # Full Deployment Settings
  full_deployment:
    enabled: true
    remove_legacy_code: true
    remove_feature_flags: true
    update_documentation: true
    finalize_architecture: true

  # Performance Optimization Settings
  performance_optimization:
    enabled: true
    routing_optimization: true
    agent_coordination_tuning: true
    caching_implementation: true
    cache_ttl_seconds: 3600  # 1 hour
    cache_max_size_mb: 512
    memory_optimization: true
    cpu_optimization: true

  # Monitoring and Maintenance Settings
  monitoring:
    enabled: true
    production_monitoring: true
    health_checks_enabled: true
    metrics_collection_enabled: true
    alert_system_enabled: true
    maintenance_procedures_enabled: true
    performance_tracking: true
    error_tracking: true

  # Configuration Management
  configuration:
    auto_update_configs: true
    validate_integration: true
    backup_old_configs: true
    environment_sync: true

  # Documentation and Knowledge Transfer
  documentation:
    generate_final_docs: true
    create_runbooks: true
    knowledge_transfer: true
    lessons_learned: true

# Development Settings (overridden in environment-specific configs)
development:
  debug_mode: false
  enable_detailed_logging: false
  mock_external_services: false
  test_data_enabled: false
