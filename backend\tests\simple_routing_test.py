#!/usr/bin/env python3
"""
Simple test to verify routing behavior without full system dependencies.
"""

import sys
import os
import asyncio
from datetime import datetime
from typing import Dict, Any

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock the unified state structure
def create_test_state(current_persona: str = None) -> Dict[str, Any]:
    """Create a minimal test state."""
    return {
        "workflow_id": "test-workflow",
        "conversation_id": "test-conversation", 
        "user_id": "test-user",
        "current_persona": current_persona,
        "selected_agent": None,
        "conversation_mode": "CONVERSATION",
        "messages": [],
        "current_message": {"content": "who are you", "type": "user"},
        "routing_analysis": None,
        "updated_at": datetime.now().isoformat()
    }


def test_routing_logic():
    """Test the routing logic directly."""
    print("🧪 Testing routing logic...")
    
    # Test state with current_persona set
    test_state = create_test_state(current_persona="composable-marketing-ai")
    
    # Mock agent nodes
    mock_agent_nodes = {
        "concierge-agent": "mock_concierge",
        "composable-marketing-ai": "mock_marketing", 
        "composable-analysis-ai": "mock_analysis",
        "composable-classifier-ai": "mock_classifier"
    }
    
    print(f"📋 Mock agent nodes: {list(mock_agent_nodes.keys())}")
    print(f"🎯 Test state current_persona: {test_state.get('current_persona')}")
    
    # Simulate the routing logic from workflow_manager._route_to_agent
    current_persona = test_state.get("current_persona")
    if current_persona and current_persona in mock_agent_nodes:
        routing_result = current_persona
        print(f"✅ SUCCESS: Routing logic would route to: {routing_result}")
        return True
    else:
        print(f"❌ FAILED: current_persona '{current_persona}' not found in agent nodes")
        return False


def test_routing_node_logic():
    """Test the routing node logic."""
    print("\n🧪 Testing routing node logic...")
    
    test_state = create_test_state(current_persona="composable-marketing-ai")
    mock_agent_nodes = {
        "concierge-agent": "mock_concierge",
        "composable-marketing-ai": "mock_marketing", 
        "composable-analysis-ai": "mock_analysis",
        "composable-classifier-ai": "mock_classifier"
    }
    
    # Simulate the routing node logic
    current_persona = test_state.get("current_persona")
    selected_agent = test_state.get("selected_agent")
    available_agents = list(mock_agent_nodes.keys())
    
    print(f"🔍 RoutingNode Debug - current_persona: {current_persona}, selected_agent: {selected_agent}")
    print(f"🔍 RoutingNode Debug - available_agents: {available_agents}")
    
    # Check if current_persona is already set - if so, skip analysis entirely
    if current_persona and current_persona in mock_agent_nodes:
        print(f"🎯 RoutingNode: current_persona already set to {current_persona}, skipping analysis entirely")
        print(f"✅ RoutingNode: Deferring to workflow manager for current_persona: {current_persona}")
        # Don't set routing_analysis - this is the key fix
        return True
    
    # Check if selected_agent is set - if so, skip analysis entirely  
    if selected_agent and selected_agent in mock_agent_nodes:
        print(f"🎯 RoutingNode: selected_agent already set to {selected_agent}, skipping analysis entirely")
        print(f"✅ RoutingNode: Deferring to workflow manager for selected_agent: {selected_agent}")
        return True
        
    # If we reach here, neither current_persona nor selected_agent are set properly
    print(f"⚠️ RoutingNode: No valid agent selection found, proceeding with analysis")
    return False


def main():
    """Run the simple routing tests."""
    print("🚀 Starting simple routing tests...\n")
    
    test1_passed = test_routing_logic()
    test2_passed = test_routing_node_logic()
    
    print(f"\n📊 Test Results:")
    print(f"   Routing Logic Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Routing Node Logic Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The routing logic should work correctly.")
        print("💡 The issue is likely that the routing node is still performing analysis")
        print("   and overriding the user's selection with routing_analysis.")
        return 0
    else:
        print("\n💥 Some tests failed. The routing logic has issues.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
