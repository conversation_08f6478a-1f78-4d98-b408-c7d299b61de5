"""
Base Agent Node for LangGraph-based Datagenius Agents.

This module provides the base class for all agent nodes in the LangGraph
system, replacing the existing agent implementations with a unified
node-based approach.
"""

import logging
import importlib
import asyncio
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Type
from datetime import datetime, timedelta
import uuid
from pathlib import Path
from enum import Enum

from ..states.unified_state import (
    UnifiedDatageniusState,
    update_agent_transition,
    add_message,
    add_cross_agent_insight,
    MessageType,
    AgentRole
)
from ..utils.performance_monitor import performance_monitor
from ..core.workflow_state_validator import state_validator, ValidationResult

logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states for agent coordination."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreaker:
    """
    Circuit breaker pattern implementation for agent coordination.

    Prevents cascading failures by temporarily disabling failing agent communications.
    """

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        """
        Initialize circuit breaker.

        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before attempting recovery
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED

    def can_execute(self) -> bool:
        """Check if operation can be executed."""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
                return True
            return False
        else:  # HALF_OPEN
            return True

    def record_success(self) -> None:
        """Record successful operation."""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED

    def record_failure(self) -> None:
        """Record failed operation."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if not self.last_failure_time:
            return True

        time_since_failure = (datetime.now() - self.last_failure_time).total_seconds()
        return time_since_failure >= self.recovery_timeout


class BaseAgentNode(ABC):
    """
    Base class for all agent nodes in the LangGraph system.
    
    This class provides the foundation for converting existing agents
    into LangGraph nodes while maintaining all current capabilities.
    """
    
    def __init__(
        self,
        agent_id: str,
        agent_type: str,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the base agent node.
        
        Args:
            agent_id: Unique identifier for this agent
            agent_type: Type/category of the agent
            config: Optional configuration for the agent
        """
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{agent_id}")
        
        # Agent capabilities and metadata
        self.capabilities: List[str] = []
        self.supported_intents: List[str] = []
        self.tools: List[str] = []
        
        # Performance tracking
        self.execution_count = 0
        self.total_execution_time = 0.0
        self.success_count = 0
        self.error_count = 0

        # Initialize circuit breakers for agent coordination
        self.coordination_circuit_breakers = {}
        self.coordination_timeout = self.config.get('coordination_timeout', 30)  # 30 seconds default
        self.max_concurrent_coordinations = self.config.get('max_concurrent_coordinations', 3)
        self.active_coordinations = 0

        self.logger.info(f"Initialized {agent_type} agent node: {agent_id}")
    
    async def execute(self, state: UnifiedDatageniusState):
        """
        Execute the agent node with the given state.

        This is the main entry point called by LangGraph.
        Supports both traditional state updates and Command pattern routing.

        Args:
            state: Current workflow state

        Returns:
            Updated workflow state or Command object for routing
        """
        start_time = datetime.now()

        # Critical validation: Check if agent instance exists
        if self.agent_instance is None:
            error_msg = f"Agent instance is None for {self.agent_id}. Cannot execute workflow."
            self.logger.error(f"🚨 {error_msg}")

            # Add error message to state and terminate workflow
            state["error"] = error_msg
            state["workflow_complete"] = True
            state["next_action"] = "END"

            # Add fallback message if no agent response exists
            if not any(msg.get("type") == "agent" for msg in state.get("messages", [])):
                fallback_message = {
                    "id": str(uuid.uuid4()),
                    "type": "agent",
                    "role": "assistant",
                    "content": "I apologize, but I'm experiencing technical difficulties. Please try again or contact support if the issue persists.",
                    "agent_id": self.agent_id,
                    "timestamp": datetime.now().isoformat(),
                    "metadata": {"error": "agent_instance_null", "fallback": True}
                }
                state["messages"].append(fallback_message)

            return state

        try:
            # Perform comprehensive state validation
            validation_result, issues, recommendations = state_validator.validate_state(
                state=state,
                agent_id=self.agent_id,
                template_config=getattr(self, 'template_config', None)
            )

            # Handle validation results
            if validation_result == ValidationResult.TERMINATE:
                self.logger.warning(f"🛑 State validation triggered termination for {self.agent_id}: {issues}")
                # Create termination message
                termination_message = {
                    "id": str(uuid.uuid4()),
                    "content": "The workflow has been terminated due to validation constraints. " +
                              ("Issues detected: " + "; ".join(issues) if issues else ""),
                    "type": MessageType.AGENT.value,
                    "timestamp": datetime.now().isoformat(),
                    "agent_id": self.agent_id,
                    "metadata": {"termination_reason": "state_validation", "issues": issues}
                }
                state = add_message(state, termination_message, MessageType.AGENT)
                return state

            elif validation_result == ValidationResult.INVALID:
                self.logger.error(f"❌ Invalid state detected for {self.agent_id}: {issues}")
                # Create error message but continue with fallback
                error_message = {
                    "id": str(uuid.uuid4()),
                    "content": "I encountered some issues while processing your request. Let me try a different approach.",
                    "type": MessageType.AGENT.value,
                    "timestamp": datetime.now().isoformat(),
                    "agent_id": self.agent_id,
                    "metadata": {"validation_issues": issues}
                }
                state = add_message(state, error_message, MessageType.AGENT)

            elif validation_result == ValidationResult.WARNING:
                self.logger.warning(f"⚠️ State validation warnings for {self.agent_id}: {issues}")

            # Check for infinite loop prevention (legacy check for backward compatibility)
            execution_count = state.get("execution_metrics", {}).get("agent_execution_count", 0)
            max_executions = self.config.get("max_agent_executions", 20)  # Reduced from 50 to 20

            # More aggressive loop detection for concierge agent
            if "concierge" in self.agent_id.lower():
                max_executions = min(max_executions, 5)  # Limit concierge to 5 executions max

            if execution_count >= max_executions:
                self.logger.error(f"❌ Maximum agent executions ({max_executions}) reached for {self.agent_id}")
                # Generate error response and terminate
                error_message = {
                    "id": str(uuid.uuid4()),
                    "content": "I apologize, but I've reached the maximum number of processing attempts. Please try rephrasing your request or contact support if this issue persists.",
                    "type": MessageType.AGENT.value,
                    "timestamp": datetime.now().isoformat(),
                    "agent_id": self.agent_id,
                    "error": True,
                    "error_type": "max_executions_reached"
                }
                state = add_message(state, error_message, MessageType.AGENT)
                return state

            # Enhanced loop detection with better state tracking
            agent_context = state.get("agent_context", {}).get(self.agent_id, {})
            last_execution_time = agent_context.get("last_execution_time")

            if last_execution_time:
                time_since_last = (datetime.now() - datetime.fromisoformat(last_execution_time)).total_seconds()
                min_interval = self.config.get("min_execution_interval", 0.5)  # 500ms minimum

                # More aggressive for concierge agent
                if "concierge" in self.agent_id.lower():
                    min_interval = max(min_interval, 1.0)  # 1 second minimum for concierge

                if time_since_last < min_interval:
                    self.logger.warning(f"⚠️ Rapid execution detected for {self.agent_id} (interval: {time_since_last:.3f}s)")

                    # Increment rapid execution count
                    rapid_execution_count = agent_context.get("rapid_execution_count", 0) + 1

                    # Update agent context with incremented count
                    if "agent_context" not in state:
                        state["agent_context"] = {}
                    if self.agent_id not in state["agent_context"]:
                        state["agent_context"][self.agent_id] = {}
                    state["agent_context"][self.agent_id]["rapid_execution_count"] = rapid_execution_count

                    if rapid_execution_count >= 3:  # After 3 rapid executions, terminate
                        self.logger.error(f"❌ Infinite loop detected for {self.agent_id} - terminating")
                        error_message = {
                            "id": str(uuid.uuid4()),
                            "content": "I've detected a processing loop and need to stop to prevent system issues. Please try rephrasing your request.",
                            "type": MessageType.AGENT.value,
                            "timestamp": datetime.now().isoformat(),
                            "agent_id": self.agent_id,
                            "error": True,
                            "error_type": "infinite_loop_detected"
                        }
                        state = add_message(state, error_message, MessageType.AGENT)
                        return state

                    # Add delay to prevent tight loops
                    await asyncio.sleep(min_interval - time_since_last)
                else:
                    # Reset rapid execution count if enough time has passed
                    if "agent_context" in state and self.agent_id in state["agent_context"]:
                        state["agent_context"][self.agent_id]["rapid_execution_count"] = 0

            self.logger.info(f"Executing agent {self.agent_id} (execution #{execution_count + 1})")

            # Update state to reflect current agent
            state = update_agent_transition(state, self.agent_id, AgentRole.PRIMARY)

            # Update execution metrics
            if "execution_metrics" not in state:
                state["execution_metrics"] = {}
            state["execution_metrics"]["agent_execution_count"] = execution_count + 1

            # Update agent context with execution time
            if "agent_context" not in state:
                state["agent_context"] = {}
            if self.agent_id not in state["agent_context"]:
                state["agent_context"][self.agent_id] = {}
            state["agent_context"][self.agent_id]["last_execution_time"] = datetime.now().isoformat()

            # Pre-execution setup
            state = await self._pre_execution(state)

            # Main agent processing - may return Command or state
            result = await self._process_message(state)

            # Handle Command objects (LangGraph Command pattern)
            if hasattr(result, 'goto') and hasattr(result, 'update'):
                # This is a Command object - apply updates and store in state
                if hasattr(result, 'apply_to_state'):
                    state = result.apply_to_state(state)

                # Store command in state for routing
                state["agent_command"] = result

                # Post-execution cleanup
                state = await self._post_execution(state)

                # Update performance metrics
                execution_time = (datetime.now() - start_time).total_seconds()
                self._update_performance_metrics(execution_time, success=True)

                # Update state metrics
                state["execution_metrics"]["total_processing_time"] += execution_time
                state["updated_at"] = datetime.now().isoformat()

                self.logger.info(f"Agent {self.agent_id} stored Command for routing to: {result.goto}")

                # Return updated state with command
                return state

            # Traditional state update path
            state = result if isinstance(result, dict) else state

            # Post-execution cleanup
            state = await self._post_execution(state)

            # Update performance metrics
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_metrics(execution_time, success=True)

            # Update state metrics
            state["execution_metrics"]["total_processing_time"] += execution_time
            state["updated_at"] = datetime.now().isoformat()

            self.logger.info(f"Completed agent {self.agent_id} execution in {execution_time:.2f}s")

            return state

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_metrics(execution_time, success=False)

            # Add error to state
            error_record = {
                "timestamp": datetime.now().isoformat(),
                "agent_id": self.agent_id,
                "error": str(e),
                "type": "agent_execution_error"
            }
            state["error_history"].append(error_record)

            self.logger.error(f"Error in agent {self.agent_id}: {e}", exc_info=True)

            # Generate error response
            error_message = {
                "id": str(uuid.uuid4()),
                "content": f"I'm sorry, I encountered an error while processing your request: {str(e)}",
                "type": MessageType.AGENT.value,
                "timestamp": datetime.now().isoformat(),
                "agent_id": self.agent_id,
                "error": True
            }
            state = add_message(state, error_message, MessageType.AGENT)

            return state
    
    async def _pre_execution(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Pre-execution setup and validation.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state
        """
        # Load agent configuration into state
        state["agent_configs"][self.agent_id] = self.config
        
        # Update available tools
        state["available_tools"].extend(self.tools)
        
        # Load business context if available
        if state.get("business_profile_id"):
            state = await self._load_business_context(state)
        
        # Load cross-agent context
        state = await self._load_cross_agent_context(state)
        
        return state
    
    @abstractmethod
    async def _process_message(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Main message processing logic for the agent.
        
        This method must be implemented by each specific agent node.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state with agent response
        """
        pass
    
    async def _post_execution(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Post-execution cleanup and insight generation.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state
        """
        # Generate insights for cross-agent intelligence
        insights = await self._generate_insights(state)
        for insight in insights:
            state = add_cross_agent_insight(state, insight, self.agent_id)
        
        # Update agent contribution tracking
        if self.agent_id not in state["agent_contributions"]:
            state["agent_contributions"][self.agent_id] = []
        
        # Record this execution
        execution_record = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id,
            "message_processed": state.get("current_message", {}).get("content", ""),
            "response_generated": len(state.get("messages", [])) > 0
        }
        state["agent_contributions"][self.agent_id].append(execution_record)
        
        return state
    
    async def _load_business_context(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Load business profile context for the agent.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state with business context
        """
        # This would integrate with the existing business profile system
        business_profile_id = state.get("business_profile_id")
        if business_profile_id:
            # TODO: Load actual business profile data
            state["business_context"]["profile_loaded"] = True
            state["business_context"]["profile_id"] = business_profile_id
            
        return state
    
    async def _load_cross_agent_context(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Load cross-agent intelligence context.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state with cross-agent context
        """
        # Filter insights relevant to this agent
        relevant_insights = []
        for insight in state.get("shared_insights", []):
            if self._is_insight_relevant(insight):
                relevant_insights.append(insight)
        
        state["cross_agent_context"]["relevant_insights"] = relevant_insights
        state["cross_agent_context"]["loaded_by"] = self.agent_id
        state["cross_agent_context"]["loaded_at"] = datetime.now().isoformat()
        
        return state
    
    def _is_insight_relevant(self, insight: Dict[str, Any]) -> bool:
        """
        Determine if an insight is relevant to this agent.
        
        Args:
            insight: Insight data
            
        Returns:
            True if relevant, False otherwise
        """
        # Check if insight type matches agent capabilities
        insight_type = insight.get("type", "")
        return any(capability in insight_type for capability in self.capabilities)
    
    async def _generate_insights(self, state: UnifiedDatageniusState) -> List[Dict[str, Any]]:
        """
        Generate insights from the current execution for cross-agent intelligence.
        
        Args:
            state: Current workflow state
            
        Returns:
            List of insights generated
        """
        insights = []
        
        # Generate basic execution insight
        if state.get("current_message"):
            insight = {
                "type": f"{self.agent_type}_execution",
                "content": f"Agent {self.agent_id} processed message",
                "metadata": {
                    "agent_type": self.agent_type,
                    "execution_time": datetime.now().isoformat(),
                    "message_length": len(state["current_message"].get("content", "")),
                    "tools_used": list(state.get("tool_status", {}).keys())
                }
            }
            insights.append(insight)
        
        return insights
    
    def _update_performance_metrics(self, execution_time: float, success: bool) -> None:
        """
        Update agent performance metrics.
        
        Args:
            execution_time: Time taken for execution
            success: Whether execution was successful
        """
        self.execution_count += 1
        self.total_execution_time += execution_time
        
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get agent performance metrics.
        
        Returns:
            Performance metrics dictionary
        """
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "execution_count": self.execution_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_count / max(self.execution_count, 1),
            "average_execution_time": self.total_execution_time / max(self.execution_count, 1),
            "total_execution_time": self.total_execution_time
        }
    
    def get_capabilities(self) -> List[str]:
        """Get agent capabilities."""
        return self.capabilities.copy()
    
    def get_supported_intents(self) -> List[str]:
        """Get supported intents."""
        return self.supported_intents.copy()
    
    def get_tools(self) -> List[str]:
        """Get available tools."""
        return self.tools.copy()


class UnifiedAgentNode(BaseAgentNode):
    """
    Unified agent node that can handle any agent dynamically.

    This node replaces the need for separate agent node classes by
    dynamically loading and executing agents based on configuration.
    """

    def __init__(
        self,
        agent_id: str,
        agent_config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the unified agent node.

        Args:
            agent_id: Unique identifier for the agent
            agent_config: Configuration containing agent details
        """
        # Extract agent type from config or derive from ID
        config = agent_config or {}
        agent_type = config.get("agent_type", self._derive_agent_type(agent_id))

        super().__init__(agent_id, agent_type, config)

        # Load agent instance dynamically
        self.agent_instance = None
        self._load_agent_instance()

        # Extract capabilities and intents from config or agent instance
        self._extract_agent_metadata()

        self.logger.info(f"UnifiedAgentNode initialized for {agent_id}")

    def _derive_agent_type(self, agent_id: str) -> str:
        """Derive agent type from agent ID."""
        if "concierge" in agent_id.lower():
            return "concierge"
        elif "marketing" in agent_id.lower():
            return "marketing"
        elif "analysis" in agent_id.lower():
            return "analysis"
        elif "classifier" in agent_id.lower():
            return "classification"
        else:
            return "general"

    def _load_agent_instance(self) -> None:
        """Load the actual agent instance based on configuration with dynamic discovery."""
        try:
            # Get agent class path from config
            agent_class_path = self.config.get("agent_class")
            if not agent_class_path:
                agent_class_path = self._discover_agent_class()

            if not agent_class_path:
                self.logger.warning(f"No agent class found for {self.agent_id}, using fallback")
                self._create_fallback_agent()
                return

            # Import and instantiate the agent
            module_path, class_name = agent_class_path.rsplit(".", 1)

            try:
                module = importlib.import_module(module_path)
                agent_class = getattr(module, class_name)
            except (ImportError, AttributeError) as e:
                self.logger.warning(f"Failed to import {agent_class_path}: {e}. Trying fallback discovery.")
                fallback_class_path = self._discover_agent_class()
                if fallback_class_path and fallback_class_path != agent_class_path:
                    module_path, class_name = fallback_class_path.rsplit(".", 1)
                    module = importlib.import_module(module_path)
                    agent_class = getattr(module, class_name)
                else:
                    raise

            # Initialize with config - handle UnifiedPersonaNode specially
            if class_name == "UnifiedPersonaNode":
                # UnifiedPersonaNode requires persona_config as first parameter
                persona_config = self.config.copy()
                # Ensure persona_id is set
                if "persona_id" not in persona_config:
                    persona_config["persona_id"] = self.agent_id
                # Get business profile if available
                business_profile = self.config.get("business_profile")
                self.agent_instance = agent_class(persona_config, business_profile)
            else:
                # For other agent classes, use the existing logic
                agent_init_config = self.config.get("agent_init_config", {})
                # Log the configuration being passed for debugging
                self.logger.debug(f"Agent init config for {self.agent_id}: {agent_init_config}")
                self.agent_instance = agent_class(**agent_init_config)

            # Validate that agent instance was created successfully
            if self.agent_instance is None:
                raise ValueError(f"Agent instance creation returned None for {agent_class_path}")

            self.logger.info(f"Successfully loaded agent instance: {agent_class_path}")

        except Exception as e:
            self.logger.error(f"Failed to load agent instance for {self.agent_id}: {e}", exc_info=True)
            self.logger.info(f"Creating fallback agent for {self.agent_id}")
            try:
                self._create_fallback_agent()
            except Exception as fallback_error:
                self.logger.error(f"Failed to create fallback agent: {fallback_error}")
                # Don't raise exception - let the system continue with null agent
                # The execute method will handle this gracefully

    def _discover_agent_class(self) -> Optional[str]:
        """Dynamically discover available agent class for this agent type."""
        try:
            # Define search patterns for different agent types
            search_patterns = {
                "concierge": [
                    "agents.langgraph.nodes.unified_persona_node.UnifiedPersonaNode"
                ],
                "marketing": [
                    "agents.langgraph.agents.marketing_agent.MarketingAgent",
                    "agents.langgraph.agents.marketing_agent.UserSelectedMarketingAgent"
                ],
                "analysis": [
                    "agents.langgraph.agents.analysis_agent.AnalysisAgent",
                    "agents.langgraph.agents.analysis_agent.UserSelectedAnalysisAgent"
                ],
                "classification": [
                    "agents.langgraph.agents.classification_agent_node.ClassificationAgent",
                    "agents.langgraph.agents.classification_agent_node.UserSelectedClassificationAgent"
                ],
                "visualization": [
                    "agents.langgraph.agents.visualization_agent.VisualizationAgent",
                    "agents.langgraph.agents.visualization_agent.UserSelectedVisualizationAgent"
                ]
            }

            # Try patterns for this agent type
            patterns = search_patterns.get(self.agent_type, [])
            for pattern in patterns:
                if self._can_import_class(pattern):
                    self.logger.info(f"Discovered agent class: {pattern}")
                    return pattern

            # If no specific pattern found, try generic discovery
            return self._generic_agent_discovery()

        except Exception as e:
            self.logger.error(f"Error in agent discovery: {e}")
            return None

    def _can_import_class(self, class_path: str) -> bool:
        """Check if a class can be imported."""
        try:
            module_path, class_name = class_path.rsplit(".", 1)
            module = importlib.import_module(module_path)
            getattr(module, class_name)
            return True
        except (ImportError, AttributeError):
            return False

    def _generic_agent_discovery(self) -> Optional[str]:
        """Generic agent discovery by scanning available modules."""
        try:
            # Scan the langgraph agents directory
            import os
            agents_dir = Path(__file__).parent.parent / "agents"

            if not agents_dir.exists():
                return None

            for agent_file in agents_dir.glob("*.py"):
                if agent_file.name.startswith("__"):
                    continue

                module_name = f"agents.langgraph.agents.{agent_file.stem}"
                try:
                    module = importlib.import_module(module_name)

                    # Look for classes that might match our agent type
                    for attr_name in dir(module):
                        if (attr_name.lower().startswith(self.agent_type.lower()) or
                            attr_name.lower().endswith("agent")):
                            attr = getattr(module, attr_name)
                            if (isinstance(attr, type) and
                                hasattr(attr, '__init__') and
                                attr_name != "BaseAgent"):
                                class_path = f"{module_name}.{attr_name}"
                                self.logger.info(f"Generic discovery found: {class_path}")
                                return class_path

                except ImportError:
                    continue

            return None

        except Exception as e:
            self.logger.error(f"Error in generic agent discovery: {e}")
            return None

    def _create_fallback_agent(self) -> None:
        """Create a fallback agent when specific agent loading fails."""
        try:
            from .fallback_agent import FallbackAgent

            # Create fallback agent with basic configuration
            fallback_config = {
                "agent_id": self.agent_id,
                "agent_type": self.agent_type,
                "capabilities": self.config.get("capabilities", []),
                "fallback_mode": True
            }

            self.agent_instance = FallbackAgent(fallback_config)
            self.logger.info(f"Created fallback agent for {self.agent_id}")

        except Exception as e:
            self.logger.error(f"Failed to create fallback agent: {e}")
            # Create minimal fallback
            self.agent_instance = self._create_minimal_fallback()

    def _create_minimal_fallback(self):
        """Create a minimal fallback agent implementation."""
        class MinimalFallbackAgent:
            def __init__(self, agent_id: str, agent_type: str):
                self.agent_id = agent_id
                self.agent_type = agent_type
                self.capabilities = ["basic_response"]

            async def process_message(self, message: str, **kwargs) -> Dict[str, Any]:
                return {
                    "message": f"I'm a {self.agent_type} assistant, but I'm currently experiencing technical difficulties. Please try again later or contact support.",
                    "metadata": {
                        "agent_type": self.agent_type,
                        "fallback_response": True,
                        "timestamp": datetime.now().isoformat()
                    },
                    "success": True
                }

            def get_agent_info(self) -> Dict[str, Any]:
                return {
                    "agent_id": self.agent_id,
                    "agent_type": self.agent_type,
                    "capabilities": self.capabilities,
                    "fallback_mode": True
                }

        return MinimalFallbackAgent(self.agent_id, self.agent_type)

    def _extract_agent_metadata(self) -> None:
        """Extract capabilities and intents from config or agent instance."""
        # From config
        self.capabilities = self.config.get("capabilities", [])
        self.supported_intents = self.config.get("supported_intents", [])
        self.tools = self.config.get("tools", [])

        # From agent instance if available
        if self.agent_instance:
            if hasattr(self.agent_instance, "capabilities"):
                self.capabilities.extend(getattr(self.agent_instance, "capabilities", []))
            if hasattr(self.agent_instance, "supported_intents"):
                self.supported_intents.extend(getattr(self.agent_instance, "supported_intents", []))
            if hasattr(self.agent_instance, "tools"):
                self.tools.extend(getattr(self.agent_instance, "tools", []))

        # Remove duplicates
        self.capabilities = list(set(self.capabilities))
        self.supported_intents = list(set(self.supported_intents))
        self.tools = list(set(self.tools))

    async def _process_message(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Process message using the loaded agent instance.

        Args:
            state: Current workflow state

        Returns:
            Updated workflow state with agent response
        """
        if not self.agent_instance:
            self.logger.warning(f"Agent instance is None for {self.agent_id}")
            return await self._handle_no_agent_error(state)

        try:
            # Extract message and context
            current_message = state.get("current_message", {})
            message_content = current_message.get("content", "")

            # If no current_message, try to get the last user message
            if not message_content:
                messages = state.get("messages", [])
                for msg in reversed(messages):
                    if msg.get("type") == MessageType.USER.value or msg.get("role") == "user":
                        message_content = msg.get("content", "")
                        break

            self.logger.info(f"Processing message for {self.agent_id}: '{message_content[:50] if message_content else 'NO MESSAGE'}...'")

            # If still no message content, provide a default greeting request
            if not message_content:
                # Check if this is a greeting generation task
                task_context = state.get("task_context", {})
                if task_context.get("is_initial_greeting") or message_content == "generate_greeting":
                    message_content = "generate_greeting"
                    self.logger.info(f"Detected greeting generation task for {self.agent_id}")
                else:
                    message_content = "Hello"
                    self.logger.info(f"No message content found, using default greeting for {self.agent_id}")

            # Convert state to legacy context format
            legacy_context = await self._convert_state_to_legacy_context(state)

            # Check if agent has process_message method
            if hasattr(self.agent_instance, "process_message"):
                self.logger.info(f"Agent {self.agent_id} has process_message method, calling it")
                # Use the agent's process_message method
                response = await self.agent_instance.process_message(
                    message=message_content,
                    user_id=state.get("user_id"),
                    conversation_id=state.get("conversation_id"),
                    context=legacy_context
                )

                self.logger.info(f"Agent {self.agent_id} response: {response}")

                # Convert response to unified format
                state = await self._convert_response_to_state(state, response)

            else:
                self.logger.warning(f"Agent {self.agent_id} does not have process_message method, using generic processing")
                # Fallback to generic processing
                state = await self._handle_generic_processing(state, message_content, legacy_context)

            return state

        except Exception as e:
            self.logger.error(f"Error in unified agent processing: {e}", exc_info=True)
            return await self._handle_processing_error(state, str(e))

    async def _convert_state_to_legacy_context(self, state: UnifiedDatageniusState) -> Dict[str, Any]:
        """Convert unified state to legacy context format."""
        return {
            "user_id": state.get("user_id"),
            "conversation_id": state.get("conversation_id"),
            "task_context": state.get("task_context", {}),
            "business_context": state.get("business_context", {}),
            "cross_agent_context": state.get("cross_agent_context", {}),
            "shared_insights": state.get("shared_insights", []),
            "is_continuing_conversation": len(state.get("messages", [])) > 1,
            "conversation_state": state.get("conversation_state"),
            "task_type": state.get("task_context", {}).get("task_type")
        }

    async def _convert_response_to_state(
        self,
        state: UnifiedDatageniusState,
        response: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Convert agent response to unified state format."""
        if response.get("success", True) and "message" in response:
            # Create response message
            response_message = {
                "id": str(uuid.uuid4()),
                "content": response["message"],
                "type": MessageType.AGENT.value,
                "timestamp": datetime.now().isoformat(),
                "agent_id": self.agent_id,
                "metadata": response.get("metadata", {}),
                "in_response_to": state.get("current_message_id")
            }
            state = add_message(state, response_message, MessageType.AGENT)

            # Handle workflow termination signals
            metadata = response.get("metadata", {})
            if metadata.get("workflow_complete") or metadata.get("next_action") == "END":
                # Set workflow completion flag to prevent infinite loops
                state["workflow_complete"] = True
                state["next_action"] = "END"
                self.logger.info(f"🏁 Agent {self.agent_id} marked workflow as complete")

        return state

    async def _handle_no_agent_error(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """Handle case where agent instance is not available."""
        # For concierge agent, provide a helpful fallback response
        if "concierge" in self.agent_id.lower():
            fallback_content = "Hello! I'm your Datagenius Concierge. I'm here to help you navigate our AI personas and find the right assistant for your needs. How can I help you today?"
        else:
            fallback_content = f"I apologize, but the {self.agent_type} agent is currently unavailable. Please try again later."

        error_message = {
            "id": str(uuid.uuid4()),
            "content": fallback_content,
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id,
            "metadata": {
                "error": "agent_not_available" if "concierge" not in self.agent_id.lower() else None,
                "agent_type": self.agent_type,
                "fallback_response": True
            },
            "in_response_to": state.get("current_message_id")
        }
        state = add_message(state, error_message, MessageType.AGENT)
        return state

    async def _coordinate_with_specialist(
        self,
        state: UnifiedDatageniusState,
        specialist_agent_id: str,
        consultation_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Allow autonomous coordination while maintaining primary conversation thread.

        This method enables background specialist consultation with async timeouts,
        circuit breaker pattern, and resource management for production reliability.

        Args:
            state: Current workflow state
            specialist_agent_id: ID of the specialist agent to consult
            consultation_context: Optional context for the consultation

        Returns:
            Dictionary containing specialist insights and recommendations
        """
        # Check resource limits
        if self.active_coordinations >= self.max_concurrent_coordinations:
            self.logger.warning(f"⚠️ Max concurrent coordinations reached: {self.active_coordinations}")
            return {
                "success": False,
                "error": "Maximum concurrent coordinations exceeded",
                "insights": []
            }

        # Get or create circuit breaker for this specialist
        if specialist_agent_id not in self.coordination_circuit_breakers:
            self.coordination_circuit_breakers[specialist_agent_id] = CircuitBreaker()

        circuit_breaker = self.coordination_circuit_breakers[specialist_agent_id]

        # Check circuit breaker
        if not circuit_breaker.can_execute():
            self.logger.warning(f"⚠️ Circuit breaker open for specialist {specialist_agent_id}")
            return {
                "success": False,
                "error": f"Circuit breaker open for specialist {specialist_agent_id}",
                "insights": []
            }

        start_time = time.time()
        success = False

        try:
            self.active_coordinations += 1
            self.logger.info(f"🤝 {self.agent_id}: Coordinating with specialist {specialist_agent_id}")

            # Validate specialist agent exists
            available_agent_ids = state.get("available_agents", [])
            if specialist_agent_id not in available_agent_ids:
                self.logger.warning(f"⚠️ Specialist agent {specialist_agent_id} not available")
                circuit_breaker.record_failure()
                return {
                    "success": False,
                    "error": f"Specialist agent {specialist_agent_id} not available",
                    "insights": []
                }

            # Prepare consultation request
            consultation_request = {
                "requesting_agent_id": self.agent_id,
                "specialist_agent_id": specialist_agent_id,
                "consultation_context": consultation_context or {},
                "current_message": state.get("current_message", {}),
                "business_context": state.get("business_profile_id"),
                "timestamp": datetime.now().isoformat()
            }

            # Add to cross-agent coordination log
            coordination_log_entry = {
                "id": str(uuid.uuid4()),
                "type": "specialist_consultation",
                "requesting_agent_id": self.agent_id,
                "specialist_agent_id": specialist_agent_id,
                "request": consultation_request,
                "timestamp": datetime.now().isoformat(),
                "status": "initiated"
            }

            # Update state with coordination record
            if "agent_coordination_log" not in state:
                state["agent_coordination_log"] = []
            state["agent_coordination_log"].append(coordination_log_entry)

            # Execute specialist consultation with timeout
            try:
                specialist_insights = await asyncio.wait_for(
                    self._execute_specialist_consultation(
                        specialist_agent_id, consultation_request, state
                    ),
                    timeout=self.coordination_timeout
                )

                # Record success
                circuit_breaker.record_success()
                success = True

            except asyncio.TimeoutError:
                self.logger.error(f"⏰ Specialist consultation timeout: {specialist_agent_id}")
                circuit_breaker.record_failure()
                coordination_log_entry["status"] = "timeout"
                coordination_log_entry["error"] = "Consultation timeout"
                return {
                    "success": False,
                    "error": f"Consultation with {specialist_agent_id} timed out",
                    "insights": []
                }

            # Update coordination record with results
            coordination_log_entry["status"] = "completed"
            coordination_log_entry["insights"] = specialist_insights
            coordination_log_entry["completed_at"] = datetime.now().isoformat()

            self.logger.info(f"✅ {self.agent_id}: Specialist consultation completed with {specialist_agent_id}")

            return {
                "success": True,
                "specialist_agent_id": specialist_agent_id,
                "insights": specialist_insights,
                "coordination_id": coordination_log_entry["id"]
            }

        except Exception as e:
            self.logger.error(f"❌ Error in specialist coordination: {e}", exc_info=True)
            circuit_breaker.record_failure()
            return {
                "success": False,
                "error": str(e),
                "insights": []
            }
        finally:
            self.active_coordinations -= 1

            # Update coordination performance metrics
            coordination_time = time.time() - start_time
            performance_monitor.update_agent_stats(
                self.agent_id,
                success,
                coordination_time,
                "coordination"
            )

            performance_monitor.record_metric(
                "coordination_duration",
                coordination_time,
                agent_id=self.agent_id,
                metadata={
                    "specialist_agent": specialist_agent_id,
                    "success": success
                }
            )

    async def _suggest_handoff(
        self,
        state: UnifiedDatageniusState,
        suggested_agent: str,
        reason: str,
        confidence: float = 0.8
    ) -> UnifiedDatageniusState:
        """
        Suggest handoff to user while maintaining control.

        This method implements handoff suggestions with user control as specified
        in workflow.md section 2.2, ensuring users maintain control over agent switches.

        Args:
            state: Current workflow state
            suggested_agent: ID of the suggested agent
            reason: Reason for the handoff suggestion
            confidence: Confidence in the suggestion (0.0 to 1.0)

        Returns:
            Updated state with handoff suggestion
        """
        try:
            self.logger.info(f"💡 {self.agent_id}: Suggesting handoff to {suggested_agent}")

            # Validate suggested agent exists
            available_agents = state.get("available_agents", [])
            if suggested_agent not in available_agents:
                self.logger.warning(f"⚠️ Suggested agent {suggested_agent} not available")
                return state

            # Create handoff suggestion message
            suggestion_message = (
                f"I think our {suggested_agent} specialist might be better suited for this request "
                f"because {reason}. Would you like me to connect you with them? "
                f"(You can say 'yes' to switch or 'no' to continue with me)"
            )

            # Add suggestion message to conversation
            suggestion_msg = {
                "id": str(uuid.uuid4()),
                "content": suggestion_message,
                "type": MessageType.AGENT.value,
                "timestamp": datetime.now().isoformat(),
                "agent_id": self.agent_id,
                "metadata": {
                    "message_type": "handoff_suggestion",
                    "suggested_agent": suggested_agent,
                    "confidence": confidence
                }
            }

            from ..states.unified_state import add_message, MessageType
            state = add_message(state, suggestion_msg, MessageType.AGENT)

            # Store pending handoff suggestion in state
            handoff_suggestion = {
                "id": str(uuid.uuid4()),
                "suggesting_agent": self.agent_id,
                "target_agent": suggested_agent,
                "reason": reason,
                "confidence": confidence,
                "timestamp": datetime.now().isoformat(),
                "status": "pending_user_response",
                "message_id": suggestion_msg["id"]
            }

            state["pending_handoff_suggestion"] = handoff_suggestion

            # Log the suggestion
            if "agent_coordination_log" not in state:
                state["agent_coordination_log"] = []

            coordination_record = {
                "id": handoff_suggestion["id"],
                "type": "handoff_suggestion",
                "suggesting_agent": self.agent_id,
                "target_agent": suggested_agent,
                "reason": reason,
                "confidence": confidence,
                "timestamp": datetime.now().isoformat(),
                "status": "pending_user_response"
            }
            state["agent_coordination_log"].append(coordination_record)

            self.logger.info(f"✅ {self.agent_id}: Handoff suggestion created for {suggested_agent}")

            return state

        except Exception as e:
            self.logger.error(f"❌ Error creating handoff suggestion: {e}", exc_info=True)
            return state

    async def _execute_specialist_consultation(
        self,
        specialist_agent: str,
        consultation_request: Dict[str, Any],
        state: UnifiedDatageniusState
    ) -> List[Dict[str, Any]]:
        """
        Execute actual specialist consultation through agent-to-agent communication.

        This method performs real agent coordination by invoking the specialist agent
        with the consultation context and returning their insights.

        Args:
            specialist_agent: ID of the specialist agent
            consultation_request: The consultation request
            state: Current workflow state

        Returns:
            List of specialist insights from actual agent consultation
        """
        try:
            # Get the specialist agent instance
            from ..core.workflow_manager import WorkflowManager
            workflow_manager = getattr(self, '_workflow_manager', None)

            if not workflow_manager:
                self.logger.warning(f"No workflow manager available for specialist consultation")
                return await self._fallback_specialist_consultation(specialist_agent, consultation_request, state)

            specialist_node = workflow_manager.agent_nodes.get(specialist_agent)
            if not specialist_node:
                self.logger.warning(f"Specialist agent {specialist_agent} not found")
                return []

            # Create consultation state for the specialist
            consultation_state = self._create_consultation_state(consultation_request, state)

            # Execute specialist agent processing
            specialist_result = await specialist_node._process_message(consultation_state)

            # Extract insights from specialist response
            insights = self._extract_insights_from_specialist_response(
                specialist_result, specialist_agent, consultation_request
            )

            self.logger.info(f"✅ Received {len(insights)} insights from specialist {specialist_agent}")
            return insights

        except Exception as e:
            self.logger.error(f"❌ Error in specialist consultation: {e}", exc_info=True)
            # Fallback to basic consultation
            return await self._fallback_specialist_consultation(specialist_agent, consultation_request, state)

    def _create_consultation_state(
        self,
        consultation_request: Dict[str, Any],
        original_state: UnifiedDatageniusState
    ) -> UnifiedDatageniusState:
        """Create a consultation-specific state for the specialist agent."""
        # Create a minimal state for consultation
        consultation_state = original_state.copy()

        # Add consultation-specific context
        consultation_state["consultation_mode"] = True
        consultation_state["consultation_request"] = consultation_request
        consultation_state["requesting_agent"] = self.agent_id

        # Preserve essential context
        consultation_state["current_message"] = consultation_request.get("current_message", {})
        consultation_state["business_profile_id"] = consultation_request.get("business_context")

        return consultation_state

    def _extract_insights_from_specialist_response(
        self,
        specialist_result: UnifiedDatageniusState,
        specialist_agent: str,
        consultation_request: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract actionable insights from specialist agent response."""
        insights = []

        # Extract from messages if available
        messages = specialist_result.get("messages", [])
        if messages:
            latest_message = messages[-1]
            if latest_message.get("agent_id") == specialist_agent:
                insights.append({
                    "type": "specialist_response",
                    "content": latest_message.get("content", ""),
                    "confidence": 0.9,
                    "specialist": specialist_agent,
                    "timestamp": latest_message.get("timestamp"),
                    "message_id": latest_message.get("id")
                })

        # Extract from agent outputs if available
        agent_outputs = specialist_result.get("agent_outputs", {})
        if specialist_agent in agent_outputs:
            specialist_output = agent_outputs[specialist_agent]
            insights.append({
                "type": "specialist_analysis",
                "content": str(specialist_output),
                "confidence": 0.8,
                "specialist": specialist_agent,
                "timestamp": datetime.now().isoformat()
            })

        # Extract from shared insights if available
        shared_insights = specialist_result.get("shared_insights", [])
        for insight in shared_insights:
            if insight.get("agent_id") == specialist_agent:
                insights.append({
                    "type": "shared_insight",
                    "content": insight.get("content", ""),
                    "confidence": insight.get("confidence", 0.7),
                    "specialist": specialist_agent,
                    "timestamp": insight.get("timestamp")
                })

        return insights

    async def _fallback_specialist_consultation(
        self,
        specialist_agent: str,
        consultation_request: Dict[str, Any],
        state: UnifiedDatageniusState
    ) -> List[Dict[str, Any]]:
        """Fallback consultation when direct agent communication fails."""
        self.logger.info(f"Using fallback consultation for {specialist_agent}")

        # Generate basic insights based on agent capabilities
        insights = []
        current_message = consultation_request.get("current_message", {})
        message_content = current_message.get("content", "")

        # Use agent capabilities if available
        agent_capabilities = getattr(self, 'agent_capabilities', {})
        specialist_info = agent_capabilities.get(specialist_agent, {})

        if specialist_info.get("capabilities"):
            for capability in specialist_info["capabilities"][:3]:  # Limit to top 3
                insights.append({
                    "type": "capability_insight",
                    "content": f"Consider leveraging {capability} for this request",
                    "confidence": 0.6,
                    "specialist": specialist_agent,
                    "capability": capability
                })
        else:
            # Generic fallback
            insights.append({
                "type": "general_consultation",
                "content": f"Specialist {specialist_agent} recommends careful analysis of the request",
                "confidence": 0.5,
                "specialist": specialist_agent
            })

        return insights

    async def _handle_processing_error(
        self,
        state: UnifiedDatageniusState,
        error: str
    ) -> UnifiedDatageniusState:
        """Handle processing errors."""
        error_message = {
            "id": str(uuid.uuid4()),
            "content": "I encountered an issue processing your request. Please try again.",
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id,
            "metadata": {
                "error": error,
                "agent_type": self.agent_type
            },
            "in_response_to": state.get("current_message_id")
        }
        state = add_message(state, error_message, MessageType.AGENT)
        return state

    async def _handle_generic_processing(
        self,
        state: UnifiedDatageniusState,
        message: str,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Handle generic processing for agents without process_message method."""
        # Basic response for agents that don't have process_message
        response_content = f"I'm the {self.agent_type} agent. I received your message: {message[:100]}..."

        response_message = {
            "id": str(uuid.uuid4()),
            "content": response_content,
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id,
            "metadata": {
                "processing_type": "generic",
                "agent_type": self.agent_type,
                "workflow_complete": True,  # Mark workflow as complete to prevent infinite loops
                "next_action": "END"  # Signal workflow should end
            },
            "in_response_to": state.get("current_message_id")
        }
        state = add_message(state, response_message, MessageType.AGENT)

        # CRITICAL: Set workflow completion flags at state level to prevent infinite loops
        # The workflow manager checks these state-level flags to determine when to end
        state["workflow_complete"] = True
        state["next_action"] = "END"
        self.logger.info(f"🏁 Generic processing for {self.agent_id} marked workflow as complete")

        return state
