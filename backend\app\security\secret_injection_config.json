{"enabled": true, "use_environment_variables": true, "use_secret_manager": true, "validate_secret_strength": true, "audit_secret_access": true, "required_secrets": ["JWT_SECRET_KEY", "DATABASE_URL", "REDIS_URL", "ENVIRONMENT"], "secret_validation": {"min_length": 32, "require_special_chars": true, "require_numbers": true, "require_uppercase": true, "require_lowercase": true, "forbidden_patterns": ["your-", "test-", "dev-", "changeme", "password123", "secret123", "development-only"], "entropy_threshold": 4.0, "check_common_passwords": true}, "injection_methods": {"environment_variables": {"enabled": true, "priority": 1, "prefix": "", "validate_on_load": true}, "secret_manager": {"enabled": true, "priority": 2, "provider": "local_keystore", "encryption_key_env": "SECRET_MANAGER_KEY", "validate_on_load": true}, "configuration_files": {"enabled": false, "priority": 3, "encrypted": true, "validate_on_load": true}}, "secret_categories": {"authentication": {"secrets": ["JWT_SECRET_KEY", "JWT_SECRET_KEY_DEVELOPMENT", "JWT_SECRET_KEY_STAGING", "JWT_SECRET_KEY_PRODUCTION", "OAUTH_CLIENT_SECRET", "GOOGLE_CLIENT_SECRET"], "rotation_interval_days": 90, "require_strong_validation": true, "audit_all_access": true}, "database": {"secrets": ["DATABASE_URL", "DATABASE_PASSWORD", "REDIS_URL", "REDIS_PASSWORD"], "rotation_interval_days": 180, "require_strong_validation": true, "audit_all_access": true}, "external_services": {"secrets": ["WEBHOOK_SECRET_KEY", "SMTP_PASSWORD", "API_KEYS", "THIRD_PARTY_TOKENS"], "rotation_interval_days": 60, "require_strong_validation": true, "audit_all_access": true}, "encryption": {"secrets": ["ENCRYPTION_KEY", "DATA_ENCRYPTION_KEY", "FILE_ENCRYPTION_KEY"], "rotation_interval_days": 365, "require_strong_validation": true, "audit_all_access": true, "backup_old_keys": true}}, "environment_specific_settings": {"development": {"allow_weak_secrets": false, "require_all_secrets": false, "log_secret_access": true, "validate_on_startup": true}, "staging": {"allow_weak_secrets": false, "require_all_secrets": true, "log_secret_access": true, "validate_on_startup": true}, "production": {"allow_weak_secrets": false, "require_all_secrets": true, "log_secret_access": true, "validate_on_startup": true, "fail_on_missing_secrets": true}, "testing": {"allow_weak_secrets": true, "require_all_secrets": false, "log_secret_access": false, "validate_on_startup": false, "use_test_secrets": true}}, "security_policies": {"access_control": {"require_authentication": true, "require_authorization": true, "log_all_access": true, "rate_limit_access": true}, "transmission": {"encrypt_in_transit": true, "use_secure_channels": true, "validate_certificates": true}, "storage": {"encrypt_at_rest": true, "use_secure_storage": true, "backup_encrypted": true}}, "monitoring": {"enabled": true, "log_secret_usage": true, "alert_on_failures": true, "alert_on_weak_secrets": true, "alert_on_rotation_needed": true, "metrics_collection": true}}