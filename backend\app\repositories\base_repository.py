"""
Base Repository Pattern Implementation.

Provides a generic, type-safe repository pattern that eliminates the need for
duplicate CRUD functions throughout the codebase.
"""

import logging
import uuid
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import (
    TypeVar, Generic, Type, Optional, List, Dict, Any, Union,
    Sequence, Callable, Tuple
)

from sqlalchemy import text, func, and_, or_, desc, asc
from sqlalchemy.orm import Session, Query
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from pydantic import BaseModel

from ..database import Base
from ..utils.error_handlers import MCPError

logger = logging.getLogger(__name__)

# Type variables for generic repository
T = TypeVar('T', bound=Base)  # Database model type
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)  # Create schema type
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)  # Update schema type


class RepositoryError(MCPError):
    """Repository-specific error."""

    def __init__(self, message: str, entity_type: Optional[str] = None, operation: Optional[str] = None, **kwargs):
        # Filter kwargs to only include parameters that MCPError accepts
        mcp_kwargs = {}
        for key in ['details', 'field_errors', 'suggestions']:
            if key in kwargs:
                mcp_kwargs[key] = kwargs[key]

        super().__init__(message, error_code="REPOSITORY_ERROR", **mcp_kwargs)
        self.entity_type = entity_type
        self.operation = operation


class RepositoryValidationError(RepositoryError):
    """Repository validation error."""

    def __init__(self, message: str, validation_errors: List[str], **kwargs):
        # Pass field_errors to MCPError if validation_errors are provided
        if validation_errors:
            kwargs['field_errors'] = {'validation': validation_errors}

        super().__init__(message, **kwargs)
        self.validation_errors = validation_errors


class BaseRepository(Generic[T], ABC):
    """
    Generic base repository providing common CRUD operations.
    
    This class eliminates the need for duplicate CRUD functions by providing
    a type-safe, generic interface for database operations.
    """
    
    def __init__(self, db: Session, model_class: Type[T]):
        self.db = db
        self.session = db  # Add session alias for compatibility
        self.model_class = model_class
        self.logger = logging.getLogger(f"{__name__}.{model_class.__name__}Repository")

        # Audit logging
        self._audit_enabled = True
        self._query_cache = {}
        self._cache_enabled = False
    
    def enable_caching(self, enabled: bool = True):
        """Enable or disable repository-level caching."""
        self._cache_enabled = enabled
        if not enabled:
            self._query_cache.clear()
        self.logger.info(f"Repository caching {'enabled' if enabled else 'disabled'}")
    
    def enable_audit_logging(self, enabled: bool = True):
        """Enable or disable audit logging."""
        self._audit_enabled = enabled
        self.logger.info(f"Audit logging {'enabled' if enabled else 'disabled'}")
    
    def _log_audit(self, operation: str, entity_id: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """Log audit information for database operations."""
        if not self._audit_enabled:
            return
        
        audit_data = {
            "operation": operation,
            "entity_type": self.model_class.__name__,
            "entity_id": entity_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "details": details or {}
        }
        
        self.logger.info(f"AUDIT: {operation} on {self.model_class.__name__}", extra=audit_data)
    
    def _build_query(self, **filters) -> Query:
        """Build a base query with optional filters."""
        query = self.db.query(self.model_class)
        
        for key, value in filters.items():
            if hasattr(self.model_class, key):
                if isinstance(value, (list, tuple)):
                    query = query.filter(getattr(self.model_class, key).in_(value))
                else:
                    query = query.filter(getattr(self.model_class, key) == value)
        
        return query
    
    def _apply_ordering(self, query: Query, order_by: Optional[str] = None, desc_order: bool = False) -> Query:
        """Apply ordering to query."""
        if order_by and hasattr(self.model_class, order_by):
            column = getattr(self.model_class, order_by)
            if desc_order:
                query = query.order_by(desc(column))
            else:
                query = query.order_by(asc(column))
        
        return query
    
    def _apply_pagination(self, query: Query, skip: int = 0, limit: Optional[int] = None) -> Query:
        """Apply pagination to query."""
        if skip > 0:
            query = query.offset(skip)
        if limit is not None:
            query = query.limit(limit)
        
        return query
    
    # Core CRUD Operations
    
    def create(self, obj_in: Union[CreateSchemaType, Dict[str, Any]], **kwargs) -> T:
        """
        Create a new entity.
        
        Args:
            obj_in: Create schema or dictionary with entity data
            **kwargs: Additional fields to set on the entity
            
        Returns:
            Created entity
            
        Raises:
            RepositoryError: If creation fails
        """
        try:
            # Convert Pydantic model to dict if needed
            if isinstance(obj_in, BaseModel):
                obj_data = obj_in.model_dump(exclude_unset=True)
            else:
                obj_data = obj_in.copy()
            
            # Add any additional kwargs
            obj_data.update(kwargs)
            
            # Generate ID if not provided and model has id field
            if hasattr(self.model_class, 'id') and 'id' not in obj_data:
                obj_data['id'] = str(uuid.uuid4())
            
            # Create entity
            db_obj = self.model_class(**obj_data)
            self.db.add(db_obj)
            self.db.commit()
            self.db.refresh(db_obj)
            
            # Log audit
            entity_id = getattr(db_obj, 'id', None)
            self._log_audit("CREATE", entity_id, {"data": obj_data})
            
            self.logger.debug(f"Created {self.model_class.__name__} with ID: {entity_id}")
            return db_obj
            
        except IntegrityError as e:
            self.db.rollback()
            self.logger.error(f"Integrity error creating {self.model_class.__name__}: {e}")
            raise RepositoryError(
                f"Failed to create {self.model_class.__name__}: Integrity constraint violation",
                entity_type=self.model_class.__name__
            )
        except SQLAlchemyError as e:
            self.db.rollback()
            self.logger.error(f"Database error creating {self.model_class.__name__}: {e}")
            raise RepositoryError(
                f"Database error creating {self.model_class.__name__}",
                entity_type=self.model_class.__name__
            )
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Unexpected error creating {self.model_class.__name__}: {e}")
            raise RepositoryError(
                f"Unexpected error creating {self.model_class.__name__}",
                entity_type=self.model_class.__name__
            )
    
    def get(self, entity_id: Union[str, int]) -> Optional[T]:
        """
        Get entity by ID.
        
        Args:
            entity_id: Entity identifier
            
        Returns:
            Entity if found, None otherwise
        """
        try:
            # Check cache first
            cache_key = f"get_{entity_id}"
            if self._cache_enabled and cache_key in self._query_cache:
                return self._query_cache[cache_key]
            
            entity = self.db.query(self.model_class).filter(
                self.model_class.id == entity_id
            ).first()
            
            # Cache result
            if self._cache_enabled:
                self._query_cache[cache_key] = entity
            
            if entity:
                self._log_audit("READ", str(entity_id))
            
            return entity
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error getting {self.model_class.__name__} {entity_id}: {e}")
            raise RepositoryError(
                f"Database error retrieving {self.model_class.__name__}",
                entity_type=self.model_class.__name__
            )
    
    def get_multi(
        self,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[str] = None,
        desc_order: bool = False,
        **filters
    ) -> List[T]:
        """
        Get multiple entities with filtering and pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            order_by: Field to order by
            desc_order: Whether to use descending order
            **filters: Additional filters to apply
            
        Returns:
            List of entities
        """
        try:
            # Build cache key
            cache_key = f"get_multi_{skip}_{limit}_{order_by}_{desc_order}_{hash(str(sorted(filters.items())))}"
            if self._cache_enabled and cache_key in self._query_cache:
                return self._query_cache[cache_key]
            
            query = self._build_query(**filters)
            query = self._apply_ordering(query, order_by, desc_order)
            query = self._apply_pagination(query, skip, limit)
            
            entities = query.all()
            
            # Cache result
            if self._cache_enabled:
                self._query_cache[cache_key] = entities
            
            self._log_audit("READ_MULTI", details={
                "count": len(entities),
                "filters": filters,
                "pagination": {"skip": skip, "limit": limit}
            })
            
            return entities
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error getting multiple {self.model_class.__name__}: {e}")
            raise RepositoryError(
                f"Database error retrieving {self.model_class.__name__} records",
                entity_type=self.model_class.__name__
            )
    
    def count(self, **filters) -> int:
        """
        Count entities with optional filters.
        
        Args:
            **filters: Filters to apply
            
        Returns:
            Number of matching entities
        """
        try:
            query = self._build_query(**filters)
            count = query.count()
            
            self._log_audit("COUNT", details={"count": count, "filters": filters})
            
            return count
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error counting {self.model_class.__name__}: {e}")
            raise RepositoryError(
                f"Database error counting {self.model_class.__name__} records",
                entity_type=self.model_class.__name__
            )
    
    def update(
        self,
        entity_id: Union[str, int],
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> Optional[T]:
        """
        Update an entity.
        
        Args:
            entity_id: Entity identifier
            obj_in: Update schema or dictionary with updated data
            
        Returns:
            Updated entity if found, None otherwise
        """
        try:
            # Get existing entity
            db_obj = self.get(entity_id)
            if not db_obj:
                return None
            
            # Convert Pydantic model to dict if needed
            if isinstance(obj_in, BaseModel):
                update_data = obj_in.model_dump(exclude_unset=True)
            else:
                update_data = obj_in
            
            # Update fields
            for field, value in update_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)
            
            # Update timestamp if available
            if hasattr(db_obj, 'updated_at'):
                db_obj.updated_at = datetime.now(timezone.utc)
            
            self.db.commit()
            self.db.refresh(db_obj)
            
            # Clear cache
            if self._cache_enabled:
                self._query_cache.clear()
            
            self._log_audit("UPDATE", str(entity_id), {"data": update_data})
            
            self.logger.debug(f"Updated {self.model_class.__name__} with ID: {entity_id}")
            return db_obj
            
        except IntegrityError as e:
            self.db.rollback()
            self.logger.error(f"Integrity error updating {self.model_class.__name__} {entity_id}: {e}")
            raise RepositoryError(
                f"Failed to update {self.model_class.__name__}: Integrity constraint violation",
                entity_type=self.model_class.__name__
            )
        except SQLAlchemyError as e:
            self.db.rollback()
            self.logger.error(f"Database error updating {self.model_class.__name__} {entity_id}: {e}")
            raise RepositoryError(
                f"Database error updating {self.model_class.__name__}",
                entity_type=self.model_class.__name__
            )
    
    def delete(self, entity_id: Union[str, int]) -> bool:
        """
        Delete an entity.
        
        Args:
            entity_id: Entity identifier
            
        Returns:
            True if entity was deleted, False if not found
        """
        try:
            db_obj = self.get(entity_id)
            if not db_obj:
                return False
            
            self.db.delete(db_obj)
            self.db.commit()
            
            # Clear cache
            if self._cache_enabled:
                self._query_cache.clear()
            
            self._log_audit("DELETE", str(entity_id))
            
            self.logger.debug(f"Deleted {self.model_class.__name__} with ID: {entity_id}")
            return True
            
        except SQLAlchemyError as e:
            self.db.rollback()
            self.logger.error(f"Database error deleting {self.model_class.__name__} {entity_id}: {e}")
            raise RepositoryError(
                f"Database error deleting {self.model_class.__name__}",
                entity_type=self.model_class.__name__
            )
    
    # Batch Operations
    
    def create_batch(self, objects: List[Union[CreateSchemaType, Dict[str, Any]]]) -> List[T]:
        """
        Create multiple entities in a single transaction.
        
        Args:
            objects: List of create schemas or dictionaries
            
        Returns:
            List of created entities
        """
        try:
            created_entities = []
            
            for obj_in in objects:
                # Convert Pydantic model to dict if needed
                if isinstance(obj_in, BaseModel):
                    obj_data = obj_in.model_dump(exclude_unset=True)
                else:
                    obj_data = obj_in.copy()
                
                # Generate ID if not provided and model has id field
                if hasattr(self.model_class, 'id') and 'id' not in obj_data:
                    obj_data['id'] = str(uuid.uuid4())
                
                db_obj = self.model_class(**obj_data)
                self.db.add(db_obj)
                created_entities.append(db_obj)
            
            self.db.commit()
            
            # Refresh all entities
            for entity in created_entities:
                self.db.refresh(entity)
            
            # Clear cache
            if self._cache_enabled:
                self._query_cache.clear()
            
            self._log_audit("CREATE_BATCH", details={"count": len(created_entities)})
            
            self.logger.debug(f"Created {len(created_entities)} {self.model_class.__name__} entities")
            return created_entities
            
        except SQLAlchemyError as e:
            self.db.rollback()
            self.logger.error(f"Database error creating batch {self.model_class.__name__}: {e}")
            raise RepositoryError(
                f"Database error creating batch {self.model_class.__name__}",
                entity_type=self.model_class.__name__
            )
    
    def delete_batch(self, entity_ids: List[Union[str, int]]) -> int:
        """
        Delete multiple entities by IDs.
        
        Args:
            entity_ids: List of entity identifiers
            
        Returns:
            Number of entities deleted
        """
        try:
            deleted_count = self.db.query(self.model_class).filter(
                self.model_class.id.in_(entity_ids)
            ).delete(synchronize_session=False)
            
            self.db.commit()
            
            # Clear cache
            if self._cache_enabled:
                self._query_cache.clear()
            
            self._log_audit("DELETE_BATCH", details={
                "count": deleted_count,
                "entity_ids": entity_ids
            })
            
            self.logger.debug(f"Deleted {deleted_count} {self.model_class.__name__} entities")
            return deleted_count
            
        except SQLAlchemyError as e:
            self.db.rollback()
            self.logger.error(f"Database error deleting batch {self.model_class.__name__}: {e}")
            raise RepositoryError(
                f"Database error deleting batch {self.model_class.__name__}",
                entity_type=self.model_class.__name__
            )
    
    # Advanced Query Methods
    
    def exists(self, entity_id: Union[str, int]) -> bool:
        """Check if entity exists by ID."""
        try:
            return self.db.query(self.model_class.id).filter(
                self.model_class.id == entity_id
            ).first() is not None
        except SQLAlchemyError as e:
            self.logger.error(f"Database error checking existence of {self.model_class.__name__} {entity_id}: {e}")
            raise RepositoryError(
                f"Database error checking {self.model_class.__name__} existence",
                entity_type=self.model_class.__name__
            )
    
    def find_by(self, **filters) -> Optional[T]:
        """Find first entity matching filters."""
        try:
            query = self._build_query(**filters)
            entity = query.first()
            
            if entity:
                entity_id = getattr(entity, 'id', None)
                self._log_audit("FIND_BY", str(entity_id), {"filters": filters})
            
            return entity
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error finding {self.model_class.__name__}: {e}")
            raise RepositoryError(
                f"Database error finding {self.model_class.__name__}",
                entity_type=self.model_class.__name__
            )
    
    def find_all_by(self, **filters) -> List[T]:
        """Find all entities matching filters."""
        return self.get_multi(**filters)
    
    # Custom query execution with security
    
    def execute_raw_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """
        Execute a raw SQL query with parameterization for security.
        
        Args:
            query: SQL query string with named parameters
            params: Dictionary of parameters for the query
            
        Returns:
            Query result
            
        Raises:
            RepositoryError: If query execution fails
        """
        try:
            # Ensure parameterized queries for security
            if params:
                result = self.db.execute(text(query), params)
            else:
                result = self.db.execute(text(query))
            
            self._log_audit("RAW_QUERY", details={
                "query": query,
                "params": params or {}
            })
            
            return result
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error executing raw query: {e}")
            raise RepositoryError(
                "Database error executing raw query",
                entity_type=self.model_class.__name__
            )
