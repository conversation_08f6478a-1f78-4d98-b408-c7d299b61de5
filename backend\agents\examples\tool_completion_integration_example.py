"""
Example implementation showing how to integrate ToolCompletionMixin in agent components.

This example demonstrates the complete workflow for using the universal tool completion
system in agent components, including:
1. Tool execution detection
2. Context reset after tool completion
3. Conversational mode handling
4. Follow-up question processing
"""

import logging
from typing import Dict, Any, Optional
from agents.mixins import ToolCompletionMixin
from agents.components.base import AgentComponent

logger = logging.getLogger(__name__)


class ExampleAgentComponent(ToolCompletionMixin, AgentComponent):
    """
    Example agent component showing proper ToolCompletionMixin integration.
    
    This component demonstrates the complete workflow for tool completion
    and conversational state management.
    """
    
    def get_agent_type(self) -> str:
        """Return the agent type identifier."""
        return "example"
    
    def get_tool_indicators(self) -> List[str]:
        """Return list of context keys that indicate tool-triggered requests."""
        return ["example_form_data", "example_task", "example_request"]
    
    def get_conversational_flags(self) -> List[str]:
        """Return list of context keys that indicate conversational mode."""
        return [
            "skip_example_execution",
            "is_conversational",
            "example_completed",
            "tool_completed",
            "auto_conversational_mode"
        ]
    
    def _get_agent_specific_new_request_patterns(self) -> List[str]:
        """Return agent-specific patterns that indicate new tool requests."""
        return [
            "create example", "generate example", "example task",
            "run example", "execute example", "process example"
        ]
    
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the component."""
        logger.info("Initializing ExampleAgentComponent with ToolCompletionMixin")
    
    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using the ToolCompletionMixin workflow.
        
        This method demonstrates the complete tool completion workflow:
        1. Analyze request intelligently
        2. Handle tool execution or conversational response
        3. Reset context for future conversational mode
        """
        logger.info("=== EXAMPLE AGENT COMPONENT PROCESSING START ===")
        
        # Convert context to dict for mixin compatibility
        ctx_dict = {
            "message": context.message,
            "user_id": context.user_id,
            "conversation_id": context.conversation_id,
            "metadata": context.metadata,
            "conversation_history": getattr(context, 'conversation_history', []),
            **context.component_data
        }
        
        # Step 1: Analyze request intelligently using the mixin
        analysis = self.analyze_request_intelligently(ctx_dict)
        
        logger.info(f"Request analysis result: {analysis}")
        
        # Step 2: Handle based on analysis
        if analysis["should_use_tools"]:
            # Execute tools
            logger.info("Executing tools based on analysis")
            result = await self._execute_example_tools(ctx_dict)
            
            # Step 3: Reset context for conversational mode after tool execution
            self.reset_context_for_conversational_mode(ctx_dict)
            self.add_conversational_state_metadata(ctx_dict, result)
            
            # Update context with results
            context.response = result.get("content", "Tool execution completed")
            context.metadata.update(ctx_dict.get("metadata", {}))
            
        elif analysis["should_be_conversational"]:
            # Handle conversationally
            logger.info("Handling request conversationally")
            conv_context = self.prepare_conversational_context(ctx_dict)
            result = await self._handle_conversational_response(conv_context)
            
            # Update context with conversational response
            context.response = result.get("content", "Conversational response")
            context.metadata.update(conv_context.get("metadata", {}))
        
        else:
            # Default handling
            logger.info("Using default handling")
            context.response = "I'm not sure how to handle this request."
        
        logger.info("=== EXAMPLE AGENT COMPONENT PROCESSING END ===")
        return context
    
    async def _execute_example_tools(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute example tools based on the context.
        
        This method simulates tool execution and returns results.
        In a real implementation, this would call actual tools.
        """
        logger.info("Executing example tools")
        
        # Simulate tool execution
        tool_result = {
            "content": "Example tool execution completed successfully!",
            "tool_type": "example_generation",
            "status": "completed",
            "metadata": {
                "execution_time": "2.5s",
                "tool_version": "1.0.0"
            }
        }
        
        logger.info(f"Tool execution result: {tool_result}")
        return tool_result
    
    async def _handle_conversational_response(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle conversational responses after tool completion.
        
        This method generates natural conversational responses for follow-up questions.
        """
        logger.info("Generating conversational response")
        
        message = context.get("message", "")
        
        # Simulate conversational response generation
        conversational_responses = {
            "explain": "Let me explain the results in more detail...",
            "more": "Here's additional information about the results...",
            "how": "The process works by analyzing your request and...",
            "why": "This approach was chosen because...",
            "what": "The results show that..."
        }
        
        # Simple keyword matching for demonstration
        response_content = "I'd be happy to help you understand the results better."
        for keyword, response in conversational_responses.items():
            if keyword in message.lower():
                response_content = response
                break
        
        result = {
            "content": response_content,
            "type": "conversational",
            "metadata": {
                "response_type": "follow_up",
                "original_tool_completed": True
            }
        }
        
        logger.info(f"Conversational response: {result}")
        return result


class ExampleWorkflowDemonstration:
    """
    Demonstration class showing complete tool completion workflows.
    """
    
    def __init__(self):
        self.component = ExampleAgentComponent()
    
    async def demonstrate_tool_to_conversational_workflow(self):
        """
        Demonstrate the complete workflow from tool execution to conversational mode.
        """
        print("=== TOOL COMPLETION WORKFLOW DEMONSTRATION ===\n")
        
        # Step 1: Initial tool request
        print("1. Initial tool request:")
        initial_context = {
            "message": "Create an example",
            "example_form_data": {"task": "generation"},
            "user_id": 1,
            "conversation_id": "demo-123",
            "metadata": {}
        }
        
        analysis = await self.component.analyze_request_intelligently(initial_context)
        print(f"   Analysis: {analysis['reasoning']}")
        print(f"   Should use tools: {analysis['should_use_tools']}")
        print()
        
        # Step 2: Tool execution and context reset
        print("2. Tool execution and context reset:")
        if analysis["should_use_tools"]:
            result = await self.component._execute_example_tools(initial_context)
            print(f"   Tool result: {result['content']}")
            
            # Reset context for conversational mode
            self.component.reset_context_for_conversational_mode(initial_context)
            self.component.add_conversational_state_metadata(initial_context, result)
            print("   Context reset for conversational mode")
        print()
        
        # Step 3: Follow-up conversational request
        print("3. Follow-up conversational request:")
        follow_up_context = {
            "message": "Can you explain how this works?",
            "metadata": initial_context["metadata"],  # Preserved metadata with conversational state
            "conversation_history": [
                {"role": "assistant", "content": result["content"], "metadata": initial_context["metadata"]}
            ]
        }
        
        follow_up_analysis = await self.component.analyze_request_intelligently(follow_up_context)
        print(f"   Analysis: {follow_up_analysis['reasoning']}")
        print(f"   Should be conversational: {follow_up_analysis['should_be_conversational']}")
        
        if follow_up_analysis["should_be_conversational"]:
            conv_context = self.component.prepare_conversational_context(follow_up_context)
            conv_result = await self.component._handle_conversational_response(conv_context)
            print(f"   Conversational response: {conv_result['content']}")
        print()
        
        # Step 4: New tool request (should override conversational mode)
        print("4. New tool request (overrides conversational mode):")
        new_tool_context = {
            "message": "Create another example",
            "example_task": {"new_task": "different_generation"},
            "metadata": follow_up_context["metadata"]  # Still has conversational state
        }
        
        new_analysis = await self.component.analyze_request_intelligently(new_tool_context)
        print(f"   Analysis: {new_analysis['reasoning']}")
        print(f"   Should use tools: {new_analysis['should_use_tools']} (overrides conversational mode)")
        print()
        
        print("=== WORKFLOW DEMONSTRATION COMPLETE ===")


# Example usage
async def main():
    """Run the tool completion workflow demonstration."""
    demo = ExampleWorkflowDemonstration()
    await demo.demonstrate_tool_to_conversational_workflow()


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
