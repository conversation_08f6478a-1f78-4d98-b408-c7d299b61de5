# Dynamic Agent Registry Configuration
# This file enables automatic agent discovery and loading

# Dynamic Discovery Configuration
discovery:
  enabled: true
  auto_discover: true

  # Directories to scan for agents
  scan_directories:
    - "agents.langgraph.agents"

  # Discovery method: automatic detection based on class inheritance and duck typing
  discovery_method: "automatic"  # Detects classes that inherit from base agent classes or implement agent interface

  # Only exclude specific base classes and test classes
  exclude_classes:
    - "BaseAgent"
    - "UserSelectedAgent"
    - "AbstractAgent"
    - "UnifiedConversationAgent"  # Base class, not a concrete agent

  # Exclude files/modules
  exclude_modules:
    - "*test*"
    - "*mock*"
    - "*base*"
    - "__pycache__"

# Core agents that should always be available
core_agents:
  concierge:
    agent_type: "concierge"
    name: "Datagenius Concierge"
    description: "Your intelligent guide to Datagenius AI personas and platform capabilities"
    agent_class: "agents.langgraph.agents.concierge_persona.ConciergePersona"
    priority: 1
    always_load: true

# Agent selection strategy when multiple agents of same type are discovered
agent_selection:
  strategy: "automatic"  # automatic, priority, or manual

  # Automatic selection rules (applied in order)
  selection_rules:
    prefer_persona: true         # Prefer "*Persona" classes over others
    prefer_unified: true         # Prefer classes inheriting from UnifiedConversationAgent
    prefer_newer: true           # Prefer classes with more recent modification dates
    prefer_complete: true        # Prefer classes with more implemented methods

  # Fallback to manual preferences only if automatic selection fails
  manual_preferences:
    marketing: "MarketingPersona"
    analysis: "AnalysisPersona"
    classification: "ClassificationPersona"
    visualization: "VisualizationPersona"

# Agent ID generation strategy
id_generation:
  strategy: "automatic"  # automatic, class_based, or manual

  # Automatic ID generation rules
  generation_rules:
    extract_from_class_name: true    # Extract type from class name (e.g., "MarketingAgent" -> "marketing")
    use_agent_type_attribute: true   # Use agent_type attribute if available
    normalize_to_lowercase: true     # Convert to lowercase
    remove_common_suffixes: true     # Remove "Agent", "Assistant", etc.
    handle_legacy_names: true        # Map legacy names automatically

# Global configuration
config:
  # Dynamic loading settings
  dynamic_loading: true
  hot_reload: true
  config_watch: true
  fallback_to_discovery: true
  
  # Fallback agents when others fail
  fallback_agents:
    - "concierge"
  
  # Default agent configuration template
  default_agent_config:
    priority: 2
    timeout: 30
    max_retries: 3
    agent_init_config:
      # Default config passed to all agents
      timeout: 30
      max_retries: 3
  
  # Discovery settings
  discovery_settings:
    cache_discoveries: true
    cache_duration: 300  # 5 minutes
    rescan_on_failure: true
    validate_agents: true
  
  # Logging settings
  logging:
    log_discoveries: true
    log_failures: true
    log_fallbacks: true
