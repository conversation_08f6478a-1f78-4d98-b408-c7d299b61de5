#!/usr/bin/env python3
"""
Test script to verify workflow completion fixes.

This script tests the fixes for:
1. LLMRequestContext import error
2. Workflow completion flag propagation
3. Infinite loop prevention
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
backend_root = Path(__file__).parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_llm_request_context_import():
    """Test that LLMRequestContext can be imported and used."""
    try:
        from agents.components.shared.shared_llm_processor import LLMRequestContext, SharedLLMProcessor
        
        # Test creating LLMRequestContext
        context = LLMRequestContext(
            user_id="test_user",
            conversation_id="test_conv",
            agent_identity="Test Agent"
        )
        
        logger.info("✅ LLMRequestContext import and creation successful")
        
        # Test conversion to AgentContext
        agent_context = context.to_agent_context("Test prompt")
        logger.info(f"✅ AgentContext conversion successful: {agent_context.get_field('prompt')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LLMRequestContext test failed: {e}")
        return False

async def test_shared_llm_processor():
    """Test that SharedLLMProcessor has the generate_response method."""
    try:
        from agents.components.shared.shared_llm_processor import SharedLLMProcessor, LLMRequestContext
        
        # Create processor (won't actually initialize providers in test)
        config = {
            "fallback_providers": ["groq"],
            "default_models": {"groq": "llama3-70b-8192"}
        }
        processor = SharedLLMProcessor(config)
        
        # Check that generate_response method exists
        assert hasattr(processor, 'generate_response'), "generate_response method not found"
        
        logger.info("✅ SharedLLMProcessor generate_response method exists")
        return True
        
    except Exception as e:
        logger.error(f"❌ SharedLLMProcessor test failed: {e}")
        return False

async def test_workflow_completion_flags():
    """Test that workflow completion flags are properly handled."""
    try:
        from agents.langgraph.states.unified_state import create_unified_state, add_message, MessageType
        
        # Create test state
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="concierge"
        )
        
        # Add a message with completion metadata
        test_message = {
            "id": "test_msg_1",
            "content": "Hello, I'm here to help!",
            "type": MessageType.AGENT.value,
            "agent_id": "concierge",
            "metadata": {
                "workflow_complete": True,
                "next_action": "END"
            }
        }
        
        state = add_message(state, test_message, MessageType.AGENT)
        
        # Simulate agent node setting state-level flags
        state["workflow_complete"] = True
        state["next_action"] = "END"
        
        # Verify flags are set
        assert state.get("workflow_complete") == True, "workflow_complete flag not set"
        assert state.get("next_action") == "END", "next_action flag not set"
        
        # Verify message metadata
        last_message = state["messages"][-1]
        assert last_message["metadata"]["workflow_complete"] == True, "Message metadata workflow_complete not set"
        assert last_message["metadata"]["next_action"] == "END", "Message metadata next_action not set"
        
        logger.info("✅ Workflow completion flags test successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Workflow completion flags test failed: {e}")
        return False

async def test_concierge_agent_response():
    """Test that concierge agent sets completion flags correctly."""
    try:
        from agents.langgraph.agents.unified_conversation_agent import UnifiedConversationAgent
        
        # Create concierge agent
        agent = UnifiedConversationAgent("concierge")
        
        # Test process_message method
        result = await agent.process_message("Hello", {})
        
        # Verify response structure
        assert "message" in result, "Response missing message field"
        assert "metadata" in result, "Response missing metadata field"
        
        # Verify completion flags in metadata
        metadata = result["metadata"]
        assert metadata.get("workflow_complete") == True, "workflow_complete not set in response metadata"
        assert metadata.get("next_action") == "END", "next_action not set in response metadata"
        
        logger.info("✅ Concierge agent response test successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Concierge agent response test failed: {e}")
        return False

async def main():
    """Run all tests."""
    logger.info("🚀 Starting workflow completion tests...")
    
    tests = [
        ("LLMRequestContext Import", test_llm_request_context_import),
        ("SharedLLMProcessor", test_shared_llm_processor),
        ("Workflow Completion Flags", test_workflow_completion_flags),
        ("Concierge Agent Response", test_concierge_agent_response),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"🧪 Running test: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("="*50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info(f"\nTotal: {len(results)} tests")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    
    if failed == 0:
        logger.info("🎉 All tests passed! Workflow completion fixes are working.")
        return True
    else:
        logger.error(f"💥 {failed} test(s) failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
