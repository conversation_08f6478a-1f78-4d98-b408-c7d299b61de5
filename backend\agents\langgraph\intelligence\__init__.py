"""
Enhanced Cross-Agent Intelligence System for LangGraph.

This module provides comprehensive cross-agent intelligence capabilities including:
- Intelligent insight sharing with relevance scoring
- Business profile context integration  
- Agent collaboration orchestration
- Consensus building mechanisms
- Intelligence analytics and monitoring

Key Components:
- CrossAgentIntelligenceService: Main intelligence coordination service
- IntelligenceAnalytics: Analytics and monitoring system
- CollaborationType: Types of agent collaboration patterns
- InsightType: Categories of intelligence insights
"""

from .cross_agent_intelligence import (
    CrossAgentIntelligenceService,
    CollaborationType,
    InsightType,
    CollaborationRequest,
    IntelligenceInsight
)

from .analytics import (
    IntelligenceAnalytics,
    CollaborationMetrics,
    InsightSharingMetrics,
    BusinessContextMetrics
)

from .business_context_integration import (
    BusinessContextIntegrationService,
    ContextScope,
    ContextUpdateType,
    BusinessContextAccess,
    ContextUpdate
)

from .collaboration_workflows import (
    CollaborationWorkflowEngine,
    WorkflowPattern,
    HandoffTrigger,
    CollaborationStatus,
    WorkflowStep,
    HandoffCriteria,
    CollaborationWorkflow
)

__all__ = [
    # Main service classes
    "CrossAgentIntelligenceService",
    "IntelligenceAnalytics",
    "BusinessContextIntegrationService",
    "CollaborationWorkflowEngine",

    # Enums and types
    "CollaborationType",
    "InsightType",
    "ContextScope",
    "ContextUpdateType",
    "WorkflowPattern",
    "HandoffTrigger",
    "CollaborationStatus",

    # Data classes
    "CollaborationRequest",
    "IntelligenceInsight",
    "CollaborationMetrics",
    "InsightSharingMetrics",
    "BusinessContextMetrics",
    "BusinessContextAccess",
    "ContextUpdate",
    "WorkflowStep",
    "HandoffCriteria",
    "CollaborationWorkflow"
]
