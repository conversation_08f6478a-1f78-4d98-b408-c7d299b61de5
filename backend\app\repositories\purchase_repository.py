"""
Purchase Repository Implementation.

Provides specialized repository operations for Purchase entities,
replacing the purchase-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, extract

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import Purchase
from ..models.schemas import PurchaseCreate, PurchaseUpdate, PurchaseResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class PurchaseRepository(BaseRepository[Purchase]):
    """Repository for Purchase entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, Purchase)
        self.logger = logger
    
    def create_purchase(
        self,
        user_id: int,
        persona_id: str,
        amount: Decimal,
        currency: str = "USD",
        payment_method: Optional[str] = None,
        transaction_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Purchase:
        """
        Create a new purchase record.
        
        Args:
            user_id: ID of the user making the purchase
            persona_id: ID of the purchased persona
            amount: Purchase amount
            currency: Currency code (default: USD)
            payment_method: Payment method used
            transaction_id: External transaction ID
            metadata: Additional purchase metadata
            
        Returns:
            Created purchase instance
            
        Raises:
            RepositoryError: If purchase creation fails
        """
        try:
            purchase_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'persona_id': persona_id,
                'amount': amount,
                'currency': currency,
                'payment_method': payment_method,
                'transaction_id': transaction_id,
                'metadata': metadata or {},
                'status': 'completed'  # Default status
            }
            
            purchase = Purchase(**purchase_data)
            
            self.session.add(purchase)
            self.session.commit()
            self.session.refresh(purchase)
            
            self.logger.info(f"Created purchase {purchase.id} for user {user_id}: {amount} {currency}")
            return purchase
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create purchase: {e}")
            raise RepositoryError(
                f"Failed to create purchase: {str(e)}",
                entity_type="Purchase",
                operation="create"
            )
    
    def get_user_purchases(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Purchase]:
        """
        Get purchases for a user.
        
        Args:
            user_id: ID of the user
            skip: Number of records to skip
            limit: Maximum number of records to return
            status: Optional status filter
            
        Returns:
            List of purchases for the user
        """
        try:
            query = self.session.query(Purchase).filter(Purchase.user_id == user_id)
            
            if status:
                query = query.filter(Purchase.status == status)
            
            return query.order_by(Purchase.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get user purchases: {e}")
            raise RepositoryError(
                f"Failed to get user purchases: {str(e)}",
                entity_type="Purchase",
                operation="get_user_purchases"
            )
    
    def get_persona_purchases(
        self,
        persona_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Purchase]:
        """
        Get purchases for a specific persona.
        
        Args:
            persona_id: ID of the persona
            skip: Number of records to skip
            limit: Maximum number of records to return
            status: Optional status filter
            
        Returns:
            List of purchases for the persona
        """
        try:
            query = self.session.query(Purchase).filter(Purchase.persona_id == persona_id)
            
            if status:
                query = query.filter(Purchase.status == status)
            
            return query.order_by(Purchase.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get persona purchases: {e}")
            raise RepositoryError(
                f"Failed to get persona purchases: {str(e)}",
                entity_type="Purchase",
                operation="get_persona_purchases"
            )
    
    def update_purchase_status(
        self,
        purchase_id: str,
        status: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Purchase]:
        """
        Update a purchase status.
        
        Args:
            purchase_id: ID of the purchase to update
            status: New status
            metadata: Optional additional metadata
            
        Returns:
            Updated purchase or None if not found
        """
        try:
            purchase = self.get_by_id(purchase_id)
            if not purchase:
                self.logger.warning(f"Purchase {purchase_id} not found for status update")
                return None
            
            purchase.status = status
            
            if metadata:
                existing_metadata = purchase.metadata or {}
                existing_metadata.update(metadata)
                purchase.metadata = existing_metadata
            
            self.session.commit()
            self.session.refresh(purchase)
            
            self.logger.info(f"Updated purchase {purchase_id} status to {status}")
            return purchase
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update purchase status: {e}")
            raise RepositoryError(
                f"Failed to update purchase status: {str(e)}",
                entity_type="Purchase",
                operation="update_status"
            )
    
    def get_revenue_by_date_range(
        self,
        start_date: date,
        end_date: date,
        currency: str = "USD",
        status: str = "completed"
    ) -> Decimal:
        """
        Calculate total revenue for a date range.
        
        Args:
            start_date: Start date for the range
            end_date: End date for the range
            currency: Currency to filter by
            status: Status to filter by
            
        Returns:
            Total revenue for the date range
        """
        try:
            result = self.session.query(func.sum(Purchase.amount)).filter(
                and_(
                    Purchase.created_at >= start_date,
                    Purchase.created_at <= end_date,
                    Purchase.currency == currency,
                    Purchase.status == status
                )
            ).scalar()
            
            return result or Decimal('0.00')
            
        except Exception as e:
            self.logger.error(f"Failed to get revenue by date range: {e}")
            raise RepositoryError(
                f"Failed to get revenue by date range: {str(e)}",
                entity_type="Purchase",
                operation="get_revenue_by_date_range"
            )
    
    def get_monthly_revenue(
        self,
        year: int,
        month: int,
        currency: str = "USD",
        status: str = "completed"
    ) -> Decimal:
        """
        Calculate total revenue for a specific month.
        
        Args:
            year: Year
            month: Month (1-12)
            currency: Currency to filter by
            status: Status to filter by
            
        Returns:
            Total revenue for the month
        """
        try:
            result = self.session.query(func.sum(Purchase.amount)).filter(
                and_(
                    extract('year', Purchase.created_at) == year,
                    extract('month', Purchase.created_at) == month,
                    Purchase.currency == currency,
                    Purchase.status == status
                )
            ).scalar()
            
            return result or Decimal('0.00')
            
        except Exception as e:
            self.logger.error(f"Failed to get monthly revenue: {e}")
            raise RepositoryError(
                f"Failed to get monthly revenue: {str(e)}",
                entity_type="Purchase",
                operation="get_monthly_revenue"
            )
    
    def get_persona_revenue(
        self,
        persona_id: str,
        currency: str = "USD",
        status: str = "completed"
    ) -> Decimal:
        """
        Calculate total revenue for a specific persona.
        
        Args:
            persona_id: ID of the persona
            currency: Currency to filter by
            status: Status to filter by
            
        Returns:
            Total revenue for the persona
        """
        try:
            result = self.session.query(func.sum(Purchase.amount)).filter(
                and_(
                    Purchase.persona_id == persona_id,
                    Purchase.currency == currency,
                    Purchase.status == status
                )
            ).scalar()
            
            return result or Decimal('0.00')
            
        except Exception as e:
            self.logger.error(f"Failed to get persona revenue: {e}")
            raise RepositoryError(
                f"Failed to get persona revenue: {str(e)}",
                entity_type="Purchase",
                operation="get_persona_revenue"
            )
    
    def get_purchase_count_by_user(self, user_id: int) -> int:
        """
        Get the count of purchases for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Number of purchases by the user
        """
        try:
            return self.session.query(Purchase).filter(
                Purchase.user_id == user_id
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get purchase count by user: {e}")
            raise RepositoryError(
                f"Failed to get purchase count by user: {str(e)}",
                entity_type="Purchase",
                operation="get_purchase_count_by_user"
            )
    
    def get_purchase_count_by_persona(self, persona_id: str) -> int:
        """
        Get the count of purchases for a persona.
        
        Args:
            persona_id: ID of the persona
            
        Returns:
            Number of purchases for the persona
        """
        try:
            return self.session.query(Purchase).filter(
                Purchase.persona_id == persona_id
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get purchase count by persona: {e}")
            raise RepositoryError(
                f"Failed to get purchase count by persona: {str(e)}",
                entity_type="Purchase",
                operation="get_purchase_count_by_persona"
            )
    
    def get_top_selling_personas(
        self,
        limit: int = 10,
        currency: str = "USD",
        status: str = "completed"
    ) -> List[Dict[str, Any]]:
        """
        Get top-selling personas by revenue.

        Args:
            limit: Maximum number of personas to return
            currency: Currency to filter by
            status: Status to filter by

        Returns:
            List of dictionaries with persona_id and total_revenue
        """
        try:
            result = self.session.query(
                Purchase.persona_id,
                func.sum(Purchase.amount).label('total_revenue'),
                func.count(Purchase.id).label('purchase_count')
            ).filter(
                and_(
                    Purchase.currency == currency,
                    Purchase.status == status
                )
            ).group_by(Purchase.persona_id).order_by(
                func.sum(Purchase.amount).desc()
            ).limit(limit).all()

            return [
                {
                    'persona_id': row.persona_id,
                    'total_revenue': row.total_revenue,
                    'purchase_count': row.purchase_count
                }
                for row in result
            ]

        except Exception as e:
            self.logger.error(f"Failed to get top selling personas: {e}")
            raise RepositoryError(
                f"Failed to get top selling personas: {str(e)}",
                entity_type="Purchase",
                operation="get_top_selling_personas"
            )

    def get_total_purchase_count(self, status: str = "completed") -> int:
        """
        Get the total count of all purchases.

        Args:
            status: Status to filter by (default: completed)

        Returns:
            Total number of purchases
        """
        try:
            query = self.session.query(Purchase)
            if status:
                query = query.filter(Purchase.status == status)
            return query.count()

        except Exception as e:
            self.logger.error(f"Failed to get total purchase count: {e}")
            raise RepositoryError(
                f"Failed to get total purchase count: {str(e)}",
                entity_type="Purchase",
                operation="get_total_purchase_count"
            )

    def get_total_revenue(
        self,
        currency: str = "USD",
        status: str = "completed"
    ) -> Decimal:
        """
        Calculate total revenue across all purchases.

        Args:
            currency: Currency to filter by
            status: Status to filter by

        Returns:
            Total revenue
        """
        try:
            result = self.session.query(func.sum(Purchase.amount)).filter(
                and_(
                    Purchase.currency == currency,
                    Purchase.status == status
                )
            ).scalar()

            return result or Decimal('0.00')

        except Exception as e:
            self.logger.error(f"Failed to get total revenue: {e}")
            raise RepositoryError(
                f"Failed to get total revenue: {str(e)}",
                entity_type="Purchase",
                operation="get_total_revenue"
            )

    def get_recent_purchases_count(
        self,
        since_date: datetime,
        status: str = "completed"
    ) -> int:
        """
        Get the count of recent purchases since a specific date.

        Args:
            since_date: Date to count purchases from
            status: Status to filter by

        Returns:
            Number of recent purchases
        """
        try:
            return self.session.query(Purchase).filter(
                and_(
                    Purchase.created_at >= since_date,
                    Purchase.status == status
                )
            ).count()

        except Exception as e:
            self.logger.error(f"Failed to get recent purchases count: {e}")
            raise RepositoryError(
                f"Failed to get recent purchases count: {str(e)}",
                entity_type="Purchase",
                operation="get_recent_purchases_count"
            )

    def get_recent_revenue(
        self,
        since_date: datetime,
        currency: str = "USD",
        status: str = "completed"
    ) -> Decimal:
        """
        Calculate recent revenue since a specific date.

        Args:
            since_date: Date to calculate revenue from
            currency: Currency to filter by
            status: Status to filter by

        Returns:
            Recent revenue
        """
        try:
            result = self.session.query(func.sum(Purchase.amount)).filter(
                and_(
                    Purchase.created_at >= since_date,
                    Purchase.currency == currency,
                    Purchase.status == status
                )
            ).scalar()

            return result or Decimal('0.00')

        except Exception as e:
            self.logger.error(f"Failed to get recent revenue: {e}")
            raise RepositoryError(
                f"Failed to get recent revenue: {str(e)}",
                entity_type="Purchase",
                operation="get_recent_revenue"
            )
