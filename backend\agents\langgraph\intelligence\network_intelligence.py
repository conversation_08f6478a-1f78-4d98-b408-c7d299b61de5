"""
Network Intelligence System for LangGraph Network Architecture.

This module provides enhanced cross-agent intelligence capabilities that enable
network-wide insight sharing, collective learning, and distributed decision making.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json
from collections import defaultdict, deque

from ..states.network_state import NetworkDatageniusState, NetworkCommunicationType
from ..core.agent_registry import network_registry, NetworkAgent
from ..communication.messaging_system import messaging_system

logger = logging.getLogger(__name__)


class InsightType(str, Enum):
    """Types of insights that can be shared across the network."""
    PATTERN_RECOGNITION = "pattern_recognition"
    PERFORMANCE_METRIC = "performance_metric"
    USER_PREFERENCE = "user_preference"
    BUSINESS_INSIGHT = "business_insight"
    TECHNICAL_SOLUTION = "technical_solution"
    WORKFLOW_OPTIMIZATION = "workflow_optimization"
    ERROR_PATTERN = "error_pattern"
    SUCCESS_PATTERN = "success_pattern"
    COLLABORATION_PATTERN = "collaboration_pattern"


class InsightPriority(str, Enum):
    """Priority levels for insights."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class NetworkInsight:
    """Represents an insight in the network intelligence system."""
    insight_id: str
    insight_type: InsightType
    source_agent: str
    title: str
    description: str
    content: Dict[str, Any]
    priority: InsightPriority = InsightPriority.MEDIUM
    relevance_score: float = 0.5
    confidence_score: float = 0.5
    applicable_agents: List[str] = field(default_factory=list)
    applicable_capabilities: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    usage_count: int = 0
    feedback_scores: List[float] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def get_average_feedback(self) -> float:
        """Get average feedback score."""
        return sum(self.feedback_scores) / len(self.feedback_scores) if self.feedback_scores else 0.0

    def is_expired(self) -> bool:
        """Check if insight has expired."""
        return self.expires_at is not None and datetime.now() > self.expires_at

    def is_relevant_for_agent(self, agent_id: str, agent_capabilities: List[str]) -> bool:
        """Check if insight is relevant for a specific agent."""
        # Check direct agent applicability
        if self.applicable_agents and agent_id not in self.applicable_agents:
            return False
        
        # Check capability overlap
        if self.applicable_capabilities:
            capability_overlap = set(agent_capabilities).intersection(set(self.applicable_capabilities))
            if not capability_overlap:
                return False
        
        return True


class NetworkIntelligenceSystem:
    """
    Network-wide intelligence system for cross-agent learning and insight sharing.
    
    This system provides:
    - Distributed insight collection and sharing
    - Pattern recognition across agent interactions
    - Collective learning mechanisms
    - Performance optimization recommendations
    - Dynamic capability enhancement
    """

    def __init__(self):
        """Initialize the network intelligence system."""
        # Insight storage
        self.insights: Dict[str, NetworkInsight] = {}
        self.insight_index: Dict[InsightType, Set[str]] = defaultdict(set)
        self.agent_insights: Dict[str, Set[str]] = defaultdict(set)
        self.capability_insights: Dict[str, Set[str]] = defaultdict(set)
        
        # Learning data
        self.interaction_patterns: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.performance_trends: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.collaboration_effectiveness: Dict[Tuple[str, str], float] = {}
        self.capability_usage_stats: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        
        # Network learning state
        self.learning_models: Dict[str, Any] = {}
        self.pattern_detectors: Dict[str, Callable] = {}
        self.recommendation_engines: Dict[str, Callable] = {}
        
        # Configuration
        self.max_insights_per_agent = 1000
        self.insight_retention_period = timedelta(days=30)
        self.min_confidence_threshold = 0.3
        self.pattern_detection_window = timedelta(hours=24)
        
        # Initialize pattern detectors
        self._initialize_pattern_detectors()
        
        # Background tasks
        self._analysis_task = None
        self._cleanup_task = None
        self._start_background_tasks()
        
        logger.info("NetworkIntelligenceSystem initialized")

    def _initialize_pattern_detectors(self):
        """Initialize pattern detection algorithms."""
        self.pattern_detectors = {
            "collaboration_patterns": self._detect_collaboration_patterns,
            "performance_patterns": self._detect_performance_patterns,
            "error_patterns": self._detect_error_patterns,
            "success_patterns": self._detect_success_patterns,
            "workflow_patterns": self._detect_workflow_patterns
        }

    def _start_background_tasks(self):
        """Start background analysis and cleanup tasks."""
        self._analysis_task = asyncio.create_task(self._continuous_analysis_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())

    async def _continuous_analysis_loop(self):
        """Continuous analysis of network patterns and insights."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._analyze_network_patterns()
                await self._generate_recommendations()
                await self._update_learning_models()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in continuous analysis loop: {e}")

    async def _cleanup_loop(self):
        """Background cleanup of expired insights and old data."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await self._cleanup_expired_insights()
                await self._cleanup_old_patterns()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")

    async def add_insight(
        self,
        source_agent: str,
        insight_type: InsightType,
        title: str,
        description: str,
        content: Dict[str, Any],
        priority: InsightPriority = InsightPriority.MEDIUM,
        relevance_score: float = 0.5,
        confidence_score: float = 0.5,
        applicable_agents: Optional[List[str]] = None,
        applicable_capabilities: Optional[List[str]] = None,
        tags: Optional[List[str]] = None,
        expires_in: Optional[timedelta] = None
    ) -> str:
        """
        Add a new insight to the network intelligence system.
        
        Args:
            source_agent: Agent that generated the insight
            insight_type: Type of insight
            title: Insight title
            description: Insight description
            content: Insight content data
            priority: Priority level
            relevance_score: Relevance score (0.0-1.0)
            confidence_score: Confidence score (0.0-1.0)
            applicable_agents: List of agents this insight applies to
            applicable_capabilities: List of capabilities this insight applies to
            tags: Tags for categorization
            expires_in: When the insight expires
            
        Returns:
            Insight ID
        """
        try:
            insight_id = str(uuid.uuid4())
            
            # Create insight
            insight = NetworkInsight(
                insight_id=insight_id,
                insight_type=insight_type,
                source_agent=source_agent,
                title=title,
                description=description,
                content=content,
                priority=priority,
                relevance_score=relevance_score,
                confidence_score=confidence_score,
                applicable_agents=applicable_agents or [],
                applicable_capabilities=applicable_capabilities or [],
                tags=tags or []
            )
            
            # Set expiration
            if expires_in:
                insight.expires_at = datetime.now() + expires_in
            
            # Store insight
            self.insights[insight_id] = insight
            
            # Update indexes
            self.insight_index[insight_type].add(insight_id)
            self.agent_insights[source_agent].add(insight_id)
            
            for capability in insight.applicable_capabilities:
                self.capability_insights[capability].add(insight_id)
            
            # Broadcast insight to relevant agents
            await self._broadcast_insight(insight)
            
            logger.info(f"Added insight {insight_id} from {source_agent}")
            return insight_id
            
        except Exception as e:
            logger.error(f"Error adding insight: {e}")
            raise

    async def _broadcast_insight(self, insight: NetworkInsight):
        """Broadcast insight to relevant agents in the network."""
        try:
            # Determine target agents
            target_agents = set()
            
            # Add explicitly applicable agents
            target_agents.update(insight.applicable_agents)
            
            # Add agents with relevant capabilities
            if insight.applicable_capabilities:
                for agent in network_registry.get_all_agents():
                    agent_caps = [cap.name for cap in agent.capabilities]
                    if insight.is_relevant_for_agent(agent.agent_id, agent_caps):
                        target_agents.add(agent.agent_id)
            
            # Remove source agent
            target_agents.discard(insight.source_agent)
            
            # Send insight sharing messages
            for target_agent in target_agents:
                await messaging_system.send_message(
                    sender_agent=insight.source_agent,
                    recipient_agent=target_agent,
                    message_type=NetworkCommunicationType.INSIGHT_SHARE,
                    content={
                        "insight_id": insight.insight_id,
                        "insight_type": insight.insight_type.value,
                        "title": insight.title,
                        "description": insight.description,
                        "content": insight.content,
                        "relevance_score": insight.relevance_score,
                        "confidence_score": insight.confidence_score,
                        "tags": insight.tags
                    },
                    priority=insight.priority.value
                )
            
            logger.debug(f"Broadcasted insight {insight.insight_id} to {len(target_agents)} agents")
            
        except Exception as e:
            logger.error(f"Error broadcasting insight {insight.insight_id}: {e}")

    async def get_insights_for_agent(
        self,
        agent_id: str,
        insight_types: Optional[List[InsightType]] = None,
        min_relevance: float = 0.3,
        limit: int = 20
    ) -> List[NetworkInsight]:
        """
        Get relevant insights for a specific agent.
        
        Args:
            agent_id: Agent ID
            insight_types: Filter by insight types
            min_relevance: Minimum relevance score
            limit: Maximum number of insights to return
            
        Returns:
            List of relevant insights
        """
        try:
            agent = network_registry.get_agent(agent_id)
            if not agent:
                return []
            
            agent_capabilities = [cap.name for cap in agent.capabilities]
            relevant_insights = []
            
            # Filter insights
            for insight in self.insights.values():
                if insight.is_expired():
                    continue
                
                if insight_types and insight.insight_type not in insight_types:
                    continue
                
                if insight.relevance_score < min_relevance:
                    continue
                
                if insight.is_relevant_for_agent(agent_id, agent_capabilities):
                    relevant_insights.append(insight)
            
            # Sort by relevance and confidence
            relevant_insights.sort(
                key=lambda i: (i.relevance_score * i.confidence_score, i.created_at),
                reverse=True
            )
            
            return relevant_insights[:limit]
            
        except Exception as e:
            logger.error(f"Error getting insights for agent {agent_id}: {e}")
            return []

    async def record_interaction_pattern(
        self,
        agent1_id: str,
        agent2_id: str,
        interaction_type: str,
        outcome: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record an interaction pattern between agents."""
        try:
            pattern_key = f"{agent1_id}:{agent2_id}:{interaction_type}"
            
            if pattern_key not in self.interaction_patterns:
                self.interaction_patterns[pattern_key] = {
                    "agent1": agent1_id,
                    "agent2": agent2_id,
                    "interaction_type": interaction_type,
                    "outcomes": defaultdict(int),
                    "total_interactions": 0,
                    "success_rate": 0.0,
                    "last_interaction": None,
                    "metadata": {}
                }
            
            pattern = self.interaction_patterns[pattern_key]
            pattern["outcomes"][outcome] += 1
            pattern["total_interactions"] += 1
            pattern["last_interaction"] = datetime.now().isoformat()
            
            # Calculate success rate
            successful_outcomes = pattern["outcomes"].get("success", 0)
            pattern["success_rate"] = successful_outcomes / pattern["total_interactions"]
            
            # Update collaboration effectiveness
            if outcome == "success":
                current_effectiveness = self.collaboration_effectiveness.get((agent1_id, agent2_id), 0.5)
                # Exponential moving average
                self.collaboration_effectiveness[(agent1_id, agent2_id)] = (
                    0.8 * current_effectiveness + 0.2 * 1.0
                )
            elif outcome == "failure":
                current_effectiveness = self.collaboration_effectiveness.get((agent1_id, agent2_id), 0.5)
                self.collaboration_effectiveness[(agent1_id, agent2_id)] = (
                    0.8 * current_effectiveness + 0.2 * 0.0
                )
            
            if metadata:
                pattern["metadata"].update(metadata)
            
        except Exception as e:
            logger.error(f"Error recording interaction pattern: {e}")

    async def record_performance_metric(
        self,
        agent_id: str,
        metric_name: str,
        metric_value: float,
        context: Optional[Dict[str, Any]] = None
    ):
        """Record a performance metric for an agent."""
        try:
            metric_key = f"{agent_id}:{metric_name}"
            
            performance_data = {
                "agent_id": agent_id,
                "metric_name": metric_name,
                "value": metric_value,
                "timestamp": datetime.now().isoformat(),
                "context": context or {}
            }
            
            self.performance_trends[metric_key].append(performance_data)
            
            # Update agent performance in registry
            agent = network_registry.get_agent(agent_id)
            if agent:
                agent.update_performance_metric(metric_name, metric_value)
            
        except Exception as e:
            logger.error(f"Error recording performance metric: {e}")

    async def _analyze_network_patterns(self):
        """Analyze network-wide patterns and generate insights."""
        try:
            for pattern_name, detector_func in self.pattern_detectors.items():
                patterns = await detector_func()
                
                for pattern in patterns:
                    # Generate insight from pattern
                    await self._generate_pattern_insight(pattern_name, pattern)
            
        except Exception as e:
            logger.error(f"Error analyzing network patterns: {e}")

    async def _detect_collaboration_patterns(self) -> List[Dict[str, Any]]:
        """Detect collaboration patterns in the network."""
        patterns = []
        
        # Analyze collaboration effectiveness
        for (agent1, agent2), effectiveness in self.collaboration_effectiveness.items():
            if effectiveness > 0.8:  # High effectiveness threshold
                patterns.append({
                    "type": "high_collaboration_effectiveness",
                    "agents": [agent1, agent2],
                    "effectiveness": effectiveness,
                    "recommendation": f"Agents {agent1} and {agent2} work well together"
                })
        
        return patterns

    async def _detect_performance_patterns(self) -> List[Dict[str, Any]]:
        """Detect performance patterns across agents."""
        patterns = []
        
        # Analyze performance trends
        for metric_key, trend_data in self.performance_trends.items():
            if len(trend_data) >= 5:  # Need sufficient data
                recent_values = [d["value"] for d in list(trend_data)[-5:]]
                avg_recent = sum(recent_values) / len(recent_values)
                
                if avg_recent > 0.8:  # High performance threshold
                    agent_id = metric_key.split(":")[0]
                    metric_name = metric_key.split(":")[1]
                    
                    patterns.append({
                        "type": "high_performance_trend",
                        "agent": agent_id,
                        "metric": metric_name,
                        "average": avg_recent,
                        "recommendation": f"Agent {agent_id} shows excellent {metric_name} performance"
                    })
        
        return patterns

    async def _detect_error_patterns(self) -> List[Dict[str, Any]]:
        """Detect error patterns in agent interactions."""
        patterns = []
        
        # Analyze interaction patterns for failures
        for pattern_key, pattern_data in self.interaction_patterns.items():
            failure_rate = pattern_data["outcomes"].get("failure", 0) / pattern_data["total_interactions"]
            
            if failure_rate > 0.3:  # High failure rate threshold
                patterns.append({
                    "type": "high_failure_rate",
                    "pattern_key": pattern_key,
                    "failure_rate": failure_rate,
                    "total_interactions": pattern_data["total_interactions"],
                    "recommendation": f"Review interaction pattern: {pattern_key}"
                })
        
        return patterns

    async def _detect_success_patterns(self) -> List[Dict[str, Any]]:
        """Detect success patterns in agent interactions."""
        patterns = []
        
        # Analyze interaction patterns for successes
        for pattern_key, pattern_data in self.interaction_patterns.items():
            success_rate = pattern_data["success_rate"]
            
            if success_rate > 0.8 and pattern_data["total_interactions"] >= 5:
                patterns.append({
                    "type": "high_success_rate",
                    "pattern_key": pattern_key,
                    "success_rate": success_rate,
                    "total_interactions": pattern_data["total_interactions"],
                    "recommendation": f"Replicate successful pattern: {pattern_key}"
                })
        
        return patterns

    async def _detect_workflow_patterns(self) -> List[Dict[str, Any]]:
        """Detect workflow optimization patterns."""
        patterns = []
        
        # This would analyze workflow execution patterns
        # Implementation depends on workflow data structure
        
        return patterns

    async def _generate_pattern_insight(self, pattern_name: str, pattern_data: Dict[str, Any]):
        """Generate an insight from a detected pattern."""
        try:
            insight_type = InsightType.PATTERN_RECOGNITION
            
            if "collaboration" in pattern_name:
                insight_type = InsightType.COLLABORATION_PATTERN
            elif "performance" in pattern_name:
                insight_type = InsightType.PERFORMANCE_METRIC
            elif "workflow" in pattern_name:
                insight_type = InsightType.WORKFLOW_OPTIMIZATION
            
            await self.add_insight(
                source_agent="network_intelligence_system",
                insight_type=insight_type,
                title=f"Pattern Detected: {pattern_name}",
                description=pattern_data.get("recommendation", "Pattern detected in network"),
                content=pattern_data,
                priority=InsightPriority.MEDIUM,
                relevance_score=0.7,
                confidence_score=0.8,
                tags=[pattern_name, "auto_generated"]
            )
            
        except Exception as e:
            logger.error(f"Error generating pattern insight: {e}")

    async def _generate_recommendations(self):
        """Generate recommendations based on network intelligence."""
        # Implementation for generating actionable recommendations
        pass

    async def _update_learning_models(self):
        """Update machine learning models with new data."""
        # Implementation for updating learning models
        pass

    async def _cleanup_expired_insights(self):
        """Clean up expired insights."""
        expired_insights = []
        
        for insight_id, insight in self.insights.items():
            if insight.is_expired():
                expired_insights.append(insight_id)
        
        for insight_id in expired_insights:
            await self._remove_insight(insight_id)
        
        if expired_insights:
            logger.info(f"Cleaned up {len(expired_insights)} expired insights")

    async def _cleanup_old_patterns(self):
        """Clean up old interaction patterns."""
        cutoff_time = datetime.now() - self.pattern_detection_window
        patterns_to_remove = []
        
        for pattern_key, pattern_data in self.interaction_patterns.items():
            last_interaction = pattern_data.get("last_interaction")
            if last_interaction:
                last_time = datetime.fromisoformat(last_interaction)
                if last_time < cutoff_time:
                    patterns_to_remove.append(pattern_key)
        
        for pattern_key in patterns_to_remove:
            del self.interaction_patterns[pattern_key]

    async def _remove_insight(self, insight_id: str):
        """Remove an insight from all storage locations."""
        insight = self.insights.pop(insight_id, None)
        if not insight:
            return
        
        # Remove from indexes
        self.insight_index[insight.insight_type].discard(insight_id)
        self.agent_insights[insight.source_agent].discard(insight_id)
        
        for capability in insight.applicable_capabilities:
            self.capability_insights[capability].discard(insight_id)

    def get_intelligence_stats(self) -> Dict[str, Any]:
        """Get network intelligence system statistics."""
        total_insights = len(self.insights)
        insights_by_type = {}
        
        for insight_type, insight_ids in self.insight_index.items():
            insights_by_type[insight_type.value] = len(insight_ids)
        
        return {
            "total_insights": total_insights,
            "insights_by_type": insights_by_type,
            "interaction_patterns": len(self.interaction_patterns),
            "collaboration_pairs": len(self.collaboration_effectiveness),
            "performance_metrics": len(self.performance_trends),
            "active_learning_models": len(self.learning_models)
        }

    async def shutdown(self):
        """Shutdown the network intelligence system."""
        if self._analysis_task:
            self._analysis_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        logger.info("NetworkIntelligenceSystem shutdown")


# Global network intelligence system instance
network_intelligence = NetworkIntelligenceSystem()
