"""
Persona Version Repository Implementation.

Provides specialized repository operations for PersonaVersion entities,
replacing the persona version-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import PersonaVersion
from ..models.schemas import PersonaVersionCreate, PersonaVersionUpdate, PersonaVersionResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class PersonaVersionRepository(BaseRepository[PersonaVersion]):
    """Repository for PersonaVersion entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, PersonaVersion)
        self.logger = logger
    
    def create_persona_version(self, version_data: Dict[str, Any]) -> PersonaVersion:
        """
        Create a new persona version.
        
        Args:
            version_data: Dictionary containing version data
            
        Returns:
            Created persona version instance
            
        Raises:
            RepositoryError: If version creation fails
        """
        try:
            # Ensure ID is set
            if 'id' not in version_data:
                version_data['id'] = str(uuid.uuid4())
            
            persona_version = PersonaVersion(**version_data)
            
            self.session.add(persona_version)
            
            # If this version is active, deactivate all other versions for the same persona
            if version_data.get('is_active', False):
                self._deactivate_other_versions(version_data['persona_id'], persona_version.id)
            
            self.session.commit()
            self.session.refresh(persona_version)
            
            self.logger.info(f"Created persona version {persona_version.id} for persona {version_data['persona_id']}")
            return persona_version
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create persona version: {e}")
            raise RepositoryError(
                f"Failed to create persona version: {str(e)}",
                entity_type="PersonaVersion",
                operation="create"
            )
    
    def get_persona_versions(
        self,
        persona_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[PersonaVersion]:
        """
        Get all versions for a persona.
        
        Args:
            persona_id: ID of the persona
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of persona versions ordered by creation date
        """
        try:
            return self.session.query(PersonaVersion).filter(
                PersonaVersion.persona_id == persona_id
            ).order_by(PersonaVersion.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get persona versions: {e}")
            raise RepositoryError(
                f"Failed to get persona versions: {str(e)}",
                entity_type="PersonaVersion",
                operation="get_persona_versions"
            )
    
    def get_active_persona_version(self, persona_id: str) -> Optional[PersonaVersion]:
        """
        Get the active version for a persona.
        
        Args:
            persona_id: ID of the persona
            
        Returns:
            Active persona version or None if not found
        """
        try:
            return self.session.query(PersonaVersion).filter(
                PersonaVersion.persona_id == persona_id,
                PersonaVersion.is_active == True
            ).first()
            
        except Exception as e:
            self.logger.error(f"Failed to get active persona version: {e}")
            raise RepositoryError(
                f"Failed to get active persona version: {str(e)}",
                entity_type="PersonaVersion",
                operation="get_active_persona_version"
            )
    
    def activate_persona_version(self, version_id: str) -> Optional[PersonaVersion]:
        """
        Activate a persona version and deactivate others.
        
        Args:
            version_id: ID of the version to activate
            
        Returns:
            Activated version or None if not found
        """
        try:
            version = self.get_by_id(version_id)
            if not version:
                self.logger.warning(f"Persona version {version_id} not found for activation")
                return None
            
            # Deactivate all other versions for this persona
            self._deactivate_other_versions(version.persona_id, version_id)
            
            # Activate this version
            version.is_active = True
            
            self.session.commit()
            self.session.refresh(version)
            
            self.logger.info(f"Activated persona version {version_id}")
            return version
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to activate persona version: {e}")
            raise RepositoryError(
                f"Failed to activate persona version: {str(e)}",
                entity_type="PersonaVersion",
                operation="activate"
            )
    
    def delete_persona_version(self, version_id: str) -> bool:
        """
        Delete a persona version.
        
        Args:
            version_id: ID of the version to delete
            
        Returns:
            True if version was deleted, False if not found
            
        Raises:
            RepositoryError: If deletion fails or version is active
        """
        try:
            version = self.get_by_id(version_id)
            if not version:
                self.logger.warning(f"Persona version {version_id} not found for deletion")
                return False
            
            # Don't allow deletion of active versions
            if version.is_active:
                raise RepositoryError(
                    "Cannot delete active persona version",
                    entity_type="PersonaVersion",
                    operation="delete"
                )
            
            self.session.delete(version)
            self.session.commit()
            
            self.logger.info(f"Deleted persona version {version_id}")
            return True
            
        except RepositoryError:
            raise
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to delete persona version: {e}")
            raise RepositoryError(
                f"Failed to delete persona version: {str(e)}",
                entity_type="PersonaVersion",
                operation="delete"
            )
    
    def update_persona_version(
        self,
        version_id: str,
        version_data: Dict[str, Any]
    ) -> Optional[PersonaVersion]:
        """
        Update a persona version.
        
        Args:
            version_id: ID of the version to update
            version_data: Dictionary containing update data
            
        Returns:
            Updated version or None if not found
        """
        try:
            version = self.get_by_id(version_id)
            if not version:
                self.logger.warning(f"Persona version {version_id} not found for update")
                return None
            
            # Update allowed fields
            allowed_fields = [
                'version_number', 'system_prompt', 'welcome_message',
                'capabilities', 'tags', 'metadata', 'is_active'
            ]
            
            for key, value in version_data.items():
                if key in allowed_fields:
                    setattr(version, key, value)
            
            # If activating this version, deactivate others
            if version_data.get('is_active', False):
                self._deactivate_other_versions(version.persona_id, version_id)
            
            self.session.commit()
            self.session.refresh(version)
            
            self.logger.info(f"Updated persona version {version_id}")
            return version
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update persona version: {e}")
            raise RepositoryError(
                f"Failed to update persona version: {str(e)}",
                entity_type="PersonaVersion",
                operation="update"
            )
    
    def get_version_count_by_persona(self, persona_id: str) -> int:
        """
        Get the count of versions for a persona.
        
        Args:
            persona_id: ID of the persona
            
        Returns:
            Number of versions for the persona
        """
        try:
            return self.session.query(PersonaVersion).filter(
                PersonaVersion.persona_id == persona_id
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get version count: {e}")
            raise RepositoryError(
                f"Failed to get version count: {str(e)}",
                entity_type="PersonaVersion",
                operation="get_version_count_by_persona"
            )
    
    def get_latest_version_number(self, persona_id: str) -> int:
        """
        Get the latest version number for a persona.
        
        Args:
            persona_id: ID of the persona
            
        Returns:
            Latest version number (0 if no versions exist)
        """
        try:
            from sqlalchemy import func
            
            result = self.session.query(func.max(PersonaVersion.version_number)).filter(
                PersonaVersion.persona_id == persona_id
            ).scalar()
            
            return result or 0
            
        except Exception as e:
            self.logger.error(f"Failed to get latest version number: {e}")
            raise RepositoryError(
                f"Failed to get latest version number: {str(e)}",
                entity_type="PersonaVersion",
                operation="get_latest_version_number"
            )
    
    def create_next_version(
        self,
        persona_id: str,
        version_data: Dict[str, Any]
    ) -> PersonaVersion:
        """
        Create the next version for a persona with auto-incremented version number.
        
        Args:
            persona_id: ID of the persona
            version_data: Dictionary containing version data
            
        Returns:
            Created persona version
        """
        try:
            # Get next version number
            latest_version = self.get_latest_version_number(persona_id)
            next_version = latest_version + 1
            
            # Set version data
            version_data['persona_id'] = persona_id
            version_data['version_number'] = next_version
            
            return self.create_persona_version(version_data)
            
        except Exception as e:
            self.logger.error(f"Failed to create next version: {e}")
            raise RepositoryError(
                f"Failed to create next version: {str(e)}",
                entity_type="PersonaVersion",
                operation="create_next_version"
            )
    
    def _deactivate_other_versions(self, persona_id: str, exclude_version_id: str):
        """
        Deactivate all versions for a persona except the specified one.
        
        Args:
            persona_id: ID of the persona
            exclude_version_id: ID of the version to exclude from deactivation
        """
        try:
            self.session.query(PersonaVersion).filter(
                PersonaVersion.persona_id == persona_id,
                PersonaVersion.id != exclude_version_id
            ).update({'is_active': False})
            
            self.logger.debug(f"Deactivated other versions for persona {persona_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to deactivate other versions: {e}")
            raise
