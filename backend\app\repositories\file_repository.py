"""
File Repository Implementation.

Provides specialized repository operations for File entities,
replacing the file-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional
from sqlalchemy.orm import Session

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import File
from ..models.schemas import FileCreate, FileUpdate, FileResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class FileRepository(BaseRepository[File]):
    """Repository for File entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, File)
        self.logger = logger
    
    def create_file(
        self,
        user_id: int,
        filename: str,
        file_path: str,
        file_size: int,
        num_rows: Optional[int] = None,
        columns: Optional[List[str]] = None
    ) -> File:
        """
        Create a new file record.
        
        Args:
            user_id: ID of the user who owns the file
            filename: Name of the file
            file_path: Path where the file is stored
            file_size: Size of the file in bytes
            num_rows: Optional number of rows (for data files)
            columns: Optional list of column names (for data files)
            
        Returns:
            Created file instance
            
        Raises:
            RepositoryError: If file creation fails
        """
        try:
            file_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'filename': filename,
                'file_path': file_path,
                'file_size': file_size,
                'num_rows': num_rows,
                'columns': columns
            }
            
            file = File(**file_data)
            
            self.session.add(file)
            self.session.commit()
            self.session.refresh(file)
            
            self.logger.info(f"Created file {file.id} for user {user_id}: {filename}")
            return file
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create file: {e}")
            raise RepositoryError(
                f"Failed to create file: {str(e)}",
                entity_type="File",
                operation="create"
            )
    
    def get_user_files(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[File]:
        """
        Get all files for a user.
        
        Args:
            user_id: ID of the user
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of files owned by the user
        """
        try:
            return self.session.query(File).filter(
                File.user_id == user_id
            ).order_by(File.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get user files: {e}")
            raise RepositoryError(
                f"Failed to get user files: {str(e)}",
                entity_type="File",
                operation="get_user_files"
            )
    
    def delete_file(self, file_id: str) -> bool:
        """
        Delete a file record.
        
        Args:
            file_id: ID of the file to delete
            
        Returns:
            True if file was deleted, False if not found
            
        Raises:
            RepositoryError: If deletion fails
        """
        try:
            file = self.get_by_id(file_id)
            if not file:
                self.logger.warning(f"File {file_id} not found for deletion")
                return False
            
            self.session.delete(file)
            self.session.commit()
            
            self.logger.info(f"Deleted file {file_id}")
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to delete file {file_id}: {e}")
            raise RepositoryError(
                f"Failed to delete file: {str(e)}",
                entity_type="File",
                operation="delete"
            )
    
    def get_files_by_user_and_filename(
        self,
        user_id: int,
        filename: str
    ) -> List[File]:
        """
        Get files by user ID and filename.
        
        Args:
            user_id: ID of the user
            filename: Name of the file to search for
            
        Returns:
            List of files matching the criteria
        """
        try:
            return self.session.query(File).filter(
                File.user_id == user_id,
                File.filename == filename
            ).order_by(File.created_at.desc()).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get files by user and filename: {e}")
            raise RepositoryError(
                f"Failed to get files by user and filename: {str(e)}",
                entity_type="File",
                operation="get_files_by_user_and_filename"
            )
    
    def get_files_by_size_range(
        self,
        user_id: int,
        min_size: int,
        max_size: int
    ) -> List[File]:
        """
        Get files within a size range for a user.
        
        Args:
            user_id: ID of the user
            min_size: Minimum file size in bytes
            max_size: Maximum file size in bytes
            
        Returns:
            List of files within the size range
        """
        try:
            return self.session.query(File).filter(
                File.user_id == user_id,
                File.file_size >= min_size,
                File.file_size <= max_size
            ).order_by(File.created_at.desc()).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get files by size range: {e}")
            raise RepositoryError(
                f"Failed to get files by size range: {str(e)}",
                entity_type="File",
                operation="get_files_by_size_range"
            )
    
    def update_file_metadata(
        self,
        file_id: str,
        num_rows: Optional[int] = None,
        columns: Optional[List[str]] = None
    ) -> Optional[File]:
        """
        Update file metadata (rows and columns).
        
        Args:
            file_id: ID of the file to update
            num_rows: New number of rows
            columns: New list of columns
            
        Returns:
            Updated file or None if not found
        """
        try:
            file = self.get_by_id(file_id)
            if not file:
                self.logger.warning(f"File {file_id} not found for metadata update")
                return None
            
            if num_rows is not None:
                file.num_rows = num_rows
            
            if columns is not None:
                file.columns = columns
            
            self.session.commit()
            self.session.refresh(file)
            
            self.logger.info(f"Updated metadata for file {file_id}")
            return file
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update file metadata: {e}")
            raise RepositoryError(
                f"Failed to update file metadata: {str(e)}",
                entity_type="File",
                operation="update_metadata"
            )
    
    def get_total_storage_used(self, user_id: int) -> int:
        """
        Get total storage used by a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Total storage used in bytes
        """
        try:
            from sqlalchemy import func
            
            result = self.session.query(func.sum(File.file_size)).filter(
                File.user_id == user_id
            ).scalar()
            
            return result or 0
            
        except Exception as e:
            self.logger.error(f"Failed to get total storage used: {e}")
            raise RepositoryError(
                f"Failed to get total storage used: {str(e)}",
                entity_type="File",
                operation="get_total_storage_used"
            )
    
    def get_file_count_by_user(self, user_id: int) -> int:
        """
        Get the count of files for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Number of files owned by the user
        """
        try:
            return self.session.query(File).filter(
                File.user_id == user_id
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get file count: {e}")
            raise RepositoryError(
                f"Failed to get file count: {str(e)}",
                entity_type="File",
                operation="get_file_count_by_user"
            )
