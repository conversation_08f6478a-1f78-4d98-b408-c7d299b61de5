"""
Decision and routing nodes for LangGraph workflows.

This module provides node implementations for making decisions
and routing within LangGraph workflows.
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

from ..states.agent_state import DatageniusAgentState, add_error

logger = logging.getLogger(__name__)


class DecisionNode:
    """
    Generic decision node for LangGraph workflows.
    
    This node evaluates conditions and updates state based on decisions.
    """

    def __init__(self, decision_name: str, decision_func: Callable[[DatageniusAgentState], Dict[str, Any]]):
        """
        Initialize the decision node.
        
        Args:
            decision_name: Name identifier for the decision
            decision_func: Function that makes the decision based on state
        """
        self.decision_name = decision_name
        self.decision_func = decision_func
        self.logger = logging.getLogger(f"{__name__}.{decision_name}")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Execute decision logic and update state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with decision results
        """
        try:
            start_time = datetime.now()
            
            # Execute decision function
            decision_result = self.decision_func(state)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Store decision result
            if "decisions" not in state:
                state["decisions"] = {}
            
            state["decisions"][self.decision_name] = {
                "result": decision_result,
                "timestamp": datetime.now().isoformat(),
                "execution_time": execution_time
            }
            
            # Update execution metrics
            state["execution_metrics"][f"{self.decision_name}_decision_time"] = execution_time

            self.logger.info(f"Decision {self.decision_name} completed: {decision_result}")
            return state

        except Exception as e:
            self.logger.error(f"Decision {self.decision_name} failed: {e}")
            return add_error(state, "decision_error", str(e), {"decision_name": self.decision_name})


class RoutingDecisionNode(DecisionNode):
    """
    Specialized decision node for routing decisions.
    
    This node makes routing decisions and updates the routing analysis
    in the state.
    """

    def __init__(self, routing_strategies: Dict[str, Callable] = None):
        """
        Initialize the routing decision node.
        
        Args:
            routing_strategies: Dictionary of routing strategy functions
        """
        self.routing_strategies = routing_strategies or {}
        super().__init__("routing", self._make_routing_decision)

    def _make_routing_decision(self, state: DatageniusAgentState) -> Dict[str, Any]:
        """
        Make routing decision based on current state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Routing decision result
        """
        # Get existing routing analysis
        routing_analysis = state.get("routing_analysis", {})
        
        # Extract key information
        intent = routing_analysis.get("intent", "general_inquiry")
        complexity = routing_analysis.get("complexity", "low")
        collaboration_needed = routing_analysis.get("collaboration_needed", False)
        
        # Make routing decision
        if collaboration_needed:
            decision = {
                "route_type": "multi_agent",
                "primary_agent": self._select_primary_agent(intent),
                "supporting_agents": self._select_supporting_agents(intent, complexity),
                "workflow_type": "collaborative"
            }
        else:
            decision = {
                "route_type": "single_agent",
                "selected_agent": self._select_single_agent(intent, complexity),
                "workflow_type": "sequential"
            }
        
        # Add confidence score
        decision["confidence"] = self._calculate_confidence(state, decision)
        
        return decision

    def _select_primary_agent(self, intent: str) -> str:
        """Select primary agent for multi-agent workflows."""
        primary_mappings = {
            "data_analysis": "analysis",
            "visualization": "analysis",
            "marketing": "marketing",
            "content_creation": "marketing",
            "classification": "classification"
        }
        return primary_mappings.get(intent, "concierge")

    def _select_supporting_agents(self, intent: str, complexity: str) -> List[str]:
        """Select supporting agents for multi-agent workflows."""
        if complexity == "high":
            if intent in ["data_analysis", "visualization"]:
                return ["concierge", "classification"]
            elif intent in ["marketing", "content_creation"]:
                return ["analysis", "concierge"]
        
        return ["concierge"]

    def _select_single_agent(self, intent: str, complexity: str) -> str:
        """Select single agent for simple workflows."""
        agent_mappings = {
            "greeting": "concierge",
            "general_inquiry": "concierge",
            "persona_recommendation": "concierge",
            "data_analysis": "analysis",
            "visualization": "analysis",
            "marketing": "marketing",
            "content_creation": "marketing",
            "classification": "classification"
        }
        
        selected = agent_mappings.get(intent, "concierge")
        
        # Consider complexity for upgrades
        if complexity == "high" and selected == "analysis":
            # Could upgrade to specialized analysis agent if available
            return "advanced_analysis" if "advanced_analysis" in self.routing_strategies else selected
        
        return selected

    def _calculate_confidence(self, state: DatageniusAgentState, decision: Dict[str, Any]) -> float:
        """Calculate confidence score for the routing decision."""
        base_confidence = 0.8
        
        # Adjust based on available information
        routing_analysis = state.get("routing_analysis", {})
        
        # Higher confidence if we have clear intent
        if routing_analysis.get("intent") != "general_inquiry":
            base_confidence += 0.1
        
        # Higher confidence if we have business context
        if state.get("business_context"):
            base_confidence += 0.05
        
        # Lower confidence for multi-agent workflows (more complex)
        if decision["route_type"] == "multi_agent":
            base_confidence -= 0.1
        
        return min(1.0, base_confidence)


class QualityGateNode:
    """
    Quality gate decision node for workflow control.
    
    This node evaluates the quality of work done so far and decides
    whether to proceed, retry, or escalate.
    """

    def __init__(self, quality_thresholds: Dict[str, float] = None):
        """
        Initialize the quality gate node.
        
        Args:
            quality_thresholds: Thresholds for quality decisions
        """
        self.quality_thresholds = quality_thresholds or {
            "proceed": 0.8,
            "retry": 0.6,
            "escalate": 0.4
        }
        self.logger = logging.getLogger(f"{__name__}.quality_gate")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Evaluate quality and make gate decision.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with quality gate decision
        """
        try:
            # Calculate overall quality score
            quality_score = self._calculate_quality_score(state)
            
            # Make gate decision
            if quality_score >= self.quality_thresholds["proceed"]:
                decision = "proceed"
            elif quality_score >= self.quality_thresholds["retry"]:
                decision = "retry"
            else:
                decision = "escalate"
            
            # Store quality gate result
            quality_gate_result = {
                "decision": decision,
                "quality_score": quality_score,
                "timestamp": datetime.now().isoformat(),
                "thresholds": self.quality_thresholds
            }
            
            state["quality_gate"] = quality_gate_result
            
            self.logger.info(f"Quality gate decision: {decision} (score: {quality_score:.2f})")
            return state

        except Exception as e:
            self.logger.error(f"Quality gate evaluation failed: {e}")
            return add_error(state, "quality_gate_error", str(e))

    def _calculate_quality_score(self, state: DatageniusAgentState) -> float:
        """
        Calculate overall quality score based on various factors.
        
        Args:
            state: Current workflow state
            
        Returns:
            Quality score between 0.0 and 1.0
        """
        scores = []
        
        # Agent output quality
        agent_outputs = state.get("agent_outputs", {})
        for agent_id, output in agent_outputs.items():
            if isinstance(output, dict) and "result" in output:
                # Simple quality heuristics
                result = output["result"]
                if isinstance(result, dict):
                    content = result.get("content", "")
                    if content and len(content) > 10:
                        scores.append(0.8)  # Basic content quality
                    else:
                        scores.append(0.4)
                else:
                    scores.append(0.6)
        
        # Error penalty
        error_count = len(state.get("error_history", []))
        error_penalty = min(0.5, error_count * 0.1)
        
        # Tool execution success
        tool_results = state.get("tool_results", {})
        tool_success_rate = 0.0
        if tool_results:
            successful_tools = sum(1 for result in tool_results.values() 
                                 if isinstance(result, dict) and not result.get("error"))
            tool_success_rate = successful_tools / len(tool_results)
        
        # Calculate weighted average
        if scores:
            base_score = sum(scores) / len(scores)
        else:
            base_score = 0.5  # Neutral score if no outputs
        
        # Apply adjustments
        final_score = base_score - error_penalty + (tool_success_rate * 0.2)
        
        return max(0.0, min(1.0, final_score))


class ConditionalBranchNode:
    """
    Conditional branching node for workflow control.
    
    This node evaluates conditions and sets branch indicators
    for conditional edges in the workflow.
    """

    def __init__(self, branch_name: str, conditions: Dict[str, Callable]):
        """
        Initialize the conditional branch node.
        
        Args:
            branch_name: Name identifier for the branch
            conditions: Dictionary of condition name to condition function mappings
        """
        self.branch_name = branch_name
        self.conditions = conditions
        self.logger = logging.getLogger(f"{__name__}.branch.{branch_name}")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Evaluate conditions and set branch indicators.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with branch conditions
        """
        try:
            # Evaluate all conditions
            condition_results = {}
            for condition_name, condition_func in self.conditions.items():
                try:
                    result = condition_func(state)
                    condition_results[condition_name] = result
                except Exception as e:
                    self.logger.error(f"Condition {condition_name} evaluation failed: {e}")
                    condition_results[condition_name] = False
            
            # Store branch results
            if "branch_conditions" not in state:
                state["branch_conditions"] = {}
            
            state["branch_conditions"][self.branch_name] = {
                "conditions": condition_results,
                "timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(f"Branch {self.branch_name} conditions evaluated: {condition_results}")
            return state

        except Exception as e:
            self.logger.error(f"Branch {self.branch_name} evaluation failed: {e}")
            return add_error(state, "branch_evaluation_error", str(e), {"branch_name": self.branch_name})


# Factory functions for creating decision nodes

def create_decision_node(decision_name: str, decision_func: Callable) -> DecisionNode:
    """Create a generic decision node."""
    return DecisionNode(decision_name, decision_func)


def create_routing_decision_node(**kwargs) -> RoutingDecisionNode:
    """Create a routing decision node."""
    return RoutingDecisionNode(**kwargs)


def create_quality_gate_node(**kwargs) -> QualityGateNode:
    """Create a quality gate node."""
    return QualityGateNode(**kwargs)


def create_conditional_branch_node(branch_name: str, conditions: Dict[str, Callable]) -> ConditionalBranchNode:
    """Create a conditional branch node."""
    return ConditionalBranchNode(branch_name, conditions)


# Common condition functions for branching

def has_errors(state: DatageniusAgentState) -> bool:
    """Check if state has any errors."""
    return bool(state.get("error_history"))


def high_quality_output(state: DatageniusAgentState) -> bool:
    """Check if output quality is high."""
    quality_scores = state.get("quality_scores", {})
    if not quality_scores:
        return False
    
    avg_quality = sum(quality_scores.values()) / len(quality_scores)
    return avg_quality >= 0.8


def requires_human_review(state: DatageniusAgentState) -> bool:
    """Check if human review is required."""
    # This could be based on various factors
    error_count = len(state.get("error_history", []))
    quality_scores = state.get("quality_scores", {})
    
    if error_count > 2:
        return True
    
    if quality_scores:
        avg_quality = sum(quality_scores.values()) / len(quality_scores)
        if avg_quality < 0.6:
            return True
    
    return False
