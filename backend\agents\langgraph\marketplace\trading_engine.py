"""
Trading Engine

Engine for managing capability trading, bidding, and optimal agent selection
in the capability marketplace.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

from ..events.event_bus import event_bus, LangGraphEvent
from ..monitoring.metrics import MetricsCollector

logger = logging.getLogger(__name__)


class TradeStatus(Enum):
    """Status of a trade."""
    PENDING = "pending"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class BidStatus(Enum):
    """Status of a bid."""
    SUBMITTED = "submitted"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"


@dataclass
class Bid:
    """Represents a bid in the trading system."""
    bid_id: str
    trade_id: str
    bidder_id: str
    capability_id: str
    price: float
    execution_time_estimate: float
    quality_guarantee: float
    status: BidStatus = BidStatus.SUBMITTED
    submitted_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Trade:
    """Represents a trade in the marketplace."""
    trade_id: str
    request_id: str
    requester_id: str
    capability_requirements: Dict[str, Any]
    budget: float
    deadline: datetime
    status: TradeStatus = TradeStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    
    # Bidding information
    bids: List[Bid] = field(default_factory=list)
    winning_bid: Optional[Bid] = None
    
    # Trading parameters
    min_quality_score: float = 0.7
    max_execution_time: float = 3600.0  # 1 hour default
    trading_mode: str = "auction"  # auction, fixed_price, negotiation
    
    # Results
    selected_agent: Optional[str] = None
    final_price: Optional[float] = None
    execution_started_at: Optional[datetime] = None
    execution_completed_at: Optional[datetime] = None


class TradingEngine:
    """
    Engine for managing capability trading and optimal agent selection.
    
    Features:
    - Automated bidding and auction management
    - Multi-criteria agent selection
    - Price optimization algorithms
    - Performance-based scoring
    - Real-time trade monitoring
    """
    
    def __init__(self):
        self.trades: Dict[str, Trade] = {}
        self.bids: Dict[str, Bid] = {}
        self.metrics = MetricsCollector("trading_engine")
        
        # Trading configuration
        self.auction_duration = timedelta(minutes=30)
        self.bid_expiry_duration = timedelta(hours=1)
        self.quality_weight = 0.4
        self.price_weight = 0.3
        self.speed_weight = 0.3
        
        # Performance tracking
        self.agent_performance: Dict[str, Dict[str, float]] = {}
        self.market_prices: Dict[str, List[float]] = {}
    
    async def initialize(self):
        """Initialize the trading engine."""
        try:
            # Load existing trades and bids
            await self._load_trading_data()
            
            # Setup event handlers
            self._setup_event_handlers()
            
            # Start background tasks
            asyncio.create_task(self._trade_monitoring_loop())
            asyncio.create_task(self._bid_expiry_loop())
            asyncio.create_task(self._market_analysis_loop())
            
            logger.info("Trading engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize trading engine: {e}")
            raise
    
    def _setup_event_handlers(self):
        """Setup event handlers for trading operations."""
        event_bus.subscribe("marketplace.capability_requested", self._handle_capability_request)
        event_bus.subscribe("agent.bid_submitted", self._handle_bid_submission)
        event_bus.subscribe("workflow.execution_completed", self._handle_execution_completion)
    
    async def initiate_trade(self, request: Any, matching_capabilities: List[Any]) -> str:
        """
        Initiate a new trade for a capability request.
        
        Args:
            request: Capability request object
            matching_capabilities: List of matching capability listings
            
        Returns:
            Trade ID
        """
        try:
            # Create trade
            trade = Trade(
                trade_id=f"trade_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{request.request_id}",
                request_id=request.request_id,
                requester_id=request.requester_id,
                capability_requirements=request.requirements,
                budget=request.budget,
                deadline=request.deadline,
                trading_mode=self._determine_trading_mode(request, matching_capabilities)
            )
            
            # Store trade
            self.trades[trade.trade_id] = trade
            
            # Initiate bidding process
            await self._initiate_bidding(trade, matching_capabilities)
            
            # Publish trade initiation event
            await event_bus.publish(LangGraphEvent(
                event_type="trading.trade_initiated",
                timestamp=datetime.now(),
                source="trading_engine",
                data={
                    "trade_id": trade.trade_id,
                    "request_id": trade.request_id,
                    "requester_id": trade.requester_id,
                    "budget": trade.budget,
                    "trading_mode": trade.trading_mode,
                    "matching_capabilities": len(matching_capabilities)
                }
            ))
            
            # Update metrics
            self.metrics.increment("trades_initiated")
            
            logger.info(f"Initiated trade {trade.trade_id} with {len(matching_capabilities)} potential bidders")
            return trade.trade_id
            
        except Exception as e:
            logger.error(f"Failed to initiate trade: {e}")
            raise
    
    async def submit_bid(self, bid_data: Dict[str, Any]) -> str:
        """
        Submit a bid for a trade.
        
        Args:
            bid_data: Bid information
            
        Returns:
            Bid ID
        """
        try:
            # Validate bid
            if not self._validate_bid(bid_data):
                raise ValueError("Invalid bid data")
            
            trade_id = bid_data["trade_id"]
            if trade_id not in self.trades:
                raise ValueError(f"Trade {trade_id} not found")
            
            trade = self.trades[trade_id]
            if trade.status != TradeStatus.ACTIVE:
                raise ValueError(f"Trade {trade_id} is not active")
            
            # Create bid
            bid = Bid(
                bid_id=f"bid_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{bid_data['bidder_id']}",
                trade_id=trade_id,
                bidder_id=bid_data["bidder_id"],
                capability_id=bid_data["capability_id"],
                price=bid_data["price"],
                execution_time_estimate=bid_data.get("execution_time_estimate", 3600.0),
                quality_guarantee=bid_data.get("quality_guarantee", 0.8),
                expires_at=datetime.now() + self.bid_expiry_duration,
                metadata=bid_data.get("metadata", {})
            )
            
            # Store bid
            self.bids[bid.bid_id] = bid
            trade.bids.append(bid)
            
            # Evaluate bid immediately for certain trading modes
            if trade.trading_mode == "fixed_price":
                await self._evaluate_fixed_price_bid(trade, bid)
            
            # Publish bid submission event
            await event_bus.publish(LangGraphEvent(
                event_type="trading.bid_submitted",
                timestamp=datetime.now(),
                source="trading_engine",
                data={
                    "bid_id": bid.bid_id,
                    "trade_id": trade_id,
                    "bidder_id": bid.bidder_id,
                    "price": bid.price,
                    "quality_guarantee": bid.quality_guarantee
                }
            ))
            
            # Update metrics
            self.metrics.increment("bids_submitted")
            
            logger.info(f"Submitted bid {bid.bid_id} for trade {trade_id}")
            return bid.bid_id
            
        except Exception as e:
            logger.error(f"Failed to submit bid: {e}")
            raise
    
    async def select_optimal_agent(self, trade_id: str) -> Optional[str]:
        """
        Select the optimal agent for a trade based on multiple criteria.
        
        Args:
            trade_id: ID of the trade
            
        Returns:
            Selected agent ID or None if no suitable agent found
        """
        try:
            if trade_id not in self.trades:
                raise ValueError(f"Trade {trade_id} not found")
            
            trade = self.trades[trade_id]
            
            if not trade.bids:
                logger.warning(f"No bids available for trade {trade_id}")
                return None
            
            # Score all bids
            scored_bids = []
            for bid in trade.bids:
                if bid.status == BidStatus.SUBMITTED:
                    score = await self._calculate_bid_score(bid, trade)
                    scored_bids.append((bid, score))
            
            if not scored_bids:
                logger.warning(f"No valid bids for trade {trade_id}")
                return None
            
            # Select best bid
            scored_bids.sort(key=lambda x: x[1], reverse=True)
            winning_bid, best_score = scored_bids[0]
            
            # Accept winning bid
            winning_bid.status = BidStatus.ACCEPTED
            trade.winning_bid = winning_bid
            trade.selected_agent = winning_bid.bidder_id
            trade.final_price = winning_bid.price
            trade.status = TradeStatus.COMPLETED
            
            # Reject other bids
            for bid in trade.bids:
                if bid.bid_id != winning_bid.bid_id and bid.status == BidStatus.SUBMITTED:
                    bid.status = BidStatus.REJECTED
            
            # Publish selection event
            await event_bus.publish(LangGraphEvent(
                event_type="trading.agent_selected",
                timestamp=datetime.now(),
                source="trading_engine",
                data={
                    "trade_id": trade_id,
                    "selected_agent": trade.selected_agent,
                    "winning_bid_id": winning_bid.bid_id,
                    "final_price": trade.final_price,
                    "selection_score": best_score
                }
            ))
            
            # Update metrics
            self.metrics.increment("agents_selected")
            
            logger.info(f"Selected agent {trade.selected_agent} for trade {trade_id} with score {best_score:.3f}")
            return trade.selected_agent
            
        except Exception as e:
            logger.error(f"Failed to select optimal agent for trade {trade_id}: {e}")
            raise
    
    async def get_market_analytics(self) -> Dict[str, Any]:
        """
        Get comprehensive market analytics.
        
        Returns:
            Market analytics data
        """
        try:
            analytics = {
                "trading_overview": {
                    "total_trades": len(self.trades),
                    "active_trades": len([t for t in self.trades.values() if t.status == TradeStatus.ACTIVE]),
                    "completed_trades": len([t for t in self.trades.values() if t.status == TradeStatus.COMPLETED]),
                    "total_bids": len(self.bids),
                    "average_bids_per_trade": self._calculate_average_bids_per_trade()
                },
                "pricing_analysis": {
                    "average_winning_price": self._calculate_average_winning_price(),
                    "price_trends": await self._get_price_trends(),
                    "price_distribution": await self._get_price_distribution()
                },
                "performance_metrics": {
                    "average_selection_time": self._calculate_average_selection_time(),
                    "success_rate": self._calculate_trade_success_rate(),
                    "agent_performance": await self._get_agent_performance_summary()
                },
                "market_efficiency": {
                    "bid_acceptance_rate": self._calculate_bid_acceptance_rate(),
                    "time_to_completion": self._calculate_average_completion_time(),
                    "market_liquidity": self._calculate_market_liquidity()
                }
            }
            
            # Update metrics
            self.metrics.increment("analytics_requests")
            
            return analytics
            
        except Exception as e:
            logger.error(f"Failed to generate market analytics: {e}")
            raise

    # Helper Methods
    def _determine_trading_mode(self, request: Any, capabilities: List[Any]) -> str:
        """Determine the best trading mode for a request."""
        if len(capabilities) == 1:
            return "fixed_price"
        elif request.budget > 0 and len(capabilities) > 3:
            return "auction"
        else:
            return "negotiation"

    async def _initiate_bidding(self, trade: Trade, capabilities: List[Any]):
        """Initiate the bidding process for a trade."""
        trade.status = TradeStatus.ACTIVE

        # Notify potential bidders
        for capability in capabilities:
            await event_bus.publish(LangGraphEvent(
                event_type="trading.bidding_invitation",
                timestamp=datetime.now(),
                source="trading_engine",
                data={
                    "trade_id": trade.trade_id,
                    "capability_id": capability.capability_id,
                    "agent_id": capability.agent_id,
                    "requirements": trade.capability_requirements,
                    "budget": trade.budget,
                    "deadline": trade.deadline.isoformat(),
                    "trading_mode": trade.trading_mode
                }
            ))

    def _validate_bid(self, bid_data: Dict[str, Any]) -> bool:
        """Validate bid data."""
        required_fields = ["trade_id", "bidder_id", "capability_id", "price"]
        return all(field in bid_data for field in required_fields)

    async def _evaluate_fixed_price_bid(self, trade: Trade, bid: Bid):
        """Evaluate a bid in fixed price mode."""
        if bid.price <= trade.budget:
            # Accept first valid bid in fixed price mode
            await self.select_optimal_agent(trade.trade_id)

    async def _calculate_bid_score(self, bid: Bid, trade: Trade) -> float:
        """Calculate a comprehensive score for a bid."""
        score = 0.0

        # Price score (lower is better)
        if trade.budget > 0:
            price_score = max(0, 1.0 - (bid.price / trade.budget))
        else:
            price_score = 0.5  # Neutral score if no budget specified

        # Quality score
        quality_score = bid.quality_guarantee

        # Speed score (faster is better)
        max_time = trade.max_execution_time
        speed_score = max(0, 1.0 - (bid.execution_time_estimate / max_time))

        # Agent performance history
        agent_performance = await self._get_agent_performance(bid.bidder_id)
        performance_score = agent_performance.get("success_rate", 0.5)

        # Weighted combination
        score = (
            price_score * self.price_weight +
            quality_score * self.quality_weight +
            speed_score * self.speed_weight +
            performance_score * 0.2  # Historical performance weight
        )

        return min(score, 1.0)

    async def _get_agent_performance(self, agent_id: str) -> Dict[str, float]:
        """Get performance metrics for an agent."""
        if agent_id not in self.agent_performance:
            # Initialize with default values
            self.agent_performance[agent_id] = {
                "success_rate": 0.8,
                "average_execution_time": 1800.0,
                "quality_score": 0.75,
                "reliability_score": 0.8
            }

        return self.agent_performance[agent_id]

    # Event Handlers
    async def _handle_capability_request(self, event: LangGraphEvent):
        """Handle capability request events by initiating trades."""
        try:
            request_data = event.data

            # Extract trade information from the capability request
            trade_data = {
                "request_id": request_data.get("request_id", f"req_{datetime.now().timestamp()}"),
                "requester_id": request_data.get("requester_id"),
                "capability_requirements": request_data.get("requirements", {}),
                "budget": request_data.get("budget", 0.0),
                "deadline": request_data.get("deadline", datetime.now() + timedelta(hours=24))
            }

            # Validate required fields
            if not trade_data["requester_id"]:
                logger.warning("Cannot initiate trade: missing requester_id")
                return

            # Initiate trade for this capability request
            trade_id = await self.initiate_trade(trade_data)

            if trade_id:
                logger.info(f"Initiated trade {trade_id} for capability request {trade_data['request_id']}")

                # Emit event that trade was initiated
                await event_bus.emit(LangGraphEvent(
                    event_type="trading.trade_initiated",
                    timestamp=datetime.now(),
                    source="trading_engine",
                    data={
                        "trade_id": trade_id,
                        "request_id": trade_data["request_id"],
                        "requester_id": trade_data["requester_id"]
                    }
                ))
            else:
                logger.warning(f"Failed to initiate trade for request {trade_data['request_id']}")

        except Exception as e:
            logger.error(f"Error handling capability request: {e}")

    async def _handle_bid_submission(self, event: LangGraphEvent):
        """Handle bid submission events."""
        try:
            bid_data = event.data
            await self.submit_bid(bid_data)
        except Exception as e:
            logger.error(f"Error handling bid submission: {e}")

    async def _handle_execution_completion(self, event: LangGraphEvent):
        """Handle workflow execution completion for performance tracking."""
        try:
            trade_id = event.data.get("trade_id")
            agent_id = event.data.get("agent_id")
            success = event.data.get("success", False)
            execution_time = event.data.get("execution_time", 0.0)
            quality_score = event.data.get("quality_score", 0.0)

            if agent_id:
                await self._update_agent_performance(agent_id, success, execution_time, quality_score)

            if trade_id and trade_id in self.trades:
                trade = self.trades[trade_id]
                trade.execution_completed_at = datetime.now()

        except Exception as e:
            logger.error(f"Error handling execution completion: {e}")

    async def _update_agent_performance(self, agent_id: str, success: bool,
                                      execution_time: float, quality_score: float):
        """Update agent performance metrics."""
        if agent_id not in self.agent_performance:
            self.agent_performance[agent_id] = {
                "success_rate": 0.8,
                "average_execution_time": 1800.0,
                "quality_score": 0.75,
                "reliability_score": 0.8,
                "total_executions": 0
            }

        perf = self.agent_performance[agent_id]
        total_executions = perf["total_executions"]

        # Update success rate
        current_successes = perf["success_rate"] * total_executions
        new_successes = current_successes + (1 if success else 0)
        perf["success_rate"] = new_successes / (total_executions + 1)

        # Update average execution time
        current_total_time = perf["average_execution_time"] * total_executions
        new_total_time = current_total_time + execution_time
        perf["average_execution_time"] = new_total_time / (total_executions + 1)

        # Update quality score
        current_total_quality = perf["quality_score"] * total_executions
        new_total_quality = current_total_quality + quality_score
        perf["quality_score"] = new_total_quality / (total_executions + 1)

        # Update reliability (combination of success rate and consistency)
        perf["reliability_score"] = (perf["success_rate"] + perf["quality_score"]) / 2

        perf["total_executions"] = total_executions + 1

        logger.info(f"Updated performance for agent {agent_id}: success_rate={perf['success_rate']:.3f}")

    # Background Tasks
    async def _trade_monitoring_loop(self):
        """Background task for monitoring active trades."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute

                current_time = datetime.now()

                # Check for expired trades
                for trade in self.trades.values():
                    if trade.status == TradeStatus.ACTIVE:
                        # Check if auction period has ended
                        if trade.trading_mode == "auction":
                            auction_end = trade.created_at + self.auction_duration
                            if current_time >= auction_end:
                                await self.select_optimal_agent(trade.trade_id)

                        # Check if deadline is approaching
                        if current_time >= trade.deadline:
                            trade.status = TradeStatus.EXPIRED
                            logger.warning(f"Trade {trade.trade_id} expired")

            except Exception as e:
                logger.error(f"Error in trade monitoring: {e}")

    async def _bid_expiry_loop(self):
        """Background task for handling bid expiry."""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes

                current_time = datetime.now()

                # Check for expired bids
                for bid in self.bids.values():
                    if (bid.status == BidStatus.SUBMITTED and
                        bid.expires_at and
                        current_time >= bid.expires_at):
                        bid.status = BidStatus.WITHDRAWN
                        logger.info(f"Bid {bid.bid_id} expired")

            except Exception as e:
                logger.error(f"Error in bid expiry check: {e}")

    async def _market_analysis_loop(self):
        """Background task for market analysis and price tracking."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour

                # Update market price trends
                await self._update_market_prices()

                # Analyze trading patterns
                await self._analyze_trading_patterns()

            except Exception as e:
                logger.error(f"Error in market analysis: {e}")

    async def _update_market_prices(self):
        """Update market price tracking."""
        for trade in self.trades.values():
            if trade.status == TradeStatus.COMPLETED and trade.final_price:
                # Track prices by capability type/category
                capability_type = "general"  # Would get from capability metadata
                if capability_type not in self.market_prices:
                    self.market_prices[capability_type] = []

                self.market_prices[capability_type].append(trade.final_price)

                # Keep only recent prices (last 100 trades)
                if len(self.market_prices[capability_type]) > 100:
                    self.market_prices[capability_type] = self.market_prices[capability_type][-100:]

    async def _analyze_trading_patterns(self):
        """Analyze trading patterns for insights."""
        # This would perform more sophisticated analysis
        logger.debug("Analyzing trading patterns")

    async def _load_trading_data(self):
        """Load existing trading data from storage."""
        try:
            # In production, this would load from PostgreSQL
            # For now, initialize with empty state and load any cached data

            # Initialize performance tracking from historical data
            await self._load_agent_performance_data()

            # Initialize market price history
            await self._load_market_price_history()

            # Load any active trades that might have been persisted
            await self._load_active_trades()

            logger.info(f"Trading data loaded: {len(self.trades)} trades, {len(self.bids)} bids")

        except Exception as e:
            logger.error(f"Error loading trading data: {e}")
            # Continue with empty state if loading fails

    async def _load_agent_performance_data(self):
        """Load historical agent performance data."""
        try:
            # This would query the database for historical performance
            # For now, initialize with empty performance tracking
            self.agent_performance = {}
            logger.debug("Agent performance data initialized")

        except Exception as e:
            logger.error(f"Error loading agent performance data: {e}")

    async def _load_market_price_history(self):
        """Load historical market pricing data."""
        try:
            # This would query the database for price history
            # For now, initialize with empty price tracking
            self.market_prices = {}
            logger.debug("Market price history initialized")

        except Exception as e:
            logger.error(f"Error loading market price history: {e}")

    async def _load_active_trades(self):
        """Load any active trades from storage."""
        try:
            # This would query the database for active trades
            # For now, start with empty active trades
            active_trade_count = 0

            # In production, would restore trade state:
            # - Load pending trades
            # - Load active auctions
            # - Restore bid states
            # - Resume monitoring loops

            logger.debug(f"Active trades loaded: {active_trade_count}")

        except Exception as e:
            logger.error(f"Error loading active trades: {e}")

    # Analytics Helper Methods
    def _calculate_average_bids_per_trade(self) -> float:
        """Calculate average number of bids per trade."""
        if not self.trades:
            return 0.0

        total_bids = sum(len(trade.bids) for trade in self.trades.values())
        return total_bids / len(self.trades)

    def _calculate_average_winning_price(self) -> float:
        """Calculate average winning price."""
        completed_trades = [t for t in self.trades.values()
                          if t.status == TradeStatus.COMPLETED and t.final_price]

        if not completed_trades:
            return 0.0

        total_price = sum(trade.final_price for trade in completed_trades)
        return total_price / len(completed_trades)

    async def _get_price_trends(self) -> Dict[str, Any]:
        """Get price trend analysis."""
        return {"trend": "stable", "volatility": 0.1, "growth_rate": 0.05}

    async def _get_price_distribution(self) -> Dict[str, Any]:
        """Get price distribution analysis."""
        return {"min": 5.0, "max": 100.0, "median": 25.0, "std_dev": 15.0}

    def _calculate_average_selection_time(self) -> float:
        """Calculate average time to select an agent."""
        completed_trades = [t for t in self.trades.values() if t.status == TradeStatus.COMPLETED]

        if not completed_trades:
            return 0.0

        total_time = 0.0
        for trade in completed_trades:
            if trade.winning_bid:
                selection_time = (trade.winning_bid.submitted_at - trade.created_at).total_seconds()
                total_time += selection_time

        return total_time / len(completed_trades)

    def _calculate_trade_success_rate(self) -> float:
        """Calculate trade success rate."""
        if not self.trades:
            return 0.0

        completed_trades = len([t for t in self.trades.values() if t.status == TradeStatus.COMPLETED])
        return completed_trades / len(self.trades)

    async def _get_agent_performance_summary(self) -> Dict[str, Any]:
        """Get agent performance summary."""
        if not self.agent_performance:
            return {}

        avg_success_rate = sum(p["success_rate"] for p in self.agent_performance.values()) / len(self.agent_performance)
        avg_quality = sum(p["quality_score"] for p in self.agent_performance.values()) / len(self.agent_performance)

        return {
            "total_agents": len(self.agent_performance),
            "average_success_rate": avg_success_rate,
            "average_quality_score": avg_quality,
            "top_performers": self._get_top_performing_agents()
        }

    def _get_top_performing_agents(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing agents."""
        agents = [(agent_id, perf["reliability_score"])
                 for agent_id, perf in self.agent_performance.items()]
        agents.sort(key=lambda x: x[1], reverse=True)

        return [{"agent_id": agent_id, "reliability_score": score}
                for agent_id, score in agents[:limit]]

    def _calculate_bid_acceptance_rate(self) -> float:
        """Calculate bid acceptance rate."""
        if not self.bids:
            return 0.0

        accepted_bids = len([b for b in self.bids.values() if b.status == BidStatus.ACCEPTED])
        return accepted_bids / len(self.bids)

    def _calculate_average_completion_time(self) -> float:
        """Calculate average time to complete trades."""
        completed_trades = [t for t in self.trades.values()
                          if t.status == TradeStatus.COMPLETED and t.execution_completed_at]

        if not completed_trades:
            return 0.0

        total_time = 0.0
        for trade in completed_trades:
            completion_time = (trade.execution_completed_at - trade.created_at).total_seconds()
            total_time += completion_time

        return total_time / len(completed_trades)

    def _calculate_market_liquidity(self) -> float:
        """Calculate market liquidity metric."""
        if not self.trades:
            return 0.0

        # Simple liquidity metric: ratio of active trades to total trades
        active_trades = len([t for t in self.trades.values() if t.status == TradeStatus.ACTIVE])
        return active_trades / len(self.trades)
