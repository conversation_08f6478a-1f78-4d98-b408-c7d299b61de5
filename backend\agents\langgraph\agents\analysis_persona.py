"""
Analysis Persona

This module provides a consolidated analysis agent that uses the unified conversation agent base
for consistent AI-powered responses and specialized data analysis capabilities.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .unified_conversation_agent import UnifiedConversationAgent

logger = logging.getLogger(__name__)


class AnalysisPersona(UnifiedConversationAgent):
    """
    Analysis persona that uses the unified conversation base.
    
    This persona provides:
    - Data analysis and statistical modeling
    - Data visualization and charting
    - Trend analysis and pattern recognition
    - Performance metrics calculation
    - Predictive analytics
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the analysis persona.
        
        Args:
            config: Optional configuration for the persona
        """
        # Get agent_id from config or use default
        agent_id = config.get('agent_id', 'analysis') if config else 'analysis'
        
        # Initialize parent class
        super().__init__(
            agent_id=agent_id,
            agent_type="analysis",
            config=config
        )
        
        # Analysis-specific configuration
        self.max_data_size = config.get('max_data_size', 10000000) if config else 10000000  # 10MB
        self.supported_formats = config.get('supported_formats', ['csv', 'xlsx', 'json', 'parquet']) if config else ['csv', 'xlsx', 'json', 'parquet']
        self.default_chart_types = config.get('default_chart_types', ['bar', 'line', 'scatter', 'histogram']) if config else ['bar', 'line', 'scatter', 'histogram']
        
        logger.info("AnalysisPersona initialized")

    def _get_specialized_capabilities(self) -> List[str]:
        """Get analysis-specific capabilities."""
        return [
            "data_analysis",
            "statistical_modeling",
            "data_visualization",
            "trend_analysis",
            "pattern_recognition",
            "performance_metrics",
            "predictive_analytics",
            "data_cleaning",
            "exploratory_analysis",
            "report_generation"
        ]

    def _get_agent_name(self) -> str:
        """Get human-readable agent name."""
        return "Data Analysis Specialist"

    def _get_agent_description(self) -> str:
        """Get agent description."""
        return "Expert data analyst specializing in insights, visualization, and statistical analysis"

    def _determine_intent_type(self, message: str) -> str:
        """Determine the intent type for analysis conversations."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["chart", "graph", "plot", "visualization"]):
            return "data_visualization"
        elif any(word in message_lower for word in ["trend", "pattern", "correlation"]):
            return "trend_analysis"
        elif any(word in message_lower for word in ["statistics", "statistical", "model"]):
            return "statistical_analysis"
        elif any(word in message_lower for word in ["predict", "forecast", "future"]):
            return "predictive_analysis"
        elif any(word in message_lower for word in ["clean", "prepare", "process"]):
            return "data_preparation"
        else:
            return "general_analysis"

    def _get_greeting_specialization(self) -> str:
        """Get analysis-specific greeting content."""
        return ("**📊 I specialize in:**\n"
                "• **Data Analysis** - Statistical insights and modeling\n"
                "• **Data Visualization** - Charts, graphs, and dashboards\n"
                "• **Trend Analysis** - Pattern recognition and forecasting\n"
                "• **Performance Metrics** - KPI tracking and measurement\n"
                "• **Predictive Analytics** - Future trend predictions\n"
                "• **Report Generation** - Comprehensive data reports")

    def _generate_capabilities_response(self) -> str:
        """Generate a response about analysis capabilities."""
        return ("I'm your **Data Analysis Specialist** with comprehensive analytical capabilities:\n\n"
                "**📊 Core Analysis:**\n"
                "• Statistical analysis and modeling\n"
                "• Trend analysis and pattern recognition\n"
                "• Performance metrics and KPI tracking\n\n"
                "**📈 Visualization:**\n"
                "• Interactive charts and graphs\n"
                "• Dashboards and reports\n"
                "• Data storytelling\n\n"
                "**🔮 Advanced Analytics:**\n"
                "• Predictive modeling\n"
                "• Forecasting and projections\n"
                "• Correlation analysis\n\n"
                f"**Supported Formats:** {', '.join(self.supported_formats)}\n"
                f"**Chart Types:** {', '.join(self.default_chart_types)}\n"
                f"**Max Data Size:** {self.max_data_size // 1000000}MB")

    async def _enhance_response(self, original_message: str, base_response: str, 
                              context: Optional[Dict[str, Any]] = None) -> str:
        """Enhance the response with analysis-specific information."""
        try:
            enhanced_response = base_response
            
            # Add visualization suggestions
            if self._is_visualization_related(original_message):
                enhanced_response += f"\n\n📈 **Visualization Options**: I can create {', '.join(self.default_chart_types)} charts to help visualize your data patterns."
            
            # Add data format guidance
            if self._is_data_upload_related(original_message):
                enhanced_response += f"\n\n📁 **Supported Formats**: I can analyze data in {', '.join(self.supported_formats)} formats up to {self.max_data_size // 1000000}MB."
            
            # Add statistical insights
            if self._is_statistical_related(original_message):
                enhanced_response += "\n\n📊 **Statistical Analysis**: I can perform descriptive statistics, correlation analysis, regression modeling, and hypothesis testing."
            
            # Add trend analysis tips
            if self._is_trend_related(original_message):
                enhanced_response += "\n\n📈 **Trend Analysis**: I can identify patterns, seasonal trends, and provide forecasting based on historical data."
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing analysis response: {e}")
            return base_response

    def _is_visualization_related(self, message: str) -> bool:
        """Check if the message is visualization-related."""
        viz_keywords = ["chart", "graph", "plot", "visualization", "dashboard", "visual"]
        return any(keyword in message.lower() for keyword in viz_keywords)

    def _is_data_upload_related(self, message: str) -> bool:
        """Check if the message is about data upload or format."""
        data_keywords = ["upload", "file", "data", "csv", "excel", "format"]
        return any(keyword in message.lower() for keyword in data_keywords)

    def _is_statistical_related(self, message: str) -> bool:
        """Check if the message is statistics-related."""
        stats_keywords = ["statistics", "statistical", "correlation", "regression", "model"]
        return any(keyword in message.lower() for keyword in stats_keywords)

    def _is_trend_related(self, message: str) -> bool:
        """Check if the message is trend-related."""
        trend_keywords = ["trend", "pattern", "forecast", "predict", "time series"]
        return any(keyword in message.lower() for keyword in trend_keywords)

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this analysis persona."""
        base_info = super().get_agent_info()
        base_info.update({
            "specialization": "data_analysis_and_visualization",
            "max_data_size": self.max_data_size,
            "supported_formats": self.supported_formats,
            "default_chart_types": self.default_chart_types,
            "supports_statistical_analysis": True,
            "supports_data_visualization": True,
            "supports_predictive_analytics": True
        })
        return base_info


# Backward compatibility aliases
RefactoredAnalysisAgent = AnalysisPersona
UserSelectedAnalysisAgent = AnalysisPersona
AnalysisAgent = AnalysisPersona
