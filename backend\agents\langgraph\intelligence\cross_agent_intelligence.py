"""
Enhanced Cross-Agent Intelligence System for LangGraph.

This module provides advanced cross-agent intelligence capabilities including:
- Intelligent insight sharing with relevance scoring
- Business profile context integration
- Agent collaboration orchestration
- Consensus building mechanisms
- Intelligence analytics and monitoring
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..states.unified_state import (
    UnifiedDatageniusState,
    add_cross_agent_insight,
    get_relevant_insights,
    build_agent_consensus,
    record_agent_vote,
    load_business_profile_context,
    share_business_context_across_agents,
    get_agent_collaboration_opportunities
)

logger = logging.getLogger(__name__)


class CrossAgentIntelligenceManager:
    """
    Manager for cross-agent intelligence and collaboration.

    This class provides advanced cross-agent intelligence capabilities including
    intelligent insight sharing, business profile context integration, and
    agent collaboration orchestration.
    """

    def __init__(self):
        """Initialize the cross-agent intelligence manager."""
        self.logger = logging.getLogger(f"{__name__}.CrossAgentIntelligenceManager")
        self.insight_cache: Dict[str, Any] = {}
        self.collaboration_history: List[Dict[str, Any]] = []
        self.agent_capabilities: Dict[str, Set[str]] = {}

        self.logger.info("CrossAgentIntelligenceManager initialized")

    async def apply_intelligence(
        self,
        context: Dict[str, Any],
        state: UnifiedDatageniusState
    ) -> Dict[str, Any]:
        """
        Apply cross-agent intelligence to enrich context.

        Args:
            context: Base context to enrich
            state: Current workflow state

        Returns:
            Enriched context with cross-agent intelligence
        """
        try:
            enriched_context = context.copy()

            # Add relevant insights from other agents
            relevant_insights = await self._get_relevant_insights(state)
            if relevant_insights:
                enriched_context["cross_agent_insights"] = relevant_insights

            # Add collaboration opportunities
            collaboration_opportunities = await self._identify_collaboration_opportunities(state)
            if collaboration_opportunities:
                enriched_context["collaboration_opportunities"] = collaboration_opportunities

            # Add shared business context
            business_context = state.get("business_context", {})
            if business_context:
                enriched_context["shared_business_context"] = business_context

            return enriched_context

        except Exception as e:
            self.logger.error(f"Error applying cross-agent intelligence: {e}")
            return context

    async def _get_relevant_insights(self, state: UnifiedDatageniusState) -> List[Dict[str, Any]]:
        """Get relevant insights from other agents."""
        try:
            shared_insights = state.get("shared_insights", [])
            current_agent = state.get("current_agent", "")

            # Filter insights relevant to current context
            relevant_insights = []
            for insight in shared_insights:
                if insight.get("source_agent") != current_agent:
                    # Simple relevance scoring based on keywords
                    relevance_score = self._calculate_relevance_score(insight, state)
                    if relevance_score > 0.5:
                        insight["relevance_score"] = relevance_score
                        relevant_insights.append(insight)

            # Sort by relevance score
            relevant_insights.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

            return relevant_insights[:5]  # Return top 5 most relevant insights

        except Exception as e:
            self.logger.error(f"Error getting relevant insights: {e}")
            return []

    async def _identify_collaboration_opportunities(
        self,
        state: UnifiedDatageniusState
    ) -> List[Dict[str, Any]]:
        """Identify opportunities for agent collaboration."""
        try:
            opportunities = []
            current_message = state.get("current_message", {})
            message_content = current_message.get("content", "").lower()

            # Simple collaboration opportunity detection
            if any(keyword in message_content for keyword in ["analyze", "data", "chart"]):
                opportunities.append({
                    "type": "data_analysis",
                    "suggested_agent": "analysis",
                    "reason": "Message contains data analysis keywords"
                })

            if any(keyword in message_content for keyword in ["marketing", "campaign", "social"]):
                opportunities.append({
                    "type": "marketing_strategy",
                    "suggested_agent": "marketing",
                    "reason": "Message contains marketing keywords"
                })

            return opportunities

        except Exception as e:
            self.logger.error(f"Error identifying collaboration opportunities: {e}")
            return []

    def _calculate_relevance_score(self, insight: Dict[str, Any], state: UnifiedDatageniusState) -> float:
        """Calculate relevance score for an insight."""
        try:
            score = 0.0

            # Base score for recent insights
            insight_time = insight.get("timestamp", "")
            if insight_time:
                # Simple time-based scoring (more recent = higher score)
                score += 0.3

            # Content-based scoring
            insight_content = insight.get("content", "").lower()
            current_message = state.get("current_message", {})
            message_content = current_message.get("content", "").lower()

            # Simple keyword matching
            common_words = set(insight_content.split()) & set(message_content.split())
            if common_words:
                score += min(len(common_words) * 0.1, 0.5)

            # Agent type relevance
            insight_type = insight.get("type", "")
            if insight_type in ["business_context", "user_intent"]:
                score += 0.2

            return min(score, 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating relevance score: {e}")
            return 0.0


class CollaborationType(str, Enum):
    """Types of agent collaboration."""
    PARALLEL = "parallel"
    SEQUENTIAL = "sequential"
    HIERARCHICAL = "hierarchical"
    CONSENSUS = "consensus"
    SUPPORTIVE = "supportive"


class InsightType(str, Enum):
    """Types of cross-agent insights."""
    DATA_ANALYSIS = "data_analysis"
    MARKETING_STRATEGY = "marketing_strategy"
    BUSINESS_CONTEXT = "business_context"
    USER_INTENT = "user_intent"
    WORKFLOW_OPTIMIZATION = "workflow_optimization"
    QUALITY_ASSURANCE = "quality_assurance"
    PERFORMANCE_METRICS = "performance_metrics"
    GENERAL = "general"


@dataclass
class CollaborationRequest:
    """Request for agent collaboration."""
    requesting_agent: str
    target_agents: List[str]
    collaboration_type: CollaborationType
    task_description: str
    priority: int = 2
    timeout_minutes: int = 30
    required_consensus: float = 0.7


@dataclass
class IntelligenceInsight:
    """Structured intelligence insight."""
    content: Dict[str, Any]
    insight_type: InsightType
    source_agent: str
    target_agents: Optional[List[str]] = None
    priority: int = 2
    relevance_score: float = 1.0
    confidence: float = 1.0
    metadata: Optional[Dict[str, Any]] = None


class CrossAgentIntelligenceService:
    """
    Enhanced cross-agent intelligence service for LangGraph workflows.
    
    This service manages intelligent collaboration between agents, including:
    - Insight sharing with relevance filtering
    - Business context integration
    - Collaborative decision making
    - Performance monitoring and analytics
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the cross-agent intelligence service.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration parameters
        self.max_insights_per_agent = self.config.get("max_insights_per_agent", 100)
        self.insight_retention_hours = self.config.get("insight_retention_hours", 24)
        self.default_relevance_threshold = self.config.get("default_relevance_threshold", 0.5)
        self.collaboration_timeout_minutes = self.config.get("collaboration_timeout_minutes", 30)
        
        # Agent capability mappings
        self.agent_capabilities = {
            "concierge_agent": ["guidance", "coordination", "routing", "user_interaction"],
            "marketing_agent": ["marketing", "strategy", "content", "campaigns", "analysis"],
            "analysis_agent": ["data_analysis", "visualization", "insights", "statistics"],
            "classification_agent": ["classification", "categorization", "organization", "structure"]
        }
        
        # Insight type priorities for different agents
        self.insight_priorities = {
            "concierge_agent": [InsightType.USER_INTENT, InsightType.WORKFLOW_OPTIMIZATION, InsightType.GENERAL],
            "marketing_agent": [InsightType.MARKETING_STRATEGY, InsightType.BUSINESS_CONTEXT, InsightType.DATA_ANALYSIS],
            "analysis_agent": [InsightType.DATA_ANALYSIS, InsightType.PERFORMANCE_METRICS, InsightType.QUALITY_ASSURANCE],
            "classification_agent": [InsightType.DATA_ANALYSIS, InsightType.BUSINESS_CONTEXT, InsightType.GENERAL]
        }
    
    async def share_insight(
        self,
        state: UnifiedDatageniusState,
        insight: IntelligenceInsight
    ) -> UnifiedDatageniusState:
        """
        Share an intelligence insight across agents.
        
        Args:
            state: Current workflow state
            insight: Intelligence insight to share
            
        Returns:
            Updated state with shared insight
        """
        try:
            # Prepare insight data
            insight_data = {
                "content": insight.content,
                "confidence": insight.confidence,
                "metadata": insight.metadata or {}
            }
            
            # Add insight to state
            updated_state = add_cross_agent_insight(
                state=state,
                insight=insight_data,
                source_agent=insight.source_agent,
                target_agents=insight.target_agents,
                insight_type=insight.insight_type.value,
                priority=insight.priority,
                relevance_score=insight.relevance_score
            )
            
            self.logger.info(
                f"Shared {insight.insight_type.value} insight from {insight.source_agent} "
                f"to {len(insight.target_agents or [])} agents"
            )
            
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error sharing insight: {e}")
            state["error_history"].append({
                "timestamp": datetime.now().isoformat(),
                "source": "cross_agent_intelligence",
                "error": str(e),
                "context": {"insight_type": insight.insight_type.value}
            })
            return state
    
    async def get_agent_insights(
        self,
        state: UnifiedDatageniusState,
        target_agent: str,
        insight_types: Optional[List[InsightType]] = None,
        max_insights: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get relevant insights for a target agent.
        
        Args:
            state: Current workflow state
            target_agent: Agent requesting insights
            insight_types: Optional filter by insight types
            max_insights: Maximum number of insights to return
            
        Returns:
            List of relevant insights
        """
        try:
            # Get agent-specific insight priorities
            agent_priorities = self.insight_priorities.get(target_agent, [InsightType.GENERAL])
            
            # Convert enum types to strings if provided
            type_filters = None
            if insight_types:
                type_filters = [t.value if isinstance(t, InsightType) else t for t in insight_types]
            elif agent_priorities:
                type_filters = [t.value for t in agent_priorities]
            
            # Get relevant insights from state
            insights = get_relevant_insights(
                state=state,
                target_agent=target_agent,
                insight_types=type_filters,
                min_priority=1,
                min_relevance=self.default_relevance_threshold,
                max_insights=max_insights
            )
            
            self.logger.debug(f"Retrieved {len(insights)} insights for agent {target_agent}")
            
            return insights
            
        except Exception as e:
            self.logger.error(f"Error getting agent insights: {e}")
            return []
    
    async def initiate_collaboration(
        self,
        state: UnifiedDatageniusState,
        collaboration_request: CollaborationRequest
    ) -> UnifiedDatageniusState:
        """
        Initiate collaboration between agents.
        
        Args:
            state: Current workflow state
            collaboration_request: Collaboration request details
            
        Returns:
            Updated state with collaboration setup
        """
        try:
            # Create collaboration decision
            decision_id = f"collaboration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            decision_data = {
                "type": "collaboration_request",
                "requesting_agent": collaboration_request.requesting_agent,
                "target_agents": collaboration_request.target_agents,
                "collaboration_type": collaboration_request.collaboration_type.value,
                "task_description": collaboration_request.task_description,
                "priority": collaboration_request.priority,
                "timeout_minutes": collaboration_request.timeout_minutes
            }
            
            # Build consensus for collaboration
            updated_state = build_agent_consensus(
                state=state,
                decision_id=decision_id,
                decision_data=decision_data,
                participating_agents=collaboration_request.target_agents,
                consensus_threshold=collaboration_request.required_consensus
            )
            
            # Update collaboration mode
            updated_state["collaboration_mode"] = collaboration_request.collaboration_type.value
            
            # Add participating agents
            for agent in collaboration_request.target_agents:
                updated_state["participating_agents"][agent] = "participant"
            updated_state["participating_agents"][collaboration_request.requesting_agent] = "coordinator"
            
            self.logger.info(
                f"Initiated {collaboration_request.collaboration_type.value} collaboration "
                f"between {len(collaboration_request.target_agents)} agents"
            )
            
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error initiating collaboration: {e}")
            state["error_history"].append({
                "timestamp": datetime.now().isoformat(),
                "source": "cross_agent_intelligence",
                "error": str(e),
                "context": {"collaboration_type": collaboration_request.collaboration_type.value}
            })
            return state
    
    async def load_business_context(
        self,
        state: UnifiedDatageniusState,
        business_profile_id: str
    ) -> UnifiedDatageniusState:
        """
        Load business profile context for cross-agent intelligence.
        
        Args:
            state: Current workflow state
            business_profile_id: Business profile identifier
            
        Returns:
            Updated state with business context
        """
        try:
            # Load business profile context
            updated_state = load_business_profile_context(
                state=state,
                business_profile_id=business_profile_id,
                include_data_sources=True,
                include_insights=True
            )
            
            # Share business context across active agents
            if state["active_agents"]:
                context_update = {
                    "business_profile_loaded": True,
                    "profile_id": business_profile_id,
                    "loaded_at": datetime.now().isoformat()
                }
                
                updated_state = share_business_context_across_agents(
                    state=updated_state,
                    context_update=context_update,
                    source_agent="cross_agent_intelligence"
                )
            
            self.logger.info(f"Loaded business context for profile {business_profile_id}")
            
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error loading business context: {e}")
            state["error_history"].append({
                "timestamp": datetime.now().isoformat(),
                "source": "cross_agent_intelligence",
                "error": str(e),
                "context": {"business_profile_id": business_profile_id}
            })
            return state
    
    async def get_collaboration_opportunities(
        self,
        state: UnifiedDatageniusState,
        current_agent: str
    ) -> List[Dict[str, Any]]:
        """
        Get collaboration opportunities for the current agent.
        
        Args:
            state: Current workflow state
            current_agent: Current agent identifier
            
        Returns:
            List of collaboration opportunities
        """
        try:
            opportunities = get_agent_collaboration_opportunities(
                state=state,
                current_agent=current_agent
            )
            
            self.logger.debug(f"Found {len(opportunities)} collaboration opportunities for {current_agent}")
            
            return opportunities
            
        except Exception as e:
            self.logger.error(f"Error getting collaboration opportunities: {e}")
            return []
