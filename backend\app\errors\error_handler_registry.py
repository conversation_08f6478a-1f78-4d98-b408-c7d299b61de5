"""
Error Handler Registry.

Provides centralized error handling with type-specific handlers and
automatic error processing, logging, and response generation.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Type, Any, Optional, Callable, List, Union
from datetime import datetime, timezone

from fastapi import HTTPException, status
from fastapi.responses import JSONResponse

from .base_errors import DatageniusError, ErrorSeverity, ErrorCategory
from .correlation_context import get_correlation_id, get_correlation_logger
from .error_sanitizer import ErrorSanitizer

logger = get_correlation_logger(__name__)


class ErrorHandler(ABC):
    """
    Abstract base class for error handlers.
    
    Defines the interface for handling specific error types.
    """
    
    def __init__(self, error_type: Type[Exception]):
        self.error_type = error_type
        self.logger = get_correlation_logger(f"{__name__}.{error_type.__name__}Handler")
    
    @abstractmethod
    def can_handle(self, error: Exception) -> bool:
        """Check if this handler can handle the given error."""
        pass
    
    @abstractmethod
    def handle(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Handle the error and return response data."""
        pass
    
    def get_http_status_code(self, error: Exception) -> int:
        """Get the appropriate HTTP status code for the error."""
        if isinstance(error, DatageniusError):
            category_status_map = {
                ErrorCategory.VALIDATION: status.HTTP_400_BAD_REQUEST,
                ErrorCategory.AUTHENTICATION: status.HTTP_401_UNAUTHORIZED,
                ErrorCategory.AUTHORIZATION: status.HTTP_403_FORBIDDEN,
                ErrorCategory.NOT_FOUND: status.HTTP_404_NOT_FOUND,
                ErrorCategory.CONFLICT: status.HTTP_409_CONFLICT,
                ErrorCategory.RATE_LIMIT: status.HTTP_429_TOO_MANY_REQUESTS,
                ErrorCategory.SERVICE_UNAVAILABLE: status.HTTP_503_SERVICE_UNAVAILABLE,
                ErrorCategory.INTERNAL_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
                ErrorCategory.EXTERNAL_SERVICE: status.HTTP_502_BAD_GATEWAY,
                ErrorCategory.CONFIGURATION: status.HTTP_500_INTERNAL_SERVER_ERROR,
                ErrorCategory.NETWORK: status.HTTP_502_BAD_GATEWAY,
                ErrorCategory.DATABASE: status.HTTP_500_INTERNAL_SERVER_ERROR
            }
            return category_status_map.get(error.category, status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return status.HTTP_500_INTERNAL_SERVER_ERROR
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """Log the error with appropriate level based on severity."""
        if isinstance(error, DatageniusError):
            log_data = error.to_dict(include_sensitive=True)
            if context:
                log_data["handler_context"] = context
            
            if error.severity == ErrorSeverity.CRITICAL:
                self.logger.critical(f"Critical error: {error.message}", extra=log_data)
            elif error.severity == ErrorSeverity.HIGH:
                self.logger.error(f"High severity error: {error.message}", extra=log_data)
            elif error.severity == ErrorSeverity.MEDIUM:
                self.logger.warning(f"Medium severity error: {error.message}", extra=log_data)
            else:
                self.logger.info(f"Low severity error: {error.message}", extra=log_data)
        else:
            self.logger.error(f"Unhandled error: {str(error)}", extra={
                "error_type": type(error).__name__,
                "error_message": str(error),
                "context": context
            })


class DatageniusErrorHandler(ErrorHandler):
    """Handler for DatageniusError and its subclasses."""
    
    def __init__(self):
        super().__init__(DatageniusError)
        self.sanitizer = ErrorSanitizer()
    
    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, DatageniusError)
    
    def handle(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        if not isinstance(error, DatageniusError):
            raise ValueError("DatageniusErrorHandler can only handle DatageniusError instances")
        
        # Log the error
        self.log_error(error, context)
        
        # Sanitize error for client response
        sanitized_error = self.sanitizer.sanitize_error(error)
        
        # Build response
        response_data = {
            "error": {
                "code": sanitized_error.error_code,
                "message": sanitized_error.user_message,
                "category": sanitized_error.category.value,
                "severity": sanitized_error.severity.value,
                "correlation_id": sanitized_error.correlation_id,
                "timestamp": sanitized_error.timestamp.isoformat()
            }
        }
        
        # Add field errors for validation errors
        if hasattr(sanitized_error, 'field_errors') and sanitized_error.field_errors:
            response_data["error"]["field_errors"] = sanitized_error.field_errors
        
        # Add retry information for rate limit errors
        if sanitized_error.category == ErrorCategory.RATE_LIMIT:
            retry_after = sanitized_error.details.get('retry_after')
            if retry_after:
                response_data["error"]["retry_after"] = retry_after
        
        return response_data


class HTTPExceptionHandler(ErrorHandler):
    """Handler for FastAPI HTTPException."""
    
    def __init__(self):
        super().__init__(HTTPException)
    
    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, HTTPException)
    
    def handle(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        if not isinstance(error, HTTPException):
            raise ValueError("HTTPExceptionHandler can only handle HTTPException instances")
        
        # Log the error
        self.log_error(error, context)
        
        # Build response
        response_data = {
            "error": {
                "code": f"HTTP_{error.status_code}",
                "message": error.detail,
                "category": "http_error",
                "severity": "medium",
                "correlation_id": get_correlation_id(),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }
        
        return response_data


class GenericExceptionHandler(ErrorHandler):
    """Handler for generic Python exceptions."""
    
    def __init__(self):
        super().__init__(Exception)
        self.sanitizer = ErrorSanitizer()
    
    def can_handle(self, error: Exception) -> bool:
        return True  # Can handle any exception as fallback
    
    def handle(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        # Log the error
        self.log_error(error, context)
        
        # Create a generic DatageniusError for consistent handling
        generic_error = DatageniusError(
            message=f"Internal server error: {type(error).__name__}",
            error_code="INTERNAL_SERVER_ERROR",
            category=ErrorCategory.INTERNAL_ERROR,
            severity=ErrorSeverity.HIGH,
            cause=error,
            user_message="An internal server error occurred. Please try again later."
        )
        
        # Sanitize error
        sanitized_error = self.sanitizer.sanitize_error(generic_error)
        
        # Build response
        response_data = {
            "error": {
                "code": sanitized_error.error_code,
                "message": sanitized_error.user_message,
                "category": sanitized_error.category.value,
                "severity": sanitized_error.severity.value,
                "correlation_id": sanitized_error.correlation_id,
                "timestamp": sanitized_error.timestamp.isoformat()
            }
        }
        
        return response_data


class ErrorHandlerRegistry:
    """
    Registry for error handlers with automatic error processing.
    
    Provides centralized error handling with type-specific handlers,
    automatic logging, and response generation.
    """
    
    def __init__(self):
        self.handlers: List[ErrorHandler] = []
        self.logger = get_correlation_logger(__name__)
        
        # Error metrics
        self.error_counts: Dict[str, int] = {}
        self.error_rates: Dict[str, List[datetime]] = {}
        
        # Register default handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default error handlers."""
        # Order matters - more specific handlers first
        self.register_handler(DatageniusErrorHandler())
        self.register_handler(HTTPExceptionHandler())
        self.register_handler(GenericExceptionHandler())  # Fallback handler
    
    def register_handler(self, handler: ErrorHandler):
        """
        Register an error handler.
        
        Args:
            handler: Error handler instance
        """
        self.handlers.append(handler)
        self.logger.info(f"Registered error handler: {handler.__class__.__name__}")
    
    def unregister_handler(self, handler_class: Type[ErrorHandler]):
        """
        Unregister error handlers of a specific type.
        
        Args:
            handler_class: Error handler class to remove
        """
        self.handlers = [h for h in self.handlers if not isinstance(h, handler_class)]
        self.logger.info(f"Unregistered error handler: {handler_class.__name__}")
    
    def handle_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """
        Handle an error using the appropriate handler.
        
        Args:
            error: Exception to handle
            context: Optional context information
            
        Returns:
            JSONResponse with error details
        """
        # Update error metrics
        self._update_error_metrics(error)
        
        # Find appropriate handler
        handler = self._find_handler(error)
        
        if not handler:
            self.logger.error(f"No handler found for error type: {type(error).__name__}")
            handler = self.handlers[-1]  # Use fallback handler
        
        try:
            # Handle the error
            response_data = handler.handle(error, context)
            status_code = handler.get_http_status_code(error)
            
            # Create JSON response
            response = JSONResponse(
                content=response_data,
                status_code=status_code
            )
            
            # Add correlation headers
            correlation_id = get_correlation_id()
            response.headers["X-Correlation-ID"] = correlation_id
            
            return response
            
        except Exception as handler_error:
            self.logger.error(f"Error in error handler: {handler_error}")
            
            # Fallback to generic error response
            fallback_response = {
                "error": {
                    "code": "HANDLER_ERROR",
                    "message": "An error occurred while processing the error",
                    "category": "internal_error",
                    "severity": "high",
                    "correlation_id": get_correlation_id(),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            }
            
            return JSONResponse(
                content=fallback_response,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _find_handler(self, error: Exception) -> Optional[ErrorHandler]:
        """Find the appropriate handler for an error."""
        for handler in self.handlers:
            if handler.can_handle(error):
                return handler
        return None
    
    def _update_error_metrics(self, error: Exception):
        """Update error metrics for monitoring."""
        error_type = type(error).__name__
        current_time = datetime.now(timezone.utc)
        
        # Update error counts
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Update error rates (keep last hour of errors)
        if error_type not in self.error_rates:
            self.error_rates[error_type] = []
        
        self.error_rates[error_type].append(current_time)
        
        # Clean old entries (older than 1 hour)
        one_hour_ago = current_time.replace(hour=current_time.hour - 1)
        self.error_rates[error_type] = [
            timestamp for timestamp in self.error_rates[error_type]
            if timestamp > one_hour_ago
        ]
    
    def get_error_metrics(self) -> Dict[str, Any]:
        """Get error metrics for monitoring."""
        return {
            "error_counts": self.error_counts.copy(),
            "error_rates": {
                error_type: len(timestamps)
                for error_type, timestamps in self.error_rates.items()
            },
            "total_errors": sum(self.error_counts.values()),
            "unique_error_types": len(self.error_counts)
        }
    
    def reset_metrics(self):
        """Reset error metrics."""
        self.error_counts.clear()
        self.error_rates.clear()
        self.logger.info("Error metrics reset")


# Global error handler registry
_error_registry: Optional[ErrorHandlerRegistry] = None


def get_error_registry() -> ErrorHandlerRegistry:
    """Get the global error handler registry."""
    global _error_registry
    
    if _error_registry is None:
        _error_registry = ErrorHandlerRegistry()
    
    return _error_registry


def handle_error(error: Exception, context: Optional[Dict[str, Any]] = None) -> JSONResponse:
    """
    Handle an error using the global error registry.

    Args:
        error: Exception to handle
        context: Optional context information

    Returns:
        JSONResponse with error details
    """
    registry = get_error_registry()
    return registry.handle_error(error, context)


# FastAPI Middleware for Error Handling
class ErrorHandlerMiddleware:
    """FastAPI middleware that integrates with the error handler registry."""

    def __init__(self, app):
        self.app = app
        self.error_registry = get_error_registry()

    async def __call__(self, scope, receive, send):
        """Process requests through the error handling middleware."""
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        try:
            await self.app(scope, receive, send)
        except Exception as exc:
            # Handle the exception using our error registry
            response = self.error_registry.handle_error(exc)

            # Convert to ASGI response
            response_body = response.body
            if isinstance(response_body, str):
                response_body = response_body.encode()

            await send({
                "type": "http.response.start",
                "status": response.status_code,
                "headers": [
                    [b"content-type", b"application/json"],
                    [b"content-length", str(len(response_body)).encode()],
                ] + [[k.encode(), v.encode()] for k, v in response.headers.items()],
            })
            await send({
                "type": "http.response.body",
                "body": response_body,
            })
