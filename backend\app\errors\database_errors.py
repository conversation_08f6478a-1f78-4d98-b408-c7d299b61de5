"""
Database-specific Error Classes.

Specialized error classes for database-related operations and failures.
"""

from typing import Optional, Dict, Any
from .base_errors import DatageniusError, ErrorCategory, ErrorSeverity


class DatabaseError(DatageniusError):
    """Base class for database-related errors."""
    
    def __init__(
        self,
        message: str,
        table_name: Optional[str] = None,
        operation: Optional[str] = None,
        query: Optional[str] = None,
        error_code: str = "DATABASE_ERROR",
        severity: ErrorSeverity = ErrorSeverity.HIGH,
        user_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        **kwargs
    ):
        details = details or {}
        if table_name:
            details["table_name"] = table_name
        if operation:
            details["operation"] = operation
        if query:
            # Sanitize query for logging (remove sensitive data)
            details["query"] = self._sanitize_query(query)
        
        super().__init__(
            message=message,
            error_code=error_code,
            category=ErrorCategory.DATABASE,
            severity=severity,
            user_message=user_message or "Database operation failed",
            details=details,
            cause=cause,
            **kwargs
        )
    
    def _sanitize_query(self, query: str) -> str:
        """Sanitize SQL query for logging."""
        # Remove potential sensitive data from queries
        import re
        
        # Remove string literals that might contain sensitive data
        sanitized = re.sub(r"'[^']*'", "'[REDACTED]'", query)
        sanitized = re.sub(r'"[^"]*"', '"[REDACTED]"', sanitized)
        
        return sanitized


class DatabaseConnectionError(DatabaseError):
    """Error connecting to database."""
    
    def __init__(
        self,
        message: str,
        host: Optional[str] = None,
        port: Optional[int] = None,
        database: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if host:
            details["host"] = host
        if port:
            details["port"] = port
        if database:
            details["database"] = database
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_CONNECTION_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.CRITICAL)
        kwargs.setdefault('user_message', 'Database connection failed')
        
        super().__init__(message, **kwargs)


class DatabaseQueryError(DatabaseError):
    """Error executing database query."""
    
    def __init__(
        self,
        message: str,
        sql_error_code: Optional[str] = None,
        constraint_name: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if sql_error_code:
            details["sql_error_code"] = sql_error_code
        if constraint_name:
            details["constraint_name"] = constraint_name
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_QUERY_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Database query failed')
        
        super().__init__(message, **kwargs)


class DatabaseIntegrityError(DatabaseError):
    """Error violating database integrity constraints."""
    
    def __init__(
        self,
        message: str,
        constraint_type: Optional[str] = None,
        constraint_name: Optional[str] = None,
        column_name: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if constraint_type:
            details["constraint_type"] = constraint_type
        if constraint_name:
            details["constraint_name"] = constraint_name
        if column_name:
            details["column_name"] = column_name
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_INTEGRITY_ERROR')
        kwargs.setdefault('category', ErrorCategory.CONFLICT)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Data integrity constraint violated')
        
        super().__init__(message, **kwargs)


class DatabaseTimeoutError(DatabaseError):
    """Error when database operation times out."""
    
    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[float] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_TIMEOUT_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Database operation timed out')
        
        super().__init__(message, **kwargs)


class DatabaseTransactionError(DatabaseError):
    """Error in database transaction management."""
    
    def __init__(
        self,
        message: str,
        transaction_id: Optional[str] = None,
        transaction_state: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if transaction_id:
            details["transaction_id"] = transaction_id
        if transaction_state:
            details["transaction_state"] = transaction_state
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_TRANSACTION_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Database transaction failed')
        
        super().__init__(message, **kwargs)


class DatabaseMigrationError(DatabaseError):
    """Error during database migration."""
    
    def __init__(
        self,
        message: str,
        migration_version: Optional[str] = None,
        migration_file: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if migration_version:
            details["migration_version"] = migration_version
        if migration_file:
            details["migration_file"] = migration_file
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_MIGRATION_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.CRITICAL)
        kwargs.setdefault('user_message', 'Database migration failed')
        
        super().__init__(message, **kwargs)


class DatabasePoolError(DatabaseError):
    """Error in database connection pool management."""
    
    def __init__(
        self,
        message: str,
        pool_size: Optional[int] = None,
        active_connections: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if pool_size:
            details["pool_size"] = pool_size
        if active_connections:
            details["active_connections"] = active_connections
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_POOL_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Database connection pool error')
        
        super().__init__(message, **kwargs)


class DatabaseLockError(DatabaseError):
    """Error related to database locking."""
    
    def __init__(
        self,
        message: str,
        lock_type: Optional[str] = None,
        resource_name: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if lock_type:
            details["lock_type"] = lock_type
        if resource_name:
            details["resource_name"] = resource_name
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_LOCK_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', 'Database lock conflict')
        
        super().__init__(message, **kwargs)


class DatabaseSchemaError(DatabaseError):
    """Error related to database schema."""
    
    def __init__(
        self,
        message: str,
        schema_name: Optional[str] = None,
        expected_version: Optional[str] = None,
        actual_version: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if schema_name:
            details["schema_name"] = schema_name
        if expected_version:
            details["expected_version"] = expected_version
        if actual_version:
            details["actual_version"] = actual_version
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_SCHEMA_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Database schema error')
        
        super().__init__(message, **kwargs)


class DatabasePermissionError(DatabaseError):
    """Error related to database permissions."""
    
    def __init__(
        self,
        message: str,
        required_permission: Optional[str] = None,
        database_user: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if required_permission:
            details["required_permission"] = required_permission
        if database_user:
            details["database_user"] = database_user
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_PERMISSION_ERROR')
        kwargs.setdefault('category', ErrorCategory.AUTHORIZATION)
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Database permission denied')
        
        super().__init__(message, **kwargs)


class DatabaseBackupError(DatabaseError):
    """Error during database backup operations."""
    
    def __init__(
        self,
        message: str,
        backup_type: Optional[str] = None,
        backup_location: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if backup_type:
            details["backup_type"] = backup_type
        if backup_location:
            details["backup_location"] = backup_location
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_BACKUP_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', 'Database backup failed')
        
        super().__init__(message, **kwargs)


class DatabaseRestoreError(DatabaseError):
    """Error during database restore operations."""
    
    def __init__(
        self,
        message: str,
        backup_file: Optional[str] = None,
        restore_point: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if backup_file:
            details["backup_file"] = backup_file
        if restore_point:
            details["restore_point"] = restore_point
        
        kwargs['details'] = details
        kwargs.setdefault('error_code', 'DATABASE_RESTORE_ERROR')
        kwargs.setdefault('severity', ErrorSeverity.CRITICAL)
        kwargs.setdefault('user_message', 'Database restore failed')
        
        super().__init__(message, **kwargs)
