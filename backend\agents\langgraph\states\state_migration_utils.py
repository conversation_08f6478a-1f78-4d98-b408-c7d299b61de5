"""
State Migration Utilities for LangGraph State Unification.

This module provides utilities to transform between legacy state formats
and the new UnifiedDatageniusState during the migration process.
"""

import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import uuid

from .unified_state import UnifiedDatageniusState, create_unified_state, WorkflowStatus, ConversationStage, AgentRole

logger = logging.getLogger(__name__)


def migrate_datagenius_agent_state(legacy_state: Dict[str, Any]) -> UnifiedDatageniusState:
    """
    Migrate DatageniusAgentState to UnifiedDatageniusState.
    
    Args:
        legacy_state: Legacy DatageniusAgentState dictionary
        
    Returns:
        UnifiedDatageniusState instance
    """
    try:
        # Extract core identifiers
        user_id = legacy_state.get("user_id", "unknown")
        conversation_id = legacy_state.get("conversation_id", str(uuid.uuid4()))
        workflow_id = legacy_state.get("workflow_id", str(uuid.uuid4()))
        
        # Create base unified state
        unified_state = create_unified_state(
            user_id=user_id,
            conversation_id=conversation_id,
            workflow_type="migrated_legacy",
            business_profile_id=legacy_state.get("business_profile_id")
        )
        
        # Override workflow_id if provided
        unified_state["workflow_id"] = workflow_id
        
        # Migrate message flow
        if "messages" in legacy_state:
            unified_state["messages"] = legacy_state["messages"]
            if legacy_state["messages"]:
                unified_state["current_message"] = legacy_state["messages"][-1]
                unified_state["current_message_id"] = legacy_state["messages"][-1].get("id")
        
        # Migrate agent coordination
        unified_state["current_agent"] = legacy_state.get("current_agent", "")
        unified_state["agent_history"] = legacy_state.get("agent_history", [])
        unified_state["agent_outputs"] = legacy_state.get("agent_outputs", {})
        
        # Migrate business context
        unified_state["business_context"] = legacy_state.get("business_context", {})
        
        # Migrate tool integration
        unified_state["tool_results"] = legacy_state.get("tool_results", {})
        unified_state["available_tools"] = legacy_state.get("available_tools", [])
        
        # Migrate user context
        unified_state["user_preferences"] = legacy_state.get("user_preferences", {})
        
        # Migrate routing analysis
        unified_state["routing_analysis"] = legacy_state.get("routing_analysis")
        
        # Migrate quality scores
        unified_state["quality_scores"] = legacy_state.get("quality_scores", {})
        unified_state["validation_results"] = legacy_state.get("validation_results", {})
        
        # Add migration metadata
        unified_state["workflow_context"]["migration_source"] = "DatageniusAgentState"
        unified_state["workflow_context"]["migration_timestamp"] = datetime.now().isoformat()
        
        logger.debug(f"Successfully migrated DatageniusAgentState for conversation {conversation_id}")
        return unified_state
        
    except Exception as e:
        logger.error(f"Error migrating DatageniusAgentState: {e}")
        # Return a minimal unified state as fallback
        return create_unified_state(
            user_id="unknown",
            conversation_id=str(uuid.uuid4()),
            workflow_type="migration_error"
        )


def migrate_workflow_state(legacy_state: Dict[str, Any]) -> UnifiedDatageniusState:
    """
    Migrate WorkflowState to UnifiedDatageniusState.
    
    Args:
        legacy_state: Legacy WorkflowState dictionary
        
    Returns:
        UnifiedDatageniusState instance
    """
    try:
        # First migrate the base DatageniusAgentState fields
        unified_state = migrate_datagenius_agent_state(legacy_state)
        
        # Migrate workflow-specific fields
        unified_state["workflow_type"] = legacy_state.get("workflow_type", "migrated_workflow")
        unified_state["workflow_phase"] = legacy_state.get("workflow_phase", "initialization")
        unified_state["current_step"] = legacy_state.get("current_step", 0)
        unified_state["total_steps"] = legacy_state.get("total_steps", 1)
        unified_state["step_history"] = legacy_state.get("step_history", [])
        unified_state["step_results"] = legacy_state.get("step_results", {})
        
        # Migrate workflow configuration
        workflow_config = legacy_state.get("workflow_config", {})
        unified_state["workflow_context"].update(workflow_config)
        
        # Migrate progress tracking
        unified_state["execution_metrics"]["progress_percentage"] = legacy_state.get("progress_percentage", 0.0)
        
        # Migrate resource management
        unified_state["workflow_context"]["allocated_resources"] = legacy_state.get("allocated_resources", {})
        unified_state["workflow_context"]["resource_usage"] = legacy_state.get("resource_usage", {})
        
        # Migrate checkpointing
        if "last_checkpoint" in legacy_state:
            unified_state["workflow_context"]["last_checkpoint"] = legacy_state["last_checkpoint"]
        if "checkpoint_data" in legacy_state:
            unified_state["workflow_context"]["checkpoint_data"] = legacy_state["checkpoint_data"]
        
        # Update migration metadata
        unified_state["workflow_context"]["migration_source"] = "WorkflowState"
        
        logger.debug(f"Successfully migrated WorkflowState for workflow {unified_state['workflow_id']}")
        return unified_state
        
    except Exception as e:
        logger.error(f"Error migrating WorkflowState: {e}")
        return migrate_datagenius_agent_state(legacy_state)


def migrate_collaboration_state(legacy_state: Dict[str, Any]) -> UnifiedDatageniusState:
    """
    Migrate CollaborationState to UnifiedDatageniusState.
    
    Args:
        legacy_state: Legacy CollaborationState dictionary
        
    Returns:
        UnifiedDatageniusState instance
    """
    try:
        # First migrate the base WorkflowState fields
        unified_state = migrate_workflow_state(legacy_state)
        
        # Migrate collaboration-specific fields
        unified_state["collaboration_mode"] = legacy_state.get("collaboration_mode", "single_agent")
        unified_state["participating_agents"] = legacy_state.get("participating_agents", {})
        unified_state["agent_capabilities"] = legacy_state.get("agent_capabilities", {})
        
        # Migrate agent coordination
        active_agents = legacy_state.get("active_agents", set())
        if isinstance(active_agents, set):
            unified_state["active_agents"] = active_agents
        else:
            unified_state["active_agents"] = set(active_agents) if active_agents else set()
        
        # Migrate shared context and intelligence
        unified_state["cross_agent_context"] = legacy_state.get("shared_knowledge_base", {})
        unified_state["shared_insights"] = legacy_state.get("cross_agent_insights", [])
        unified_state["agent_contributions"] = legacy_state.get("agent_contributions", {})
        
        # Migrate consensus and voting
        unified_state["pending_decisions"] = legacy_state.get("pending_decisions", [])
        unified_state["agent_votes"] = legacy_state.get("agent_votes", {})
        unified_state["consensus_threshold"] = legacy_state.get("consensus_threshold", 0.7)
        
        # Migrate quality assurance
        unified_state["peer_reviews"] = legacy_state.get("peer_reviews", {})
        unified_state["quality_gates"] = legacy_state.get("quality_gates", {})
        
        # Migrate inter-agent communication
        unified_state["inter_agent_messages"] = legacy_state.get("inter_agent_messages", [])
        
        # Update migration metadata
        unified_state["workflow_context"]["migration_source"] = "CollaborationState"
        
        logger.debug(f"Successfully migrated CollaborationState for workflow {unified_state['workflow_id']}")
        return unified_state
        
    except Exception as e:
        logger.error(f"Error migrating CollaborationState: {e}")
        return migrate_workflow_state(legacy_state)


def auto_migrate_state(legacy_state: Dict[str, Any]) -> UnifiedDatageniusState:
    """
    Automatically detect and migrate any legacy state format.
    
    Args:
        legacy_state: Legacy state dictionary
        
    Returns:
        UnifiedDatageniusState instance
    """
    try:
        # Detect state type based on fields
        if "collaboration_mode" in legacy_state or "participating_agents" in legacy_state:
            return migrate_collaboration_state(legacy_state)
        elif "workflow_type" in legacy_state or "workflow_phase" in legacy_state:
            return migrate_workflow_state(legacy_state)
        else:
            return migrate_datagenius_agent_state(legacy_state)
            
    except Exception as e:
        logger.error(f"Error in auto migration: {e}")
        # Return minimal unified state as ultimate fallback
        return create_unified_state(
            user_id="unknown",
            conversation_id=str(uuid.uuid4()),
            workflow_type="auto_migration_fallback"
        )


def validate_migrated_state(state: UnifiedDatageniusState) -> bool:
    """
    Validate that a migrated state is properly formed.
    
    Args:
        state: Migrated UnifiedDatageniusState
        
    Returns:
        True if state is valid
    """
    try:
        required_fields = [
            "workflow_id", "conversation_id", "user_id",
            "messages", "current_agent", "workflow_status",
            "workflow_type", "conversation_stage"
        ]
        
        for field in required_fields:
            if field not in state:
                logger.error(f"Missing required field in migrated state: {field}")
                return False
        
        # Validate field types
        if not isinstance(state["messages"], list):
            logger.error("Messages field must be a list")
            return False
        
        if not isinstance(state["agent_outputs"], dict):
            logger.error("Agent outputs field must be a dict")
            return False
        
        logger.debug("Migrated state validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Error validating migrated state: {e}")
        return False


# Backward compatibility functions for gradual migration
def ensure_unified_state(state: Union[Dict[str, Any], UnifiedDatageniusState]) -> UnifiedDatageniusState:
    """
    Ensure the provided state is a UnifiedDatageniusState.
    
    Args:
        state: State that might be legacy or unified
        
    Returns:
        UnifiedDatageniusState instance
    """
    if isinstance(state, dict) and "workflow_id" in state and "conversation_stage" in state:
        # Already a UnifiedDatageniusState
        return state
    else:
        # Legacy state, migrate it
        return auto_migrate_state(state)
