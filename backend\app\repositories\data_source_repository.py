"""
Data Source Repository Implementation.

Provides specialized repository operations for DataSource entities,
replacing the data source-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import DataSource
from ..models.schemas import DataSourceCreate, DataSourceUpdate, DataSourceResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class DataSourceRepository(BaseRepository[DataSource]):
    """Repository for DataSource entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, DataSource)
        self.logger = logger
    
    def create_data_source(
        self,
        user_id: int,
        name: str,
        source_type: str,
        source_metadata: Dict[str, Any],
        description: Optional[str] = None
    ) -> DataSource:
        """
        Create a new data source.
        
        Args:
            user_id: ID of the user who owns the data source
            name: Name of the data source
            source_type: Type of the data source (file, database, api, mcp)
            source_metadata: Metadata specific to the source type
            description: Optional description
            
        Returns:
            Created data source instance
            
        Raises:
            RepositoryError: If data source creation fails
        """
        try:
            data_source_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'name': name,
                'type': source_type,
                'source_metadata': source_metadata,
                'description': description
            }
            
            data_source = DataSource(**data_source_data)
            
            self.session.add(data_source)
            self.session.commit()
            self.session.refresh(data_source)
            
            self.logger.info(f"Created data source {data_source.id} for user {user_id}: {name}")
            return data_source
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create data source: {e}")
            raise RepositoryError(
                f"Failed to create data source: {str(e)}",
                entity_type="DataSource",
                operation="create"
            )
    
    def get_user_data_sources(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        source_type: Optional[str] = None
    ) -> List[DataSource]:
        """
        Get data sources for a user with optional type filtering.
        
        Args:
            user_id: ID of the user
            skip: Number of records to skip
            limit: Maximum number of records to return
            source_type: Optional type filter
            
        Returns:
            List of data sources owned by the user
        """
        try:
            query = self.session.query(DataSource).filter(DataSource.user_id == user_id)
            
            if source_type:
                query = query.filter(DataSource.type == source_type)
            
            return query.order_by(DataSource.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get user data sources: {e}")
            raise RepositoryError(
                f"Failed to get user data sources: {str(e)}",
                entity_type="DataSource",
                operation="get_user_data_sources"
            )
    
    def update_data_source(
        self,
        data_source_id: str,
        update_data: Dict[str, Any]
    ) -> Optional[DataSource]:
        """
        Update a data source.
        
        Args:
            data_source_id: ID of the data source to update
            update_data: Dictionary containing update data
            
        Returns:
            Updated data source or None if not found
        """
        try:
            data_source = self.get_by_id(data_source_id)
            if not data_source:
                self.logger.warning(f"Data source {data_source_id} not found for update")
                return None
            
            # Update allowed fields
            allowed_fields = ['name', 'description', 'source_metadata']
            for key, value in update_data.items():
                if key in allowed_fields:
                    setattr(data_source, key, value)
            
            self.session.commit()
            self.session.refresh(data_source)
            
            self.logger.info(f"Updated data source {data_source_id}")
            return data_source
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update data source: {e}")
            raise RepositoryError(
                f"Failed to update data source: {str(e)}",
                entity_type="DataSource",
                operation="update"
            )
    
    def delete_data_source(self, data_source_id: str) -> bool:
        """
        Delete a data source.
        
        Args:
            data_source_id: ID of the data source to delete
            
        Returns:
            True if data source was deleted, False if not found
            
        Raises:
            RepositoryError: If deletion fails
        """
        try:
            data_source = self.get_by_id(data_source_id)
            if not data_source:
                self.logger.warning(f"Data source {data_source_id} not found for deletion")
                return False
            
            self.session.delete(data_source)
            self.session.commit()
            
            self.logger.info(f"Deleted data source {data_source_id}")
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to delete data source: {e}")
            raise RepositoryError(
                f"Failed to delete data source: {str(e)}",
                entity_type="DataSource",
                operation="delete"
            )
    
    def get_data_sources_by_file_id(
        self,
        file_id: str,
        user_id: int
    ) -> List[DataSource]:
        """
        Get data sources that use a specific file.
        
        Args:
            file_id: ID of the file
            user_id: ID of the user (for security)
            
        Returns:
            List of data sources using the file
        """
        try:
            return self.session.query(DataSource).filter(
                DataSource.user_id == user_id,
                DataSource.type == "file",
                DataSource.source_metadata.op('->>')('file_id') == file_id
            ).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get data sources by file ID: {e}")
            raise RepositoryError(
                f"Failed to get data sources by file ID: {str(e)}",
                entity_type="DataSource",
                operation="get_data_sources_by_file_id"
            )
    
    def get_data_sources_by_type(
        self,
        source_type: str,
        user_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[DataSource]:
        """
        Get data sources by type, optionally filtered by user.
        
        Args:
            source_type: Type of data sources to retrieve
            user_id: Optional user ID filter
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of data sources with the specified type
        """
        try:
            query = self.session.query(DataSource).filter(DataSource.type == source_type)
            
            if user_id:
                query = query.filter(DataSource.user_id == user_id)
            
            return query.order_by(DataSource.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get data sources by type: {e}")
            raise RepositoryError(
                f"Failed to get data sources by type: {str(e)}",
                entity_type="DataSource",
                operation="get_data_sources_by_type"
            )
    
    def update_data_source_metadata(
        self,
        data_source_id: str,
        metadata_updates: Dict[str, Any]
    ) -> Optional[DataSource]:
        """
        Update data source metadata.
        
        Args:
            data_source_id: ID of the data source
            metadata_updates: Dictionary of metadata updates
            
        Returns:
            Updated data source or None if not found
        """
        try:
            data_source = self.get_by_id(data_source_id)
            if not data_source:
                self.logger.warning(f"Data source {data_source_id} not found for metadata update")
                return None
            
            # Merge with existing metadata
            existing_metadata = data_source.source_metadata or {}
            existing_metadata.update(metadata_updates)
            data_source.source_metadata = existing_metadata
            
            self.session.commit()
            self.session.refresh(data_source)
            
            self.logger.info(f"Updated metadata for data source {data_source_id}")
            return data_source
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update data source metadata: {e}")
            raise RepositoryError(
                f"Failed to update data source metadata: {str(e)}",
                entity_type="DataSource",
                operation="update_metadata"
            )
    
    def get_data_source_count_by_user(self, user_id: int) -> int:
        """
        Get the count of data sources for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Number of data sources owned by the user
        """
        try:
            return self.session.query(DataSource).filter(
                DataSource.user_id == user_id
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get data source count: {e}")
            raise RepositoryError(
                f"Failed to get data source count: {str(e)}",
                entity_type="DataSource",
                operation="get_data_source_count_by_user"
            )
    
    def get_data_source_count_by_type(self, source_type: str) -> int:
        """
        Get the count of data sources by type.
        
        Args:
            source_type: Type to count
            
        Returns:
            Number of data sources with the specified type
        """
        try:
            return self.session.query(DataSource).filter(
                DataSource.type == source_type
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get data source count by type: {e}")
            raise RepositoryError(
                f"Failed to get data source count by type: {str(e)}",
                entity_type="DataSource",
                operation="get_data_source_count_by_type"
            )
    
    def search_data_sources(
        self,
        user_id: int,
        search_term: str,
        source_type: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[DataSource]:
        """
        Search data sources by name or description.
        
        Args:
            user_id: ID of the user
            search_term: Term to search for
            source_type: Optional type filter
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of matching data sources
        """
        try:
            query = self.session.query(DataSource).filter(
                DataSource.user_id == user_id
            ).filter(
                (DataSource.name.ilike(f"%{search_term}%")) |
                (DataSource.description.ilike(f"%{search_term}%"))
            )
            
            if source_type:
                query = query.filter(DataSource.type == source_type)
            
            return query.order_by(DataSource.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to search data sources: {e}")
            raise RepositoryError(
                f"Failed to search data sources: {str(e)}",
                entity_type="DataSource",
                operation="search_data_sources"
            )
