"""
Unified State Schema for LangGraph-based Datagenius System.

This module provides the single, comprehensive state schema that replaces
all existing state management systems in Datagenius, including:
- DatageniusAgentState
- ConversationState  
- WorkflowState
- CollaborationState
- Context management systems
- Cross-agent intelligence state

The unified state provides a single source of truth for all workflow
and agent coordination needs.
"""

from typing import TypedDict, Annotated, List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
from enum import Enum
import uuid
import operator
import logging
import sys
import gc
import weakref
from collections import deque

# Initialize module-level logger
logger = logging.getLogger(__name__)


class WorkflowStatus(str, Enum):
    """Workflow execution status."""
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ConversationStage(str, Enum):
    """Conversation stage tracking."""
    INITIAL = "initial"
    INTENT_ANALYSIS = "intent_analysis"
    PERSONA_SELECTION = "persona_selection"
    DATA_ATTACHMENT = "data_attachment"
    TASK_EXECUTION = "task_execution"
    COLLABORATION = "collaboration"
    REVIEW = "review"
    COMPLETED = "completed"


class AgentRole(str, Enum):
    """Agent roles in collaboration."""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    REVIEWER = "reviewer"
    COORDINATOR = "coordinator"
    SPECIALIST = "specialist"


class MessageType(str, Enum):
    """Message types in the system."""
    USER = "user"
    AGENT = "agent"
    SYSTEM = "system"
    TOOL = "tool"
    INTER_AGENT = "inter_agent"


class ToolStatus(str, Enum):
    """Tool execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class ConversationMode(str, Enum):
    """Conversation mode tracking for agent routing."""
    INITIAL = "initial"
    GREETING = "greeting"
    CONVERSATION = "conversation"
    AGENT_SWITCH = "agent_switch"
    HANDOFF = "handoff"


class UnifiedDatageniusState(TypedDict):
    """
    Unified state schema for all Datagenius LangGraph workflows.

    This state consolidates all previous state management systems into
    a single, comprehensive schema that handles:
    - Message flow and conversation management
    - Agent coordination and routing with Command pattern support
    - Business context and cross-agent intelligence
    - Workflow orchestration and task management
    - Tool integration and execution
    - Performance monitoring and error handling
    - User context and preferences
    - Collaboration and consensus building
    """

    # === CORE IDENTIFICATION ===
    workflow_id: str
    conversation_id: str
    user_id: str
    session_id: Optional[str]

    # === MESSAGE FLOW ===
    messages: Annotated[List[Dict[str, Any]], operator.add]
    current_message: Optional[Dict[str, Any]]
    current_message_id: Optional[str]
    message_history: Annotated[List[Dict[str, Any]], operator.add]

    # === AGENT COORDINATION & ROUTING ===
    current_agent: str
    selected_agent: Optional[str]  # Agent selected by user/routing logic
    conversation_mode: ConversationMode  # Track conversation state for routing
    agent_context: Dict[str, Any]  # Agent-specific context preservation
    agent_command: Optional[Any]  # Command object for LangGraph routing
    agent_history: Annotated[List[str], operator.add]
    agent_outputs: Dict[str, Any]
    agent_roles: Dict[str, AgentRole]
    active_agents: Set[str]
    available_agents: List[str]
    
    # === WORKFLOW MANAGEMENT ===
    workflow_status: WorkflowStatus
    workflow_type: str
    workflow_phase: str
    current_step: int
    total_steps: int
    step_history: Annotated[List[Dict[str, Any]], operator.add]
    step_results: Dict[str, Any]
    workflow_plan: Optional[Dict[str, Any]]
    task_dependencies: Dict[str, List[str]]
    
    # === CONVERSATION STATE ===
    conversation_stage: ConversationStage
    conversation_context: Dict[str, Any]
    conversation_metadata: Dict[str, Any]
    
    # === BUSINESS CONTEXT ===
    business_profile_id: Optional[str]
    business_context: Dict[str, Any]
    business_metadata: Dict[str, Any]
    
    # === CROSS-AGENT INTELLIGENCE ===
    shared_insights: Annotated[List[Dict[str, Any]], operator.add]
    cross_agent_context: Dict[str, Any]
    collaboration_opportunities: List[str]
    agent_contributions: Dict[str, List[Dict[str, Any]]]
    
    # === TOOL INTEGRATION ===
    available_tools: List[str]
    tool_results: Dict[str, Any]
    tool_execution_history: Annotated[List[Dict[str, Any]], operator.add]
    tool_status: Dict[str, ToolStatus]
    mcp_tools: Dict[str, Any]
    
    # === DATA MANAGEMENT ===
    attached_files: List[Dict[str, Any]]
    data_sources: List[Dict[str, Any]]
    processed_data: Dict[str, Any]
    data_context: Dict[str, Any]
    
    # === USER CONTEXT ===
    user_preferences: Dict[str, Any]
    user_profile: Dict[str, Any]
    user_history: Dict[str, Any]
    user_intent: Optional[Dict[str, Any]]
    
    # === ROUTING AND ANALYSIS ===
    routing_analysis: Optional[Dict[str, Any]]
    intent_analysis: Optional[Dict[str, Any]]
    complexity_analysis: Optional[Dict[str, Any]]
    
    # === COLLABORATION ===
    collaboration_mode: str
    participating_agents: Dict[str, AgentRole]
    agent_capabilities: Dict[str, List[str]]
    pending_decisions: List[Dict[str, Any]]
    agent_votes: Dict[str, Dict[str, Any]]
    consensus_threshold: float
    inter_agent_messages: Annotated[List[Dict[str, Any]], operator.add]
    
    # === QUALITY ASSURANCE ===
    quality_scores: Dict[str, float]
    validation_results: Dict[str, Any]
    peer_reviews: Dict[str, List[Dict[str, Any]]]
    quality_gates: Dict[str, Dict[str, Any]]
    
    # === PERFORMANCE MONITORING ===
    execution_metrics: Dict[str, Any]
    performance_data: Dict[str, Any]
    timing_data: Dict[str, float]
    resource_usage: Dict[str, Any]
    
    # === ERROR HANDLING ===
    error_history: Annotated[List[Dict[str, Any]], operator.add]
    warnings: Annotated[List[Dict[str, Any]], operator.add]
    recovery_actions: List[Dict[str, Any]]
    
    # === CONTEXT MANAGEMENT ===
    context_version: int
    context_history: Annotated[List[Dict[str, Any]], operator.add]
    tracked_entities: Dict[str, Any]
    entity_relationships: Dict[str, List[str]]
    
    # === STREAMING AND REAL-TIME ===
    streaming_enabled: bool
    real_time_updates: List[Dict[str, Any]]
    websocket_connections: List[str]
    
    # === CONFIGURATION ===
    agent_configs: Dict[str, Dict[str, Any]]
    workflow_config: Dict[str, Any]
    feature_flags: Dict[str, bool]
    
    # === METADATA ===
    created_at: str
    updated_at: str
    version: str
    tags: List[str]
    custom_metadata: Dict[str, Any]


def create_unified_state(
    user_id: str,
    conversation_id: str,
    workflow_type: str = "default",
    initial_message: Optional[Dict[str, Any]] = None,
    business_profile_id: Optional[str] = None,
    agent_configs: Optional[Dict[str, Dict[str, Any]]] = None,
    **kwargs
) -> UnifiedDatageniusState:
    """
    Create a new unified state for a LangGraph workflow.

    Args:
        user_id: User identifier
        conversation_id: Conversation identifier
        workflow_type: Type of workflow being created
        initial_message: Optional initial message to include
        business_profile_id: Optional business profile context
        agent_configs: Optional agent configurations
        **kwargs: Additional state fields to override defaults

    Returns:
        Initialized UnifiedDatageniusState
    """
    workflow_id = str(uuid.uuid4())
    current_time = datetime.now().isoformat()

    # Create the base state
    state: UnifiedDatageniusState = {
        # === CORE IDENTIFICATION ===
        "workflow_id": workflow_id,
        "conversation_id": conversation_id,
        "user_id": user_id,
        "session_id": kwargs.get("session_id"),

        # === MESSAGE FLOW ===
        "messages": [initial_message] if initial_message else [],
        "current_message": initial_message,
        "current_message_id": initial_message.get("id") if initial_message else None,
        "message_history": [],

        # === AGENT COORDINATION & ROUTING ===
        "current_agent": "",
        "selected_agent": None,
        "conversation_mode": ConversationMode.INITIAL,
        "agent_context": {},
        "agent_command": None,
        "agent_history": [],
        "agent_outputs": {},
        "agent_roles": {},
        "active_agents": set(),
        "available_agents": [],

        # === WORKFLOW MANAGEMENT ===
        "workflow_status": WorkflowStatus.CREATED,
        "workflow_type": workflow_type,
        "workflow_phase": "initialization",
        "current_step": 0,
        "total_steps": 1,
        "step_history": [],
        "step_results": {},
        "workflow_plan": None,
        "task_dependencies": {},

        # === CONVERSATION STATE ===
        "conversation_stage": ConversationStage.INITIAL,
        "conversation_context": {},
        "conversation_metadata": {},

        # === BUSINESS CONTEXT ===
        "business_profile_id": business_profile_id,
        "business_context": {},
        "business_metadata": {},

        # === CROSS-AGENT INTELLIGENCE ===
        "shared_insights": [],
        "cross_agent_context": {},
        "collaboration_opportunities": [],
        "agent_contributions": {},

        # === TOOL INTEGRATION ===
        "available_tools": [],
        "tool_results": {},
        "tool_execution_history": [],
        "tool_status": {},
        "mcp_tools": {},

        # === DATA MANAGEMENT ===
        "attached_files": [],
        "data_sources": [],
        "processed_data": {},
        "data_context": {},

        # === USER CONTEXT ===
        "user_preferences": {},
        "user_profile": {},
        "user_history": {},
        "user_intent": None,

        # === ROUTING AND ANALYSIS ===
        "routing_analysis": None,
        "intent_analysis": None,
        "complexity_analysis": None,

        # === COLLABORATION ===
        "collaboration_mode": "single_agent",
        "participating_agents": {},
        "agent_capabilities": {},
        "pending_decisions": [],
        "agent_votes": {},
        "consensus_threshold": 0.7,
        "inter_agent_messages": [],

        # === QUALITY ASSURANCE ===
        "quality_scores": {},
        "validation_results": {},
        "peer_reviews": {},
        "quality_gates": {},

        # === PERFORMANCE MONITORING ===
        "execution_metrics": {
            "start_time": current_time,
            "agent_transitions": 0,
            "tool_executions": 0,
            "total_processing_time": 0.0,
            "message_count": 1 if initial_message else 0
        },
        "performance_data": {},
        "timing_data": {},
        "resource_usage": {},

        # === ERROR HANDLING ===
        "error_history": [],
        "warnings": [],
        "recovery_actions": [],

        # === CONTEXT MANAGEMENT ===
        "context_version": 1,
        "context_history": [],
        "tracked_entities": {},
        "entity_relationships": {},

        # === STREAMING AND REAL-TIME ===
        "streaming_enabled": False,
        "real_time_updates": [],
        "websocket_connections": [],

        # === CONFIGURATION ===
        "agent_configs": agent_configs or {},
        "workflow_config": {},
        "feature_flags": {},

        # === METADATA ===
        "created_at": current_time,
        "updated_at": current_time,
        "version": "1.0.0",
        "tags": [workflow_type],
        "custom_metadata": {}
    }

    # Apply any additional kwargs
    for key, value in kwargs.items():
        if key in state:
            state[key] = value

    return state


def update_agent_transition(
    state: UnifiedDatageniusState,
    new_agent: str,
    role: AgentRole = AgentRole.PRIMARY
) -> UnifiedDatageniusState:
    """
    Update state when transitioning to a new agent.

    Args:
        state: Current state
        new_agent: New agent identifier
        role: Role of the new agent

    Returns:
        Updated state
    """
    # Record previous agent in history
    if state["current_agent"]:
        state["agent_history"].append(state["current_agent"])

    # Update current agent
    state["current_agent"] = new_agent
    state["agent_roles"][new_agent] = role
    state["active_agents"].add(new_agent)

    # Update metrics (safely handle missing execution_metrics)
    if "execution_metrics" not in state:
        state["execution_metrics"] = {
            "message_count": 0,
            "agent_execution_count": 0,
            "agent_transitions": 0,
            "tool_executions": 0,
            "total_processing_time": 0.0,
            "errors_encountered": 0
        }

    if "agent_transitions" not in state["execution_metrics"]:
        state["execution_metrics"]["agent_transitions"] = 0

    state["execution_metrics"]["agent_transitions"] += 1
    state["updated_at"] = datetime.now().isoformat()

    return state


def set_selected_agent(
    state: UnifiedDatageniusState,
    agent_id: str,
    conversation_mode: ConversationMode = ConversationMode.CONVERSATION,
    agent_context: Optional[Dict[str, Any]] = None
) -> UnifiedDatageniusState:
    """
    Set the selected agent and update conversation mode for proper routing.

    This function implements the LangGraph Command pattern for agent persistence.

    Args:
        state: Current state
        agent_id: Agent to select and persist
        conversation_mode: Conversation mode for routing logic
        agent_context: Optional agent-specific context

    Returns:
        Updated state with selected agent persistence
    """
    # Set selected agent for routing persistence
    state["selected_agent"] = agent_id
    state["conversation_mode"] = conversation_mode

    # Update agent context
    if agent_context:
        # Ensure agent_context exists in state
        if "agent_context" not in state:
            state["agent_context"] = {}
        state["agent_context"][agent_id] = agent_context

    # Ensure agent is in active agents
    state["active_agents"].add(agent_id)

    # Update metadata
    state["updated_at"] = datetime.now().isoformat()

    return state


def get_routing_target(state: UnifiedDatageniusState) -> Optional[str]:
    """
    Get the target agent for routing based on user-centric LangGraph architecture.

    This function implements the simplified routing logic that prioritizes
    user selection over automatic routing analysis, as per workflow.md.

    Args:
        state: Current workflow state containing agent selection information

    Returns:
        Target agent ID or None if no routing target

    Raises:
        ValueError: If state parameter is invalid

    Example:
        >>> state = {"selected_agent": "marketing"}
        >>> get_routing_target(state)
        "marketing"
    """
    # Validate state parameter
    if state is None or not isinstance(state, dict):
        logger.warning("🚨 get_routing_target: Invalid state parameter")
        return None

    # Priority 1: ALWAYS respect user's selected agent
    selected_agent = state.get("selected_agent")
    if selected_agent:
        logger.info(f"✅ get_routing_target: Using selected agent: {selected_agent}")
        return selected_agent

    # Priority 2: Only use Command pattern for explicit handoffs
    agent_command = state.get("agent_command")
    if agent_command and hasattr(agent_command, 'goto'):
        logger.info(f"🎯 get_routing_target: Using Command pattern routing to: {agent_command.goto}")
        return agent_command.goto

    # Remove automatic message-based routing
    # No fallback to routing analysis - user selection takes precedence
    logger.info("🎯 get_routing_target: No routing target found (user-centric architecture)")
    return None


def should_route_to_selected_agent(state: UnifiedDatageniusState) -> bool:
    """
    Determine if routing should go to the selected agent.

    This prevents routing fallback to default agents when a specific
    agent has been selected by the user.

    Args:
        state: Current state

    Returns:
        True if should route to selected agent, False otherwise
    """
    # Validate state parameter
    if state is None or not isinstance(state, dict):
        return False

    # Route to selected agent in conversation mode
    if (state.get("conversation_mode") == ConversationMode.CONVERSATION and
        state.get("selected_agent")):
        return True

    # Route to selected agent during handoffs
    if (state.get("conversation_mode") == ConversationMode.HANDOFF and
        state.get("selected_agent")):
        return True

    return False


def add_message(
    state: UnifiedDatageniusState,
    message: Dict[str, Any],
    message_type: MessageType = MessageType.USER
) -> UnifiedDatageniusState:
    """
    Add a new message to the state.

    Args:
        state: Current state
        message: Message to add
        message_type: Type of message

    Returns:
        Updated state
    """
    # Ensure message has required fields
    if "id" not in message:
        message["id"] = str(uuid.uuid4())
    if "timestamp" not in message:
        message["timestamp"] = datetime.now().isoformat()
    if "type" not in message:
        message["type"] = message_type.value

    # Add to messages
    state["messages"].append(message)
    state["current_message"] = message
    state["current_message_id"] = message["id"]  # Set current_message_id for workflow termination logic

    # Update metrics (safely handle missing execution_metrics)
    if "execution_metrics" not in state:
        state["execution_metrics"] = {
            "message_count": 0,
            "agent_execution_count": 0,
            "total_processing_time": 0.0,
            "errors_encountered": 0
        }

    state["execution_metrics"]["message_count"] += 1
    state["updated_at"] = datetime.now().isoformat()

    return state


def update_tool_status(
    state: UnifiedDatageniusState,
    tool_name: str,
    status: ToolStatus,
    result: Optional[Any] = None
) -> UnifiedDatageniusState:
    """
    Update tool execution status.

    Args:
        state: Current state
        tool_name: Name of the tool
        status: New status
        result: Optional tool result

    Returns:
        Updated state
    """
    state["tool_status"][tool_name] = status

    if result is not None:
        state["tool_results"][tool_name] = result

    # Record execution history
    execution_record = {
        "tool_name": tool_name,
        "status": status.value,
        "timestamp": datetime.now().isoformat(),
        "agent": state["current_agent"]
    }
    if result is not None:
        execution_record["result"] = result

    state["tool_execution_history"].append(execution_record)

    # Update metrics (safely handle missing execution_metrics)
    if status == ToolStatus.COMPLETED:
        if "execution_metrics" not in state:
            state["execution_metrics"] = {
                "message_count": 0,
                "agent_execution_count": 0,
                "agent_transitions": 0,
                "tool_executions": 0,
                "total_processing_time": 0.0,
                "errors_encountered": 0
            }

        if "tool_executions" not in state["execution_metrics"]:
            state["execution_metrics"]["tool_executions"] = 0

        state["execution_metrics"]["tool_executions"] += 1

    state["updated_at"] = datetime.now().isoformat()

    return state


def add_cross_agent_insight(
    state: UnifiedDatageniusState,
    insight: Dict[str, Any],
    source_agent: str,
    target_agents: Optional[List[str]] = None,
    insight_type: str = "general",
    priority: int = 1,
    relevance_score: float = 1.0
) -> UnifiedDatageniusState:
    """
    Add a cross-agent intelligence insight with enhanced metadata.

    Args:
        state: Current state
        insight: Insight data
        source_agent: Agent that generated the insight
        target_agents: Optional list of specific target agents
        insight_type: Type of insight (general, data, marketing, analysis, etc.)
        priority: Priority level (1=low, 2=medium, 3=high, 4=critical)
        relevance_score: Relevance score (0.0-1.0)

    Returns:
        Updated state
    """
    # Ensure insight has required fields
    if "id" not in insight:
        insight["id"] = str(uuid.uuid4())
    if "timestamp" not in insight:
        insight["timestamp"] = datetime.now().isoformat()
    if "source_agent" not in insight:
        insight["source_agent"] = source_agent
    if "business_profile_id" not in insight:
        insight["business_profile_id"] = state["business_profile_id"]

    # Add enhanced metadata
    insight.update({
        "target_agents": target_agents or [],
        "insight_type": insight_type,
        "priority": priority,
        "relevance_score": relevance_score,
        "context_version": state["context_version"],
        "workflow_id": state["workflow_id"]
    })

    # Add to shared insights
    state["shared_insights"].append(insight)

    # Track agent contributions
    if source_agent not in state["agent_contributions"]:
        state["agent_contributions"][source_agent] = []
    state["agent_contributions"][source_agent].append(insight)

    # Update cross-agent context with new insight
    if "recent_insights" not in state["cross_agent_context"]:
        state["cross_agent_context"]["recent_insights"] = []
    state["cross_agent_context"]["recent_insights"].append({
        "insight_id": insight["id"],
        "source_agent": source_agent,
        "insight_type": insight_type,
        "priority": priority,
        "timestamp": insight["timestamp"]
    })

    # Keep only recent insights in context (last 20)
    state["cross_agent_context"]["recent_insights"] = \
        state["cross_agent_context"]["recent_insights"][-20:]

    state["updated_at"] = datetime.now().isoformat()

    return state


def get_relevant_insights(
    state: UnifiedDatageniusState,
    target_agent: str,
    insight_types: Optional[List[str]] = None,
    min_priority: int = 1,
    min_relevance: float = 0.5,
    max_insights: int = 10
) -> List[Dict[str, Any]]:
    """
    Get relevant insights for a target agent.

    Args:
        state: Current state
        target_agent: Agent requesting insights
        insight_types: Optional filter by insight types
        min_priority: Minimum priority level
        min_relevance: Minimum relevance score
        max_insights: Maximum number of insights to return

    Returns:
        List of relevant insights
    """
    relevant_insights = []

    for insight in state["shared_insights"]:
        # Skip insights from the same agent
        if insight.get("source_agent") == target_agent:
            continue

        # Check business profile scope
        if insight.get("business_profile_id") != state["business_profile_id"]:
            continue

        # Check if insight is targeted to this agent
        target_agents = insight.get("target_agents", [])
        if target_agents and target_agent not in target_agents:
            continue

        # Filter by insight type
        if insight_types and insight.get("insight_type") not in insight_types:
            continue

        # Filter by priority and relevance
        if insight.get("priority", 1) < min_priority:
            continue
        if insight.get("relevance_score", 1.0) < min_relevance:
            continue

        relevant_insights.append(insight)

    # Sort by priority and relevance
    relevant_insights.sort(
        key=lambda x: (x.get("priority", 1), x.get("relevance_score", 1.0)),
        reverse=True
    )

    return relevant_insights[:max_insights]


def build_agent_consensus(
    state: UnifiedDatageniusState,
    decision_id: str,
    decision_data: Dict[str, Any],
    participating_agents: List[str],
    consensus_threshold: Optional[float] = None
) -> UnifiedDatageniusState:
    """
    Build consensus among participating agents for a decision.

    Args:
        state: Current state
        decision_id: Unique identifier for the decision
        decision_data: Data about the decision to be made
        participating_agents: List of agents that should participate
        consensus_threshold: Optional override for consensus threshold

    Returns:
        Updated state
    """
    threshold = consensus_threshold or state["consensus_threshold"]

    # Create decision record
    decision = {
        "id": decision_id,
        "data": decision_data,
        "participating_agents": participating_agents,
        "consensus_threshold": threshold,
        "votes": {},
        "status": "pending",
        "created_at": datetime.now().isoformat(),
        "created_by": state["current_agent"]
    }

    # Add to pending decisions
    state["pending_decisions"].append(decision)

    # Initialize votes for participating agents
    for agent in participating_agents:
        if agent not in state["agent_votes"]:
            state["agent_votes"][agent] = {}
        state["agent_votes"][agent][decision_id] = {
            "vote": None,
            "confidence": 0.0,
            "reasoning": "",
            "timestamp": None
        }

    state["updated_at"] = datetime.now().isoformat()

    return state


def record_agent_vote(
    state: UnifiedDatageniusState,
    decision_id: str,
    agent_id: str,
    vote: Any,
    confidence: float = 1.0,
    reasoning: str = ""
) -> UnifiedDatageniusState:
    """
    Record an agent's vote on a pending decision.

    Args:
        state: Current state
        decision_id: Decision identifier
        agent_id: Agent casting the vote
        vote: The vote value
        confidence: Confidence level (0.0-1.0)
        reasoning: Optional reasoning for the vote

    Returns:
        Updated state
    """
    # Record the vote
    if agent_id not in state["agent_votes"]:
        state["agent_votes"][agent_id] = {}

    state["agent_votes"][agent_id][decision_id] = {
        "vote": vote,
        "confidence": confidence,
        "reasoning": reasoning,
        "timestamp": datetime.now().isoformat()
    }

    # Update decision record
    for decision in state["pending_decisions"]:
        if decision["id"] == decision_id:
            decision["votes"][agent_id] = {
                "vote": vote,
                "confidence": confidence,
                "reasoning": reasoning,
                "timestamp": datetime.now().isoformat()
            }

            # Check if consensus is reached
            total_agents = len(decision["participating_agents"])
            votes_cast = len(decision["votes"])

            if votes_cast >= total_agents * decision["consensus_threshold"]:
                decision["status"] = "consensus_reached"
                decision["consensus_result"] = _calculate_consensus_result(decision["votes"])
                decision["completed_at"] = datetime.now().isoformat()

            break

    state["updated_at"] = datetime.now().isoformat()

    return state


def update_workflow_status(
    state: UnifiedDatageniusState,
    status: WorkflowStatus,
    phase: Optional[str] = None
) -> UnifiedDatageniusState:
    """
    Update workflow status and phase.

    Args:
        state: Current state
        status: New workflow status
        phase: Optional new phase

    Returns:
        Updated state
    """
    state["workflow_status"] = status

    if phase:
        state["workflow_phase"] = phase

    # Record status change
    status_change = {
        "timestamp": datetime.now().isoformat(),
        "previous_status": state.get("workflow_status"),
        "new_status": status.value,
        "phase": phase or state["workflow_phase"]
    }
    state["step_history"].append(status_change)

    state["updated_at"] = datetime.now().isoformat()

    return state


def load_business_profile_context(
    state: UnifiedDatageniusState,
    business_profile_id: str,
    include_data_sources: bool = True,
    include_insights: bool = True
) -> UnifiedDatageniusState:
    """
    Load business profile context into the state.

    Args:
        state: Current state
        business_profile_id: Business profile identifier
        include_data_sources: Whether to include data sources
        include_insights: Whether to include historical insights

    Returns:
        Updated state with business context
    """
    try:
        # Import here to avoid circular imports
        from app.services.business_profile_service import BusinessProfileService
        from app.database import get_db

        # Get database session
        db = next(get_db())
        service = BusinessProfileService(db)

        # Load business profile
        profile = service.get_business_profile(business_profile_id)
        if not profile:
            state["warnings"].append({
                "timestamp": datetime.now().isoformat(),
                "source": "business_profile_context",
                "message": f"Business profile {business_profile_id} not found"
            })
            return state

        # Update state with business context
        state["business_profile_id"] = business_profile_id
        state["business_context"] = {
            "profile_data": profile.model_dump(),
            "loaded_at": datetime.now().isoformat(),
            "context_version": state["context_version"]
        }

        # Load data sources if requested
        if include_data_sources:
            data_sources = service.get_profile_data_sources(business_profile_id)
            state["business_context"]["data_sources"] = [
                ds.model_dump() for ds in data_sources
            ]

        # Load historical insights if requested
        if include_insights:
            # Filter insights by business profile
            profile_insights = [
                insight for insight in state["shared_insights"]
                if insight.get("business_profile_id") == business_profile_id
            ]
            state["business_context"]["historical_insights"] = profile_insights[-50:]  # Last 50

        # Update cross-agent context with business info
        state["cross_agent_context"]["business_profile"] = {
            "id": business_profile_id,
            "name": profile.business_name,
            "industry": profile.industry,
            "description": profile.business_description,
            "loaded_at": datetime.now().isoformat()
        }

    except Exception as e:
        state["error_history"].append({
            "timestamp": datetime.now().isoformat(),
            "source": "business_profile_context",
            "error": str(e),
            "context": {"business_profile_id": business_profile_id}
        })

    state["updated_at"] = datetime.now().isoformat()
    return state


def share_business_context_across_agents(
    state: UnifiedDatageniusState,
    context_update: Dict[str, Any],
    source_agent: str
) -> UnifiedDatageniusState:
    """
    Share business context updates across all active agents.

    Args:
        state: Current state
        context_update: Business context update to share
        source_agent: Agent that generated the update

    Returns:
        Updated state
    """
    # Create context sharing insight
    context_insight = {
        "id": str(uuid.uuid4()),
        "timestamp": datetime.now().isoformat(),
        "source_agent": source_agent,
        "insight_type": "business_context_update",
        "priority": 2,  # Medium priority
        "relevance_score": 1.0,
        "business_profile_id": state["business_profile_id"],
        "content": context_update,
        "target_agents": list(state["active_agents"])
    }

    # Add to shared insights
    state["shared_insights"].append(context_insight)

    # Update business context
    if "updates" not in state["business_context"]:
        state["business_context"]["updates"] = []

    state["business_context"]["updates"].append({
        "timestamp": datetime.now().isoformat(),
        "source_agent": source_agent,
        "update": context_update
    })

    # Keep only recent updates (last 20)
    state["business_context"]["updates"] = state["business_context"]["updates"][-20:]

    state["updated_at"] = datetime.now().isoformat()
    return state


def get_agent_collaboration_opportunities(
    state: UnifiedDatageniusState,
    current_agent: str
) -> List[Dict[str, Any]]:
    """
    Get collaboration opportunities for the current agent.

    Args:
        state: Current state
        current_agent: Current agent identifier

    Returns:
        List of collaboration opportunities
    """
    opportunities = []

    # Get current agent capabilities
    current_capabilities = state["agent_capabilities"].get(current_agent, [])

    # Check other active agents for collaboration potential
    for agent_id in state["active_agents"]:
        if agent_id == current_agent:
            continue

        agent_capabilities = state["agent_capabilities"].get(agent_id, [])

        # Find complementary capabilities
        complementary_caps = []
        for cap in agent_capabilities:
            if cap not in current_capabilities:
                complementary_caps.append(cap)

        if complementary_caps:
            # Check recent contributions for collaboration potential
            recent_contributions = state["agent_contributions"].get(agent_id, [])
            recent_insights = [c for c in recent_contributions
                             if datetime.fromisoformat(c["timestamp"]) >
                             datetime.now() - timedelta(hours=1)]

            opportunity = {
                "agent_id": agent_id,
                "complementary_capabilities": complementary_caps,
                "recent_activity_level": len(recent_insights),
                "collaboration_type": _determine_collaboration_type(
                    current_capabilities, agent_capabilities
                ),
                "potential_value": _calculate_collaboration_value(
                    current_capabilities, agent_capabilities, recent_insights
                )
            }
            opportunities.append(opportunity)

    # Sort by potential value
    opportunities.sort(key=lambda x: x["potential_value"], reverse=True)

    return opportunities


def _calculate_consensus_result(votes: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate consensus result from agent votes.

    Args:
        votes: Dictionary of agent votes

    Returns:
        Consensus result with aggregated decision
    """
    if not votes:
        return {"result": None, "confidence": 0.0, "agreement_level": 0.0}

    # Simple majority voting with confidence weighting
    vote_counts = {}
    total_confidence = 0.0

    for _, vote_data in votes.items():
        vote = vote_data["vote"]
        confidence = vote_data["confidence"]

        if vote not in vote_counts:
            vote_counts[vote] = {"count": 0, "confidence": 0.0}

        vote_counts[vote]["count"] += 1
        vote_counts[vote]["confidence"] += confidence
        total_confidence += confidence

    # Find the winning vote
    winning_vote = max(vote_counts.items(), key=lambda x: x[1]["confidence"])

    return {
        "result": winning_vote[0],
        "confidence": winning_vote[1]["confidence"] / total_confidence if total_confidence > 0 else 0.0,
        "agreement_level": winning_vote[1]["count"] / len(votes),
        "vote_distribution": vote_counts
    }


def _determine_collaboration_type(
    caps1: List[str],
    caps2: List[str]
) -> str:
    """Determine the type of collaboration between two agents."""
    # Simple heuristic based on capability overlap
    overlap = set(caps1) & set(caps2)
    unique1 = set(caps1) - set(caps2)
    unique2 = set(caps2) - set(caps1)

    if len(overlap) > len(unique1) and len(overlap) > len(unique2):
        return "parallel"  # Similar capabilities, can work in parallel
    elif len(unique1) > 0 and len(unique2) > 0:
        return "sequential"  # Complementary capabilities, sequential workflow
    else:
        return "supportive"  # One agent supports the other


def _calculate_collaboration_value(
    caps1: List[str],
    caps2: List[str],
    recent_insights: List[Dict[str, Any]]
) -> float:
    """Calculate the potential value of collaboration."""
    # Base value from capability complementarity
    unique_caps = len(set(caps1) | set(caps2))
    base_value = unique_caps / 10.0  # Normalize to 0-1 range

    # Bonus for recent activity
    activity_bonus = min(len(recent_insights) / 5.0, 0.5)

    # Bonus for high-priority insights
    priority_bonus = sum(
        insight.get("priority", 1) for insight in recent_insights
    ) / (len(recent_insights) * 4.0) if recent_insights else 0.0

    return min(base_value + activity_bonus + priority_bonus, 1.0)


# === MEMORY OPTIMIZATION FUNCTIONS ===

class StateMemoryManager:
    """Manages memory usage and optimization for unified state."""

    def __init__(self, max_history_size: int = 1000, cleanup_threshold_mb: int = 100):
        self.max_history_size = max_history_size
        self.cleanup_threshold_mb = cleanup_threshold_mb
        self._state_refs = weakref.WeakSet()
        self._cleanup_count = 0

    def register_state(self, state: UnifiedDatageniusState) -> None:
        """Register a state for memory monitoring."""
        self._state_refs.add(state)

    def get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage statistics."""
        return {
            "total_memory_mb": sys.getsizeof(self._state_refs) / (1024 * 1024),
            "active_states": len(self._state_refs),
            "cleanup_count": self._cleanup_count,
            "gc_stats": {
                "collections": gc.get_stats(),
                "objects": len(gc.get_objects())
            }
        }

    def optimize_state_memory(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """Optimize memory usage of a state object."""
        # Limit history sizes
        if len(state["agent_history"]) > self.max_history_size:
            # Keep only recent history
            state["agent_history"] = state["agent_history"][-self.max_history_size:]

        if len(state["conversation_history"]) > self.max_history_size:
            # Keep only recent conversations
            state["conversation_history"] = state["conversation_history"][-self.max_history_size:]

        if len(state["workflow_history"]) > self.max_history_size:
            # Keep only recent workflow history
            state["workflow_history"] = state["workflow_history"][-self.max_history_size:]

        # Clean up old insights (keep only last 500)
        if len(state["insights"]) > 500:
            state["insights"] = state["insights"][-500:]

        # Clean up old execution metrics
        if "detailed_metrics" in state["execution_metrics"]:
            metrics = state["execution_metrics"]["detailed_metrics"]
            if isinstance(metrics, list) and len(metrics) > 100:
                state["execution_metrics"]["detailed_metrics"] = metrics[-100:]

        # Clean up old tool results
        if len(state["tool_results"]) > 50:
            # Keep only recent tool results
            recent_tools = dict(list(state["tool_results"].items())[-50:])
            state["tool_results"] = recent_tools

        self._cleanup_count += 1
        return state

    def force_garbage_collection(self) -> Dict[str, int]:
        """Force garbage collection and return statistics."""
        before_objects = len(gc.get_objects())
        collected = gc.collect()
        after_objects = len(gc.get_objects())

        return {
            "objects_before": before_objects,
            "objects_after": after_objects,
            "objects_collected": collected,
            "objects_freed": before_objects - after_objects
        }

    def should_cleanup(self) -> bool:
        """Check if memory cleanup should be triggered."""
        memory_usage = self.get_memory_usage()
        return memory_usage["total_memory_mb"] > self.cleanup_threshold_mb


# Global memory manager instance
memory_manager = StateMemoryManager()


def optimize_state_transition(
    state: UnifiedDatageniusState,
    new_agent: str,
    role: AgentRole = AgentRole.PRIMARY,
    optimize_memory: bool = True
) -> UnifiedDatageniusState:
    """
    Optimized version of update_agent_transition with memory management.

    Args:
        state: Current state
        new_agent: New agent identifier
        role: Role of the new agent
        optimize_memory: Whether to apply memory optimizations

    Returns:
        Updated and optimized state
    """
    # Perform the standard transition
    state = update_agent_transition(state, new_agent, role)

    # Apply memory optimizations if requested
    if optimize_memory:
        state = memory_manager.optimize_state_memory(state)

        # Force garbage collection if memory usage is high
        if memory_manager.should_cleanup():
            gc_stats = memory_manager.force_garbage_collection()
            logger.info(f"Memory cleanup triggered: {gc_stats}")

    return state


def get_state_memory_stats(state: UnifiedDatageniusState) -> Dict[str, Any]:
    """Get memory statistics for a specific state."""
    return {
        "state_size_bytes": sys.getsizeof(state),
        "state_size_mb": sys.getsizeof(state) / (1024 * 1024),
        "conversation_history_size": len(state["conversation_history"]),
        "agent_history_size": len(state["agent_history"]),
        "workflow_history_size": len(state["workflow_history"]),
        "insights_count": len(state["insights"]),
        "tool_results_count": len(state["tool_results"]),
        "active_agents_count": len(state["active_agents"]),
        "memory_manager_stats": memory_manager.get_memory_usage()
    }
