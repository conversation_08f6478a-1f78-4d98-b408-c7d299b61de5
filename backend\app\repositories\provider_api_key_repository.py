"""
Provider API Key Repository Implementation.

Provides specialized repository operations for ProviderApiKey entities,
replacing the provider API key-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional
from sqlalchemy.orm import Session

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import ProviderApiKey
from ..models.schemas import ProviderApiKeyCreate, ProviderApiKeyUpdate, ProviderApiKeyResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class ProviderApiKeyRepository(BaseRepository[ProviderApiKey]):
    """Repository for ProviderApiKey entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, ProviderApiKey)
        self.logger = logger
    
    def get_provider_api_key(
        self,
        user_id: int,
        provider_id: str
    ) -> Optional[ProviderApiKey]:
        """
        Get a provider API key for a user.
        
        Args:
            user_id: ID of the user
            provider_id: ID of the provider
            
        Returns:
            Provider API key or None if not found
        """
        try:
            return self.session.query(ProviderApiKey).filter(
                ProviderApiKey.user_id == user_id,
                ProviderApiKey.provider_id == provider_id
            ).first()
            
        except Exception as e:
            self.logger.error(f"Failed to get provider API key: {e}")
            raise RepositoryError(
                f"Failed to get provider API key: {str(e)}",
                entity_type="ProviderApiKey",
                operation="get_provider_api_key"
            )
    
    def get_user_provider_api_keys(self, user_id: int) -> List[ProviderApiKey]:
        """
        Get all provider API keys for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            List of provider API keys for the user
        """
        try:
            return self.session.query(ProviderApiKey).filter(
                ProviderApiKey.user_id == user_id
            ).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get user provider API keys: {e}")
            raise RepositoryError(
                f"Failed to get user provider API keys: {str(e)}",
                entity_type="ProviderApiKey",
                operation="get_user_provider_api_keys"
            )
    
    def create_or_update_provider_api_key(
        self,
        user_id: int,
        provider_id: str,
        api_key: str,
        is_active: bool = True
    ) -> ProviderApiKey:
        """
        Create or update a provider API key for a user.
        
        Args:
            user_id: ID of the user
            provider_id: ID of the provider
            api_key: The API key value
            is_active: Whether the key is active
            
        Returns:
            Created or updated provider API key
            
        Raises:
            RepositoryError: If operation fails
        """
        try:
            # Check if key already exists
            existing_key = self.get_provider_api_key(user_id, provider_id)
            
            if existing_key:
                # Update existing key
                existing_key.api_key = api_key
                existing_key.is_active = is_active
                
                self.session.commit()
                self.session.refresh(existing_key)
                
                self.logger.info(f"Updated provider API key for user {user_id}, provider {provider_id}")
                return existing_key
            else:
                # Create new key
                provider_api_key_data = {
                    'id': str(uuid.uuid4()),
                    'user_id': user_id,
                    'provider_id': provider_id,
                    'api_key': api_key,
                    'is_active': is_active
                }
                
                provider_api_key = ProviderApiKey(**provider_api_key_data)
                
                self.session.add(provider_api_key)
                self.session.commit()
                self.session.refresh(provider_api_key)
                
                self.logger.info(f"Created provider API key for user {user_id}, provider {provider_id}")
                return provider_api_key
                
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create/update provider API key: {e}")
            raise RepositoryError(
                f"Failed to create/update provider API key: {str(e)}",
                entity_type="ProviderApiKey",
                operation="create_or_update"
            )
    
    def delete_provider_api_key(
        self,
        user_id: int,
        provider_id: str
    ) -> bool:
        """
        Delete a provider API key for a user.
        
        Args:
            user_id: ID of the user
            provider_id: ID of the provider
            
        Returns:
            True if key was deleted, False if not found
            
        Raises:
            RepositoryError: If deletion fails
        """
        try:
            provider_api_key = self.get_provider_api_key(user_id, provider_id)
            if not provider_api_key:
                self.logger.warning(f"Provider API key not found for user {user_id}, provider {provider_id}")
                return False
            
            self.session.delete(provider_api_key)
            self.session.commit()
            
            self.logger.info(f"Deleted provider API key for user {user_id}, provider {provider_id}")
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to delete provider API key: {e}")
            raise RepositoryError(
                f"Failed to delete provider API key: {str(e)}",
                entity_type="ProviderApiKey",
                operation="delete"
            )
    
    def get_active_provider_api_keys(self, user_id: int) -> List[ProviderApiKey]:
        """
        Get all active provider API keys for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            List of active provider API keys for the user
        """
        try:
            return self.session.query(ProviderApiKey).filter(
                ProviderApiKey.user_id == user_id,
                ProviderApiKey.is_active == True
            ).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get active provider API keys: {e}")
            raise RepositoryError(
                f"Failed to get active provider API keys: {str(e)}",
                entity_type="ProviderApiKey",
                operation="get_active_provider_api_keys"
            )
    
    def deactivate_provider_api_key(
        self,
        user_id: int,
        provider_id: str
    ) -> Optional[ProviderApiKey]:
        """
        Deactivate a provider API key without deleting it.
        
        Args:
            user_id: ID of the user
            provider_id: ID of the provider
            
        Returns:
            Deactivated provider API key or None if not found
        """
        try:
            provider_api_key = self.get_provider_api_key(user_id, provider_id)
            if not provider_api_key:
                self.logger.warning(f"Provider API key not found for deactivation: user {user_id}, provider {provider_id}")
                return None
            
            provider_api_key.is_active = False
            
            self.session.commit()
            self.session.refresh(provider_api_key)
            
            self.logger.info(f"Deactivated provider API key for user {user_id}, provider {provider_id}")
            return provider_api_key
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to deactivate provider API key: {e}")
            raise RepositoryError(
                f"Failed to deactivate provider API key: {str(e)}",
                entity_type="ProviderApiKey",
                operation="deactivate"
            )
    
    def activate_provider_api_key(
        self,
        user_id: int,
        provider_id: str
    ) -> Optional[ProviderApiKey]:
        """
        Activate a provider API key.
        
        Args:
            user_id: ID of the user
            provider_id: ID of the provider
            
        Returns:
            Activated provider API key or None if not found
        """
        try:
            provider_api_key = self.get_provider_api_key(user_id, provider_id)
            if not provider_api_key:
                self.logger.warning(f"Provider API key not found for activation: user {user_id}, provider {provider_id}")
                return None
            
            provider_api_key.is_active = True
            
            self.session.commit()
            self.session.refresh(provider_api_key)
            
            self.logger.info(f"Activated provider API key for user {user_id}, provider {provider_id}")
            return provider_api_key
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to activate provider API key: {e}")
            raise RepositoryError(
                f"Failed to activate provider API key: {str(e)}",
                entity_type="ProviderApiKey",
                operation="activate"
            )
    
    def get_provider_api_key_count_by_user(self, user_id: int) -> int:
        """
        Get the count of provider API keys for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Number of provider API keys for the user
        """
        try:
            return self.session.query(ProviderApiKey).filter(
                ProviderApiKey.user_id == user_id
            ).count()
            
        except Exception as e:
            self.logger.error(f"Failed to get provider API key count: {e}")
            raise RepositoryError(
                f"Failed to get provider API key count: {str(e)}",
                entity_type="ProviderApiKey",
                operation="get_provider_api_key_count_by_user"
            )
    
    def get_users_by_provider(self, provider_id: str) -> List[int]:
        """
        Get list of user IDs that have API keys for a specific provider.
        
        Args:
            provider_id: ID of the provider
            
        Returns:
            List of user IDs
        """
        try:
            result = self.session.query(ProviderApiKey.user_id).filter(
                ProviderApiKey.provider_id == provider_id,
                ProviderApiKey.is_active == True
            ).distinct().all()
            
            return [row[0] for row in result]
            
        except Exception as e:
            self.logger.error(f"Failed to get users by provider: {e}")
            raise RepositoryError(
                f"Failed to get users by provider: {str(e)}",
                entity_type="ProviderApiKey",
                operation="get_users_by_provider"
            )
