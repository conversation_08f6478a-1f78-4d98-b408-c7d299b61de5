{"policy_version": "1.0", "last_updated": "2025-01-27T00:00:00Z", "environments": {"development": {"security_level": "basic", "https_enforcement": {"enabled": false, "redirect_http": false, "hsts_enabled": false}, "authentication": {"require_2fa": false, "session_timeout_minutes": 480, "max_concurrent_sessions": 10, "password_policy": {"min_length": 8, "require_uppercase": false, "require_lowercase": true, "require_numbers": false, "require_special_chars": false}}, "cors_policy": {"strict_mode": false, "allowed_origins": ["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"], "allow_credentials": true, "max_age": 86400}, "input_validation": {"enabled": true, "strict_mode": false, "sanitize_html": true, "validate_json": true, "max_request_size_mb": 10}, "logging": {"level": "DEBUG", "audit_enabled": true, "security_events": true, "pii_logging": true, "retention_days": 30}, "file_upload": {"max_size_mb": 10, "allowed_types": ["pdf", "docx", "xlsx", "csv", "txt", "json", "yaml"], "scan_for_malware": false, "quarantine_suspicious": false}}, "staging": {"security_level": "standard", "https_enforcement": {"enabled": true, "redirect_http": true, "hsts_enabled": true, "hsts_max_age": 31536000}, "authentication": {"require_2fa": true, "session_timeout_minutes": 240, "max_concurrent_sessions": 5, "password_policy": {"min_length": 12, "require_uppercase": true, "require_lowercase": true, "require_numbers": true, "require_special_chars": true}}, "cors_policy": {"strict_mode": true, "allowed_origins": ["https://staging.datagenius.com"], "allow_credentials": true, "max_age": 3600}, "input_validation": {"enabled": true, "strict_mode": true, "sanitize_html": true, "validate_json": true, "max_request_size_mb": 10}, "logging": {"level": "INFO", "audit_enabled": true, "security_events": true, "pii_logging": false, "retention_days": 90}, "file_upload": {"max_size_mb": 10, "allowed_types": ["pdf", "docx", "xlsx", "csv", "txt"], "scan_for_malware": true, "quarantine_suspicious": true}}, "production": {"security_level": "maximum", "https_enforcement": {"enabled": true, "redirect_http": true, "hsts_enabled": true, "hsts_max_age": 63072000, "hsts_include_subdomains": true, "hsts_preload": true}, "authentication": {"require_2fa": true, "session_timeout_minutes": 120, "max_concurrent_sessions": 3, "password_policy": {"min_length": 16, "require_uppercase": true, "require_lowercase": true, "require_numbers": true, "require_special_chars": true, "min_special_chars": 2, "password_history": 12}}, "cors_policy": {"strict_mode": true, "allowed_origins": ["https://datagenius.com", "https://www.datagenius.com"], "allow_credentials": true, "max_age": 3600}, "input_validation": {"enabled": true, "strict_mode": true, "sanitize_html": true, "validate_json": true, "max_request_size_mb": 5, "deep_validation": true}, "logging": {"level": "WARNING", "audit_enabled": true, "security_events": true, "pii_logging": false, "retention_days": 365, "encrypt_logs": true}, "file_upload": {"max_size_mb": 5, "allowed_types": ["pdf", "docx", "xlsx", "csv"], "scan_for_malware": true, "quarantine_suspicious": true, "deep_scan": true}}, "testing": {"security_level": "minimal", "https_enforcement": {"enabled": false, "redirect_http": false, "hsts_enabled": false}, "authentication": {"require_2fa": false, "session_timeout_minutes": 60, "max_concurrent_sessions": 1, "password_policy": {"min_length": 4, "require_uppercase": false, "require_lowercase": false, "require_numbers": false, "require_special_chars": false}}, "cors_policy": {"strict_mode": false, "allowed_origins": ["*"], "allow_credentials": false, "max_age": 0}, "input_validation": {"enabled": true, "strict_mode": false, "sanitize_html": false, "validate_json": false, "max_request_size_mb": 1}, "logging": {"level": "ERROR", "audit_enabled": false, "security_events": false, "pii_logging": false, "retention_days": 1}, "file_upload": {"max_size_mb": 1, "allowed_types": ["txt", "json"], "scan_for_malware": false, "quarantine_suspicious": false}}}, "global_policies": {"ip_blocking": {"enabled": true, "auto_block_suspicious_ips": true, "block_duration_minutes": 60, "whitelist": ["127.0.0.1", "::1"]}, "ddos_protection": {"enabled": true, "detection_threshold": 1000, "mitigation_enabled": true}, "security_headers": {"x_frame_options": "DENY", "x_content_type_options": "nosniff", "x_xss_protection": "1; mode=block", "referrer_policy": "strict-origin-when-cross-origin", "permissions_policy": "geolocation=(), microphone=(), camera=()"}}}