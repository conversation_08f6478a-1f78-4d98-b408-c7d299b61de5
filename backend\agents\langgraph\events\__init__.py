"""
Event-driven architecture for LangGraph system.

This module provides a high-performance event bus system for real-time
notifications and communication between LangGraph components.

Key Components:
- LangGraphEventBus: Core event distribution system
- Event types: Standardized event definitions
- Event handlers: Specialized handlers for different event types
- Performance monitoring: Event processing metrics and optimization
"""

from .event_bus import LangGraphEventBus, LangGraphEvent, EventPriority, event_bus
from .types import (
    AgentRegistrationEvent,
    AgentUnregistrationEvent,
    WorkflowStartedEvent,
    WorkflowCompletedEvent,
    WorkflowFailedEvent,
    CapabilityUpdateEvent,
    DashboardUpdateEvent,
    AgentPerformanceEvent,
    SystemHealthEvent,
    UserActivityEvent,
    BusinessProfileUpdateEvent
)

__version__ = "1.0.0"

__all__ = [
    "LangGraphEventBus",
    "LangGraphEvent",
    "EventPriority",
    "event_bus",
    "AgentRegistrationEvent",
    "AgentUnregistrationEvent",
    "WorkflowStartedEvent",
    "WorkflowCompletedEvent",
    "WorkflowFailedEvent",
    "CapabilityUpdateEvent",
    "DashboardUpdateEvent",
    "AgentPerformanceEvent",
    "SystemHealthEvent",
    "UserActivityEvent",
    "BusinessProfileUpdateEvent"
]
