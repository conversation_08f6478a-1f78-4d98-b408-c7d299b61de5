"""
Centralized Configuration Manager for LangGraph Integration.

This module provides a comprehensive configuration management system
that handles environment-specific settings, runtime configuration,
and dynamic configuration updates.
"""

import logging
import os
import yaml
import json
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from datetime import datetime, timezone

from pydantic import BaseModel, Field, field_validator
from pydantic.v1 import BaseSettings

logger = logging.getLogger(__name__)


class LangGraphConfig(BaseModel):
    """Configuration model for LangGraph settings."""
    
    # Core LangGraph settings
    enabled: bool = Field(default=False, description="Enable LangGraph workflows")
    max_workflow_duration: int = Field(default=3600, description="Maximum workflow duration in seconds")
    max_agent_transitions: int = Field(default=20, description="Maximum agent transitions per workflow")
    max_tool_executions: int = Field(default=50, description="Maximum tool executions per workflow")
    
    # State management
    enable_checkpointing: bool = Field(default=True, description="Enable workflow checkpointing")
    checkpoint_interval: int = Field(default=30, description="Checkpoint interval in seconds")
    max_checkpoints_per_workflow: int = Field(default=100, description="Maximum checkpoints per workflow")
    
    # Performance settings
    enable_monitoring: bool = Field(default=True, description="Enable performance monitoring")
    monitoring_interval: int = Field(default=60, description="Monitoring interval in seconds")
    performance_cache_ttl: int = Field(default=300, description="Performance cache TTL in seconds")
    
    # Recovery settings
    enable_auto_recovery: bool = Field(default=True, description="Enable automatic workflow recovery")
    max_recovery_attempts: int = Field(default=3, description="Maximum recovery attempts")
    recovery_timeout: int = Field(default=300, description="Recovery timeout in seconds")
    
    # Migration settings
    migration_phase: str = Field(default="preparation", description="Current migration phase")
    rollout_percentage: float = Field(default=0.0, description="Rollout percentage for gradual migration")
    enable_legacy_fallback: bool = Field(default=True, description="Enable fallback to legacy orchestrator")
    
    @field_validator('rollout_percentage')
    @classmethod
    def validate_rollout_percentage(cls, v):
        if not 0.0 <= v <= 100.0:
            raise ValueError('Rollout percentage must be between 0.0 and 100.0')
        return v


class DatabaseConfig(BaseModel):
    """Database configuration for LangGraph."""
    
    host: str = Field(default="localhost", description="Database host")
    port: int = Field(default=5432, description="Database port")
    database: str = Field(default="datagenius_db", description="Database name")
    username: str = Field(default="datagenius_user", description="Database username")
    password: str = Field(default="", description="Database password")
    pool_size: int = Field(default=10, description="Connection pool size")
    max_overflow: int = Field(default=20, description="Maximum pool overflow")
    pool_timeout: int = Field(default=30, description="Pool timeout in seconds")
    
    def get_connection_string(self) -> str:
        """Get database connection string."""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


class LoggingConfig(BaseModel):
    """Logging configuration for LangGraph."""
    
    level: str = Field(default="INFO", description="Logging level")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    enable_file_logging: bool = Field(default=True, description="Enable file logging")
    log_file_path: str = Field(default="logs/langgraph.log", description="Log file path")
    max_file_size: int = Field(default=10485760, description="Maximum log file size in bytes")  # 10MB
    backup_count: int = Field(default=5, description="Number of backup log files")
    
    @field_validator('level')
    @classmethod
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of {valid_levels}')
        return v.upper()


class ConfigManager:
    """
    Centralized configuration manager for LangGraph integration.
    
    This manager provides:
    - Environment-specific configuration loading
    - Runtime configuration updates
    - Configuration validation and defaults
    - Configuration persistence and caching
    """

    def __init__(self, config_dir: Optional[Path] = None, environment: Optional[str] = None):
        """
        Initialize the configuration manager.

        Args:
            config_dir: Directory containing configuration files
            environment: Environment name (development, staging, production)
        """
        self.config_dir = config_dir or Path(__file__).parent
        self.environment = environment or os.getenv("ENVIRONMENT", "development")

        # Configuration inheritance paths (in order of priority)
        self._inheritance_paths = [
            self.config_dir,  # LangGraph-specific configs (highest priority)
            self.config_dir.parent.parent.parent / "config",  # Main app configs
        ]

        # Configuration cache
        self._config_cache: Dict[str, Any] = {}
        self._cache_timestamp = datetime.min.replace(tzinfo=timezone.utc)
        self._cache_ttl = 300  # 5 minutes

        # Configuration models
        self.langgraph_config: Optional[LangGraphConfig] = None
        self.database_config: Optional[DatabaseConfig] = None
        self.logging_config: Optional[LoggingConfig] = None

        self.logger = logging.getLogger(__name__)

        # Load initial configuration
        self._load_configuration()

        # Validate configuration after loading
        self._validate_configuration()

    def _load_configuration(self):
        """Load configuration from files and environment variables."""
        try:
            # Load base configuration
            base_config = self._load_config_file("base.yaml")
            
            # Load environment-specific configuration
            env_config = self._load_config_file(f"{self.environment}.yaml")
            
            # Merge configurations (environment overrides base)
            merged_config = self._merge_configs(base_config, env_config)
            
            # Override with environment variables
            final_config = self._apply_env_overrides(merged_config)
            
            # Create configuration models
            self._create_config_models(final_config)
            
            # Update cache
            self._config_cache = final_config
            self._cache_timestamp = datetime.now(timezone.utc)
            
            self.logger.info(f"Configuration loaded for environment: {self.environment}")
            
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            # Load default configuration on error
            self._load_default_configuration()

    def _load_config_file(self, filename: str) -> Dict[str, Any]:
        """Load configuration from a YAML file with inheritance support."""
        merged_config = {}

        # Load from all inheritance paths (reverse order for proper merging)
        for config_path in reversed(self._inheritance_paths):
            config_file = config_path / filename

            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f) or {}

                    # Merge with existing config (later configs override earlier ones)
                    merged_config = self._merge_configs(merged_config, config)
                    self.logger.debug(f"Loaded configuration from {config_file}")

                except Exception as e:
                    self.logger.error(f"Error loading config from {config_file}: {e}")

        if not merged_config:
            self.logger.warning(f"No configuration found for {filename} in any inheritance path")

        return merged_config

    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Merge two configuration dictionaries."""
        merged = base.copy()
        
        for key, value in override.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
        
        return merged

    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration."""
        # Define environment variable mappings
        env_mappings = {
            "LANGGRAPH_ENABLED": ("langgraph", "enabled"),
            "LANGGRAPH_MAX_DURATION": ("langgraph", "max_workflow_duration"),
            "LANGGRAPH_ROLLOUT_PERCENTAGE": ("langgraph", "rollout_percentage"),
            "DATABASE_HOST": ("database", "host"),
            "DATABASE_PORT": ("database", "port"),
            "DATABASE_NAME": ("database", "database"),
            "DATABASE_USER": ("database", "username"),
            "DATABASE_PASSWORD": ("database", "password"),
            "LOG_LEVEL": ("logging", "level"),
            "LOG_FILE_PATH": ("logging", "log_file_path")
        }
        
        for env_var, (section, key) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                if section not in config:
                    config[section] = {}
                
                # Convert value to appropriate type
                config[section][key] = self._convert_env_value(env_value)
        
        return config

    def _convert_env_value(self, value: str) -> Union[str, int, float, bool]:
        """Convert environment variable string to appropriate type."""
        # Boolean conversion
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Integer conversion
        try:
            return int(value)
        except ValueError:
            pass
        
        # Float conversion
        try:
            return float(value)
        except ValueError:
            pass
        
        # Return as string
        return value

    def _create_config_models(self, config: Dict[str, Any]):
        """Create Pydantic configuration models from loaded configuration."""
        try:
            # LangGraph configuration
            langgraph_config = config.get("langgraph", {})
            self.langgraph_config = LangGraphConfig(**langgraph_config)
            
            # Database configuration
            database_config = config.get("database", {})
            self.database_config = DatabaseConfig(**database_config)
            
            # Logging configuration
            logging_config = config.get("logging", {})
            self.logging_config = LoggingConfig(**logging_config)
            
        except Exception as e:
            self.logger.error(f"Error creating configuration models: {e}")
            self._load_default_configuration()

    def _load_default_configuration(self):
        """Load default configuration when file loading fails."""
        self.langgraph_config = LangGraphConfig()
        self.database_config = DatabaseConfig()
        self.logging_config = LoggingConfig()
        
        self.logger.info("Loaded default configuration")

    def get_langgraph_config(self) -> LangGraphConfig:
        """Get LangGraph configuration."""
        if self._is_cache_expired():
            self._load_configuration()
        
        return self.langgraph_config or LangGraphConfig()

    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration."""
        if self._is_cache_expired():
            self._load_configuration()
        
        return self.database_config or DatabaseConfig()

    def get_logging_config(self) -> LoggingConfig:
        """Get logging configuration."""
        if self._is_cache_expired():
            self._load_configuration()
        
        return self.logging_config or LoggingConfig()

    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        Get a configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the configuration value
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        if self._is_cache_expired():
            self._load_configuration()
        
        try:
            keys = key_path.split('.')
            value = self._config_cache
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default

    def update_config_value(self, key_path: str, value: Any) -> bool:
        """
        Update a configuration value at runtime.
        
        Args:
            key_path: Dot-separated path to the configuration value
            value: New value to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            keys = key_path.split('.')
            config = self._config_cache
            
            # Navigate to the parent of the target key
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # Set the value
            config[keys[-1]] = value
            
            # Recreate configuration models
            self._create_config_models(self._config_cache)
            
            self.logger.info(f"Updated configuration: {key_path} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating configuration {key_path}: {e}")
            return False

    def _is_cache_expired(self) -> bool:
        """Check if configuration cache has expired."""
        return (datetime.now(timezone.utc) - self._cache_timestamp).total_seconds() > self._cache_ttl

    def reload_configuration(self) -> bool:
        """Reload configuration from files."""
        try:
            self._load_configuration()
            return True
        except Exception as e:
            self.logger.error(f"Error reloading configuration: {e}")
            return False

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate current configuration and return validation results."""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # Validate LangGraph configuration
            if self.langgraph_config:
                if self.langgraph_config.max_workflow_duration < 60:
                    validation_results["warnings"].append(
                        "Max workflow duration is less than 60 seconds"
                    )
                
                if self.langgraph_config.rollout_percentage > 0 and not self.langgraph_config.enabled:
                    validation_results["errors"].append(
                        "Rollout percentage is set but LangGraph is not enabled"
                    )
                    validation_results["valid"] = False
            
            # Validate database configuration
            if self.database_config:
                if not self.database_config.password and self.environment == "production":
                    validation_results["errors"].append(
                        "Database password is required in production environment"
                    )
                    validation_results["valid"] = False
            
            # Validate logging configuration
            if self.logging_config:
                log_dir = Path(self.logging_config.log_file_path).parent
                if not log_dir.exists():
                    validation_results["warnings"].append(
                        f"Log directory does not exist: {log_dir}"
                    )
            
        except Exception as e:
            validation_results["valid"] = False
            validation_results["errors"].append(f"Validation error: {str(e)}")
        
        return validation_results

    def export_configuration(self, format: str = "yaml") -> str:
        """
        Export current configuration to string.
        
        Args:
            format: Export format ("yaml" or "json")
            
        Returns:
            Configuration as string
        """
        try:
            if format.lower() == "json":
                return json.dumps(self._config_cache, indent=2, default=str)
            else:  # yaml
                return yaml.dump(self._config_cache, default_flow_style=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error exporting configuration: {e}")
            return f"Error exporting configuration: {str(e)}"

    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration."""
        return {
            "environment": self.environment,
            "config_dir": str(self.config_dir),
            "inheritance_paths": [str(p) for p in self._inheritance_paths],
            "cache_timestamp": self._cache_timestamp.isoformat(),
            "cache_expired": self._is_cache_expired(),
            "langgraph_enabled": self.langgraph_config.enabled if self.langgraph_config else False,
            "migration_phase": self.langgraph_config.migration_phase if self.langgraph_config else "unknown",
            "rollout_percentage": self.langgraph_config.rollout_percentage if self.langgraph_config else 0.0,
            "database_host": self.database_config.host if self.database_config else "unknown",
            "logging_level": self.logging_config.level if self.logging_config else "INFO"
        }

    def _validate_configuration(self) -> None:
        """Validate the loaded configuration for consistency and completeness."""
        validation_errors = []

        # Validate LangGraph configuration
        if self.langgraph_config:
            if self.langgraph_config.enabled and not self.database_config:
                validation_errors.append("LangGraph is enabled but database configuration is missing")

            if self.langgraph_config.rollout_percentage > 0 and not self.langgraph_config.enabled:
                validation_errors.append("Rollout percentage is set but LangGraph is not enabled")

        # Validate database configuration
        if self.database_config:
            if not self.database_config.password and self.environment == "production":
                validation_errors.append("Database password is required in production environment")

        # Validate logging configuration
        if self.logging_config:
            if self.logging_config.enable_file_logging and not self.logging_config.log_file_path:
                validation_errors.append("File logging is enabled but log file path is not specified")

        # Log validation results
        if validation_errors:
            for error in validation_errors:
                self.logger.warning(f"Configuration validation warning: {error}")
        else:
            self.logger.info("Configuration validation passed")

    def get_inheritance_info(self) -> Dict[str, Any]:
        """Get information about configuration inheritance."""
        inheritance_info = {
            "inheritance_paths": [],
            "loaded_files": {}
        }

        for path in self._inheritance_paths:
            path_info = {
                "path": str(path),
                "exists": path.exists(),
                "files": []
            }

            if path.exists():
                for config_file in ["base.yaml", f"{self.environment}.yaml"]:
                    file_path = path / config_file
                    if file_path.exists():
                        path_info["files"].append(config_file)
                        inheritance_info["loaded_files"][str(file_path)] = True

            inheritance_info["inheritance_paths"].append(path_info)

        return inheritance_info
