"""
LangGraph Managers Module.

This module provides consolidated management systems for LangGraph-based
workflows, including the new ConsolidatedToolManager and legacy compatibility.
"""

# Import the new consolidated tool manager
from .consolidated_tool_manager import (
    ConsolidatedToolManager,
    consolidated_tool_manager,
    ToolStatus,
    ToolCategory,
    ToolDefinition,
    ToolExecutionResult,
    ToolMetrics
)

# Import legacy compatibility wrappers
from .tool_manager_migration import (
    LegacyToolManagerWrapper,
    UnifiedToolManager,
    MCPToolManager,
    MCPToolRegistry,
    migrate_tool_manager_imports
)

# Import existing unified tool manager for backward compatibility
try:
    from .unified_tool_manager import UnifiedToolManager as LegacyUnifiedToolManager
except ImportError:
    LegacyUnifiedToolManager = None

__all__ = [
    # New consolidated system
    'ConsolidatedToolManager',
    'consolidated_tool_manager',
    'ToolStatus',
    'ToolCategory', 
    'ToolDefinition',
    'ToolExecutionResult',
    'ToolMetrics',
    
    # Legacy compatibility
    'LegacyToolManagerWrapper',
    'UnifiedToolManager',
    'MCPToolManager',
    'MCPToolRegistry',
    'migrate_tool_manager_imports',
    
    # Legacy imports
    'LegacyUnifiedToolManager'
]
