#!/usr/bin/env python3
"""
Test script to verify agent loading fixes.

This script tests the dynamic agent loading system and fallback mechanisms.
"""

import sys
import os
import asyncio
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_agent_loading():
    """Test the agent loading system."""
    try:
        logger.info("🧪 Testing agent loading system...")
        
        # Test 1: Load agent registry
        logger.info("📋 Test 1: Loading agent registry...")
        from agents.langgraph.core.agent_factory import agent_factory
        
        agent_factory.load_agent_registry()
        logger.info("✅ Agent registry loaded successfully")
        
        # Test 2: Test concierge agent creation
        logger.info("🤖 Test 2: Creating concierge agent...")
        concierge_node = agent_factory.create_agent_node('concierge')
        
        if concierge_node:
            logger.info("✅ Concierge agent node created successfully")
            
            if hasattr(concierge_node, 'agent_instance') and concierge_node.agent_instance is not None:
                logger.info("✅ Agent instance is not None")
                logger.info(f"   Agent instance type: {type(concierge_node.agent_instance)}")
                
                # Test agent info
                if hasattr(concierge_node.agent_instance, 'get_agent_info'):
                    info = concierge_node.agent_instance.get_agent_info()
                    logger.info(f"   Agent info: {info}")
                
            else:
                logger.warning("⚠️ Agent instance is None - fallback should be working")
        else:
            logger.error("❌ Failed to create concierge agent node")
        
        # Test 3: Test other agents (should use fallbacks)
        logger.info("🔄 Test 3: Testing other agents with fallbacks...")
        
        test_agents = ['composable-marketing-ai', 'composable-analysis-ai', 'composable-classifier-ai']
        
        for agent_id in test_agents:
            logger.info(f"   Testing {agent_id}...")
            try:
                agent_node = agent_factory.create_agent_node(agent_id)
                if agent_node and agent_node.agent_instance:
                    logger.info(f"   ✅ {agent_id} created successfully")
                    if hasattr(agent_node.agent_instance, 'fallback_mode'):
                        logger.info(f"   📦 {agent_id} is using fallback mode")
                else:
                    logger.warning(f"   ⚠️ {agent_id} failed to create")
            except Exception as e:
                logger.error(f"   ❌ {agent_id} failed with error: {e}")
        
        # Test 4: Test fallback agent directly
        logger.info("🛡️ Test 4: Testing fallback agent directly...")
        from agents.langgraph.nodes.fallback_agent import FallbackAgent
        
        fallback = FallbackAgent({
            "agent_id": "test_fallback",
            "agent_type": "concierge"
        })
        
        response = await fallback.process_message("Hello, test message")
        logger.info(f"   ✅ Fallback agent response: {response.get('message', '')[:100]}...")
        
        logger.info("🎉 All tests completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_agent_loading())
    if success:
        print("\n✅ Agent loading system is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Agent loading system has issues!")
        sys.exit(1)
