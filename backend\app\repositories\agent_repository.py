"""
Agent Repository Implementation.

Specialized repository for Agent entity operations with agent-specific
business logic and workflow management.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc

from ..database import Agent
from ..models.agent import AgentC<PERSON>, AgentUpdate
from .base_repository import BaseRepository, RepositoryError

logger = logging.getLogger(__name__)


class AgentRepository(BaseRepository[Agent]):
    """
    Specialized repository for Agent entity operations.
    
    Provides agent-specific methods for managing AI agents, their configurations,
    and execution states.
    """
    
    def __init__(self, db: Session):
        super().__init__(db, Agent)
    
    def get_user_agents(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 50,
        include_inactive: bool = False
    ) -> List[Agent]:
        """Get agents for a specific user."""
        try:
            filters = {"user_id": user_id}
            
            if not include_inactive:
                filters["is_active"] = True
            
            return self.get_multi(
                skip=skip,
                limit=limit,
                order_by="updated_at",
                desc_order=True,
                **filters
            )
            
        except Exception as e:
            self.logger.error(f"Error getting agents for user {user_id}: {e}")
            raise RepositoryError(
                "Failed to retrieve user agents",
                entity_type="Agent"
            )
    
    def get_agents_by_type(
        self,
        agent_type: str,
        skip: int = 0,
        limit: int = 100,
        include_inactive: bool = False
    ) -> List[Agent]:
        """Get agents by type."""
        try:
            filters = {"agent_type": agent_type}
            
            if not include_inactive:
                filters["is_active"] = True
            
            return self.get_multi(
                skip=skip,
                limit=limit,
                order_by="created_at",
                desc_order=True,
                **filters
            )
            
        except Exception as e:
            self.logger.error(f"Error getting agents by type {agent_type}: {e}")
            raise RepositoryError(
                "Failed to retrieve agents by type",
                entity_type="Agent"
            )
    
    def get_active_agents(
        self,
        user_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Agent]:
        """Get active agents."""
        try:
            filters = {"is_active": True}
            
            if user_id:
                filters["user_id"] = user_id
            
            return self.get_multi(
                skip=skip,
                limit=limit,
                order_by="last_used_at",
                desc_order=True,
                **filters
            )
            
        except Exception as e:
            self.logger.error(f"Error getting active agents: {e}")
            raise RepositoryError(
                "Failed to retrieve active agents",
                entity_type="Agent"
            )
    
    def search_agents(
        self,
        user_id: str,
        query: str,
        skip: int = 0,
        limit: int = 50
    ) -> List[Agent]:
        """Search agents by name, description, or type."""
        try:
            base_query = self.db.query(Agent).filter(
                and_(
                    Agent.user_id == user_id,
                    Agent.is_active == True
                )
            )
            
            # Search in name, description, and agent_type
            search_filter = or_(
                Agent.name.ilike(f"%{query}%"),
                Agent.description.ilike(f"%{query}%"),
                Agent.agent_type.ilike(f"%{query}%")
            )
            
            base_query = base_query.filter(search_filter)
            base_query = self._apply_pagination(base_query, skip, limit)
            base_query = base_query.order_by(desc(Agent.updated_at))
            
            return base_query.all()
            
        except Exception as e:
            self.logger.error(f"Error searching agents for user {user_id}: {e}")
            raise RepositoryError(
                "Failed to search agents",
                entity_type="Agent"
            )
    
    def create_agent(
        self,
        agent_data: AgentCreate,
        user_id: str,
        **kwargs
    ) -> Agent:
        """Create a new agent for a user."""
        try:
            # Add user_id to agent data
            create_data = agent_data.model_dump()
            create_data["user_id"] = user_id
            
            # Set default values
            create_data.setdefault("is_active", True)
            create_data.setdefault("execution_count", 0)
            create_data.setdefault("status", "idle")
            
            return self.create(create_data, **kwargs)
            
        except Exception as e:
            self.logger.error(f"Error creating agent for user {user_id}: {e}")
            raise RepositoryError(
                "Failed to create agent",
                entity_type="Agent"
            )
    
    def update_agent_status(
        self,
        agent_id: str,
        status: str,
        user_id: Optional[str] = None
    ) -> Optional[Agent]:
        """Update agent status."""
        try:
            agent = self.get(agent_id)
            if not agent:
                return None
            
            # Verify ownership if user_id provided
            if user_id and agent.user_id != user_id:
                return None
            
            update_data = {
                "status": status,
                "updated_at": datetime.now(timezone.utc)
            }
            
            # Update last_used_at if status indicates activity
            if status in ["running", "executing"]:
                update_data["last_used_at"] = datetime.now(timezone.utc)
            
            return self.update(agent_id, update_data)
            
        except Exception as e:
            self.logger.error(f"Error updating agent status {agent_id}: {e}")
            raise RepositoryError(
                "Failed to update agent status",
                entity_type="Agent"
            )
    
    def increment_execution_count(self, agent_id: str) -> Optional[Agent]:
        """Increment the execution count for an agent."""
        try:
            agent = self.get(agent_id)
            if not agent:
                return None
            
            new_count = (agent.execution_count or 0) + 1
            return self.update(agent_id, {
                "execution_count": new_count,
                "last_used_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            })
            
        except Exception as e:
            self.logger.error(f"Error incrementing execution count for {agent_id}: {e}")
            raise RepositoryError(
                "Failed to increment execution count",
                entity_type="Agent"
            )
    
    def update_agent_config(
        self,
        agent_id: str,
        config: Dict[str, Any],
        user_id: str
    ) -> Optional[Agent]:
        """Update agent configuration."""
        try:
            # Verify ownership
            agent = self.get(agent_id)
            if not agent or agent.user_id != user_id:
                return None
            
            # Merge with existing configuration
            current_config = agent.configuration or {}
            current_config.update(config)
            
            return self.update(agent_id, {"configuration": current_config})
            
        except Exception as e:
            self.logger.error(f"Error updating agent config {agent_id}: {e}")
            raise RepositoryError(
                "Failed to update agent configuration",
                entity_type="Agent"
            )
    
    def deactivate_agent(self, agent_id: str, user_id: str) -> Optional[Agent]:
        """Deactivate an agent."""
        try:
            # Verify ownership
            agent = self.get(agent_id)
            if not agent or agent.user_id != user_id:
                return None
            
            return self.update(agent_id, {
                "is_active": False,
                "status": "inactive"
            })
            
        except Exception as e:
            self.logger.error(f"Error deactivating agent {agent_id}: {e}")
            raise RepositoryError(
                "Failed to deactivate agent",
                entity_type="Agent"
            )
    
    def activate_agent(self, agent_id: str, user_id: str) -> Optional[Agent]:
        """Activate an agent."""
        try:
            # Verify ownership
            agent = self.get(agent_id)
            if not agent or agent.user_id != user_id:
                return None
            
            return self.update(agent_id, {
                "is_active": True,
                "status": "idle"
            })
            
        except Exception as e:
            self.logger.error(f"Error activating agent {agent_id}: {e}")
            raise RepositoryError(
                "Failed to activate agent",
                entity_type="Agent"
            )
    
    def get_agent_stats(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get agent statistics."""
        try:
            base_query = self.db.query(Agent)
            
            if user_id:
                base_query = base_query.filter(Agent.user_id == user_id)
            
            total_agents = base_query.count()
            active_agents = base_query.filter(Agent.is_active == True).count()
            inactive_agents = total_agents - active_agents
            
            # Get status distribution
            status_stats = base_query.filter(Agent.is_active == True).with_entities(
                Agent.status,
                func.count(Agent.id).label('count')
            ).group_by(Agent.status).all()
            
            status_distribution = {
                status: count for status, count in status_stats
                if status is not None
            }
            
            # Get type distribution
            type_stats = base_query.filter(Agent.is_active == True).with_entities(
                Agent.agent_type,
                func.count(Agent.id).label('count')
            ).group_by(Agent.agent_type).all()
            
            type_distribution = {
                agent_type: count for agent_type, count in type_stats
                if agent_type is not None
            }
            
            # Get total executions
            total_executions = base_query.with_entities(
                func.sum(Agent.execution_count)
            ).scalar() or 0
            
            # Get recent agents (last 7 days)
            seven_days_ago = datetime.now(timezone.utc).replace(
                day=datetime.now(timezone.utc).day - 7
            )
            
            recent_agents = base_query.filter(
                Agent.created_at >= seven_days_ago
            ).count()
            
            return {
                "total_agents": total_agents,
                "active_agents": active_agents,
                "inactive_agents": inactive_agents,
                "recent_agents": recent_agents,
                "total_executions": int(total_executions),
                "status_distribution": status_distribution,
                "type_distribution": type_distribution,
                "average_executions": round(float(total_executions) / max(total_agents, 1), 2)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting agent statistics: {e}")
            raise RepositoryError(
                "Failed to retrieve agent statistics",
                entity_type="Agent"
            )
    
    def get_agents_by_status(
        self,
        status: str,
        user_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Agent]:
        """Get agents by status."""
        try:
            filters = {"status": status, "is_active": True}
            
            if user_id:
                filters["user_id"] = user_id
            
            return self.get_multi(
                skip=skip,
                limit=limit,
                order_by="updated_at",
                desc_order=True,
                **filters
            )
            
        except Exception as e:
            self.logger.error(f"Error getting agents by status {status}: {e}")
            raise RepositoryError(
                "Failed to retrieve agents by status",
                entity_type="Agent"
            )
    
    def bulk_update_status(
        self,
        agent_ids: List[str],
        status: str,
        user_id: str
    ) -> int:
        """Bulk update agent status for user's agents."""
        try:
            updated_count = self.db.query(Agent).filter(
                and_(
                    Agent.id.in_(agent_ids),
                    Agent.user_id == user_id
                )
            ).update(
                {
                    "status": status,
                    "updated_at": datetime.now(timezone.utc)
                },
                synchronize_session=False
            )
            
            self.db.commit()
            
            self._log_audit("BULK_UPDATE_STATUS", details={
                "user_id": user_id,
                "agent_ids": agent_ids,
                "status": status,
                "updated_count": updated_count
            })
            
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error bulk updating agent status: {e}")
            raise RepositoryError(
                "Failed to bulk update agent status",
                entity_type="Agent"
            )
    
    def cleanup_inactive_agents(
        self,
        days_inactive: int = 30,
        user_id: Optional[str] = None
    ) -> int:
        """Clean up agents that have been inactive for specified days."""
        try:
            cutoff_date = datetime.now(timezone.utc).replace(
                day=datetime.now(timezone.utc).day - days_inactive
            )
            
            query = self.db.query(Agent).filter(
                and_(
                    Agent.is_active == True,
                    Agent.status == "idle",
                    or_(
                        Agent.last_used_at < cutoff_date,
                        and_(
                            Agent.last_used_at.is_(None),
                            Agent.created_at < cutoff_date
                        )
                    )
                )
            )
            
            if user_id:
                query = query.filter(Agent.user_id == user_id)
            
            # Deactivate instead of delete
            updated_count = query.update(
                {
                    "is_active": False,
                    "status": "inactive",
                    "updated_at": datetime.now(timezone.utc)
                },
                synchronize_session=False
            )
            
            self.db.commit()
            
            self._log_audit("CLEANUP_INACTIVE", details={
                "days_inactive": days_inactive,
                "user_id": user_id,
                "updated_count": updated_count,
                "cutoff_date": cutoff_date.isoformat()
            })
            
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error cleaning up inactive agents: {e}")
            raise RepositoryError(
                "Failed to cleanup inactive agents",
                entity_type="Agent"
            )
