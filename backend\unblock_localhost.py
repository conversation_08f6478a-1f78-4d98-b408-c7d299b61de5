#!/usr/bin/env python3
"""
Quick utility to unblock localhost IP during development.
Run this script if your localhost gets blocked by the security middleware.
"""

import requests
import sys

def unblock_localhost():
    """Unblock localhost IP addresses."""
    localhost_ips = ['127.0.0.1', 'localhost', '::1']
    base_url = 'http://localhost:8000'
    
    print("🔧 Attempting to unblock localhost IPs...")
    
    for ip in localhost_ips:
        try:
            response = requests.post(f"{base_url}/debug/security/unblock-ip/{ip}")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {ip}: {result.get('message', 'Success')}")
            else:
                print(f"❌ {ip}: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {ip}: Connection error - {e}")
    
    print("\n🔍 Checking IP status...")
    for ip in localhost_ips:
        try:
            response = requests.get(f"{base_url}/debug/security/ip-status/{ip}")
            if response.status_code == 200:
                status = response.json()
                blocked = status.get('is_blocked', False)
                failed_attempts = status.get('failed_attempts', 0)
                print(f"📊 {ip}: {'🚫 BLOCKED' if blocked else '✅ OK'} (failed attempts: {failed_attempts})")
            else:
                print(f"❌ {ip}: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {ip}: Connection error - {e}")

if __name__ == "__main__":
    print("🚀 Localhost IP Unblock Utility")
    print("=" * 40)
    unblock_localhost()
    print("\n✨ Done! Try refreshing your frontend application.")
