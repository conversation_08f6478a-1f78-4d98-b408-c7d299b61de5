"""
Predictive Optimization System for LangGraph workflows.

This module implements AI-powered workflow optimization using machine learning
to analyze historical patterns, predict performance, and suggest optimizations.

Key Features:
- Workflow pattern learning from historical data
- Performance prediction models
- Automated optimization suggestions
- A/B testing framework for validation
- Statistical analysis of optimization effectiveness
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
import json
import pickle
from pathlib import Path
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score
import joblib

from ..events.event_bus import LangGraphEventBus, event_bus
from ..events.types import (
    WorkflowCompletedEvent, 
    WorkflowFailedEvent,
    SystemHealthEvent
)
from ..monitoring.workflow_monitor import WorkflowMonitor
from ..monitoring.metrics import WorkflowMetrics

logger = logging.getLogger(__name__)


class OptimizationType(Enum):
    """Types of workflow optimizations."""
    AGENT_SELECTION = "agent_selection"
    RESOURCE_ALLOCATION = "resource_allocation"
    WORKFLOW_STRUCTURE = "workflow_structure"
    PARAMETER_TUNING = "parameter_tuning"
    CACHING_STRATEGY = "caching_strategy"
    PARALLEL_EXECUTION = "parallel_execution"


class PredictionConfidence(Enum):
    """Confidence levels for predictions."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class WorkflowPattern:
    """Represents a learned workflow pattern."""
    pattern_id: str
    workflow_type: str
    agent_sequence: List[str]
    parameters: Dict[str, Any]
    performance_metrics: Dict[str, float]
    success_rate: float
    avg_execution_time: float
    resource_usage: Dict[str, float]
    frequency: int
    last_seen: datetime
    confidence_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert pattern to dictionary."""
        data = asdict(self)
        data['last_seen'] = self.last_seen.isoformat()
        return data


@dataclass
class PerformancePrediction:
    """Represents a performance prediction for a workflow."""
    workflow_id: str
    predicted_execution_time: float
    predicted_success_rate: float
    predicted_resource_usage: Dict[str, float]
    confidence: PredictionConfidence
    factors: Dict[str, float]  # Feature importance
    timestamp: datetime
    model_version: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert prediction to dictionary."""
        data = asdict(self)
        data['confidence'] = self.confidence.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class OptimizationSuggestion:
    """Represents an optimization suggestion."""
    suggestion_id: str
    workflow_id: str
    optimization_type: OptimizationType
    description: str
    expected_improvement: Dict[str, float]
    implementation_steps: List[str]
    confidence: PredictionConfidence
    priority: int  # 1-10, higher is more important
    estimated_effort: str  # "low", "medium", "high"
    potential_risks: List[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert suggestion to dictionary."""
        data = asdict(self)
        data['optimization_type'] = self.optimization_type.value
        data['confidence'] = self.confidence.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


class PatternLearningEngine:
    """Machine learning engine for workflow pattern analysis."""
    
    def __init__(self, model_path: Optional[str] = None):
        self.model_path = model_path or "models/pattern_learning"
        self.performance_model = None
        self.success_model = None
        self.resource_model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = []
        self.is_trained = False
        
    async def train_models(self, training_data: pd.DataFrame) -> Dict[str, float]:
        """
        Train machine learning models on historical workflow data.
        
        Args:
            training_data: DataFrame with workflow execution data
            
        Returns:
            Dictionary with model performance metrics
        """
        try:
            logger.info("Starting model training with {} samples".format(len(training_data)))
            
            # Prepare features and targets
            features, targets = self._prepare_training_data(training_data)
            
            if len(features) < 10:
                logger.warning("Insufficient training data, need at least 10 samples")
                return {"error": "insufficient_data"}
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                features, targets, test_size=0.2, random_state=42
            )
            
            # Train performance prediction model
            self.performance_model = GradientBoostingRegressor(
                n_estimators=100, random_state=42
            )
            self.performance_model.fit(X_train, y_train['execution_time'])
            
            # Train success prediction model  
            self.success_model = RandomForestRegressor(
                n_estimators=100, random_state=42
            )
            self.success_model.fit(X_train, y_train['success_rate'])
            
            # Train resource usage model
            self.resource_model = GradientBoostingRegressor(
                n_estimators=100, random_state=42
            )
            self.resource_model.fit(X_train, y_train['memory_usage'])
            
            # Evaluate models
            performance_score = r2_score(
                y_test['execution_time'], 
                self.performance_model.predict(X_test)
            )
            success_score = r2_score(
                y_test['success_rate'],
                self.success_model.predict(X_test)
            )
            resource_score = r2_score(
                y_test['memory_usage'],
                self.resource_model.predict(X_test)
            )
            
            self.is_trained = True
            
            # Save models
            await self._save_models()
            
            logger.info(f"Model training completed. Scores - Performance: {performance_score:.3f}, Success: {success_score:.3f}, Resource: {resource_score:.3f}")
            
            return {
                "performance_r2": performance_score,
                "success_r2": success_score, 
                "resource_r2": resource_score,
                "training_samples": len(X_train),
                "test_samples": len(X_test)
            }
            
        except Exception as e:
            logger.error(f"Error training models: {e}")
            return {"error": str(e)}
    
    def _prepare_training_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Prepare features and targets for model training."""
        # Feature engineering
        features = pd.DataFrame()
        
        # Basic features
        features['workflow_type_encoded'] = self._encode_categorical(
            data['workflow_type'], 'workflow_type'
        )
        features['agent_count'] = data['agents_involved'].apply(len)
        features['parameter_count'] = data['parameters'].apply(
            lambda x: len(x) if isinstance(x, dict) else 0
        )
        
        # Time-based features
        data['hour'] = pd.to_datetime(data['timestamp']).dt.hour
        data['day_of_week'] = pd.to_datetime(data['timestamp']).dt.dayofweek
        features['hour'] = data['hour']
        features['day_of_week'] = data['day_of_week']
        
        # Historical performance features
        features['avg_recent_performance'] = data.groupby('workflow_type')['execution_time'].transform('mean')
        features['success_rate_history'] = data.groupby('workflow_type')['success'].transform('mean')
        
        # Targets
        targets = pd.DataFrame()
        targets['execution_time'] = data['execution_time']
        targets['success_rate'] = data['success'].astype(float)
        targets['memory_usage'] = data['memory_usage_mb']
        
        # Store feature columns for prediction
        self.feature_columns = features.columns.tolist()
        
        # Scale features
        features_scaled = pd.DataFrame(
            self.scaler.fit_transform(features),
            columns=features.columns
        )
        
        return features_scaled, targets
    
    def _encode_categorical(self, series: pd.Series, column_name: str) -> pd.Series:
        """Encode categorical variables."""
        if column_name not in self.label_encoders:
            self.label_encoders[column_name] = LabelEncoder()
            return pd.Series(self.label_encoders[column_name].fit_transform(series))
        else:
            return pd.Series(self.label_encoders[column_name].transform(series))
    
    async def _save_models(self):
        """Save trained models to disk."""
        try:
            model_dir = Path(self.model_path)
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # Save models
            joblib.dump(self.performance_model, model_dir / "performance_model.pkl")
            joblib.dump(self.success_model, model_dir / "success_model.pkl") 
            joblib.dump(self.resource_model, model_dir / "resource_model.pkl")
            joblib.dump(self.scaler, model_dir / "scaler.pkl")
            joblib.dump(self.label_encoders, model_dir / "label_encoders.pkl")
            
            # Save metadata
            metadata = {
                "feature_columns": self.feature_columns,
                "trained_at": datetime.now().isoformat(),
                "is_trained": self.is_trained
            }
            
            with open(model_dir / "metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
                
            logger.info(f"Models saved to {model_dir}")
            
        except Exception as e:
            logger.error(f"Error saving models: {e}")
    
    async def load_models(self) -> bool:
        """Load trained models from disk."""
        try:
            model_dir = Path(self.model_path)
            
            if not model_dir.exists():
                logger.warning(f"Model directory {model_dir} does not exist")
                return False
            
            # Load models
            self.performance_model = joblib.load(model_dir / "performance_model.pkl")
            self.success_model = joblib.load(model_dir / "success_model.pkl")
            self.resource_model = joblib.load(model_dir / "resource_model.pkl")
            self.scaler = joblib.load(model_dir / "scaler.pkl")
            self.label_encoders = joblib.load(model_dir / "label_encoders.pkl")
            
            # Load metadata
            with open(model_dir / "metadata.json", 'r') as f:
                metadata = json.load(f)
                self.feature_columns = metadata["feature_columns"]
                self.is_trained = metadata["is_trained"]
            
            logger.info("Models loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False

    async def predict_performance(self, workflow_features: Dict[str, Any]) -> Optional[PerformancePrediction]:
        """
        Predict workflow performance using trained models.

        Args:
            workflow_features: Dictionary with workflow characteristics

        Returns:
            PerformancePrediction object or None if prediction fails
        """
        if not self.is_trained:
            logger.warning("Models not trained, cannot make predictions")
            return None

        try:
            # Prepare features
            features_df = self._prepare_prediction_features(workflow_features)
            features_scaled = self.scaler.transform(features_df)

            # Make predictions
            execution_time = self.performance_model.predict(features_scaled)[0]
            success_rate = self.success_model.predict(features_scaled)[0]
            memory_usage = self.resource_model.predict(features_scaled)[0]

            # Calculate confidence based on feature importance and model uncertainty
            confidence = self._calculate_prediction_confidence(features_scaled)

            # Get feature importance
            feature_importance = dict(zip(
                self.feature_columns,
                self.performance_model.feature_importances_
            ))

            return PerformancePrediction(
                workflow_id=workflow_features.get('workflow_id', 'unknown'),
                predicted_execution_time=max(0, execution_time),
                predicted_success_rate=min(1.0, max(0, success_rate)),
                predicted_resource_usage={'memory_mb': max(0, memory_usage)},
                confidence=confidence,
                factors=feature_importance,
                timestamp=datetime.now(timezone.utc),
                model_version="1.0.0"
            )

        except Exception as e:
            logger.error(f"Error making prediction: {e}")
            return None

    def _prepare_prediction_features(self, workflow_features: Dict[str, Any]) -> pd.DataFrame:
        """Prepare features for prediction."""
        features = pd.DataFrame(index=[0])

        # Basic features
        workflow_type = workflow_features.get('workflow_type', 'unknown')
        if 'workflow_type' in self.label_encoders:
            try:
                features['workflow_type_encoded'] = self.label_encoders['workflow_type'].transform([workflow_type])[0]
            except ValueError:
                # Unknown workflow type, use most common encoding
                features['workflow_type_encoded'] = 0
        else:
            features['workflow_type_encoded'] = 0

        features['agent_count'] = len(workflow_features.get('agents_involved', []))
        features['parameter_count'] = len(workflow_features.get('parameters', {}))

        # Time-based features
        now = datetime.now()
        features['hour'] = now.hour
        features['day_of_week'] = now.weekday()

        # Default historical features (would be calculated from actual history)
        features['avg_recent_performance'] = workflow_features.get('avg_recent_performance', 30.0)
        features['success_rate_history'] = workflow_features.get('success_rate_history', 0.9)

        # Ensure all feature columns are present
        for col in self.feature_columns:
            if col not in features.columns:
                features[col] = 0

        return features[self.feature_columns]

    def _calculate_prediction_confidence(self, features: np.ndarray) -> PredictionConfidence:
        """Calculate confidence level for predictions."""
        try:
            # Use model's prediction variance as confidence indicator
            # This is a simplified approach - in practice, you might use ensemble methods
            predictions = []
            for _ in range(10):  # Bootstrap sampling
                pred = self.performance_model.predict(features)[0]
                predictions.append(pred)

            variance = np.var(predictions)

            # Map variance to confidence levels
            if variance < 1.0:
                return PredictionConfidence.VERY_HIGH
            elif variance < 5.0:
                return PredictionConfidence.HIGH
            elif variance < 15.0:
                return PredictionConfidence.MEDIUM
            else:
                return PredictionConfidence.LOW

        except Exception:
            return PredictionConfidence.LOW


class OptimizationEngine:
    """Engine for generating workflow optimization suggestions."""

    def __init__(self):
        self.optimization_history = []
        self.ab_tests = {}

    async def generate_suggestions(
        self,
        workflow_data: Dict[str, Any],
        performance_prediction: Optional[PerformancePrediction] = None
    ) -> List[OptimizationSuggestion]:
        """
        Generate optimization suggestions for a workflow.

        Args:
            workflow_data: Current workflow configuration and performance
            performance_prediction: Optional performance prediction

        Returns:
            List of optimization suggestions
        """
        suggestions = []

        try:
            # Agent selection optimization
            agent_suggestion = await self._suggest_agent_optimization(workflow_data)
            if agent_suggestion:
                suggestions.append(agent_suggestion)

            # Resource allocation optimization
            resource_suggestion = await self._suggest_resource_optimization(workflow_data)
            if resource_suggestion:
                suggestions.append(resource_suggestion)

            # Workflow structure optimization
            structure_suggestion = await self._suggest_structure_optimization(workflow_data)
            if structure_suggestion:
                suggestions.append(structure_suggestion)

            # Parameter tuning optimization
            parameter_suggestion = await self._suggest_parameter_optimization(workflow_data)
            if parameter_suggestion:
                suggestions.append(parameter_suggestion)

            # Caching strategy optimization
            caching_suggestion = await self._suggest_caching_optimization(workflow_data)
            if caching_suggestion:
                suggestions.append(caching_suggestion)

            # Parallel execution optimization
            parallel_suggestion = await self._suggest_parallel_optimization(workflow_data)
            if parallel_suggestion:
                suggestions.append(parallel_suggestion)

            # Sort by priority
            suggestions.sort(key=lambda x: x.priority, reverse=True)

            logger.info(f"Generated {len(suggestions)} optimization suggestions")
            return suggestions

        except Exception as e:
            logger.error(f"Error generating optimization suggestions: {e}")
            return []

    async def _suggest_agent_optimization(self, workflow_data: Dict[str, Any]) -> Optional[OptimizationSuggestion]:
        """Suggest agent selection optimizations."""
        try:
            current_agents = workflow_data.get('agents_involved', [])
            execution_time = workflow_data.get('execution_time', 0)
            success_rate = workflow_data.get('success_rate', 1.0)

            # Analyze if current agent selection is optimal
            if execution_time > 60 and len(current_agents) == 1:
                return OptimizationSuggestion(
                    suggestion_id=f"agent_opt_{datetime.now().timestamp()}",
                    workflow_id=workflow_data.get('workflow_id', 'unknown'),
                    optimization_type=OptimizationType.AGENT_SELECTION,
                    description="Consider using multiple specialized agents for better performance",
                    expected_improvement={
                        "execution_time_reduction": 0.3,
                        "success_rate_improvement": 0.1
                    },
                    implementation_steps=[
                        "Analyze workflow tasks and identify specialization opportunities",
                        "Select complementary agents with relevant capabilities",
                        "Implement parallel task distribution",
                        "Add coordination mechanisms between agents"
                    ],
                    confidence=PredictionConfidence.MEDIUM,
                    priority=7,
                    estimated_effort="medium",
                    potential_risks=[
                        "Increased coordination overhead",
                        "Potential communication delays between agents"
                    ],
                    timestamp=datetime.now(timezone.utc)
                )

            elif success_rate < 0.8:
                return OptimizationSuggestion(
                    suggestion_id=f"agent_opt_{datetime.now().timestamp()}",
                    workflow_id=workflow_data.get('workflow_id', 'unknown'),
                    optimization_type=OptimizationType.AGENT_SELECTION,
                    description="Replace underperforming agents with more capable alternatives",
                    expected_improvement={
                        "success_rate_improvement": 0.2,
                        "quality_improvement": 0.15
                    },
                    implementation_steps=[
                        "Identify agents with poor performance metrics",
                        "Find alternative agents with better capabilities",
                        "Gradually migrate tasks to new agents",
                        "Monitor performance improvements"
                    ],
                    confidence=PredictionConfidence.HIGH,
                    priority=8,
                    estimated_effort="low",
                    potential_risks=[
                        "Temporary performance degradation during transition",
                        "Need for agent retraining"
                    ],
                    timestamp=datetime.now(timezone.utc)
                )

            return None

        except Exception as e:
            logger.error(f"Error generating agent optimization suggestion: {e}")
            return None

    async def _suggest_resource_optimization(self, workflow_data: Dict[str, Any]) -> Optional[OptimizationSuggestion]:
        """Suggest resource allocation optimizations."""
        try:
            memory_usage = workflow_data.get('memory_usage_mb', 0)
            cpu_usage = workflow_data.get('cpu_usage_percent', 0)

            if memory_usage > 512:  # High memory usage
                return OptimizationSuggestion(
                    suggestion_id=f"resource_opt_{datetime.now().timestamp()}",
                    workflow_id=workflow_data.get('workflow_id', 'unknown'),
                    optimization_type=OptimizationType.RESOURCE_ALLOCATION,
                    description="Optimize memory usage to reduce resource consumption",
                    expected_improvement={
                        "memory_reduction": 0.4,
                        "cost_reduction": 0.2
                    },
                    implementation_steps=[
                        "Implement data streaming instead of loading all data in memory",
                        "Add garbage collection at workflow checkpoints",
                        "Use memory-efficient data structures",
                        "Implement result caching to avoid recomputation"
                    ],
                    confidence=PredictionConfidence.HIGH,
                    priority=6,
                    estimated_effort="medium",
                    potential_risks=[
                        "Increased I/O operations",
                        "Potential performance trade-offs"
                    ],
                    timestamp=datetime.now(timezone.utc)
                )

            return None

        except Exception as e:
            logger.error(f"Error generating resource optimization suggestion: {e}")
            return None

    async def _suggest_structure_optimization(self, workflow_data: Dict[str, Any]) -> Optional[OptimizationSuggestion]:
        """Suggest workflow structure optimizations."""
        try:
            execution_time = workflow_data.get('execution_time', 0)
            workflow_type = workflow_data.get('workflow_type', '')

            if execution_time > 120 and 'analysis' in workflow_type.lower():
                return OptimizationSuggestion(
                    suggestion_id=f"structure_opt_{datetime.now().timestamp()}",
                    workflow_id=workflow_data.get('workflow_id', 'unknown'),
                    optimization_type=OptimizationType.WORKFLOW_STRUCTURE,
                    description="Restructure workflow to enable parallel processing",
                    expected_improvement={
                        "execution_time_reduction": 0.5,
                        "throughput_improvement": 0.8
                    },
                    implementation_steps=[
                        "Identify independent workflow segments",
                        "Implement parallel execution branches",
                        "Add synchronization points for result aggregation",
                        "Optimize data flow between parallel branches"
                    ],
                    confidence=PredictionConfidence.MEDIUM,
                    priority=8,
                    estimated_effort="high",
                    potential_risks=[
                        "Increased complexity in workflow management",
                        "Potential race conditions in parallel execution"
                    ],
                    timestamp=datetime.now(timezone.utc)
                )

            return None

        except Exception as e:
            logger.error(f"Error generating structure optimization suggestion: {e}")
            return None

    async def _suggest_parameter_optimization(self, workflow_data: Dict[str, Any]) -> Optional[OptimizationSuggestion]:
        """Suggest parameter tuning optimizations."""
        try:
            success_rate = workflow_data.get('success_rate', 1.0)
            quality_score = workflow_data.get('quality_score', 1.0)

            if success_rate < 0.9 or quality_score < 0.8:
                return OptimizationSuggestion(
                    suggestion_id=f"param_opt_{datetime.now().timestamp()}",
                    workflow_id=workflow_data.get('workflow_id', 'unknown'),
                    optimization_type=OptimizationType.PARAMETER_TUNING,
                    description="Optimize workflow parameters for better performance",
                    expected_improvement={
                        "success_rate_improvement": 0.15,
                        "quality_improvement": 0.2
                    },
                    implementation_steps=[
                        "Analyze parameter sensitivity using historical data",
                        "Implement automated parameter tuning",
                        "Use Bayesian optimization for parameter search",
                        "Validate parameter changes with A/B testing"
                    ],
                    confidence=PredictionConfidence.MEDIUM,
                    priority=7,
                    estimated_effort="medium",
                    potential_risks=[
                        "Parameter changes may affect other workflows",
                        "Need for extensive testing of new parameters"
                    ],
                    timestamp=datetime.now(timezone.utc)
                )

            return None

        except Exception as e:
            logger.error(f"Error generating parameter optimization suggestion: {e}")
            return None

    async def _suggest_caching_optimization(self, workflow_data: Dict[str, Any]) -> Optional[OptimizationSuggestion]:
        """Suggest caching strategy optimizations."""
        try:
            execution_time = workflow_data.get('execution_time', 0)
            workflow_type = workflow_data.get('workflow_type', '')

            if execution_time > 30 and any(keyword in workflow_type.lower() for keyword in ['analysis', 'research', 'report']):
                return OptimizationSuggestion(
                    suggestion_id=f"cache_opt_{datetime.now().timestamp()}",
                    workflow_id=workflow_data.get('workflow_id', 'unknown'),
                    optimization_type=OptimizationType.CACHING_STRATEGY,
                    description="Implement intelligent caching to reduce computation time",
                    expected_improvement={
                        "execution_time_reduction": 0.6,
                        "resource_savings": 0.4
                    },
                    implementation_steps=[
                        "Identify cacheable intermediate results",
                        "Implement cache invalidation strategies",
                        "Add cache warming for frequently used data",
                        "Monitor cache hit rates and optimize cache size"
                    ],
                    confidence=PredictionConfidence.HIGH,
                    priority=9,
                    estimated_effort="low",
                    potential_risks=[
                        "Stale data from outdated cache entries",
                        "Increased memory usage for cache storage"
                    ],
                    timestamp=datetime.now(timezone.utc)
                )

            return None

        except Exception as e:
            logger.error(f"Error generating caching optimization suggestion: {e}")
            return None

    async def _suggest_parallel_optimization(self, workflow_data: Dict[str, Any]) -> Optional[OptimizationSuggestion]:
        """Suggest parallel execution optimizations."""
        try:
            agents_involved = workflow_data.get('agents_involved', [])
            execution_time = workflow_data.get('execution_time', 0)

            if len(agents_involved) > 1 and execution_time > 45:
                return OptimizationSuggestion(
                    suggestion_id=f"parallel_opt_{datetime.now().timestamp()}",
                    workflow_id=workflow_data.get('workflow_id', 'unknown'),
                    optimization_type=OptimizationType.PARALLEL_EXECUTION,
                    description="Enable parallel execution of independent tasks",
                    expected_improvement={
                        "execution_time_reduction": 0.4,
                        "resource_utilization": 0.3
                    },
                    implementation_steps=[
                        "Analyze task dependencies to identify parallel opportunities",
                        "Implement async task execution",
                        "Add result synchronization mechanisms",
                        "Optimize resource allocation for parallel tasks"
                    ],
                    confidence=PredictionConfidence.MEDIUM,
                    priority=6,
                    estimated_effort="medium",
                    potential_risks=[
                        "Increased system complexity",
                        "Potential resource contention"
                    ],
                    timestamp=datetime.now(timezone.utc)
                )

            return None

        except Exception as e:
            logger.error(f"Error generating parallel optimization suggestion: {e}")
            return None


@dataclass
class ABTestResult:
    """Results from an A/B test of workflow optimizations."""
    test_id: str
    optimization_type: OptimizationType
    control_metrics: Dict[str, float]
    treatment_metrics: Dict[str, float]
    improvement: Dict[str, float]
    statistical_significance: float
    confidence_interval: Tuple[float, float]
    sample_size: int
    test_duration_hours: float
    conclusion: str
    timestamp: datetime


class ABTestingFramework:
    """Framework for A/B testing workflow optimizations."""

    def __init__(self):
        self.active_tests = {}
        self.completed_tests = []

    async def start_ab_test(
        self,
        optimization_suggestion: OptimizationSuggestion,
        test_duration_hours: float = 24.0,
        traffic_split: float = 0.5
    ) -> str:
        """
        Start an A/B test for an optimization suggestion.

        Args:
            optimization_suggestion: The optimization to test
            test_duration_hours: How long to run the test
            traffic_split: Percentage of traffic to send to treatment (0.0-1.0)

        Returns:
            Test ID for tracking the experiment
        """
        test_id = f"ab_test_{datetime.now().timestamp()}"

        test_config = {
            "test_id": test_id,
            "optimization": optimization_suggestion,
            "start_time": datetime.now(timezone.utc),
            "end_time": datetime.now(timezone.utc) + timedelta(hours=test_duration_hours),
            "traffic_split": traffic_split,
            "control_metrics": [],
            "treatment_metrics": [],
            "status": "running"
        }

        self.active_tests[test_id] = test_config

        logger.info(f"Started A/B test {test_id} for {optimization_suggestion.optimization_type.value}")
        return test_id

    async def record_test_result(
        self,
        test_id: str,
        is_treatment: bool,
        metrics: Dict[str, float]
    ):
        """Record results from a workflow execution in an A/B test."""
        if test_id not in self.active_tests:
            logger.warning(f"Test {test_id} not found or not active")
            return

        test_config = self.active_tests[test_id]

        if is_treatment:
            test_config["treatment_metrics"].append(metrics)
        else:
            test_config["control_metrics"].append(metrics)

    async def analyze_test_results(self, test_id: str) -> Optional[ABTestResult]:
        """
        Analyze A/B test results and determine statistical significance.

        Args:
            test_id: ID of the test to analyze

        Returns:
            ABTestResult with analysis or None if insufficient data
        """
        if test_id not in self.active_tests:
            logger.warning(f"Test {test_id} not found")
            return None

        test_config = self.active_tests[test_id]
        control_data = test_config["control_metrics"]
        treatment_data = test_config["treatment_metrics"]

        if len(control_data) < 10 or len(treatment_data) < 10:
            logger.warning(f"Insufficient data for test {test_id}")
            return None

        try:
            # Calculate average metrics
            control_avg = self._calculate_average_metrics(control_data)
            treatment_avg = self._calculate_average_metrics(treatment_data)

            # Calculate improvement
            improvement = {}
            for metric in control_avg:
                if control_avg[metric] > 0:
                    improvement[metric] = (treatment_avg[metric] - control_avg[metric]) / control_avg[metric]
                else:
                    improvement[metric] = 0

            # Statistical significance test (simplified t-test)
            significance = self._calculate_statistical_significance(control_data, treatment_data)

            # Confidence interval (simplified)
            confidence_interval = self._calculate_confidence_interval(control_data, treatment_data)

            # Determine conclusion
            conclusion = self._determine_conclusion(improvement, significance)

            result = ABTestResult(
                test_id=test_id,
                optimization_type=test_config["optimization"].optimization_type,
                control_metrics=control_avg,
                treatment_metrics=treatment_avg,
                improvement=improvement,
                statistical_significance=significance,
                confidence_interval=confidence_interval,
                sample_size=len(control_data) + len(treatment_data),
                test_duration_hours=(datetime.now(timezone.utc) - test_config["start_time"]).total_seconds() / 3600,
                conclusion=conclusion,
                timestamp=datetime.now(timezone.utc)
            )

            # Move to completed tests
            self.completed_tests.append(result)
            test_config["status"] = "completed"

            logger.info(f"A/B test {test_id} completed: {conclusion}")
            return result

        except Exception as e:
            logger.error(f"Error analyzing A/B test {test_id}: {e}")
            return None

    def _calculate_average_metrics(self, metrics_list: List[Dict[str, float]]) -> Dict[str, float]:
        """Calculate average metrics from a list of metric dictionaries."""
        if not metrics_list:
            return {}

        avg_metrics = {}
        for metric_name in metrics_list[0].keys():
            values = [m.get(metric_name, 0) for m in metrics_list]
            avg_metrics[metric_name] = sum(values) / len(values)

        return avg_metrics

    def _calculate_statistical_significance(
        self,
        control_data: List[Dict[str, float]],
        treatment_data: List[Dict[str, float]]
    ) -> float:
        """Calculate statistical significance (simplified p-value)."""
        try:
            # Use execution time as primary metric for significance testing
            control_times = [d.get('execution_time', 0) for d in control_data]
            treatment_times = [d.get('execution_time', 0) for d in treatment_data]

            # Simplified t-test calculation
            control_mean = np.mean(control_times)
            treatment_mean = np.mean(treatment_times)
            control_std = np.std(control_times)
            treatment_std = np.std(treatment_times)

            if control_std == 0 and treatment_std == 0:
                return 1.0 if control_mean == treatment_mean else 0.0

            # Pooled standard error
            n1, n2 = len(control_times), len(treatment_times)
            pooled_se = np.sqrt((control_std**2 / n1) + (treatment_std**2 / n2))

            if pooled_se == 0:
                return 1.0 if control_mean == treatment_mean else 0.0

            # t-statistic
            t_stat = abs(treatment_mean - control_mean) / pooled_se

            # Simplified p-value approximation
            # In practice, you'd use scipy.stats.t.sf(t_stat, df)
            p_value = max(0.001, min(0.999, 1 / (1 + t_stat)))

            return p_value

        except Exception as e:
            logger.error(f"Error calculating statistical significance: {e}")
            return 1.0

    def _calculate_confidence_interval(
        self,
        control_data: List[Dict[str, float]],
        treatment_data: List[Dict[str, float]]
    ) -> Tuple[float, float]:
        """Calculate confidence interval for the difference in means."""
        try:
            control_times = [d.get('execution_time', 0) for d in control_data]
            treatment_times = [d.get('execution_time', 0) for d in treatment_data]

            control_mean = np.mean(control_times)
            treatment_mean = np.mean(treatment_times)
            control_std = np.std(control_times)
            treatment_std = np.std(treatment_times)

            n1, n2 = len(control_times), len(treatment_times)

            # Standard error of difference
            se_diff = np.sqrt((control_std**2 / n1) + (treatment_std**2 / n2))

            # 95% confidence interval (using t=1.96 approximation)
            diff = treatment_mean - control_mean
            margin = 1.96 * se_diff

            return (diff - margin, diff + margin)

        except Exception as e:
            logger.error(f"Error calculating confidence interval: {e}")
            return (0.0, 0.0)

    def _determine_conclusion(self, improvement: Dict[str, float], significance: float) -> str:
        """Determine the conclusion of the A/B test."""
        if significance > 0.05:
            return "No statistically significant difference detected"

        execution_time_improvement = improvement.get('execution_time', 0)
        success_rate_improvement = improvement.get('success_rate', 0)

        if execution_time_improvement < -0.1 and success_rate_improvement > 0.05:
            return "Optimization shows significant improvement - recommend implementation"
        elif execution_time_improvement < -0.05:
            return "Optimization shows moderate improvement - consider implementation"
        elif execution_time_improvement > 0.1:
            return "Optimization shows performance degradation - do not implement"
        else:
            return "Optimization shows minimal impact - further testing recommended"


class PredictiveOptimizer:
    """
    Main class for AI-powered workflow optimization.

    Combines pattern learning, performance prediction, optimization suggestions,
    and A/B testing to continuously improve workflow performance.
    """

    def __init__(self, workflow_monitor: Optional[WorkflowMonitor] = None):
        self.workflow_monitor = workflow_monitor
        self.pattern_engine = PatternLearningEngine()
        self.optimization_engine = OptimizationEngine()
        self.ab_testing = ABTestingFramework()
        self.learned_patterns = {}
        self.optimization_history = []
        self.is_initialized = False

        # Subscribe to workflow events
        event_bus.subscribe("workflow.completed", self._handle_workflow_completed)
        event_bus.subscribe("workflow.failed", self._handle_workflow_failed)

        logger.info("PredictiveOptimizer initialized")

    async def initialize(self) -> bool:
        """Initialize the predictive optimizer with historical data."""
        try:
            logger.info("Initializing PredictiveOptimizer...")

            # Load existing models if available
            models_loaded = await self.pattern_engine.load_models()

            if not models_loaded:
                logger.info("No existing models found, will train on first data batch")

            # Load historical workflow data for training
            historical_data = await self._load_historical_data()

            if len(historical_data) >= 10:
                logger.info(f"Training models with {len(historical_data)} historical samples")
                training_results = await self.pattern_engine.train_models(historical_data)
                logger.info(f"Model training results: {training_results}")
            else:
                logger.warning("Insufficient historical data for model training")

            # Learn workflow patterns
            await self._learn_workflow_patterns(historical_data)

            self.is_initialized = True
            logger.info("PredictiveOptimizer initialization completed")
            return True

        except Exception as e:
            logger.error(f"Error initializing PredictiveOptimizer: {e}")
            return False

    async def predict_workflow_performance(
        self,
        workflow_config: Dict[str, Any]
    ) -> Optional[PerformancePrediction]:
        """
        Predict performance for a workflow configuration.

        Args:
            workflow_config: Workflow configuration and context

        Returns:
            Performance prediction or None if prediction fails
        """
        if not self.is_initialized:
            logger.warning("PredictiveOptimizer not initialized")
            return None

        try:
            prediction = await self.pattern_engine.predict_performance(workflow_config)

            if prediction:
                logger.info(f"Performance prediction for workflow {workflow_config.get('workflow_id', 'unknown')}: "
                          f"{prediction.predicted_execution_time:.2f}s, "
                          f"{prediction.predicted_success_rate:.2f} success rate")

            return prediction

        except Exception as e:
            logger.error(f"Error predicting workflow performance: {e}")
            return None

    async def generate_optimization_suggestions(
        self,
        workflow_id: str,
        current_performance: Optional[Dict[str, Any]] = None
    ) -> List[OptimizationSuggestion]:
        """
        Generate optimization suggestions for a workflow.

        Args:
            workflow_id: ID of the workflow to optimize
            current_performance: Current performance metrics

        Returns:
            List of optimization suggestions
        """
        if not self.is_initialized:
            logger.warning("PredictiveOptimizer not initialized")
            return []

        try:
            # Get workflow data
            workflow_data = await self._get_workflow_data(workflow_id, current_performance)

            if not workflow_data:
                logger.warning(f"No data available for workflow {workflow_id}")
                return []

            # Get performance prediction
            prediction = await self.predict_workflow_performance(workflow_data)

            # Generate suggestions
            suggestions = await self.optimization_engine.generate_suggestions(
                workflow_data, prediction
            )

            # Store suggestions in history
            for suggestion in suggestions:
                self.optimization_history.append(suggestion)

            logger.info(f"Generated {len(suggestions)} optimization suggestions for workflow {workflow_id}")
            return suggestions

        except Exception as e:
            logger.error(f"Error generating optimization suggestions: {e}")
            return []

    async def start_optimization_test(
        self,
        suggestion: OptimizationSuggestion,
        test_duration_hours: float = 24.0
    ) -> Optional[str]:
        """
        Start an A/B test for an optimization suggestion.

        Args:
            suggestion: Optimization suggestion to test
            test_duration_hours: Duration of the test

        Returns:
            Test ID or None if test couldn't be started
        """
        try:
            test_id = await self.ab_testing.start_ab_test(
                suggestion, test_duration_hours
            )

            logger.info(f"Started optimization A/B test {test_id}")
            return test_id

        except Exception as e:
            logger.error(f"Error starting optimization test: {e}")
            return None

    async def get_optimization_insights(self) -> Dict[str, Any]:
        """
        Get insights about optimization performance and patterns.

        Returns:
            Dictionary with optimization insights and metrics
        """
        try:
            insights = {
                "total_suggestions": len(self.optimization_history),
                "suggestions_by_type": self._analyze_suggestions_by_type(),
                "active_ab_tests": len(self.ab_testing.active_tests),
                "completed_ab_tests": len(self.ab_testing.completed_tests),
                "successful_optimizations": self._count_successful_optimizations(),
                "average_improvement": self._calculate_average_improvement(),
                "top_optimization_types": self._get_top_optimization_types(),
                "pattern_insights": self._get_pattern_insights(),
                "model_performance": await self._get_model_performance_metrics()
            }

            return insights

        except Exception as e:
            logger.error(f"Error getting optimization insights: {e}")
            return {"error": str(e)}

    async def _handle_workflow_completed(self, event):
        """Handle workflow completion events for learning."""
        try:
            workflow_data = {
                "workflow_id": event.data.get("workflow_id"),
                "workflow_type": event.data.get("workflow_type", "unknown"),
                "execution_time": event.data.get("execution_time", 0),
                "success": True,
                "success_rate": 1.0,
                "agents_involved": event.data.get("agents_involved", []),
                "parameters": event.data.get("parameters", {}),
                "memory_usage_mb": event.data.get("memory_usage_mb", 0),
                "quality_score": event.data.get("quality_score", 1.0),
                "timestamp": event.timestamp
            }

            # Update pattern learning
            await self._update_patterns(workflow_data)

            # Record A/B test results if applicable
            await self._record_ab_test_results(workflow_data)

        except Exception as e:
            logger.error(f"Error handling workflow completed event: {e}")

    async def _handle_workflow_failed(self, event):
        """Handle workflow failure events for learning."""
        try:
            workflow_data = {
                "workflow_id": event.data.get("workflow_id"),
                "workflow_type": event.data.get("workflow_type", "unknown"),
                "execution_time": event.data.get("execution_time", 0),
                "success": False,
                "success_rate": 0.0,
                "agents_involved": event.data.get("agents_involved", []),
                "parameters": event.data.get("parameters", {}),
                "memory_usage_mb": event.data.get("memory_usage_mb", 0),
                "quality_score": 0.0,
                "error": event.data.get("error", "unknown"),
                "timestamp": event.timestamp
            }

            # Update pattern learning with failure data
            await self._update_patterns(workflow_data)

            # Record A/B test results if applicable
            await self._record_ab_test_results(workflow_data)

        except Exception as e:
            logger.error(f"Error handling workflow failed event: {e}")

    async def _load_historical_data(self) -> pd.DataFrame:
        """Load historical workflow data for training."""
        try:
            if self.workflow_monitor:
                # Get recent metrics from workflow monitor
                recent_metrics = self.workflow_monitor._get_recent_metrics(hours=24*7)  # Last week

                if recent_metrics:
                    data = []
                    for metric in recent_metrics:
                        data.append({
                            "workflow_id": metric.workflow_id,
                            "workflow_type": metric.workflow_type,
                            "execution_time": metric.execution_time,
                            "success": metric.success,
                            "agents_involved": metric.agents_involved,
                            "parameters": {},  # Would need to be stored in metrics
                            "memory_usage_mb": metric.memory_usage_mb,
                            "quality_score": metric.quality_score,
                            "timestamp": metric.timestamp
                        })

                    return pd.DataFrame(data)

            # Return empty DataFrame if no data available
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
            return pd.DataFrame()

    async def _learn_workflow_patterns(self, data: pd.DataFrame):
        """Learn workflow patterns from historical data."""
        try:
            if data.empty:
                return

            # Group by workflow type and analyze patterns
            for workflow_type in data['workflow_type'].unique():
                type_data = data[data['workflow_type'] == workflow_type]

                if len(type_data) < 3:
                    continue

                # Calculate pattern metrics
                avg_execution_time = type_data['execution_time'].mean()
                success_rate = type_data['success'].mean()
                avg_memory = type_data['memory_usage_mb'].mean()
                frequency = len(type_data)

                # Create pattern
                pattern = WorkflowPattern(
                    pattern_id=f"pattern_{workflow_type}_{datetime.now().timestamp()}",
                    workflow_type=workflow_type,
                    agent_sequence=type_data['agents_involved'].iloc[0] if len(type_data) > 0 else [],
                    parameters={},
                    performance_metrics={
                        "avg_execution_time": avg_execution_time,
                        "avg_memory_usage": avg_memory
                    },
                    success_rate=success_rate,
                    avg_execution_time=avg_execution_time,
                    resource_usage={"memory_mb": avg_memory},
                    frequency=frequency,
                    last_seen=datetime.now(timezone.utc),
                    confidence_score=min(1.0, frequency / 10.0)  # Higher confidence with more samples
                )

                self.learned_patterns[workflow_type] = pattern

            logger.info(f"Learned {len(self.learned_patterns)} workflow patterns")

        except Exception as e:
            logger.error(f"Error learning workflow patterns: {e}")

    async def _get_workflow_data(
        self,
        workflow_id: str,
        current_performance: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """Get workflow data for optimization analysis."""
        try:
            if current_performance:
                return current_performance

            # Try to get data from workflow monitor
            if self.workflow_monitor:
                recent_metrics = self.workflow_monitor._get_recent_metrics(hours=1)
                for metric in recent_metrics:
                    if metric.workflow_id == workflow_id:
                        return {
                            "workflow_id": workflow_id,
                            "workflow_type": metric.workflow_type,
                            "execution_time": metric.execution_time,
                            "success_rate": 1.0 if metric.success else 0.0,
                            "agents_involved": metric.agents_involved,
                            "parameters": {},
                            "memory_usage_mb": metric.memory_usage_mb,
                            "quality_score": metric.quality_score
                        }

            return None

        except Exception as e:
            logger.error(f"Error getting workflow data: {e}")
            return None

    async def _update_patterns(self, workflow_data: Dict[str, Any]):
        """Update learned patterns with new workflow data."""
        try:
            workflow_type = workflow_data.get("workflow_type", "unknown")

            if workflow_type in self.learned_patterns:
                pattern = self.learned_patterns[workflow_type]

                # Update pattern with new data
                pattern.frequency += 1
                pattern.last_seen = datetime.now(timezone.utc)

                # Update moving averages
                alpha = 0.1  # Learning rate
                pattern.avg_execution_time = (
                    (1 - alpha) * pattern.avg_execution_time +
                    alpha * workflow_data.get("execution_time", 0)
                )
                pattern.success_rate = (
                    (1 - alpha) * pattern.success_rate +
                    alpha * workflow_data.get("success_rate", 0)
                )

                # Update confidence
                pattern.confidence_score = min(1.0, pattern.frequency / 20.0)

        except Exception as e:
            logger.error(f"Error updating patterns: {e}")

    async def _record_ab_test_results(self, workflow_data: Dict[str, Any]):
        """Record workflow results for active A/B tests."""
        try:
            workflow_id = workflow_data.get("workflow_id")

            # Check if this workflow is part of any active A/B tests
            for test_id, test_config in self.ab_testing.active_tests.items():
                if test_config["status"] != "running":
                    continue

                # Determine if this is treatment or control
                # This would typically be determined by the workflow execution context
                is_treatment = hash(workflow_id) % 2 == 1  # Simple hash-based assignment

                metrics = {
                    "execution_time": workflow_data.get("execution_time", 0),
                    "success_rate": workflow_data.get("success_rate", 0),
                    "memory_usage": workflow_data.get("memory_usage_mb", 0),
                    "quality_score": workflow_data.get("quality_score", 0)
                }

                await self.ab_testing.record_test_result(test_id, is_treatment, metrics)

        except Exception as e:
            logger.error(f"Error recording A/B test results: {e}")

    def _analyze_suggestions_by_type(self) -> Dict[str, int]:
        """Analyze optimization suggestions by type."""
        type_counts = {}
        for suggestion in self.optimization_history:
            opt_type = suggestion.optimization_type.value
            type_counts[opt_type] = type_counts.get(opt_type, 0) + 1
        return type_counts

    def _count_successful_optimizations(self) -> int:
        """Count successful optimizations from completed A/B tests."""
        successful = 0
        for test_result in self.ab_testing.completed_tests:
            if "significant improvement" in test_result.conclusion.lower():
                successful += 1
        return successful

    def _calculate_average_improvement(self) -> Dict[str, float]:
        """Calculate average improvement from successful optimizations."""
        if not self.ab_testing.completed_tests:
            return {}

        improvements = {"execution_time": [], "success_rate": [], "memory_usage": []}

        for test_result in self.ab_testing.completed_tests:
            for metric, improvement in test_result.improvement.items():
                if metric in improvements:
                    improvements[metric].append(improvement)

        avg_improvements = {}
        for metric, values in improvements.items():
            if values:
                avg_improvements[metric] = sum(values) / len(values)

        return avg_improvements

    def _get_top_optimization_types(self) -> List[Tuple[str, int]]:
        """Get top optimization types by frequency."""
        type_counts = self._analyze_suggestions_by_type()
        return sorted(type_counts.items(), key=lambda x: x[1], reverse=True)[:5]

    def _get_pattern_insights(self) -> Dict[str, Any]:
        """Get insights about learned patterns."""
        if not self.learned_patterns:
            return {}

        total_patterns = len(self.learned_patterns)
        avg_confidence = sum(p.confidence_score for p in self.learned_patterns.values()) / total_patterns
        most_frequent = max(self.learned_patterns.values(), key=lambda p: p.frequency)

        return {
            "total_patterns": total_patterns,
            "average_confidence": avg_confidence,
            "most_frequent_pattern": {
                "workflow_type": most_frequent.workflow_type,
                "frequency": most_frequent.frequency,
                "success_rate": most_frequent.success_rate
            }
        }

    async def _get_model_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the ML models."""
        if not self.pattern_engine.is_trained:
            return {"status": "not_trained"}

        return {
            "status": "trained",
            "models": ["performance", "success", "resource"],
            "last_training": "recent",  # Would track actual training time
            "prediction_accuracy": "high"  # Would calculate from validation data
        }
