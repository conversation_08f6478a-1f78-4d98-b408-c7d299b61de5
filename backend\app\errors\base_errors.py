"""
Base Error Classes for Datagenius.

Defines the core error hierarchy that all application errors inherit from.
"""

import uuid
import traceback
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Union
from enum import Enum

from .correlation_context import get_correlation_id


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    NOT_FOUND = "not_found"
    CONFLICT = "conflict"
    RATE_LIMIT = "rate_limit"
    SERVICE_UNAVAILABLE = "service_unavailable"
    INTERNAL_ERROR = "internal_error"
    EXTERNAL_SERVICE = "external_service"
    CONFIGURATION = "configuration"
    NETWORK = "network"
    DATABASE = "database"


class DatageniusError(Exception):
    """
    Base exception class for all Datagenius application errors.
    
    Provides structured error information with correlation IDs, severity levels,
    and security-aware error details.
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        category: Optional[ErrorCategory] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        user_message: Optional[str] = None,
        correlation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        
        # Core error information
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.category = category or ErrorCategory.INTERNAL_ERROR
        self.severity = severity
        
        # Additional error details
        self.details = details or {}
        self.cause = cause
        self.user_message = user_message or message
        
        # Tracking and context
        self.correlation_id = correlation_id or get_correlation_id()
        self.context = context or {}
        self.timestamp = datetime.now(timezone.utc)
        self.error_id = str(uuid.uuid4())
        
        # Stack trace information
        self.stack_trace = traceback.format_exc() if cause else None
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """
        Convert error to dictionary representation.
        
        Args:
            include_sensitive: Whether to include potentially sensitive information
            
        Returns:
            Dictionary representation of the error
        """
        error_dict = {
            "error_id": self.error_id,
            "error_code": self.error_code,
            "message": self.user_message,
            "category": self.category.value,
            "severity": self.severity.value,
            "timestamp": self.timestamp.isoformat(),
            "correlation_id": self.correlation_id
        }
        
        if include_sensitive:
            error_dict.update({
                "internal_message": self.message,
                "details": self.details,
                "context": self.context,
                "stack_trace": self.stack_trace,
                "cause": str(self.cause) if self.cause else None
            })
        
        return error_dict
    
    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message} (ID: {self.error_id})"
    
    def __repr__(self) -> str:
        return (
            f"{self.__class__.__name__}("
            f"message='{self.message}', "
            f"error_code='{self.error_code}', "
            f"severity={self.severity.value}, "
            f"correlation_id='{self.correlation_id}'"
            f")"
        )


class ValidationError(DatageniusError):
    """Error raised when input validation fails."""
    
    def __init__(
        self,
        message: str,
        field_errors: Optional[Dict[str, List[str]]] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="VALIDATION_ERROR",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            **kwargs
        )
        self.field_errors = field_errors or {}
        if self.field_errors:
            self.details["field_errors"] = self.field_errors


class AuthenticationError(DatageniusError):
    """Error raised when authentication fails."""
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(
            message,
            error_code="AUTHENTICATION_ERROR",
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.HIGH,
            user_message="Authentication required",
            **kwargs
        )


class AuthorizationError(DatageniusError):
    """Error raised when authorization fails."""
    
    def __init__(
        self,
        message: str = "Access denied",
        required_permissions: Optional[List[str]] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="AUTHORIZATION_ERROR",
            category=ErrorCategory.AUTHORIZATION,
            severity=ErrorSeverity.HIGH,
            user_message="Insufficient permissions",
            **kwargs
        )
        if required_permissions:
            self.details["required_permissions"] = required_permissions


class NotFoundError(DatageniusError):
    """Error raised when a requested resource is not found."""
    
    def __init__(
        self,
        message: str = "Resource not found",
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="NOT_FOUND_ERROR",
            category=ErrorCategory.NOT_FOUND,
            severity=ErrorSeverity.LOW,
            **kwargs
        )
        if resource_type:
            self.details["resource_type"] = resource_type
        if resource_id:
            self.details["resource_id"] = resource_id


class ConflictError(DatageniusError):
    """Error raised when a resource conflict occurs."""
    
    def __init__(
        self,
        message: str = "Resource conflict",
        conflicting_resource: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="CONFLICT_ERROR",
            category=ErrorCategory.CONFLICT,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        if conflicting_resource:
            self.details["conflicting_resource"] = conflicting_resource


class RateLimitError(DatageniusError):
    """Error raised when rate limits are exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        limit: Optional[int] = None,
        window: Optional[int] = None,
        retry_after: Optional[int] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="RATE_LIMIT_ERROR",
            category=ErrorCategory.RATE_LIMIT,
            severity=ErrorSeverity.MEDIUM,
            user_message="Too many requests. Please try again later.",
            **kwargs
        )
        if limit:
            self.details["limit"] = limit
        if window:
            self.details["window"] = window
        if retry_after:
            self.details["retry_after"] = retry_after


class ServiceUnavailableError(DatageniusError):
    """Error raised when a service is temporarily unavailable."""
    
    def __init__(
        self,
        message: str = "Service temporarily unavailable",
        service_name: Optional[str] = None,
        estimated_recovery: Optional[datetime] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="SERVICE_UNAVAILABLE_ERROR",
            category=ErrorCategory.SERVICE_UNAVAILABLE,
            severity=ErrorSeverity.HIGH,
            user_message="Service is temporarily unavailable. Please try again later.",
            **kwargs
        )
        if service_name:
            self.details["service_name"] = service_name
        if estimated_recovery:
            self.details["estimated_recovery"] = estimated_recovery.isoformat()


class ConfigurationError(DatageniusError):
    """Error raised when configuration is invalid or missing."""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="CONFIGURATION_ERROR",
            category=ErrorCategory.CONFIGURATION,
            severity=ErrorSeverity.HIGH,
            user_message="System configuration error",
            **kwargs
        )
        if config_key:
            self.details["config_key"] = config_key
        if config_value is not None:
            # Don't include sensitive config values in details
            if not any(sensitive in config_key.lower() for sensitive in ['password', 'secret', 'key', 'token']):
                self.details["config_value"] = str(config_value)


class NetworkError(DatageniusError):
    """Error raised when network operations fail."""
    
    def __init__(
        self,
        message: str,
        endpoint: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="NETWORK_ERROR",
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.MEDIUM,
            user_message="Network communication error",
            **kwargs
        )
        if endpoint:
            self.details["endpoint"] = endpoint
        if status_code:
            self.details["status_code"] = status_code


class ExternalServiceError(DatageniusError):
    """Error raised when external service operations fail."""
    
    def __init__(
        self,
        message: str,
        service_name: str,
        operation: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="EXTERNAL_SERVICE_ERROR",
            category=ErrorCategory.EXTERNAL_SERVICE,
            severity=ErrorSeverity.MEDIUM,
            user_message="External service error",
            **kwargs
        )
        self.details.update({
            "service_name": service_name,
            "operation": operation,
            "status_code": status_code
        })


class TimeoutError(DatageniusError):
    """Error raised when operations timeout."""
    
    def __init__(
        self,
        message: str = "Operation timed out",
        timeout_duration: Optional[float] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="TIMEOUT_ERROR",
            category=ErrorCategory.INTERNAL_ERROR,
            severity=ErrorSeverity.MEDIUM,
            user_message="Operation timed out. Please try again.",
            **kwargs
        )
        if timeout_duration:
            self.details["timeout_duration"] = timeout_duration
        if operation:
            self.details["operation"] = operation


class ResourceError(DatageniusError):
    """Error raised when system resources are exhausted."""
    
    def __init__(
        self,
        message: str,
        resource_type: str,
        current_usage: Optional[Union[int, float]] = None,
        limit: Optional[Union[int, float]] = None,
        **kwargs
    ):
        super().__init__(
            message,
            error_code="RESOURCE_ERROR",
            category=ErrorCategory.INTERNAL_ERROR,
            severity=ErrorSeverity.HIGH,
            user_message="System resources temporarily unavailable",
            **kwargs
        )
        self.details.update({
            "resource_type": resource_type,
            "current_usage": current_usage,
            "limit": limit
        })
