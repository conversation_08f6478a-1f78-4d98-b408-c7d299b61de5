#!/usr/bin/env python3
"""
Test script to verify rate limiting recovery for the concierge agent.

This script tests that the concierge agent provides immediate fallback responses
when rate limits are encountered, instead of hanging or waiting for retries.
"""

import asyncio
import logging
import sys
import os
from unittest.mock import AsyncMock, patch, MagicMock

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_conversation_tool_rate_limit_handling():
    """Test that ConversationTool handles rate limits gracefully."""
    print("Testing ConversationTool rate limit handling...")
    
    try:
        from agents.tools.mcp.conversation_tool import ConversationTool
        
        # Create conversation tool instance
        tool = ConversationTool()
        
        # Mock the model to raise a rate limit error
        mock_model = AsyncMock()
        mock_model.ainvoke.side_effect = Exception("HTTP/1.1 429 Too Many Requests")
        
        # Test arguments
        test_args = {
            "message": "Tell me about cows",
            "conversation_history": [],
            "user_context": {},
            "intent_type": "general_question",
            "confidence": 0.8,
            "is_continuing_conversation": False,
            "provider": "groq",
            "model": "llama-3.1-8b-instant",
            "temperature": 0.7
        }
        
        # Mock get_model to return our mock model
        with patch('agents.tools.mcp.conversation_tool.get_model', return_value=mock_model):
            result = await tool.execute(test_args)
        
        # Verify we get a fallback response instead of an error
        assert not result.get("isError", False), f"Expected fallback response, got error: {result}"
        
        content = result.get("content", [])
        assert len(content) > 0, "Expected content in response"
        
        response_text = content[0].get("text", "")
        assert "high demand" in response_text.lower(), f"Expected rate limit fallback message, got: {response_text}"
        
        print("✅ ConversationTool rate limit handling test passed")
        return True
        
    except Exception as e:
        print(f"❌ ConversationTool rate limit handling test failed: {e}")
        return False


async def test_concierge_agent_timeout_handling():
    """Test that ConciergeAgent handles timeouts gracefully."""
    print("Testing ConciergeAgent timeout handling...")
    
    try:
        from agents.concierge_agent.concierge import ConciergeAgent
        
        # Create concierge agent instance
        agent = ConciergeAgent()
        
        # Mock the LLM to simulate a timeout
        mock_llm = AsyncMock()
        mock_llm.ainvoke.side_effect = asyncio.TimeoutError("Request timed out")
        
        # Test the retry method directly
        messages = [{"role": "user", "content": "test message"}]
        
        try:
            await agent._call_llm_with_retry(mock_llm, messages)
            print("❌ Expected TimeoutError to be raised")
            return False
        except asyncio.TimeoutError:
            print("✅ ConciergeAgent timeout handling test passed")
            return True
        
    except Exception as e:
        print(f"❌ ConciergeAgent timeout handling test failed: {e}")
        return False


async def test_groq_provider_timeout_config():
    """Test that GroqProvider has proper timeout configuration."""
    print("Testing GroqProvider timeout configuration...")
    
    try:
        from agents.utils.model_providers.groq_provider import GroqProvider
        
        # Create provider instance
        provider = GroqProvider()
        
        # Test model creation with timeout config
        config = {
            "temperature": 0.7,
            "timeout": 5,
            "max_tokens": 1000
        }
        
        # Mock the ChatGroq class to verify timeout parameters
        with patch('agents.utils.model_providers.groq_provider.ChatGroq') as mock_chatgroq:
            mock_model = MagicMock()
            mock_chatgroq.return_value = mock_model
            
            # Set a dummy API key for testing
            provider._api_key = "test_key"
            
            model = await provider.get_model("llama-3.1-8b-instant", config)
            
            # Verify ChatGroq was called with timeout parameters
            call_args = mock_chatgroq.call_args
            assert call_args is not None, "ChatGroq should have been called"
            
            kwargs = call_args.kwargs
            assert "timeout" in kwargs, "Timeout should be in ChatGroq kwargs"
            assert "max_retries" in kwargs, "max_retries should be in ChatGroq kwargs"
            assert kwargs["max_retries"] == 0, "max_retries should be 0 to prevent hanging"
            
            print("✅ GroqProvider timeout configuration test passed")
            return True
        
    except Exception as e:
        print(f"❌ GroqProvider timeout configuration test failed: {e}")
        return False


async def test_dynamic_intent_handling():
    """Test that the system handles dynamic intents without hardcoded dependencies."""
    print("Testing dynamic intent handling...")
    
    try:
        from agents.tools.mcp.conversation_tool import ConversationTool
        
        tool = ConversationTool()
        
        # Test with a custom/unknown intent type
        custom_intent = "custom_business_analysis"
        instruction = tool._get_user_prompt_instruction(custom_intent)
        
        # Should get a dynamic fallback instruction
        assert custom_intent in instruction, f"Custom intent should be mentioned in instruction: {instruction}"
        assert "Datagenius Concierge" in instruction, f"Should identify as Datagenius Concierge: {instruction}"
        
        print("✅ Dynamic intent handling test passed")
        return True
        
    except Exception as e:
        print(f"❌ Dynamic intent handling test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting rate limiting recovery tests...\n")
    
    tests = [
        test_conversation_tool_rate_limit_handling,
        test_concierge_agent_timeout_handling,
        test_groq_provider_timeout_config,
        test_dynamic_intent_handling
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()  # Add spacing between tests
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Rate limiting recovery is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
