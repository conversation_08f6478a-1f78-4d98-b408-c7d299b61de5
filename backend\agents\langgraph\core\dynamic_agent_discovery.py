"""
Dynamic Agent Discovery System

This module provides robust dynamic discovery of available agent classes
without requiring hardcoded paths in configuration files.
"""

import importlib
import inspect
import logging
import yaml
from typing import Dict, List, Optional, Any, Type
from pathlib import Path

logger = logging.getLogger(__name__)


class DynamicAgentDiscovery:
    """
    Dynamic agent discovery system that automatically finds and registers
    available agent classes.
    """

    def __init__(self, agents_directory: Optional[str] = None, config_path: Optional[str] = None):
        """
        Initialize the dynamic agent discovery system.

        Args:
            agents_directory: Optional path to agents directory
            config_path: Optional path to dynamic registry config
        """
        self.agents_directory = agents_directory or self._get_default_agents_directory()
        self.config_path = config_path or self._get_default_config_path()
        self.discovered_agents: Dict[str, Dict[str, Any]] = {}
        self.agent_classes: Dict[str, Type] = {}
        self.config = self._load_config()

        logger.info(f"DynamicAgentDiscovery initialized with directory: {self.agents_directory}")

    def _get_default_agents_directory(self) -> str:
        """Get the default agents directory path."""
        current_file = Path(__file__)
        agents_dir = current_file.parent.parent / "agents"
        return str(agents_dir)

    def _get_default_config_path(self) -> str:
        """Get the default config path."""
        current_file = Path(__file__)
        config_path = current_file.parent.parent / "config" / "dynamic_agent_registry.yaml"
        return str(config_path)

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from the dynamic registry file."""
        try:
            config_path = Path(self.config_path)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    logger.info(f"Loaded dynamic agent registry config from {config_path}")
                    return config
            else:
                logger.warning(f"Dynamic registry config not found at {config_path}, using defaults")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration when config file is not available."""
        return {
            "discovery": {
                "enabled": True,
                "auto_discover": True,
                "discovery_method": "automatic",
                "exclude_classes": ["BaseAgent", "UserSelectedAgent", "AbstractAgent", "UnifiedConversationAgent"],
                "exclude_modules": ["*test*", "*mock*", "*base*", "__pycache__"]
            },
            "agent_selection": {
                "strategy": "automatic",
                "selection_rules": {
                    "prefer_persona": True,
                    "prefer_unified": True,
                    "prefer_complete": True
                }
            },
            "id_generation": {
                "strategy": "automatic",
                "generation_rules": {
                    "extract_from_class_name": True,
                    "normalize_to_lowercase": True,
                    "remove_common_suffixes": True
                }
            },
            "config": {
                "dynamic_loading": True,
                "fallback_to_discovery": True,
                "fallback_agents": ["concierge"]
            }
        }

    def discover_agents(self) -> Dict[str, Dict[str, Any]]:
        """
        Discover all available agent classes.

        Returns:
            Dictionary mapping agent IDs to their configurations
        """
        try:
            logger.info("Starting dynamic agent discovery...")
            
            # Clear previous discoveries
            self.discovered_agents.clear()
            self.agent_classes.clear()
            
            # Scan for agent files
            agent_files = self._find_agent_files()
            logger.info(f"Found {len(agent_files)} potential agent files")
            
            # Process each agent file
            for agent_file in agent_files:
                try:
                    self._process_agent_file(agent_file)
                except Exception as e:
                    logger.warning(f"Failed to process agent file {agent_file}: {e}")
            
            logger.info(f"Discovered {len(self.discovered_agents)} working agents")
            return self.discovered_agents
            
        except Exception as e:
            logger.error(f"Error in agent discovery: {e}")
            return {}

    def _find_agent_files(self) -> List[str]:
        """
        Find all Python files that might contain agent classes.

        Uses automatic detection instead of hardcoded keywords.
        """
        agent_files = []

        try:
            agents_path = Path(self.agents_directory)
            if not agents_path.exists():
                logger.warning(f"Agents directory does not exist: {agents_path}")
                return []

            # Scan all Python files (not just those with specific keywords)
            for file_path in agents_path.glob("*.py"):
                # Skip special files
                if file_path.name.startswith("__"):
                    continue

                # Skip test and base files
                if any(keyword in file_path.name.lower() for keyword in ["test", "mock", "base"]):
                    continue

                # Include all other Python files for scanning
                agent_files.append(str(file_path))

            logger.debug(f"Found {len(agent_files)} Python files to scan for agents")
            return agent_files

        except Exception as e:
            logger.error(f"Error finding agent files: {e}")
            return []

    def _process_agent_file(self, file_path: str) -> None:
        """Process a single agent file to extract agent classes."""
        try:
            # Convert file path to module path
            module_path = self._file_path_to_module_path(file_path)
            if not module_path:
                return
            
            # Import the module
            module = importlib.import_module(module_path)
            
            # Find agent classes in the module
            agent_classes = self._find_agent_classes_in_module(module)
            
            # Register discovered agent classes
            for agent_class in agent_classes:
                self._register_agent_class(agent_class, module_path)
                
        except Exception as e:
            logger.debug(f"Could not process agent file {file_path}: {e}")

    def _file_path_to_module_path(self, file_path: str) -> Optional[str]:
        """Convert a file path to a Python module path."""
        try:
            # Get relative path from the backend directory
            file_path = Path(file_path)
            
            # Find the backend directory in the path
            parts = file_path.parts
            backend_index = -1
            for i, part in enumerate(parts):
                if part == "backend":
                    backend_index = i
                    break
            
            if backend_index == -1:
                return None
            
            # Get parts after backend
            module_parts = parts[backend_index + 1:]
            
            # Remove .py extension
            if module_parts[-1].endswith(".py"):
                module_parts = module_parts[:-1] + (module_parts[-1][:-3],)
            
            return ".".join(module_parts)
            
        except Exception as e:
            logger.debug(f"Error converting file path to module path: {e}")
            return None

    def _find_agent_classes_in_module(self, module) -> List[Type]:
        """Find agent classes in a module."""
        agent_classes = []
        
        try:
            for name, obj in inspect.getmembers(module, inspect.isclass):
                # Check if this looks like an agent class
                if self._is_agent_class(obj, name):
                    agent_classes.append(obj)
                    
        except Exception as e:
            logger.debug(f"Error finding agent classes in module: {e}")
            
        return agent_classes

    def _is_agent_class(self, cls: Type, name: str) -> bool:
        """
        Check if a class is an agent class using automatic detection.

        Uses inheritance checking and duck typing to identify agent classes
        without relying on hardcoded patterns.
        """
        try:
            # Get excluded classes from config
            excluded_classes = self.config.get("discovery", {}).get("exclude_classes", [
                "BaseAgent", "UserSelectedAgent", "Agent", "AbstractAgent",
                "UnifiedConversationAgent"
            ])
            if name in excluded_classes:
                return False

            # Exclude test and mock classes
            if any(keyword in name.lower() for keyword in ["test", "mock", "base"]):
                return False

            # Check if it's not an abstract class
            if hasattr(cls, '__abstractmethods__') and cls.__abstractmethods__:
                return False

            # Method 1: Check inheritance from known agent base classes
            try:
                # Import known base classes
                from ..agents.unified_conversation_agent import UnifiedConversationAgent
                if issubclass(cls, UnifiedConversationAgent) and cls != UnifiedConversationAgent:
                    return True
            except (ImportError, TypeError):
                pass

            # Method 2: Duck typing - check for agent-like methods
            agent_methods = ['process_message', 'handle_message', 'handle_with_own_capabilities']
            if any(hasattr(cls, method) and callable(getattr(cls, method)) for method in agent_methods):
                # Additional validation: check if it has __init__ and can be instantiated
                if hasattr(cls, '__init__'):
                    return True

            # Method 3: Check for agent-like attributes
            agent_attributes = ['agent_type', 'agent_id', 'capabilities']
            if any(hasattr(cls, attr) for attr in agent_attributes):
                return True

            # Method 4: Check class name patterns as last resort (but more flexible)
            if name.endswith("Agent") and len(name) > 5:  # More than just "Agent"
                # Verify it's not just a base class by checking for concrete implementation
                if hasattr(cls, '__init__') and not name.startswith("Base"):
                    return True

            return False

        except Exception as e:
            logger.debug(f"Error checking if class is agent: {e}")
            return False

    def _register_agent_class(self, agent_class: Type, module_path: str) -> None:
        """Register a discovered agent class."""
        try:
            # Determine agent ID and type from class name
            class_name = agent_class.__name__
            agent_id = self._class_name_to_agent_id(class_name)
            agent_type = self._class_name_to_agent_type(class_name)
            
            # Create agent configuration
            agent_config = {
                "agent_type": agent_type,
                "name": self._generate_agent_name(class_name),
                "description": self._extract_agent_description(agent_class),
                "agent_class": f"{module_path}.{class_name}",
                "agent_init_config": {
                    "agent_id": agent_id
                },
                "discovered": True,
                "discovery_method": "dynamic",
                "module_path": module_path,
                "class_name": class_name
            }
            
            # Handle duplicate agent IDs (multiple agents of same type)
            if agent_id in self.discovered_agents:
                existing_agent = self.discovered_agents[agent_id]
                selected_agent = self._select_best_agent(
                    existing_config=existing_agent,
                    new_config=agent_config,
                    existing_class=self.agent_classes[agent_id],
                    new_class=agent_class
                )

                if selected_agent == "new":
                    logger.info(f"Replacing agent {agent_id}: {existing_agent['class_name']} -> {class_name}")
                    self.discovered_agents[agent_id] = agent_config
                    self.agent_classes[agent_id] = agent_class
                else:
                    logger.info(f"Keeping existing agent {agent_id}: {existing_agent['class_name']} (rejected {class_name})")
            else:
                # Store the configuration
                self.discovered_agents[agent_id] = agent_config
                self.agent_classes[agent_id] = agent_class
                logger.info(f"Registered agent: {agent_id} -> {module_path}.{class_name}")

        except Exception as e:
            logger.error(f"Error registering agent class {agent_class}: {e}")

    def _select_best_agent(self, existing_config: Dict[str, Any], new_config: Dict[str, Any],
                          existing_class: Type, new_class: Type) -> str:
        """
        Select the best agent when multiple agents of the same type are discovered.

        Returns:
            "existing" to keep the existing agent, "new" to use the new agent
        """
        try:
            selection_config = self.config.get("agent_selection", {})
            selection_rules = selection_config.get("selection_rules", {})

            # Rule 1: Prefer persona classes
            if selection_rules.get("prefer_persona", True):
                existing_is_persona = "persona" in existing_config["class_name"].lower()
                new_is_persona = "persona" in new_config["class_name"].lower()

                if new_is_persona and not existing_is_persona:
                    return "new"
                elif existing_is_persona and not new_is_persona:
                    return "existing"

            # Rule 2: Prefer unified conversation agents
            if selection_rules.get("prefer_unified", True):
                try:
                    from ..agents.unified_conversation_agent import UnifiedConversationAgent
                    existing_is_unified = issubclass(existing_class, UnifiedConversationAgent)
                    new_is_unified = issubclass(new_class, UnifiedConversationAgent)

                    if new_is_unified and not existing_is_unified:
                        return "new"
                    elif existing_is_unified and not new_is_unified:
                        return "existing"
                except ImportError:
                    pass

            # Rule 3: Prefer classes with more implemented methods
            if selection_rules.get("prefer_complete", True):
                existing_methods = len([m for m in dir(existing_class) if not m.startswith('_')])
                new_methods = len([m for m in dir(new_class) if not m.startswith('_')])

                if new_methods > existing_methods:
                    return "new"
                elif existing_methods > new_methods:
                    return "existing"

            # Fallback to manual preferences
            manual_prefs = selection_config.get("manual_preferences", {})
            agent_type = new_config.get("agent_type", "")
            preferred_class = manual_prefs.get(agent_type, "")

            if preferred_class:
                if preferred_class in new_config["class_name"]:
                    return "new"
                elif preferred_class in existing_config["class_name"]:
                    return "existing"

            # Default: keep existing
            return "existing"

        except Exception as e:
            logger.error(f"Error selecting best agent: {e}")
            return "existing"

    def _class_name_to_agent_id(self, class_name: str) -> str:
        """
        Convert class name to agent ID using automatic generation rules.

        Args:
            class_name: The class name to convert

        Returns:
            Generated agent ID
        """
        try:
            # Get ID generation config
            id_config = self.config.get("id_generation", {})
            generation_rules = id_config.get("generation_rules", {})

            # Start with the class name
            name = class_name

            # Remove common prefixes and suffixes
            if generation_rules.get("remove_common_suffixes", True):
                suffixes_to_remove = ["Agent", "Assistant", "Specialist", "Handler", "Processor"]
                for suffix in suffixes_to_remove:
                    if name.endswith(suffix):
                        name = name[:-len(suffix)]
                        break

            # Remove common prefixes
            prefixes_to_remove = ["Refactored", "UserSelected", "Simple", "Unified", "Base"]
            for prefix in prefixes_to_remove:
                if name.startswith(prefix):
                    name = name[len(prefix):]
                    break

            # Convert CamelCase to lowercase
            if generation_rules.get("normalize_to_lowercase", True):
                import re
                # Insert underscores before capital letters (except the first one)
                name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', name)
                name = name.lower()

            # Clean up the result
            name = name.strip("_")

            # Handle empty or invalid names
            if not name or len(name) < 2:
                # Fallback to extracting from original class name
                name = self._extract_agent_type_from_class_name(class_name)

            return name

        except Exception as e:
            logger.error(f"Error generating agent ID from class name {class_name}: {e}")
            # Fallback to simple conversion
            return class_name.lower().replace("agent", "")

    def _extract_agent_type_from_class_name(self, class_name: str) -> str:
        """Extract agent type from class name using pattern matching."""
        class_lower = class_name.lower()

        # Common agent types
        agent_types = [
            "concierge", "marketing", "analysis", "classification",
            "visualization", "data", "text", "content", "research"
        ]

        for agent_type in agent_types:
            if agent_type in class_lower:
                return agent_type

        # Fallback: use the first part of the class name
        import re
        # Remove common suffixes and get the base name
        base_name = re.sub(r'(Agent|Assistant|Specialist|Handler)$', '', class_name)
        return base_name.lower() if base_name else "unknown"

    def _class_name_to_agent_type(self, class_name: str) -> str:
        """Convert class name to agent type."""
        return self._class_name_to_agent_id(class_name)

    def _generate_agent_name(self, class_name: str) -> str:
        """Generate a human-readable agent name."""
        # Remove "Agent" suffix and add spaces
        name = class_name.replace("Agent", "").replace("UserSelected", "").replace("Simple", "")
        
        # Add spaces before capital letters
        import re
        name = re.sub('([a-z0-9])([A-Z])', r'\1 \2', name)
        
        return name.strip()

    def _extract_agent_description(self, agent_class: Type) -> str:
        """Extract description from agent class docstring."""
        try:
            if agent_class.__doc__:
                # Get first line of docstring
                lines = agent_class.__doc__.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('"""') and not line.startswith("'''"):
                        return line
            
            # Fallback description
            class_name = agent_class.__name__
            agent_type = self._class_name_to_agent_type(class_name)
            return f"AI assistant specialized in {agent_type} tasks"
            
        except Exception as e:
            logger.debug(f"Error extracting agent description: {e}")
            return "AI assistant"

    def get_agent_class(self, agent_id: str) -> Optional[Type]:
        """Get the agent class for a given agent ID."""
        return self.agent_classes.get(agent_id)

    def is_agent_available(self, agent_id: str) -> bool:
        """Check if an agent is available."""
        return agent_id in self.discovered_agents

    def get_available_agents(self) -> List[str]:
        """Get list of available agent IDs."""
        return list(self.discovered_agents.keys())

    def get_agent_config(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific agent."""
        return self.discovered_agents.get(agent_id)


# Global instance
agent_discovery = DynamicAgentDiscovery()
