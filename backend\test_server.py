#!/usr/bin/env python3
"""
Test script to verify backend server is running and CORS is configured correctly.
"""

import requests
import sys
import json
from typing import Dict, Any

def test_server_health() -> bool:
    """Test if the server is running and accessible."""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"❌ Server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server at http://localhost:8000")
        print("   Make sure the backend server is running with: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server request timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {e}")
        return False

def test_cors_headers() -> bool:
    """Test CORS headers with a preflight request."""
    try:
        # Test preflight request
        headers = {
            'Origin': 'http://localhost:5173',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type,Authorization'
        }
        
        response = requests.options("http://localhost:8000/api/v1/auth/refresh", headers=headers, timeout=5)
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
        }
        
        print("CORS Headers received:")
        for header, value in cors_headers.items():
            print(f"  {header}: {value}")
        
        # Check if frontend origin is allowed
        allowed_origin = cors_headers.get('Access-Control-Allow-Origin')
        if allowed_origin == '*' or allowed_origin == 'http://localhost:5173':
            print("✅ CORS is configured correctly for frontend origin")
            return True
        else:
            print("❌ CORS is not configured correctly for frontend origin")
            print(f"   Expected: http://localhost:5173 or *, Got: {allowed_origin}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing CORS: {e}")
        return False

def test_auth_endpoint() -> bool:
    """Test if auth endpoints are accessible."""
    try:
        # Test auth refresh endpoint (should return 401 without token)
        response = requests.post("http://localhost:8000/api/v1/auth/refresh", timeout=5)
        
        if response.status_code == 401:
            print("✅ Auth endpoint is accessible (returns 401 as expected without token)")
            return True
        else:
            print(f"❌ Auth endpoint returned unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing auth endpoint: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Datagenius Backend Server...")
    print("=" * 50)
    
    tests = [
        ("Server Health", test_server_health),
        ("CORS Configuration", test_cors_headers),
        ("Auth Endpoints", test_auth_endpoint)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Backend is ready for frontend connections.")
    else:
        print("\n⚠️  Some tests failed. Please check the backend configuration.")
        print("\n🔧 Troubleshooting steps:")
        print("1. Make sure the backend server is running:")
        print("   cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        print("2. Check the .env file has correct CORS_ORIGINS setting")
        print("3. Verify the JWT_SECRET_KEY is set in .env")
        print("4. Check server logs for any errors")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
