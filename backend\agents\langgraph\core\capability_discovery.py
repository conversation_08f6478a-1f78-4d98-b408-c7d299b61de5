"""
Agent Capability Discovery System for LangGraph-based Datagenius.

This module provides automatic discovery and registration of agent
capabilities, tools, and intents for dynamic workflow construction.
"""

import logging
from typing import Dict, Any, List, Set, Optional, Type
from datetime import datetime
import inspect
import ast
import importlib

from ..nodes.base_agent_node import BaseAgentNode
from .agent_factory import AgentNodeFactory

logger = logging.getLogger(__name__)


class CapabilityDiscovery:
    """
    System for discovering and cataloging agent capabilities.
    
    This system automatically analyzes agent implementations to
    discover their capabilities, tools, and supported intents.
    """
    
    def __init__(self, agent_factory: AgentNodeFactory):
        """
        Initialize the capability discovery system.
        
        Args:
            agent_factory: Agent factory instance
        """
        self.agent_factory = agent_factory
        self.logger = logging.getLogger(__name__)
        
        # Capability registry
        self.capabilities_registry: Dict[str, Dict[str, Any]] = {}
        
        # Intent registry
        self.intents_registry: Dict[str, Dict[str, Any]] = {}
        
        # Tool registry
        self.tools_registry: Dict[str, Dict[str, Any]] = {}
        
        # Agent-capability mappings
        self.agent_capabilities: Dict[str, Set[str]] = {}
        self.capability_agents: Dict[str, Set[str]] = {}
        
        # Discovery patterns
        self.capability_patterns = [
            r"def.*handle.*",
            r"def.*process.*",
            r"def.*analyze.*",
            r"def.*generate.*",
            r"def.*create.*"
        ]
        
        self.logger.info("CapabilityDiscovery initialized")
    
    async def discover_all_capabilities(self) -> Dict[str, Any]:
        """
        Discover capabilities for all registered agents.
        
        Returns:
            Discovery results summary
        """
        try:
            self.logger.info("Starting capability discovery for all agents")
            
            discovered_capabilities = 0
            discovered_intents = 0
            discovered_tools = 0
            
            # Discover capabilities for each agent
            for agent_id in self.agent_factory.get_available_agents():
                agent_results = await self.discover_agent_capabilities(agent_id)
                
                discovered_capabilities += len(agent_results.get("capabilities", []))
                discovered_intents += len(agent_results.get("intents", []))
                discovered_tools += len(agent_results.get("tools", []))
            
            # Build cross-references
            self._build_cross_references()
            
            results = {
                "total_agents": len(self.agent_factory.get_available_agents()),
                "discovered_capabilities": discovered_capabilities,
                "discovered_intents": discovered_intents,
                "discovered_tools": discovered_tools,
                "unique_capabilities": len(self.capabilities_registry),
                "unique_intents": len(self.intents_registry),
                "unique_tools": len(self.tools_registry),
                "discovery_timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(f"Capability discovery complete: {results}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in capability discovery: {e}", exc_info=True)
            return {"error": str(e)}
    
    async def discover_agent_capabilities(self, agent_id: str) -> Dict[str, Any]:
        """
        Discover capabilities for a specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Discovered capabilities, intents, and tools
        """
        try:
            # Get agent class
            if agent_id not in self.agent_factory.agent_classes:
                return {"error": f"Agent {agent_id} not found"}
            
            agent_class = self.agent_factory.agent_classes[agent_id]
            
            # Discover from class attributes
            static_capabilities = self._discover_static_capabilities(agent_class)
            
            # Discover from method analysis
            dynamic_capabilities = self._discover_dynamic_capabilities(agent_class)
            
            # Discover from docstrings and comments
            documented_capabilities = self._discover_documented_capabilities(agent_class)
            
            # Merge all discoveries
            all_capabilities = self._merge_capability_discoveries(
                static_capabilities,
                dynamic_capabilities,
                documented_capabilities
            )
            
            # Register discoveries
            self._register_agent_capabilities(agent_id, all_capabilities)
            
            self.logger.info(f"Discovered capabilities for agent {agent_id}: {len(all_capabilities.get('capabilities', []))} capabilities")
            
            return all_capabilities
            
        except Exception as e:
            self.logger.error(f"Error discovering capabilities for agent {agent_id}: {e}")
            return {"error": str(e)}
    
    def _discover_static_capabilities(self, agent_class: Type[BaseAgentNode]) -> Dict[str, Any]:
        """
        Discover capabilities from static class attributes.
        
        Args:
            agent_class: Agent class to analyze
            
        Returns:
            Static capabilities
        """
        capabilities = {
            "capabilities": [],
            "intents": [],
            "tools": [],
            "source": "static"
        }
        
        # Get capabilities from class attributes
        if hasattr(agent_class, "capabilities"):
            capabilities["capabilities"] = list(agent_class.capabilities)
        
        if hasattr(agent_class, "supported_intents"):
            capabilities["intents"] = list(agent_class.supported_intents)
        
        if hasattr(agent_class, "tools"):
            capabilities["tools"] = list(agent_class.tools)
        
        return capabilities
    
    def _discover_dynamic_capabilities(self, agent_class: Type[BaseAgentNode]) -> Dict[str, Any]:
        """
        Discover capabilities from method analysis.
        
        Args:
            agent_class: Agent class to analyze
            
        Returns:
            Dynamic capabilities
        """
        capabilities = {
            "capabilities": [],
            "intents": [],
            "tools": [],
            "source": "dynamic"
        }
        
        # Analyze class methods
        for method_name in dir(agent_class):
            if method_name.startswith("_handle_"):
                # Extract capability from handler method name
                capability = method_name.replace("_handle_", "").replace("_", " ")
                capabilities["capabilities"].append(capability)
            
            elif method_name.startswith("_analyze_"):
                # Extract analysis capability
                capability = method_name.replace("_analyze_", "").replace("_", " ") + " analysis"
                capabilities["capabilities"].append(capability)
            
            elif method_name.startswith("_generate_"):
                # Extract generation capability
                capability = method_name.replace("_generate_", "").replace("_", " ") + " generation"
                capabilities["capabilities"].append(capability)
        
        # Analyze method signatures and bodies for tool usage
        try:
            source = inspect.getsource(agent_class)
            tree = ast.parse(source)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    # Look for tool calls
                    if (isinstance(node.func, ast.Attribute) and 
                        node.func.attr == "update_tool_status"):
                        # Extract tool name from arguments
                        if node.args and isinstance(node.args[1], ast.Constant):
                            tool_name = node.args[1].value
                            if tool_name not in capabilities["tools"]:
                                capabilities["tools"].append(tool_name)
        
        except Exception as e:
            self.logger.debug(f"Error analyzing method bodies: {e}")
        
        return capabilities
    
    def _discover_documented_capabilities(self, agent_class: Type[BaseAgentNode]) -> Dict[str, Any]:
        """
        Discover capabilities from docstrings and comments.
        
        Args:
            agent_class: Agent class to analyze
            
        Returns:
            Documented capabilities
        """
        capabilities = {
            "capabilities": [],
            "intents": [],
            "tools": [],
            "source": "documented"
        }
        
        # Analyze class docstring
        if agent_class.__doc__:
            docstring = agent_class.__doc__.lower()
            
            # Look for capability keywords
            capability_keywords = [
                "analysis", "generation", "classification", "recommendation",
                "optimization", "visualization", "reporting", "processing"
            ]
            
            for keyword in capability_keywords:
                if keyword in docstring:
                    capabilities["capabilities"].append(keyword)
        
        # Analyze method docstrings
        for method_name in dir(agent_class):
            try:
                method = getattr(agent_class, method_name)
                if callable(method) and hasattr(method, "__doc__") and method.__doc__:
                    docstring = method.__doc__.lower()
                    
                    # Look for intent keywords
                    if "intent" in docstring or "handle" in docstring:
                        # Extract potential intent from method name or docstring
                        if method_name.startswith("_handle_"):
                            intent = method_name.replace("_handle_", "").replace("_", " ")
                            capabilities["intents"].append(intent)
            
            except Exception:
                continue
        
        return capabilities
    
    def _merge_capability_discoveries(self, *discoveries: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge multiple capability discoveries.
        
        Args:
            *discoveries: Discovery results to merge
            
        Returns:
            Merged capabilities
        """
        merged = {
            "capabilities": set(),
            "intents": set(),
            "tools": set(),
            "sources": []
        }
        
        for discovery in discoveries:
            merged["capabilities"].update(discovery.get("capabilities", []))
            merged["intents"].update(discovery.get("intents", []))
            merged["tools"].update(discovery.get("tools", []))
            merged["sources"].append(discovery.get("source", "unknown"))
        
        # Convert sets back to lists
        return {
            "capabilities": list(merged["capabilities"]),
            "intents": list(merged["intents"]),
            "tools": list(merged["tools"]),
            "sources": merged["sources"]
        }
    
    def _register_agent_capabilities(self, agent_id: str, capabilities: Dict[str, Any]) -> None:
        """
        Register discovered capabilities for an agent.
        
        Args:
            agent_id: Agent identifier
            capabilities: Discovered capabilities
        """
        # Register capabilities
        for capability in capabilities.get("capabilities", []):
            if capability not in self.capabilities_registry:
                self.capabilities_registry[capability] = {
                    "name": capability,
                    "agents": set(),
                    "description": "",
                    "category": self._categorize_capability(capability),
                    "discovered_at": datetime.now().isoformat()
                }
            self.capabilities_registry[capability]["agents"].add(agent_id)
        
        # Register intents
        for intent in capabilities.get("intents", []):
            if intent not in self.intents_registry:
                self.intents_registry[intent] = {
                    "name": intent,
                    "agents": set(),
                    "description": "",
                    "category": self._categorize_intent(intent),
                    "discovered_at": datetime.now().isoformat()
                }
            self.intents_registry[intent]["agents"].add(agent_id)
        
        # Register tools
        for tool in capabilities.get("tools", []):
            if tool not in self.tools_registry:
                self.tools_registry[tool] = {
                    "name": tool,
                    "agents": set(),
                    "description": "",
                    "category": self._categorize_tool(tool),
                    "discovered_at": datetime.now().isoformat()
                }
            self.tools_registry[tool]["agents"].add(agent_id)
        
        # Update agent mappings
        self.agent_capabilities[agent_id] = set(capabilities.get("capabilities", []))
    
    def _categorize_capability(self, capability: str) -> str:
        """Categorize a capability."""
        capability_lower = capability.lower()
        
        if any(word in capability_lower for word in ["analysis", "analyze", "statistical"]):
            return "analysis"
        elif any(word in capability_lower for word in ["generation", "create", "content"]):
            return "generation"
        elif any(word in capability_lower for word in ["classification", "categorization", "organize"]):
            return "classification"
        elif any(word in capability_lower for word in ["visualization", "chart", "graph"]):
            return "visualization"
        else:
            return "general"
    
    def _categorize_intent(self, intent: str) -> str:
        """Categorize an intent."""
        intent_lower = intent.lower()
        
        if any(word in intent_lower for word in ["data", "analysis"]):
            return "data_processing"
        elif any(word in intent_lower for word in ["content", "marketing", "social"]):
            return "content_creation"
        elif any(word in intent_lower for word in ["classification", "organization"]):
            return "organization"
        else:
            return "general"
    
    def _categorize_tool(self, tool: str) -> str:
        """Categorize a tool."""
        tool_lower = tool.lower()
        
        if any(word in tool_lower for word in ["analyzer", "analysis"]):
            return "analysis"
        elif any(word in tool_lower for word in ["generator", "creator"]):
            return "generation"
        elif any(word in tool_lower for word in ["classifier", "categorizer"]):
            return "classification"
        else:
            return "utility"
    
    def _build_cross_references(self) -> None:
        """Build cross-reference mappings."""
        # Build capability-to-agents mapping
        for capability, info in self.capabilities_registry.items():
            self.capability_agents[capability] = info["agents"]
    
    def get_agents_by_capability(self, capability: str) -> List[str]:
        """Get agents that have a specific capability."""
        return list(self.capability_agents.get(capability, set()))
    
    def get_capabilities_by_agent(self, agent_id: str) -> List[str]:
        """Get capabilities for a specific agent."""
        return list(self.agent_capabilities.get(agent_id, set()))
    
    def get_capability_categories(self) -> Dict[str, List[str]]:
        """Get capabilities grouped by category."""
        categories = {}
        for capability, info in self.capabilities_registry.items():
            category = info["category"]
            if category not in categories:
                categories[category] = []
            categories[category].append(capability)
        return categories
    
    def get_discovery_stats(self) -> Dict[str, Any]:
        """Get discovery statistics."""
        return {
            "total_capabilities": len(self.capabilities_registry),
            "total_intents": len(self.intents_registry),
            "total_tools": len(self.tools_registry),
            "agents_analyzed": len(self.agent_capabilities),
            "capability_categories": len(set(
                info["category"] for info in self.capabilities_registry.values()
            )),
            "most_capable_agent": max(
                self.agent_capabilities.items(),
                key=lambda x: len(x[1])
            )[0] if self.agent_capabilities else None
        }
