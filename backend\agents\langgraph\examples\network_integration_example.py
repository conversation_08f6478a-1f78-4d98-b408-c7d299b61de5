"""
Complete Network Architecture Integration Example.

This example demonstrates how to use the LangGraph Network of Agents architecture
to create a sophisticated multi-agent system for business data analysis.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Network Architecture Components
from ..core.agent_registry import network_registry
from ..core.agent_discovery import discovery_service, DiscoveryRequest, DiscoveryStrategy
from ..core.network_graph_builder import network_graph_builder
from ..communication.messaging_system import messaging_system, NetworkCommunicationType, NetworkMessagePriority
from ..orchestration.network_workflow_manager import network_workflow_manager, WorkflowPattern
from ..consensus.consensus_mechanisms import consensus_manager, ConsensusType
from ..intelligence.network_intelligence import network_intelligence, InsightType
from ..states.network_state import create_network_state

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NetworkIntegrationDemo:
    """
    Comprehensive demonstration of the Network of Agents architecture.
    
    This demo shows:
    1. Agent registration and discovery
    2. Inter-agent communication
    3. Multi-agent workflows
    4. Consensus building
    5. Network intelligence and learning
    """

    def __init__(self):
        """Initialize the demo."""
        self.demo_agents = [
            {
                "agent_id": "concierge_agent",
                "agent_type": "concierge",
                "name": "Business Concierge",
                "description": "General business assistant and coordinator",
                "capabilities": [
                    {"name": "conversation", "confidence_level": 0.95},
                    {"name": "task_coordination", "confidence_level": 0.9},
                    {"name": "business_context", "confidence_level": 0.85}
                ]
            },
            {
                "agent_id": "data_analyst",
                "agent_type": "analysis",
                "name": "Data Analysis Specialist",
                "description": "Expert in data analysis and statistical modeling",
                "capabilities": [
                    {"name": "data_analysis", "confidence_level": 0.95},
                    {"name": "statistical_modeling", "confidence_level": 0.9},
                    {"name": "data_visualization", "confidence_level": 0.8}
                ]
            },
            {
                "agent_id": "marketing_expert",
                "agent_type": "marketing",
                "name": "Marketing Strategy Expert",
                "description": "Specialized in marketing strategy and campaign planning",
                "capabilities": [
                    {"name": "marketing_strategy", "confidence_level": 0.95},
                    {"name": "campaign_planning", "confidence_level": 0.9},
                    {"name": "market_research", "confidence_level": 0.85}
                ]
            },
            {
                "agent_id": "visualization_specialist",
                "agent_type": "visualization",
                "name": "Data Visualization Expert",
                "description": "Expert in creating charts, dashboards, and visual reports",
                "capabilities": [
                    {"name": "data_visualization", "confidence_level": 0.95},
                    {"name": "dashboard_creation", "confidence_level": 0.9},
                    {"name": "report_generation", "confidence_level": 0.85}
                ]
            },
            {
                "agent_id": "business_analyst",
                "agent_type": "business",
                "name": "Business Intelligence Analyst",
                "description": "Focuses on business insights and strategic recommendations",
                "capabilities": [
                    {"name": "business_intelligence", "confidence_level": 0.9},
                    {"name": "strategic_analysis", "confidence_level": 0.85},
                    {"name": "performance_metrics", "confidence_level": 0.8}
                ]
            }
        ]

    async def run_complete_demo(self):
        """Run the complete network architecture demonstration."""
        logger.info("🚀 Starting Network of Agents Integration Demo")
        
        try:
            # Step 1: Register agents in the network
            await self.register_demo_agents()
            
            # Step 2: Demonstrate agent discovery
            await self.demonstrate_agent_discovery()
            
            # Step 3: Show inter-agent communication
            await self.demonstrate_inter_agent_communication()
            
            # Step 4: Create and execute multi-agent workflow
            await self.demonstrate_multi_agent_workflow()
            
            # Step 5: Build consensus among agents
            await self.demonstrate_consensus_building()
            
            # Step 6: Show network intelligence and learning
            await self.demonstrate_network_intelligence()
            
            # Step 7: Create network graph and execute workflow
            await self.demonstrate_network_graph_execution()
            
            # Step 8: Display final network statistics
            await self.display_network_statistics()
            
            logger.info("✅ Network Architecture Demo Completed Successfully!")
            
        except Exception as e:
            logger.error(f"❌ Demo failed with error: {e}")
            raise

    async def register_demo_agents(self):
        """Register all demo agents in the network."""
        logger.info("📝 Registering agents in the network...")
        
        for agent_config in self.demo_agents:
            success = await network_registry.register_agent(
                agent_id=agent_config["agent_id"],
                agent_type=agent_config["agent_type"],
                name=agent_config["name"],
                description=agent_config["description"],
                capabilities=agent_config["capabilities"],
                communication_capabilities=[
                    "direct_message",
                    "consultation",
                    "delegation",
                    "collaboration"
                ]
            )
            
            if success:
                # Register agent in graph builder
                network_graph_builder.register_network_agent(
                    agent_id=agent_config["agent_id"],
                    agent_config=agent_config,
                    capabilities=[cap["name"] for cap in agent_config["capabilities"]]
                )
                
                logger.info(f"✅ Registered: {agent_config['name']}")
            else:
                logger.error(f"❌ Failed to register: {agent_config['name']}")
        
        # Display registry statistics
        stats = network_registry.get_registry_stats()
        logger.info(f"📊 Network Registry: {stats['total_agents']} agents, {stats['total_capabilities']} capabilities")

    async def demonstrate_agent_discovery(self):
        """Demonstrate agent discovery capabilities."""
        logger.info("🔍 Demonstrating agent discovery...")
        
        # Scenario: Concierge needs to find agents for data analysis
        discovery_request = DiscoveryRequest(
            requesting_agent="concierge_agent",
            required_capabilities=["data_analysis"],
            preferred_capabilities=["data_visualization"],
            strategy=DiscoveryStrategy.HYBRID,
            max_results=3
        )
        
        results = await discovery_service.discover_agents(discovery_request)
        
        logger.info(f"🎯 Found {len(results)} agents for data analysis:")
        for result in results:
            logger.info(f"  - {result.agent.name} (score: {result.match_score:.2f}) - {result.recommendation_reason}")
        
        # Find specialist for marketing strategy
        marketing_specialist = await discovery_service.find_specialist_for_capability(
            capability="marketing_strategy",
            requesting_agent="concierge_agent"
        )
        
        if marketing_specialist:
            logger.info(f"🎯 Marketing specialist found: {marketing_specialist.name}")

    async def demonstrate_inter_agent_communication(self):
        """Demonstrate direct communication between agents."""
        logger.info("💬 Demonstrating inter-agent communication...")
        
        # Concierge requests consultation from data analyst
        consultation_message_id = await messaging_system.send_message(
            sender_agent="concierge_agent",
            recipient_agent="data_analyst",
            message_type=NetworkCommunicationType.CONSULTATION_REQUEST,
            content={
                "consultation_topic": "sales_data_analysis",
                "context": {
                    "data_type": "quarterly_sales",
                    "analysis_needed": "trend_analysis",
                    "urgency": "high"
                },
                "deadline": (datetime.now() + timedelta(hours=2)).isoformat()
            },
            priority=NetworkMessagePriority.HIGH,
            response_expected=True
        )
        
        logger.info(f"📤 Consultation request sent (ID: {consultation_message_id})")
        
        # Allow message processing
        await asyncio.sleep(0.5)
        
        # Data analyst receives and responds to consultation
        messages = await messaging_system.get_messages_for_agent("data_analyst")
        consultation_msg = next(
            (m for m in messages if m.message_type == NetworkCommunicationType.CONSULTATION_REQUEST),
            None
        )
        
        if consultation_msg:
            logger.info(f"📥 Data analyst received consultation request")
            
            # Send response
            response_id = await messaging_system.send_response(
                original_message=consultation_msg,
                response_content={
                    "consultation_response": "I can perform the sales trend analysis",
                    "estimated_time": "30 minutes",
                    "required_data": ["sales_records", "customer_data"],
                    "analysis_methods": ["time_series", "regression_analysis"],
                    "deliverables": ["trend_report", "forecast_model"]
                },
                response_type=NetworkCommunicationType.CONSULTATION_RESPONSE
            )
            
            logger.info(f"📤 Data analyst sent response (ID: {response_id})")

    async def demonstrate_multi_agent_workflow(self):
        """Demonstrate complex multi-agent workflow execution."""
        logger.info("🔄 Creating multi-agent workflow...")
        
        # Define a comprehensive business analysis workflow
        workflow_tasks = [
            {
                "name": "Data Collection and Preparation",
                "description": "Gather and prepare business data for analysis",
                "required_capabilities": ["data_analysis"],
                "dependencies": [],
                "priority": 1
            },
            {
                "name": "Statistical Analysis",
                "description": "Perform statistical analysis on prepared data",
                "required_capabilities": ["statistical_modeling", "data_analysis"],
                "dependencies": ["data_collection_and_preparation"],
                "priority": 1
            },
            {
                "name": "Market Research Integration",
                "description": "Integrate market research insights",
                "required_capabilities": ["market_research", "marketing_strategy"],
                "dependencies": ["data_collection_and_preparation"],
                "priority": 2
            },
            {
                "name": "Visualization Creation",
                "description": "Create visualizations and dashboards",
                "required_capabilities": ["data_visualization", "dashboard_creation"],
                "dependencies": ["statistical_analysis"],
                "priority": 2
            },
            {
                "name": "Business Intelligence Report",
                "description": "Generate comprehensive business intelligence report",
                "required_capabilities": ["business_intelligence", "report_generation"],
                "dependencies": ["statistical_analysis", "market_research_integration", "visualization_creation"],
                "priority": 3
            }
        ]
        
        # Create workflow
        workflow_id = await network_workflow_manager.create_workflow(
            name="Comprehensive Business Analysis",
            description="Multi-agent workflow for complete business data analysis",
            pattern=WorkflowPattern.PIPELINE,
            tasks=workflow_tasks,
            coordinator_agent="concierge_agent",
            deadline=datetime.now() + timedelta(hours=4)
        )
        
        logger.info(f"📋 Created workflow: {workflow_id}")
        
        # Start workflow execution
        success = await network_workflow_manager.start_workflow(workflow_id)
        if success:
            logger.info("🚀 Workflow execution started")
            
            # Monitor workflow progress
            await asyncio.sleep(1)  # Allow some processing time
            
            status = network_workflow_manager.get_workflow_status(workflow_id)
            if status:
                logger.info(f"📊 Workflow progress: {status['progress']:.1f}%")
                logger.info(f"👥 Participating agents: {', '.join(status['participating_agents'])}")

    async def demonstrate_consensus_building(self):
        """Demonstrate consensus building among agents."""
        logger.info("🤝 Demonstrating consensus building...")
        
        # Create consensus on analysis methodology
        consensus_id = await consensus_manager.initiate_consensus(
            initiating_agent="concierge_agent",
            title="Choose Analysis Methodology",
            description="Select the best methodology for quarterly business analysis",
            consensus_type=ConsensusType.WEIGHTED_VOTING,
            options=[
                {
                    "id": "traditional_stats",
                    "name": "Traditional Statistical Analysis",
                    "description": "Use classical statistical methods"
                },
                {
                    "id": "machine_learning",
                    "name": "Machine Learning Approach",
                    "description": "Apply ML algorithms for analysis"
                },
                {
                    "id": "hybrid_approach",
                    "name": "Hybrid Statistical-ML Approach",
                    "description": "Combine traditional stats with ML"
                }
            ],
            participating_agents=["data_analyst", "marketing_expert", "business_analyst"],
            threshold=0.6,
            deadline=datetime.now() + timedelta(hours=1)
        )
        
        logger.info(f"🗳️ Consensus process initiated: {consensus_id}")
        
        # Simulate agent votes
        votes = [
            ("data_analyst", "hybrid_approach", "Best of both worlds", 0.9),
            ("marketing_expert", "machine_learning", "Better for market predictions", 0.8),
            ("business_analyst", "hybrid_approach", "More comprehensive insights", 0.85)
        ]
        
        for agent_id, choice, reasoning, confidence in votes:
            await consensus_manager.submit_vote(
                consensus_id=consensus_id,
                voter_agent=agent_id,
                vote_data={"option": choice},
                reasoning=reasoning,
                confidence=confidence
            )
            logger.info(f"✅ {agent_id} voted for {choice}")
        
        # Allow consensus evaluation
        await asyncio.sleep(0.5)
        
        # Check consensus result
        status = consensus_manager.get_consensus_status(consensus_id)
        if status:
            logger.info(f"📊 Consensus status: {status['status']}")
            logger.info(f"📈 Participation rate: {status['participation_rate']:.1%}")

    async def demonstrate_network_intelligence(self):
        """Demonstrate network intelligence and learning capabilities."""
        logger.info("🧠 Demonstrating network intelligence...")
        
        # Record performance metrics
        await network_intelligence.record_performance_metric(
            agent_id="data_analyst",
            metric_name="analysis_accuracy",
            metric_value=0.92,
            context={"task_type": "sales_analysis", "data_size": "large"}
        )
        
        await network_intelligence.record_performance_metric(
            agent_id="marketing_expert",
            metric_name="strategy_effectiveness",
            metric_value=0.88,
            context={"campaign_type": "digital", "target_audience": "b2b"}
        )
        
        # Record successful collaboration
        await network_intelligence.record_interaction_pattern(
            agent1_id="data_analyst",
            agent2_id="marketing_expert",
            interaction_type="collaboration",
            outcome="success",
            metadata={"project_type": "market_analysis", "duration": 45}
        )
        
        # Add insights to the network
        insight_id = await network_intelligence.add_insight(
            source_agent="data_analyst",
            insight_type=InsightType.WORKFLOW_OPTIMIZATION,
            title="Parallel Processing Optimization",
            description="Using parallel processing for large datasets improves analysis speed by 40%",
            content={
                "optimization_type": "parallel_processing",
                "performance_gain": 0.4,
                "applicable_data_sizes": ["large", "very_large"],
                "implementation": "use_multiprocessing_library"
            },
            relevance_score=0.9,
            confidence_score=0.85,
            applicable_capabilities=["data_analysis", "statistical_modeling"]
        )
        
        logger.info(f"💡 Added network insight: {insight_id}")
        
        # Retrieve insights for an agent
        insights = await network_intelligence.get_insights_for_agent(
            agent_id="marketing_expert",
            min_relevance=0.5,
            limit=5
        )
        
        logger.info(f"🔍 Found {len(insights)} relevant insights for marketing expert")
        for insight in insights[:2]:  # Show first 2
            logger.info(f"  - {insight.title} (relevance: {insight.relevance_score:.2f})")

    async def demonstrate_network_graph_execution(self):
        """Demonstrate network graph creation and execution."""
        logger.info("🕸️ Creating and executing network graph...")
        
        # Create network workflow configuration
        workflow_config = network_graph_builder.create_network_workflow(
            user_id="demo_user",
            conversation_id="demo_conversation",
            initial_message={"text": "Analyze our Q4 business performance"},
            workflow_type="business_analysis",
            initial_agent="concierge_agent",
            business_profile_id="demo_business_profile"
        )
        
        logger.info(f"📊 Network workflow created with {len(workflow_config['available_agents'])} agents")
        logger.info(f"🌐 Network topology: {len(workflow_config['network_topology'])} connections")
        
        # The graph would be executed here in a real scenario
        # For demo purposes, we'll just show the configuration
        initial_state = workflow_config["initial_state"]
        logger.info(f"🎯 Initial agent: {initial_state.get('current_agent', 'Not set')}")
        logger.info(f"📝 Workflow type: {initial_state.get('workflow_type')}")

    async def display_network_statistics(self):
        """Display comprehensive network statistics."""
        logger.info("📊 Final Network Statistics:")
        
        # Registry statistics
        registry_stats = network_registry.get_registry_stats()
        logger.info(f"  🤖 Total Agents: {registry_stats['total_agents']}")
        logger.info(f"  ⚡ Active Agents: {registry_stats['active_agents']}")
        logger.info(f"  🔧 Total Capabilities: {registry_stats['total_capabilities']}")
        logger.info(f"  🔗 Network Connections: {registry_stats['total_connections']}")
        
        # Messaging statistics
        messaging_stats = messaging_system.get_messaging_stats()
        logger.info(f"  📨 Total Messages: {messaging_stats['total_messages']}")
        logger.info(f"  💬 Active Conversations: {messaging_stats['active_conversations']}")
        
        # Workflow statistics
        workflow_stats = network_workflow_manager.get_manager_stats()
        logger.info(f"  🔄 Total Workflows: {workflow_stats['total_workflows']}")
        logger.info(f"  ✅ Completed Workflows: {workflow_stats['completed_workflows']}")
        
        # Consensus statistics
        consensus_stats = consensus_manager.get_consensus_stats()
        logger.info(f"  🗳️ Active Consensus: {consensus_stats['active_consensus']}")
        logger.info(f"  📈 Success Rate: {consensus_stats['success_rate']:.1%}")
        
        # Intelligence statistics
        intelligence_stats = network_intelligence.get_intelligence_stats()
        logger.info(f"  🧠 Total Insights: {intelligence_stats['total_insights']}")
        logger.info(f"  🤝 Collaboration Pairs: {intelligence_stats['collaboration_pairs']}")
        
        # Network graph statistics
        network_stats = network_graph_builder.get_network_stats()
        logger.info(f"  🕸️ Registered Network Agents: {network_stats['registered_agents']}")


async def main():
    """Run the network integration demonstration."""
    demo = NetworkIntegrationDemo()
    await demo.run_complete_demo()


if __name__ == "__main__":
    # Run the demonstration
    asyncio.run(main())
