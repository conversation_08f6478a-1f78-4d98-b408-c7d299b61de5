"""
Consensus Mechanisms for LangGraph Network Architecture.

This module provides consensus building mechanisms that enable multiple agents
to reach agreement on decisions, solutions, and collaborative outcomes.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json
from collections import defaultdict

from ..states.network_state import NetworkDatageniusState, NetworkCommunicationType, NetworkMessagePriority
from ..core.agent_registry import network_registry, NetworkAgent
from ..communication.messaging_system import messaging_system

logger = logging.getLogger(__name__)


class ConsensusType(str, Enum):
    """Types of consensus mechanisms."""
    SIMPLE_MAJORITY = "simple_majority"
    QUALIFIED_MAJORITY = "qualified_majority"
    UNANIMOUS = "unanimous"
    WEIGHTED_VOTING = "weighted_voting"
    RANKED_CHOICE = "ranked_choice"
    APPROVAL_VOTING = "approval_voting"
    EXPERT_CONSENSUS = "expert_consensus"
    ITERATIVE_REFINEMENT = "iterative_refinement"


class VoteStatus(str, Enum):
    """Status of votes."""
    PENDING = "pending"
    SUBMITTED = "submitted"
    ABSTAINED = "abstained"
    INVALID = "invalid"


class ConsensusStatus(str, Enum):
    """Status of consensus processes."""
    INITIALIZING = "initializing"
    VOTING = "voting"
    DELIBERATING = "deliberating"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Vote:
    """Represents a vote in a consensus process."""
    vote_id: str
    voter_agent: str
    consensus_id: str
    vote_data: Dict[str, Any]
    weight: float = 1.0
    status: VoteStatus = VoteStatus.PENDING
    submitted_at: Optional[datetime] = None
    reasoning: Optional[str] = None
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConsensusProcess:
    """Represents a consensus building process."""
    consensus_id: str
    title: str
    description: str
    consensus_type: ConsensusType
    initiating_agent: str
    participating_agents: List[str]
    options: List[Dict[str, Any]]
    votes: Dict[str, Vote] = field(default_factory=dict)
    status: ConsensusStatus = ConsensusStatus.INITIALIZING
    threshold: float = 0.5  # Threshold for consensus (0.0-1.0)
    deadline: Optional[datetime] = None
    allow_abstention: bool = True
    allow_discussion: bool = True
    min_participation: float = 0.5  # Minimum participation required
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    discussion_messages: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def get_participation_rate(self) -> float:
        """Calculate current participation rate."""
        if not self.participating_agents:
            return 0.0
        
        submitted_votes = len([v for v in self.votes.values() if v.status == VoteStatus.SUBMITTED])
        return submitted_votes / len(self.participating_agents)

    def has_reached_threshold(self) -> bool:
        """Check if consensus threshold has been reached."""
        return self.get_participation_rate() >= self.min_participation

    def is_expired(self) -> bool:
        """Check if consensus process has expired."""
        return self.deadline is not None and datetime.now() > self.deadline


class ConsensusManager:
    """
    Manager for consensus building processes in the network.
    
    This manager provides:
    - Multiple consensus mechanisms
    - Vote collection and validation
    - Discussion facilitation
    - Result calculation and distribution
    - Consensus process monitoring
    """

    def __init__(self):
        """Initialize the consensus manager."""
        self.active_consensus: Dict[str, ConsensusProcess] = {}
        self.consensus_history: Dict[str, ConsensusProcess] = {}
        self.consensus_handlers: Dict[ConsensusType, Callable] = {}
        
        # Performance tracking
        self.consensus_metrics: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.agent_voting_patterns: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # Configuration
        self.max_active_consensus = 20
        self.default_voting_period = timedelta(hours=24)
        self.discussion_period = timedelta(hours=2)
        
        # Initialize consensus handlers
        self._initialize_consensus_handlers()
        
        # Background tasks
        self._monitoring_task = None
        self._cleanup_task = None
        self._start_background_tasks()
        
        logger.info("ConsensusManager initialized")

    def _initialize_consensus_handlers(self):
        """Initialize consensus calculation handlers."""
        self.consensus_handlers = {
            ConsensusType.SIMPLE_MAJORITY: self._calculate_simple_majority,
            ConsensusType.QUALIFIED_MAJORITY: self._calculate_qualified_majority,
            ConsensusType.UNANIMOUS: self._calculate_unanimous,
            ConsensusType.WEIGHTED_VOTING: self._calculate_weighted_voting,
            ConsensusType.RANKED_CHOICE: self._calculate_ranked_choice,
            ConsensusType.APPROVAL_VOTING: self._calculate_approval_voting,
            ConsensusType.EXPERT_CONSENSUS: self._calculate_expert_consensus,
            ConsensusType.ITERATIVE_REFINEMENT: self._calculate_iterative_refinement
        }

    def _start_background_tasks(self):
        """Start background monitoring and cleanup tasks."""
        self._monitoring_task = asyncio.create_task(self._consensus_monitoring_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())

    async def _consensus_monitoring_loop(self):
        """Background loop for monitoring consensus processes."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self._monitor_active_consensus()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in consensus monitoring loop: {e}")

    async def _cleanup_loop(self):
        """Background loop for cleaning up old consensus processes."""
        while True:
            try:
                await asyncio.sleep(3600)  # Check every hour
                await self._cleanup_old_consensus()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")

    async def initiate_consensus(
        self,
        initiating_agent: str,
        title: str,
        description: str,
        consensus_type: ConsensusType,
        options: List[Dict[str, Any]],
        participating_agents: Optional[List[str]] = None,
        threshold: float = 0.5,
        deadline: Optional[datetime] = None,
        allow_discussion: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Initiate a new consensus process.
        
        Args:
            initiating_agent: Agent initiating the consensus
            title: Title of the consensus process
            description: Description of what needs consensus
            consensus_type: Type of consensus mechanism to use
            options: List of options to choose from
            participating_agents: List of agents to participate (None for all)
            threshold: Consensus threshold (0.0-1.0)
            deadline: Deadline for consensus
            allow_discussion: Whether to allow discussion
            metadata: Additional metadata
            
        Returns:
            Consensus ID
        """
        try:
            consensus_id = str(uuid.uuid4())
            
            # Determine participating agents
            if participating_agents is None:
                # Include all available agents except initiator
                all_agents = network_registry.get_all_agents()
                participating_agents = [
                    agent.agent_id for agent in all_agents 
                    if agent.agent_id != initiating_agent and agent.is_available()
                ]
            
            # Set default deadline if not provided
            if deadline is None:
                deadline = datetime.now() + self.default_voting_period
            
            # Create consensus process
            consensus_process = ConsensusProcess(
                consensus_id=consensus_id,
                title=title,
                description=description,
                consensus_type=consensus_type,
                initiating_agent=initiating_agent,
                participating_agents=participating_agents,
                options=options,
                threshold=threshold,
                deadline=deadline,
                allow_discussion=allow_discussion,
                metadata=metadata or {}
            )
            
            # Store consensus process
            self.active_consensus[consensus_id] = consensus_process
            
            # Send consensus invitations
            await self._send_consensus_invitations(consensus_process)
            
            # Start consensus process
            consensus_process.status = ConsensusStatus.VOTING
            consensus_process.started_at = datetime.now()
            
            logger.info(f"Initiated consensus {consensus_id} with {len(participating_agents)} participants")
            return consensus_id
            
        except Exception as e:
            logger.error(f"Error initiating consensus: {e}")
            raise

    async def _send_consensus_invitations(self, consensus_process: ConsensusProcess):
        """Send consensus invitations to participating agents."""
        for agent_id in consensus_process.participating_agents:
            await messaging_system.send_message(
                sender_agent=consensus_process.initiating_agent,
                recipient_agent=agent_id,
                message_type=NetworkCommunicationType.CONSENSUS_VOTE,
                content={
                    "consensus_id": consensus_process.consensus_id,
                    "title": consensus_process.title,
                    "description": consensus_process.description,
                    "consensus_type": consensus_process.consensus_type.value,
                    "options": consensus_process.options,
                    "deadline": consensus_process.deadline.isoformat() if consensus_process.deadline else None,
                    "allow_discussion": consensus_process.allow_discussion,
                    "action": "vote_request"
                },
                priority=NetworkMessagePriority.HIGH,
                response_expected=True
            )

    async def submit_vote(
        self,
        consensus_id: str,
        voter_agent: str,
        vote_data: Dict[str, Any],
        reasoning: Optional[str] = None,
        confidence: float = 1.0
    ) -> bool:
        """
        Submit a vote for a consensus process.
        
        Args:
            consensus_id: ID of the consensus process
            voter_agent: Agent submitting the vote
            vote_data: Vote data (format depends on consensus type)
            reasoning: Optional reasoning for the vote
            confidence: Confidence in the vote (0.0-1.0)
            
        Returns:
            True if vote was accepted
        """
        try:
            consensus_process = self.active_consensus.get(consensus_id)
            if not consensus_process:
                logger.error(f"Consensus process {consensus_id} not found")
                return False
            
            if consensus_process.status != ConsensusStatus.VOTING:
                logger.error(f"Consensus process {consensus_id} is not in voting state")
                return False
            
            if voter_agent not in consensus_process.participating_agents:
                logger.error(f"Agent {voter_agent} not authorized to vote in {consensus_id}")
                return False
            
            if consensus_process.is_expired():
                logger.error(f"Consensus process {consensus_id} has expired")
                return False
            
            # Create vote
            vote = Vote(
                vote_id=str(uuid.uuid4()),
                voter_agent=voter_agent,
                consensus_id=consensus_id,
                vote_data=vote_data,
                status=VoteStatus.SUBMITTED,
                submitted_at=datetime.now(),
                reasoning=reasoning,
                confidence=confidence
            )
            
            # Store vote
            consensus_process.votes[voter_agent] = vote
            
            # Check if consensus is reached
            if consensus_process.has_reached_threshold():
                await self._evaluate_consensus(consensus_process)
            
            logger.info(f"Vote submitted by {voter_agent} for consensus {consensus_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error submitting vote: {e}")
            return False

    async def _evaluate_consensus(self, consensus_process: ConsensusProcess):
        """Evaluate if consensus has been reached."""
        try:
            # Get consensus handler
            handler = self.consensus_handlers.get(consensus_process.consensus_type)
            if not handler:
                logger.error(f"No handler for consensus type {consensus_process.consensus_type}")
                return
            
            # Calculate consensus result
            result = await handler(consensus_process)
            
            if result.get("consensus_reached", False):
                # Consensus reached
                consensus_process.status = ConsensusStatus.COMPLETED
                consensus_process.completed_at = datetime.now()
                consensus_process.result = result
                
                # Notify participants of result
                await self._notify_consensus_result(consensus_process)
                
                # Move to history
                self.consensus_history[consensus_process.consensus_id] = consensus_process
                del self.active_consensus[consensus_process.consensus_id]
                
                logger.info(f"Consensus {consensus_process.consensus_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Error evaluating consensus: {e}")

    async def _calculate_simple_majority(self, consensus_process: ConsensusProcess) -> Dict[str, Any]:
        """Calculate simple majority consensus."""
        vote_counts = defaultdict(int)
        total_votes = 0
        
        for vote in consensus_process.votes.values():
            if vote.status == VoteStatus.SUBMITTED:
                option = vote.vote_data.get("option")
                if option is not None:
                    vote_counts[option] += 1
                    total_votes += 1
        
        if total_votes == 0:
            return {"consensus_reached": False, "reason": "No votes submitted"}
        
        # Find option with most votes
        winning_option = max(vote_counts.items(), key=lambda x: x[1])
        winning_percentage = winning_option[1] / total_votes
        
        consensus_reached = winning_percentage > 0.5
        
        return {
            "consensus_reached": consensus_reached,
            "winning_option": winning_option[0],
            "vote_percentage": winning_percentage,
            "vote_counts": dict(vote_counts),
            "total_votes": total_votes
        }

    async def _calculate_qualified_majority(self, consensus_process: ConsensusProcess) -> Dict[str, Any]:
        """Calculate qualified majority consensus (requires higher threshold)."""
        result = await self._calculate_simple_majority(consensus_process)
        
        if result["consensus_reached"]:
            # Check if it meets qualified majority threshold
            qualified_threshold = consensus_process.threshold
            consensus_reached = result["vote_percentage"] >= qualified_threshold
            result["consensus_reached"] = consensus_reached
            
            if not consensus_reached:
                result["reason"] = f"Did not meet qualified majority threshold of {qualified_threshold}"
        
        return result

    async def _calculate_unanimous(self, consensus_process: ConsensusProcess) -> Dict[str, Any]:
        """Calculate unanimous consensus."""
        vote_counts = defaultdict(int)
        total_votes = 0
        
        for vote in consensus_process.votes.values():
            if vote.status == VoteStatus.SUBMITTED:
                option = vote.vote_data.get("option")
                if option is not None:
                    vote_counts[option] += 1
                    total_votes += 1
        
        if total_votes == 0:
            return {"consensus_reached": False, "reason": "No votes submitted"}
        
        # Check if all votes are for the same option
        if len(vote_counts) == 1:
            winning_option = list(vote_counts.keys())[0]
            return {
                "consensus_reached": True,
                "winning_option": winning_option,
                "vote_percentage": 1.0,
                "vote_counts": dict(vote_counts),
                "total_votes": total_votes
            }
        else:
            return {
                "consensus_reached": False,
                "reason": "Not unanimous",
                "vote_counts": dict(vote_counts),
                "total_votes": total_votes
            }

    async def _calculate_weighted_voting(self, consensus_process: ConsensusProcess) -> Dict[str, Any]:
        """Calculate weighted voting consensus."""
        vote_weights = defaultdict(float)
        total_weight = 0.0
        
        for vote in consensus_process.votes.values():
            if vote.status == VoteStatus.SUBMITTED:
                option = vote.vote_data.get("option")
                if option is not None:
                    weight = vote.weight * vote.confidence
                    vote_weights[option] += weight
                    total_weight += weight
        
        if total_weight == 0:
            return {"consensus_reached": False, "reason": "No weighted votes submitted"}
        
        # Find option with highest weighted score
        winning_option = max(vote_weights.items(), key=lambda x: x[1])
        winning_percentage = winning_option[1] / total_weight
        
        consensus_reached = winning_percentage >= consensus_process.threshold
        
        return {
            "consensus_reached": consensus_reached,
            "winning_option": winning_option[0],
            "weighted_percentage": winning_percentage,
            "weighted_scores": dict(vote_weights),
            "total_weight": total_weight
        }

    async def _calculate_ranked_choice(self, consensus_process: ConsensusProcess) -> Dict[str, Any]:
        """Calculate ranked choice consensus."""
        # Implementation for ranked choice voting
        # This is a simplified version - full implementation would handle elimination rounds
        
        first_choice_counts = defaultdict(int)
        total_votes = 0
        
        for vote in consensus_process.votes.values():
            if vote.status == VoteStatus.SUBMITTED:
                rankings = vote.vote_data.get("rankings", [])
                if rankings:
                    first_choice = rankings[0]
                    first_choice_counts[first_choice] += 1
                    total_votes += 1
        
        if total_votes == 0:
            return {"consensus_reached": False, "reason": "No ranked votes submitted"}
        
        # Check if any option has majority of first choices
        for option, count in first_choice_counts.items():
            if count / total_votes > 0.5:
                return {
                    "consensus_reached": True,
                    "winning_option": option,
                    "vote_percentage": count / total_votes,
                    "first_choice_counts": dict(first_choice_counts),
                    "total_votes": total_votes
                }
        
        return {
            "consensus_reached": False,
            "reason": "No majority winner in first round",
            "first_choice_counts": dict(first_choice_counts),
            "total_votes": total_votes
        }

    async def _calculate_approval_voting(self, consensus_process: ConsensusProcess) -> Dict[str, Any]:
        """Calculate approval voting consensus."""
        approval_counts = defaultdict(int)
        total_voters = 0
        
        for vote in consensus_process.votes.values():
            if vote.status == VoteStatus.SUBMITTED:
                approved_options = vote.vote_data.get("approved_options", [])
                total_voters += 1
                
                for option in approved_options:
                    approval_counts[option] += 1
        
        if total_voters == 0:
            return {"consensus_reached": False, "reason": "No approval votes submitted"}
        
        # Find option with highest approval
        winning_option = max(approval_counts.items(), key=lambda x: x[1])
        approval_percentage = winning_option[1] / total_voters
        
        consensus_reached = approval_percentage >= consensus_process.threshold
        
        return {
            "consensus_reached": consensus_reached,
            "winning_option": winning_option[0],
            "approval_percentage": approval_percentage,
            "approval_counts": dict(approval_counts),
            "total_voters": total_voters
        }

    async def _calculate_expert_consensus(self, consensus_process: ConsensusProcess) -> Dict[str, Any]:
        """Calculate expert-weighted consensus."""
        # Weight votes based on agent expertise/performance
        weighted_votes = defaultdict(float)
        total_weight = 0.0
        
        for vote in consensus_process.votes.values():
            if vote.status == VoteStatus.SUBMITTED:
                # Get agent expertise weight
                agent = network_registry.get_agent(vote.voter_agent)
                if agent:
                    # Calculate expertise weight based on performance metrics
                    expertise_weight = self._calculate_expertise_weight(agent)
                    
                    option = vote.vote_data.get("option")
                    if option is not None:
                        weight = expertise_weight * vote.confidence
                        weighted_votes[option] += weight
                        total_weight += weight
        
        if total_weight == 0:
            return {"consensus_reached": False, "reason": "No expert votes submitted"}
        
        # Find option with highest expert-weighted score
        winning_option = max(weighted_votes.items(), key=lambda x: x[1])
        winning_percentage = winning_option[1] / total_weight
        
        consensus_reached = winning_percentage >= consensus_process.threshold
        
        return {
            "consensus_reached": consensus_reached,
            "winning_option": winning_option[0],
            "expert_weighted_percentage": winning_percentage,
            "expert_weighted_scores": dict(weighted_votes),
            "total_expert_weight": total_weight
        }

    async def _calculate_iterative_refinement(self, consensus_process: ConsensusProcess) -> Dict[str, Any]:
        """Calculate iterative refinement consensus."""
        # This would implement multiple rounds of voting with refinement
        # For now, fall back to simple majority
        return await self._calculate_simple_majority(consensus_process)

    def _calculate_expertise_weight(self, agent: NetworkAgent) -> float:
        """Calculate expertise weight for an agent."""
        if not agent.performance_metrics:
            return 1.0  # Default weight
        
        # Average performance metrics as expertise indicator
        avg_performance = sum(agent.performance_metrics.values()) / len(agent.performance_metrics)
        
        # Scale to reasonable weight range (0.5 to 2.0)
        expertise_weight = 0.5 + (avg_performance * 1.5)
        
        return min(2.0, max(0.5, expertise_weight))

    async def _notify_consensus_result(self, consensus_process: ConsensusProcess):
        """Notify all participants of consensus result."""
        for agent_id in consensus_process.participating_agents + [consensus_process.initiating_agent]:
            await messaging_system.send_message(
                sender_agent="consensus_manager",
                recipient_agent=agent_id,
                message_type=NetworkCommunicationType.CONSENSUS_VOTE,
                content={
                    "consensus_id": consensus_process.consensus_id,
                    "title": consensus_process.title,
                    "result": consensus_process.result,
                    "status": consensus_process.status.value,
                    "completed_at": consensus_process.completed_at.isoformat() if consensus_process.completed_at else None,
                    "action": "consensus_result"
                },
                priority=NetworkMessagePriority.HIGH
            )

    async def _monitor_active_consensus(self):
        """Monitor active consensus processes for completion or expiration."""
        for consensus_process in list(self.active_consensus.values()):
            if consensus_process.is_expired():
                # Handle expired consensus
                consensus_process.status = ConsensusStatus.FAILED
                consensus_process.completed_at = datetime.now()
                
                # Move to history
                self.consensus_history[consensus_process.consensus_id] = consensus_process
                del self.active_consensus[consensus_process.consensus_id]
                
                logger.info(f"Consensus {consensus_process.consensus_id} expired")

    async def _cleanup_old_consensus(self):
        """Clean up old consensus processes from history."""
        cutoff_time = datetime.now() - timedelta(days=30)
        old_consensus = []
        
        for consensus_id, consensus_process in self.consensus_history.items():
            if consensus_process.completed_at and consensus_process.completed_at < cutoff_time:
                old_consensus.append(consensus_id)
        
        for consensus_id in old_consensus:
            del self.consensus_history[consensus_id]
        
        if old_consensus:
            logger.info(f"Cleaned up {len(old_consensus)} old consensus processes")

    def get_consensus_status(self, consensus_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a consensus process."""
        consensus_process = self.active_consensus.get(consensus_id) or self.consensus_history.get(consensus_id)
        if not consensus_process:
            return None
        
        return {
            "consensus_id": consensus_process.consensus_id,
            "title": consensus_process.title,
            "status": consensus_process.status.value,
            "consensus_type": consensus_process.consensus_type.value,
            "participation_rate": consensus_process.get_participation_rate(),
            "votes_submitted": len([v for v in consensus_process.votes.values() if v.status == VoteStatus.SUBMITTED]),
            "total_participants": len(consensus_process.participating_agents),
            "deadline": consensus_process.deadline.isoformat() if consensus_process.deadline else None,
            "result": consensus_process.result,
            "created_at": consensus_process.created_at.isoformat(),
            "completed_at": consensus_process.completed_at.isoformat() if consensus_process.completed_at else None
        }

    def get_consensus_stats(self) -> Dict[str, Any]:
        """Get consensus manager statistics."""
        active_count = len(self.active_consensus)
        completed_count = len([c for c in self.consensus_history.values() if c.status == ConsensusStatus.COMPLETED])
        failed_count = len([c for c in self.consensus_history.values() if c.status == ConsensusStatus.FAILED])
        
        return {
            "active_consensus": active_count,
            "completed_consensus": completed_count,
            "failed_consensus": failed_count,
            "total_historical": len(self.consensus_history),
            "consensus_types": list(self.consensus_handlers.keys()),
            "success_rate": completed_count / (completed_count + failed_count) if (completed_count + failed_count) > 0 else 0.0
        }

    async def shutdown(self):
        """Shutdown the consensus manager."""
        if self._monitoring_task:
            self._monitoring_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        logger.info("ConsensusManager shutdown")


# Global consensus manager instance
consensus_manager = ConsensusManager()
