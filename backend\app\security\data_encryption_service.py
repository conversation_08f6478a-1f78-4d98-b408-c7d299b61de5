"""
Data Encryption Service for Phase 4 Privacy & Compliance.

This module provides comprehensive encryption services for sensitive data
including end-to-end encryption, key management, and encryption auditing.
"""

import logging
import os
import base64
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class EncryptionKeyManager:
    """Manages encryption keys with rotation and auditing."""
    
    def __init__(self):
        """Initialize the encryption key manager."""
        self.master_key = self._get_or_create_master_key()
        self.key_rotation_days = 90  # Rotate keys every 90 days
        self.key_cache: Dict[str, bytes] = {}
        
    def _get_or_create_master_key(self) -> bytes:
        """Get or create the master encryption key."""
        master_key = os.getenv('DATAGENIUS_MASTER_KEY')
        if not master_key:
            # Generate a new master key for development
            master_key = Fernet.generate_key().decode()
            logger.warning(
                "DATAGENIUS_MASTER_KEY not set, using generated key. "
                f"For production, set DATAGENIUS_MASTER_KEY: {master_key}"
            )
        
        return master_key.encode() if isinstance(master_key, str) else master_key
    
    def derive_key(self, context: str, salt: Optional[bytes] = None) -> bytes:
        """
        Derive a context-specific encryption key.
        
        Args:
            context: Context for key derivation (e.g., 'user_data', 'conversation')
            salt: Optional salt for key derivation
            
        Returns:
            Derived encryption key
        """
        if salt is None:
            salt = context.encode()[:16].ljust(16, b'0')
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        return base64.urlsafe_b64encode(kdf.derive(self.master_key))
    
    def get_encryption_key(self, context: str) -> Fernet:
        """
        Get a Fernet encryption instance for a specific context.
        
        Args:
            context: Encryption context
            
        Returns:
            Fernet encryption instance
        """
        if context not in self.key_cache:
            derived_key = self.derive_key(context)
            self.key_cache[context] = derived_key
        
        return Fernet(self.key_cache[context])
    
    def rotate_key(self, context: str) -> None:
        """
        Rotate encryption key for a specific context.
        
        Args:
            context: Context to rotate key for
        """
        # Remove from cache to force regeneration
        if context in self.key_cache:
            del self.key_cache[context]
        
        logger.info(f"Rotated encryption key for context: {context}")


class DataEncryptionService:
    """Comprehensive data encryption service."""
    
    def __init__(self):
        """Initialize the data encryption service."""
        self.key_manager = EncryptionKeyManager()
        self.audit_log: List[Dict[str, Any]] = []
        
    def encrypt_sensitive_data(
        self,
        data: Union[str, Dict[str, Any]],
        context: str = "general",
        user_id: Optional[str] = None
    ) -> str:
        """
        Encrypt sensitive data with context-specific keys.
        
        Args:
            data: Data to encrypt
            context: Encryption context
            user_id: Optional user ID for auditing
            
        Returns:
            Encrypted data as base64 string
        """
        try:
            # Convert data to JSON string if it's a dict
            if isinstance(data, dict):
                data_str = json.dumps(data, sort_keys=True)
            else:
                data_str = str(data)
            
            # Get encryption key for context
            fernet = self.key_manager.get_encryption_key(context)
            
            # Encrypt the data
            encrypted_data = fernet.encrypt(data_str.encode())
            
            # Convert to base64 for storage
            encrypted_b64 = base64.b64encode(encrypted_data).decode()
            
            # Log encryption event
            self._log_encryption_event(
                action="encrypt",
                context=context,
                user_id=user_id,
                data_size=len(data_str)
            )
            
            return encrypted_b64
            
        except Exception as e:
            logger.error(f"Encryption failed for context {context}: {e}")
            raise
    
    def decrypt_sensitive_data(
        self,
        encrypted_data: str,
        context: str = "general",
        user_id: Optional[str] = None
    ) -> Union[str, Dict[str, Any]]:
        """
        Decrypt sensitive data.
        
        Args:
            encrypted_data: Base64 encoded encrypted data
            context: Encryption context
            user_id: Optional user ID for auditing
            
        Returns:
            Decrypted data
        """
        try:
            # Decode from base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            
            # Get encryption key for context
            fernet = self.key_manager.get_encryption_key(context)
            
            # Decrypt the data
            decrypted_bytes = fernet.decrypt(encrypted_bytes)
            decrypted_str = decrypted_bytes.decode()
            
            # Try to parse as JSON, fallback to string
            try:
                decrypted_data = json.loads(decrypted_str)
            except json.JSONDecodeError:
                decrypted_data = decrypted_str
            
            # Log decryption event
            self._log_encryption_event(
                action="decrypt",
                context=context,
                user_id=user_id,
                data_size=len(decrypted_str)
            )
            
            return decrypted_data
            
        except Exception as e:
            logger.error(f"Decryption failed for context {context}: {e}")
            raise
    
    def encrypt_business_profile(self, profile_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """
        Encrypt sensitive fields in business profile data.
        
        Args:
            profile_data: Business profile data
            user_id: User ID
            
        Returns:
            Profile data with sensitive fields encrypted
        """
        sensitive_fields = [
            'financial_data', 'revenue', 'profit', 'budget_indicators',
            'competitive_landscape', 'internal_notes', 'contact_info',
            'employee_data', 'customer_data', 'vendor_data'
        ]
        
        encrypted_profile = profile_data.copy()
        
        for field in sensitive_fields:
            if field in encrypted_profile and encrypted_profile[field]:
                encrypted_profile[field] = self.encrypt_sensitive_data(
                    encrypted_profile[field],
                    context=f"business_profile_{field}",
                    user_id=user_id
                )
        
        return encrypted_profile
    
    def decrypt_business_profile(self, encrypted_profile: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """
        Decrypt sensitive fields in business profile data.
        
        Args:
            encrypted_profile: Encrypted business profile data
            user_id: User ID
            
        Returns:
            Profile data with sensitive fields decrypted
        """
        sensitive_fields = [
            'financial_data', 'revenue', 'profit', 'budget_indicators',
            'competitive_landscape', 'internal_notes', 'contact_info',
            'employee_data', 'customer_data', 'vendor_data'
        ]
        
        decrypted_profile = encrypted_profile.copy()
        
        for field in sensitive_fields:
            if field in decrypted_profile and decrypted_profile[field]:
                try:
                    decrypted_profile[field] = self.decrypt_sensitive_data(
                        decrypted_profile[field],
                        context=f"business_profile_{field}",
                        user_id=user_id
                    )
                except Exception as e:
                    logger.warning(f"Failed to decrypt field {field}: {e}")
                    # Keep encrypted data if decryption fails
        
        return decrypted_profile
    
    def _log_encryption_event(
        self,
        action: str,
        context: str,
        user_id: Optional[str] = None,
        data_size: Optional[int] = None
    ) -> None:
        """Log encryption/decryption events for auditing."""
        event = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "context": context,
            "user_id": user_id,
            "data_size": data_size
        }
        
        self.audit_log.append(event)
        
        # Keep only last 1000 events in memory
        if len(self.audit_log) > 1000:
            self.audit_log = self.audit_log[-1000:]
        
        logger.info(f"ENCRYPTION_AUDIT: {action} operation for context {context}")
    
    def get_encryption_audit_log(self, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get encryption audit log.
        
        Args:
            user_id: Optional user ID to filter by
            
        Returns:
            List of audit log entries
        """
        if user_id:
            return [event for event in self.audit_log if event.get("user_id") == user_id]
        return self.audit_log.copy()


# Global encryption service instance
encryption_service = DataEncryptionService()
