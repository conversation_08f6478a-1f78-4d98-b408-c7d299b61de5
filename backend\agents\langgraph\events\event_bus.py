"""
High-performance event bus system for LangGraph.

This module provides the core event distribution mechanism with async processing,
priority handling, and comprehensive metrics tracking.
"""

import asyncio
import json
import logging
from typing import Dict, List, Callable, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict

logger = logging.getLogger(__name__)


class EventPriority(Enum):
    """Event priority levels for processing order."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class LangGraphEvent:
    """Base event class for LangGraph system."""
    event_type: str
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    priority: EventPriority = EventPriority.MEDIUM
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        return {
            "event_type": self.event_type,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source,
            "data": self.data,
            "priority": self.priority.value,
            "correlation_id": self.correlation_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LangGraphEvent':
        """Create event from dictionary."""
        return cls(
            event_type=data["event_type"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            source=data["source"],
            data=data["data"],
            priority=EventPriority(data.get("priority", EventPriority.MEDIUM.value)),
            correlation_id=data.get("correlation_id")
        )


class LangGraphEventBus:
    """High-performance event bus for LangGraph system."""
    
    def __init__(self, max_history: int = 1000, enable_persistence: bool = False):
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.event_history: List[LangGraphEvent] = []
        self.max_history = max_history
        self.enable_persistence = enable_persistence
        self.running = False
        self.event_queue = asyncio.Queue()
        self.priority_queues = {
            EventPriority.CRITICAL: asyncio.Queue(),
            EventPriority.HIGH: asyncio.Queue(),
            EventPriority.MEDIUM: asyncio.Queue(),
            EventPriority.LOW: asyncio.Queue()
        }
        self.metrics = {
            "events_published": 0,
            "events_processed": 0,
            "processing_errors": 0,
            "average_processing_time": 0.0,
            "last_processing_time": 0.0
        }
        self._processing_times = []
        self._max_processing_times = 100  # Keep last 100 processing times
    
    async def start(self):
        """Start the event processing loop."""
        if self.running:
            logger.warning("Event bus is already running")
            return
            
        self.running = True
        # Start processing tasks for each priority level
        asyncio.create_task(self._process_events())
        logger.info("LangGraph Event Bus started")
    
    async def stop(self):
        """Stop the event processing loop."""
        self.running = False
        logger.info("LangGraph Event Bus stopped")
    
    async def publish(self, event: LangGraphEvent):
        """Publish event to the bus."""
        if not self.running:
            logger.warning("Event bus is not running, starting it now")
            await self.start()
        
        # Add to priority queue
        await self.priority_queues[event.priority].put(event)
        self.metrics["events_published"] += 1
        
        # Maintain event history
        self.event_history.append(event)
        if len(self.event_history) > self.max_history:
            self.event_history.pop(0)
        
        logger.debug(f"Published event: {event.event_type} with priority {event.priority.name}")

    async def emit(self, event: LangGraphEvent):
        """
        Emit event (alias for publish for compatibility).

        Args:
            event: The event to emit
        """
        if not isinstance(event, LangGraphEvent):
            raise ValueError(f"Event must be a LangGraphEvent instance, got {type(event)}")

        await self.publish(event)

    def subscribe(self, event_type: str, callback: Callable, priority: bool = False):
        """Subscribe to event type."""
        if priority:
            self.subscribers[event_type].insert(0, callback)
        else:
            self.subscribers[event_type].append(callback)
        
        logger.debug(f"Subscribed to {event_type}, total subscribers: {len(self.subscribers[event_type])}")
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """Unsubscribe from event type."""
        if event_type in self.subscribers:
            try:
                self.subscribers[event_type].remove(callback)
                logger.debug(f"Unsubscribed from {event_type}")
            except ValueError:
                logger.warning(f"Callback not found for {event_type}")
    
    async def _process_events(self):
        """Process events from priority queues."""
        while self.running:
            try:
                # Process events by priority (CRITICAL -> HIGH -> MEDIUM -> LOW)
                event = None
                for priority in [EventPriority.CRITICAL, EventPriority.HIGH, 
                               EventPriority.MEDIUM, EventPriority.LOW]:
                    try:
                        event = self.priority_queues[priority].get_nowait()
                        break
                    except asyncio.QueueEmpty:
                        continue
                
                if event:
                    start_time = datetime.now()
                    await self._handle_event(event)
                    processing_time = (datetime.now() - start_time).total_seconds() * 1000  # ms
                    
                    # Update metrics
                    self.metrics["events_processed"] += 1
                    self.metrics["last_processing_time"] = processing_time
                    self._update_average_processing_time(processing_time)
                else:
                    # No events to process, sleep briefly
                    await asyncio.sleep(0.001)  # 1ms
                    
            except Exception as e:
                logger.error(f"Error in event processing loop: {e}")
                self.metrics["processing_errors"] += 1
                await asyncio.sleep(0.1)  # Brief pause on error
    
    def _update_average_processing_time(self, processing_time: float):
        """Update average processing time."""
        self._processing_times.append(processing_time)
        if len(self._processing_times) > self._max_processing_times:
            self._processing_times.pop(0)
        
        self.metrics["average_processing_time"] = sum(self._processing_times) / len(self._processing_times)
    
    async def _handle_event(self, event: LangGraphEvent):
        """Handle individual event."""
        if event.event_type in self.subscribers:
            subscribers = self.subscribers[event.event_type]
            
            # Execute callbacks concurrently for better performance
            tasks = []
            for callback in subscribers:
                task = asyncio.create_task(self._safe_callback(callback, event))
                tasks.append(task)
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                # Log any exceptions that occurred
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Error in callback {i} for {event.event_type}: {result}")
    
    async def _safe_callback(self, callback: Callable, event: LangGraphEvent):
        """Execute callback with error handling."""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(event)
            else:
                callback(event)
        except Exception as e:
            logger.error(f"Error in event callback for {event.event_type}: {e}")
            raise  # Re-raise for gather to catch
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get event bus metrics."""
        return {
            **self.metrics,
            "active_subscriptions": {
                event_type: len(callbacks) 
                for event_type, callbacks in self.subscribers.items()
            },
            "event_history_size": len(self.event_history),
            "queue_sizes": {
                priority.name: queue.qsize() 
                for priority, queue in self.priority_queues.items()
            },
            "running": self.running
        }
    
    def get_event_history(self, event_type: Optional[str] = None, 
                         limit: Optional[int] = None) -> List[LangGraphEvent]:
        """Get event history, optionally filtered by type."""
        history = self.event_history
        
        if event_type:
            history = [event for event in history if event.event_type == event_type]
        
        if limit:
            history = history[-limit:]
        
        return history


# Global event bus instance
event_bus = LangGraphEventBus()


async def initialize_event_handlers():
    """Initialize and register event handlers with the event bus."""
    try:
        # Import and initialize tool execution handler
        from .handlers.tool_execution_handler import tool_execution_handler
        await tool_execution_handler.initialize()

        # Register tool execution event handlers
        event_bus.subscribe("tool.execution_started", tool_execution_handler.handle_event)
        event_bus.subscribe("tool.execution_progress", tool_execution_handler.handle_event)
        event_bus.subscribe("tool.execution_completed", tool_execution_handler.handle_event)
        event_bus.subscribe("tool.execution_failed", tool_execution_handler.handle_event)

        logger.info("Event handlers initialized and registered successfully")

    except Exception as e:
        logger.error(f"Failed to initialize event handlers: {e}")


# Initialize event handlers when the module is imported
import asyncio
try:
    # Try to get the current event loop
    loop = asyncio.get_event_loop()
    if loop.is_running():
        # If loop is running, schedule the initialization
        asyncio.create_task(initialize_event_handlers())
    else:
        # If no loop is running, run the initialization
        loop.run_until_complete(initialize_event_handlers())
except RuntimeError:
    # No event loop available, will be initialized later
    logger.info("No event loop available, event handlers will be initialized later")
