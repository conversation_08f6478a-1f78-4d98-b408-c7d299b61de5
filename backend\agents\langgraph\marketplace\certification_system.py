"""
Certification System

System for certifying and validating agent capabilities with performance
metrics and quality scoring in the marketplace.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

from ..events.event_bus import event_bus, LangGraphEvent
from ..monitoring.metrics import MetricsCollector

logger = logging.getLogger(__name__)


class CertificationLevel(Enum):
    """Certification levels for capabilities."""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"
    PREMIUM = "premium"


class CertificationStatus(Enum):
    """Status of certification process."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"
    REVOKED = "revoked"


@dataclass
class CertificationCriteria:
    """Criteria for capability certification."""
    level: CertificationLevel
    min_success_rate: float
    min_performance_score: float
    min_execution_count: int
    max_average_execution_time: float
    min_quality_score: float
    required_features: List[str] = field(default_factory=list)
    performance_consistency: float = 0.8
    uptime_requirement: float = 0.95
    security_compliance: bool = True


@dataclass
class CertificationRecord:
    """Record of a capability certification."""
    certification_id: str
    capability_id: str
    agent_id: str
    level: CertificationLevel
    status: CertificationStatus
    criteria_met: Dict[str, bool] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    
    # Timestamps
    requested_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    
    # Certification details
    test_results: Dict[str, Any] = field(default_factory=dict)
    quality_assessment: Dict[str, float] = field(default_factory=dict)
    compliance_check: Dict[str, bool] = field(default_factory=dict)
    
    # Metadata
    certifier_id: str = "system"
    notes: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


class CertificationSystem:
    """
    System for certifying and validating agent capabilities.
    
    Features:
    - Multi-level certification process
    - Automated performance testing
    - Quality assessment and scoring
    - Compliance verification
    - Certification lifecycle management
    """
    
    def __init__(self):
        self.certifications: Dict[str, CertificationRecord] = {}
        self.criteria: Dict[CertificationLevel, CertificationCriteria] = {}
        self.metrics = MetricsCollector("certification_system")
        
        # Initialize certification criteria
        self._initialize_certification_criteria()
        
        # Configuration
        self.certification_validity_period = timedelta(days=365)  # 1 year
        self.test_duration = timedelta(hours=24)  # 24 hour testing period
        self.min_test_executions = 10
    
    def _initialize_certification_criteria(self):
        """Initialize certification criteria for different levels."""
        self.criteria = {
            CertificationLevel.BASIC: CertificationCriteria(
                level=CertificationLevel.BASIC,
                min_success_rate=0.8,
                min_performance_score=0.6,
                min_execution_count=5,
                max_average_execution_time=3600.0,
                min_quality_score=0.6,
                performance_consistency=0.7,
                uptime_requirement=0.9
            ),
            CertificationLevel.INTERMEDIATE: CertificationCriteria(
                level=CertificationLevel.INTERMEDIATE,
                min_success_rate=0.85,
                min_performance_score=0.7,
                min_execution_count=20,
                max_average_execution_time=1800.0,
                min_quality_score=0.7,
                performance_consistency=0.8,
                uptime_requirement=0.95
            ),
            CertificationLevel.ADVANCED: CertificationCriteria(
                level=CertificationLevel.ADVANCED,
                min_success_rate=0.9,
                min_performance_score=0.8,
                min_execution_count=50,
                max_average_execution_time=900.0,
                min_quality_score=0.8,
                required_features=["error_handling", "optimization"],
                performance_consistency=0.85,
                uptime_requirement=0.98
            ),
            CertificationLevel.EXPERT: CertificationCriteria(
                level=CertificationLevel.EXPERT,
                min_success_rate=0.95,
                min_performance_score=0.9,
                min_execution_count=100,
                max_average_execution_time=600.0,
                min_quality_score=0.9,
                required_features=["error_handling", "optimization", "self_healing"],
                performance_consistency=0.9,
                uptime_requirement=0.99
            ),
            CertificationLevel.PREMIUM: CertificationCriteria(
                level=CertificationLevel.PREMIUM,
                min_success_rate=0.98,
                min_performance_score=0.95,
                min_execution_count=200,
                max_average_execution_time=300.0,
                min_quality_score=0.95,
                required_features=["error_handling", "optimization", "self_healing", "predictive_analytics"],
                performance_consistency=0.95,
                uptime_requirement=0.995
            )
        }
    
    async def initialize(self):
        """Initialize the certification system."""
        try:
            # Load existing certifications
            await self._load_certifications()
            
            # Setup event handlers
            self._setup_event_handlers()
            
            # Start background tasks
            asyncio.create_task(self._certification_monitoring_loop())
            asyncio.create_task(self._expiry_check_loop())
            
            logger.info("Certification system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize certification system: {e}")
            raise
    
    def _setup_event_handlers(self):
        """Setup event handlers for certification operations."""
        event_bus.subscribe("marketplace.capability_listed", self._handle_capability_listing)
        event_bus.subscribe("workflow.execution_completed", self._handle_execution_completion)
        event_bus.subscribe("agent.performance_updated", self._handle_performance_update)
    
    async def request_certification(self, capability_id: str, agent_id: str, 
                                  target_level: CertificationLevel) -> str:
        """
        Request certification for a capability.
        
        Args:
            capability_id: ID of the capability to certify
            agent_id: ID of the agent owning the capability
            target_level: Target certification level
            
        Returns:
            Certification ID
        """
        try:
            # Create certification record
            certification = CertificationRecord(
                certification_id=f"cert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{capability_id}",
                capability_id=capability_id,
                agent_id=agent_id,
                level=target_level,
                status=CertificationStatus.PENDING,
                expires_at=datetime.now() + self.certification_validity_period
            )
            
            # Store certification
            self.certifications[certification.certification_id] = certification
            
            # Start certification process
            await self._start_certification_process(certification)
            
            # Publish certification request event
            await event_bus.publish(LangGraphEvent(
                event_type="certification.requested",
                timestamp=datetime.now(),
                source="certification_system",
                data={
                    "certification_id": certification.certification_id,
                    "capability_id": capability_id,
                    "agent_id": agent_id,
                    "target_level": target_level.value
                }
            ))
            
            # Update metrics
            self.metrics.increment("certifications_requested")
            
            logger.info(f"Requested {target_level.value} certification for capability {capability_id}")
            return certification.certification_id
            
        except Exception as e:
            logger.error(f"Failed to request certification: {e}")
            raise
    
    async def get_certification_level(self, capability_id: str) -> str:
        """
        Get the current certification level for a capability.
        
        Args:
            capability_id: ID of the capability
            
        Returns:
            Certification level string
        """
        # Find the highest valid certification for this capability
        valid_certifications = [
            cert for cert in self.certifications.values()
            if (cert.capability_id == capability_id and 
                cert.status == CertificationStatus.COMPLETED and
                cert.expires_at and cert.expires_at > datetime.now())
        ]
        
        if not valid_certifications:
            return "basic"
        
        # Return the highest level
        levels = [cert.level for cert in valid_certifications]
        level_order = [CertificationLevel.BASIC, CertificationLevel.INTERMEDIATE, 
                      CertificationLevel.ADVANCED, CertificationLevel.EXPERT, 
                      CertificationLevel.PREMIUM]
        
        for level in reversed(level_order):
            if level in levels:
                return level.value
        
        return "basic"
    
    async def validate_certification(self, certification_id: str) -> bool:
        """
        Validate a certification record.
        
        Args:
            certification_id: ID of the certification to validate
            
        Returns:
            True if certification is valid, False otherwise
        """
        if certification_id not in self.certifications:
            return False
        
        certification = self.certifications[certification_id]
        
        # Check status
        if certification.status != CertificationStatus.COMPLETED:
            return False
        
        # Check expiry
        if certification.expires_at and certification.expires_at <= datetime.now():
            certification.status = CertificationStatus.EXPIRED
            return False
        
        return True
    
    async def get_certification_analytics(self) -> Dict[str, Any]:
        """
        Get comprehensive certification analytics.
        
        Returns:
            Analytics data for certifications
        """
        try:
            analytics = {
                "overview": {
                    "total_certifications": len(self.certifications),
                    "active_certifications": len([
                        c for c in self.certifications.values()
                        if c.status == CertificationStatus.COMPLETED
                    ]),
                    "pending_certifications": len([
                        c for c in self.certifications.values()
                        if c.status in [CertificationStatus.PENDING, CertificationStatus.IN_PROGRESS]
                    ]),
                    "expired_certifications": len([
                        c for c in self.certifications.values()
                        if c.status == CertificationStatus.EXPIRED
                    ])
                },
                "level_distribution": self._get_level_distribution(),
                "success_rates": self._get_certification_success_rates(),
                "performance_trends": await self._get_performance_trends(),
                "quality_metrics": await self._get_quality_metrics(),
                "compliance_status": await self._get_compliance_status()
            }
            
            # Update metrics
            self.metrics.increment("analytics_requests")
            
            return analytics
            
        except Exception as e:
            logger.error(f"Failed to generate certification analytics: {e}")
            raise

    # Certification Process Methods
    async def _start_certification_process(self, certification: CertificationRecord):
        """Start the certification process for a capability."""
        try:
            certification.status = CertificationStatus.IN_PROGRESS
            certification.started_at = datetime.now()

            # Get capability performance data
            performance_data = await self._collect_performance_data(certification.capability_id)
            certification.performance_metrics = performance_data

            # Run certification tests
            test_results = await self._run_certification_tests(certification)
            certification.test_results = test_results

            # Perform quality assessment
            quality_assessment = await self._perform_quality_assessment(certification)
            certification.quality_assessment = quality_assessment

            # Check compliance
            compliance_check = await self._check_compliance(certification)
            certification.compliance_check = compliance_check

            # Evaluate certification
            await self._evaluate_certification(certification)

        except Exception as e:
            certification.status = CertificationStatus.FAILED
            certification.notes = f"Certification failed: {e}"
            logger.error(f"Certification process failed for {certification.certification_id}: {e}")

    async def _collect_performance_data(self, capability_id: str) -> Dict[str, float]:
        """Collect performance data for a capability."""
        # This would integrate with the monitoring system
        # For now, return mock data
        return {
            "success_rate": 0.92,
            "average_execution_time": 1.5,
            "performance_score": 0.88,
            "quality_score": 0.85,
            "consistency_score": 0.9,
            "uptime": 0.98
        }

    async def _run_certification_tests(self, certification: CertificationRecord) -> Dict[str, Any]:
        """Run automated tests for certification."""
        criteria = self.criteria[certification.level]
        performance = certification.performance_metrics

        test_results = {
            "success_rate_test": performance.get("success_rate", 0) >= criteria.min_success_rate,
            "performance_test": performance.get("performance_score", 0) >= criteria.min_performance_score,
            "execution_time_test": performance.get("average_execution_time", float('inf')) <= criteria.max_average_execution_time,
            "quality_test": performance.get("quality_score", 0) >= criteria.min_quality_score,
            "consistency_test": performance.get("consistency_score", 0) >= criteria.performance_consistency,
            "uptime_test": performance.get("uptime", 0) >= criteria.uptime_requirement
        }

        # Check required features
        if criteria.required_features:
            feature_tests = await self._test_required_features(certification.capability_id, criteria.required_features)
            test_results.update(feature_tests)

        return test_results

    async def _test_required_features(self, capability_id: str, required_features: List[str]) -> Dict[str, bool]:
        """Test required features for certification."""
        # This would test specific features of the capability
        # For now, return mock results
        return {f"feature_{feature}_test": True for feature in required_features}

    async def _perform_quality_assessment(self, certification: CertificationRecord) -> Dict[str, float]:
        """Perform quality assessment for certification."""
        # This would perform detailed quality analysis
        return {
            "code_quality": 0.9,
            "documentation_quality": 0.85,
            "error_handling": 0.88,
            "security_score": 0.92,
            "maintainability": 0.87
        }

    async def _check_compliance(self, certification: CertificationRecord) -> Dict[str, bool]:
        """Check compliance requirements for certification."""
        criteria = self.criteria[certification.level]

        return {
            "security_compliance": criteria.security_compliance,
            "data_privacy": True,
            "api_standards": True,
            "performance_standards": True,
            "documentation_standards": True
        }

    async def _evaluate_certification(self, certification: CertificationRecord):
        """Evaluate certification based on all test results."""
        try:
            # Check if all tests passed
            all_tests_passed = all(certification.test_results.values())
            quality_threshold_met = all(score >= 0.8 for score in certification.quality_assessment.values())
            compliance_met = all(certification.compliance_check.values())

            if all_tests_passed and quality_threshold_met and compliance_met:
                certification.status = CertificationStatus.COMPLETED
                certification.completed_at = datetime.now()

                # Publish success event
                await event_bus.publish(LangGraphEvent(
                    event_type="certification.completed",
                    timestamp=datetime.now(),
                    source="certification_system",
                    data={
                        "certification_id": certification.certification_id,
                        "capability_id": certification.capability_id,
                        "agent_id": certification.agent_id,
                        "level": certification.level.value,
                        "success": True
                    }
                ))

                self.metrics.increment("certifications_completed")
                logger.info(f"Certification {certification.certification_id} completed successfully")

            else:
                certification.status = CertificationStatus.FAILED
                certification.notes = "Failed to meet certification requirements"

                # Publish failure event
                await event_bus.publish(LangGraphEvent(
                    event_type="certification.failed",
                    timestamp=datetime.now(),
                    source="certification_system",
                    data={
                        "certification_id": certification.certification_id,
                        "capability_id": certification.capability_id,
                        "agent_id": certification.agent_id,
                        "level": certification.level.value,
                        "success": False,
                        "failed_tests": [test for test, passed in certification.test_results.items() if not passed]
                    }
                ))

                self.metrics.increment("certifications_failed")
                logger.warning(f"Certification {certification.certification_id} failed")

        except Exception as e:
            certification.status = CertificationStatus.FAILED
            certification.notes = f"Evaluation error: {e}"
            logger.error(f"Error evaluating certification {certification.certification_id}: {e}")

    # Event Handlers
    async def _handle_capability_listing(self, event: LangGraphEvent):
        """Handle capability listing events for automatic certification."""
        try:
            capability_id = event.data.get("capability_id")
            agent_id = event.data.get("agent_id")

            if capability_id and agent_id:
                # Automatically request basic certification for new capabilities
                await self.request_certification(capability_id, agent_id, CertificationLevel.BASIC)

        except Exception as e:
            logger.error(f"Error handling capability listing: {e}")

    async def _handle_execution_completion(self, event: LangGraphEvent):
        """Handle execution completion events for certification tracking."""
        try:
            capability_id = event.data.get("capability_id")

            if capability_id:
                # Update performance data for ongoing certifications
                ongoing_certifications = [
                    cert for cert in self.certifications.values()
                    if (cert.capability_id == capability_id and
                        cert.status == CertificationStatus.IN_PROGRESS)
                ]

                for certification in ongoing_certifications:
                    # Update performance metrics
                    performance_data = await self._collect_performance_data(capability_id)
                    certification.performance_metrics.update(performance_data)

        except Exception as e:
            logger.error(f"Error handling execution completion: {e}")

    async def _handle_performance_update(self, event: LangGraphEvent):
        """Handle performance update events."""
        try:
            capability_id = event.data.get("capability_id")
            performance_data = event.data.get("performance", {})

            if capability_id and performance_data:
                # Check if any certifications need re-evaluation
                await self._check_certification_validity(capability_id, performance_data)

        except Exception as e:
            logger.error(f"Error handling performance update: {e}")

    async def _check_certification_validity(self, capability_id: str, performance_data: Dict[str, float]):
        """Check if existing certifications are still valid based on performance."""
        active_certifications = [
            cert for cert in self.certifications.values()
            if (cert.capability_id == capability_id and
                cert.status == CertificationStatus.COMPLETED)
        ]

        for certification in active_certifications:
            criteria = self.criteria[certification.level]

            # Check if performance still meets criteria
            if (performance_data.get("success_rate", 1.0) < criteria.min_success_rate or
                performance_data.get("performance_score", 1.0) < criteria.min_performance_score):

                certification.status = CertificationStatus.REVOKED
                certification.notes = "Performance degraded below certification requirements"

                await event_bus.publish(LangGraphEvent(
                    event_type="certification.revoked",
                    timestamp=datetime.now(),
                    source="certification_system",
                    data={
                        "certification_id": certification.certification_id,
                        "capability_id": capability_id,
                        "reason": "performance_degradation"
                    }
                ))

                logger.warning(f"Revoked certification {certification.certification_id} due to performance degradation")

    # Background Tasks
    async def _certification_monitoring_loop(self):
        """Background task for monitoring certification processes."""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes

                # Check for stalled certifications
                current_time = datetime.now()
                stall_threshold = timedelta(hours=48)

                for certification in self.certifications.values():
                    if (certification.status == CertificationStatus.IN_PROGRESS and
                        certification.started_at and
                        current_time - certification.started_at > stall_threshold):

                        certification.status = CertificationStatus.FAILED
                        certification.notes = "Certification process stalled"
                        logger.warning(f"Certification {certification.certification_id} marked as failed due to stall")

            except Exception as e:
                logger.error(f"Error in certification monitoring: {e}")

    async def _expiry_check_loop(self):
        """Background task for checking certification expiry."""
        while True:
            try:
                await asyncio.sleep(3600)  # Check every hour

                current_time = datetime.now()

                for certification in self.certifications.values():
                    if (certification.status == CertificationStatus.COMPLETED and
                        certification.expires_at and
                        current_time >= certification.expires_at):

                        certification.status = CertificationStatus.EXPIRED

                        await event_bus.publish(LangGraphEvent(
                            event_type="certification.expired",
                            timestamp=datetime.now(),
                            source="certification_system",
                            data={
                                "certification_id": certification.certification_id,
                                "capability_id": certification.capability_id,
                                "agent_id": certification.agent_id
                            }
                        ))

                        logger.info(f"Certification {certification.certification_id} expired")

            except Exception as e:
                logger.error(f"Error in expiry check: {e}")

    async def _load_certifications(self):
        """Load existing certifications from storage."""
        try:
            # In production, this would load from PostgreSQL
            # Load existing certifications and their status
            await self._load_certification_records()

            # Load certification criteria and standards
            await self._load_certification_criteria()

            # Load historical certification data for analytics
            await self._load_certification_analytics_data()

            logger.info(f"Certification data loaded: {len(self.certifications)} certifications")

        except Exception as e:
            logger.error(f"Error loading certification data: {e}")
            # Continue with default state if loading fails

    async def _load_certification_records(self):
        """Load existing certification records from database."""
        try:
            # This would query the database for existing certifications
            # For now, start with empty certification records
            certification_count = len(self.certifications)
            logger.debug(f"Loaded {certification_count} certification records")

        except Exception as e:
            logger.error(f"Error loading certification records: {e}")

    async def _load_certification_criteria(self):
        """Load certification criteria and standards."""
        try:
            # Ensure all certification levels have proper criteria
            for level in CertificationLevel:
                if level not in self.criteria:
                    self.criteria[level] = self._get_default_criteria_for_level(level)

            logger.debug(f"Certification criteria loaded for {len(self.criteria)} levels")

        except Exception as e:
            logger.error(f"Error loading certification criteria: {e}")

    def _get_default_criteria_for_level(self, level: CertificationLevel) -> Dict[str, Any]:
        """Get default certification criteria for a given level."""
        base_criteria = {
            "min_success_rate": 0.8,
            "min_quality_score": 0.7,
            "min_executions": 10,
            "max_avg_execution_time": 300.0,
            "required_tests": ["functionality", "performance", "reliability"],
            "documentation_required": True,
            "security_check_required": True
        }

        # Adjust criteria based on certification level
        level_adjustments = {
            CertificationLevel.BASIC: {
                "min_success_rate": 0.7,
                "min_quality_score": 0.6,
                "min_executions": 5,
                "required_tests": ["functionality"]
            },
            CertificationLevel.INTERMEDIATE: {
                "min_success_rate": 0.8,
                "min_quality_score": 0.7,
                "min_executions": 10,
                "required_tests": ["functionality", "performance"]
            },
            CertificationLevel.ADVANCED: {
                "min_success_rate": 0.85,
                "min_quality_score": 0.8,
                "min_executions": 20,
                "required_tests": ["functionality", "performance", "reliability"]
            },
            CertificationLevel.EXPERT: {
                "min_success_rate": 0.9,
                "min_quality_score": 0.85,
                "min_executions": 30,
                "required_tests": ["functionality", "performance", "reliability", "security"]
            },
            CertificationLevel.PROFESSIONAL: {
                "min_success_rate": 0.95,
                "min_quality_score": 0.9,
                "min_executions": 50,
                "required_tests": ["functionality", "performance", "reliability", "security", "scalability"]
            }
        }

        # Apply level-specific adjustments
        if level in level_adjustments:
            base_criteria.update(level_adjustments[level])

        return base_criteria

    async def _load_certification_analytics_data(self):
        """Load historical data for certification analytics."""
        try:
            # This would load historical certification data for analytics
            # For now, initialize empty analytics tracking
            logger.debug("Certification analytics data initialized")

        except Exception as e:
            logger.error(f"Error loading certification analytics data: {e}")

    # Analytics Helper Methods
    def _get_level_distribution(self) -> Dict[str, int]:
        """Get distribution of certification levels."""
        distribution = {}
        for level in CertificationLevel:
            count = len([
                c for c in self.certifications.values()
                if c.level == level and c.status == CertificationStatus.COMPLETED
            ])
            distribution[level.value] = count
        return distribution

    def _get_certification_success_rates(self) -> Dict[str, float]:
        """Get success rates for different certification levels."""
        success_rates = {}
        for level in CertificationLevel:
            level_certs = [c for c in self.certifications.values() if c.level == level]
            if level_certs:
                successful = len([c for c in level_certs if c.status == CertificationStatus.COMPLETED])
                success_rates[level.value] = successful / len(level_certs)
            else:
                success_rates[level.value] = 0.0
        return success_rates

    async def _get_performance_trends(self) -> Dict[str, Any]:
        """Get performance trends for certified capabilities."""
        return {
            "average_success_rate_trend": "increasing",
            "quality_score_trend": "stable",
            "certification_completion_time": "decreasing"
        }

    async def _get_quality_metrics(self) -> Dict[str, float]:
        """Get quality metrics across all certifications."""
        completed_certs = [
            c for c in self.certifications.values()
            if c.status == CertificationStatus.COMPLETED and c.quality_assessment
        ]

        if not completed_certs:
            return {}

        quality_metrics = {}
        for metric in ["code_quality", "documentation_quality", "error_handling", "security_score", "maintainability"]:
            scores = [cert.quality_assessment.get(metric, 0) for cert in completed_certs]
            quality_metrics[f"average_{metric}"] = sum(scores) / len(scores) if scores else 0.0

        return quality_metrics

    async def _get_compliance_status(self) -> Dict[str, Any]:
        """Get compliance status across all certifications."""
        completed_certs = [
            c for c in self.certifications.values()
            if c.status == CertificationStatus.COMPLETED and c.compliance_check
        ]

        if not completed_certs:
            return {}

        compliance_status = {}
        for check in ["security_compliance", "data_privacy", "api_standards", "performance_standards", "documentation_standards"]:
            compliant_count = sum(1 for cert in completed_certs if cert.compliance_check.get(check, False))
            compliance_status[f"{check}_rate"] = compliant_count / len(completed_certs) if completed_certs else 0.0

        return compliance_status
