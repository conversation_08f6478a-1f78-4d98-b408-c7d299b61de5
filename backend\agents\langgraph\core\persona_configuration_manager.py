"""
Persona Configuration Manager for Enhanced Marketplace Integration.

This module provides comprehensive persona configuration management with
database persistence, validation, and real-time synchronization.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json
import uuid

from .marketplace_database_manager import marketplace_db_manager

logger = logging.getLogger(__name__)


@dataclass
class PersonaConfigurationSchema:
    """Schema for persona configuration validation."""
    persona_id: str
    name: str
    description: str
    industry_specialization: Optional[str] = None
    methodology_framework: str = "UNDERSTAND_ASSESS_EXECUTE_DELIVER"
    enable_cross_agent_intelligence: bool = True
    enable_business_profile_context: bool = True
    specialized_tools: List[str] = None
    compliance_requirements: List[str] = None
    workflow_patterns: List[Dict[str, Any]] = None
    performance_optimization: Dict[str, Any] = None
    capabilities: List[str] = None
    skills: List[str] = None
    pricing_model: str = "fixed"
    base_price: float = 10.0
    certification_level: str = "basic"
    version: str = "1.0.0"
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.specialized_tools is None:
            self.specialized_tools = []
        if self.compliance_requirements is None:
            self.compliance_requirements = []
        if self.workflow_patterns is None:
            self.workflow_patterns = []
        if self.performance_optimization is None:
            self.performance_optimization = {}
        if self.capabilities is None:
            self.capabilities = []
        if self.skills is None:
            self.skills = []
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()


class PersonaConfigurationManager:
    """
    Comprehensive persona configuration manager with database persistence,
    validation, caching, and real-time synchronization capabilities.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_manager = marketplace_db_manager
        
        # Configuration cache
        self._config_cache: Dict[str, PersonaConfigurationSchema] = {}
        self._cache_expiry: Dict[str, datetime] = {}
        self._cache_ttl = timedelta(minutes=15)
        
        # Validation rules
        self._validation_rules = self._load_validation_rules()
        
        # Configuration templates
        self._industry_templates = self._load_industry_templates()
        
        # Event handlers
        self._event_handlers: Dict[str, List[callable]] = {
            "configuration_created": [],
            "configuration_updated": [],
            "configuration_deleted": [],
            "validation_failed": []
        }
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        
        # Initialize manager
        self._initialize_manager()
    
    def _initialize_manager(self) -> None:
        """Initialize the configuration manager."""
        try:
            self.logger.info("Initializing persona configuration manager...")
            
            # Start background tasks
            self._start_background_tasks()
            
            self.logger.info("Persona configuration manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize persona configuration manager: {e}")
            raise
    
    def _start_background_tasks(self) -> None:
        """Start background tasks for cache management and synchronization."""
        try:
            # Cache cleanup task
            task = asyncio.create_task(self._cache_cleanup_task())
            self._background_tasks.append(task)
            
            # Configuration sync task
            task = asyncio.create_task(self._configuration_sync_task())
            self._background_tasks.append(task)
            
            self.logger.info("Background tasks started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start background tasks: {e}")
    
    async def _cache_cleanup_task(self) -> None:
        """Background task to clean up expired cache entries."""
        while True:
            try:
                current_time = datetime.utcnow()
                expired_keys = [
                    key for key, expiry in self._cache_expiry.items()
                    if current_time > expiry
                ]
                
                for key in expired_keys:
                    self._config_cache.pop(key, None)
                    self._cache_expiry.pop(key, None)
                
                if expired_keys:
                    self.logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
                
                await asyncio.sleep(300)  # Run every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in cache cleanup task: {e}")
                await asyncio.sleep(300)
    
    async def _configuration_sync_task(self) -> None:
        """Background task to synchronize configurations with database."""
        while True:
            try:
                # Sync cached configurations with database
                for persona_id in list(self._config_cache.keys()):
                    await self._sync_configuration_with_db(persona_id)
                
                await asyncio.sleep(600)  # Run every 10 minutes
                
            except Exception as e:
                self.logger.error(f"Error in configuration sync task: {e}")
                await asyncio.sleep(600)
    
    async def create_persona_configuration(
        self,
        persona_id: str,
        configuration_data: Dict[str, Any],
        validate: bool = True
    ) -> bool:
        """
        Create new persona configuration.
        
        Args:
            persona_id: Persona identifier
            configuration_data: Configuration data
            validate: Whether to validate configuration
            
        Returns:
            True if created successfully
        """
        try:
            self.logger.info(f"Creating persona configuration for {persona_id}")
            
            # Validate configuration if requested
            if validate:
                validation_result = await self.validate_configuration(configuration_data)
                if not validation_result["is_valid"]:
                    self.logger.error(f"Configuration validation failed: {validation_result['errors']}")
                    await self._trigger_event("validation_failed", {
                        "persona_id": persona_id,
                        "errors": validation_result["errors"]
                    })
                    return False
            
            # Create configuration schema
            config_schema = PersonaConfigurationSchema(
                persona_id=persona_id,
                **configuration_data
            )
            
            # Save to database
            success = await self.db_manager.save_persona_configuration(
                persona_id=persona_id,
                configuration=asdict(config_schema),
                industry_specialization=config_schema.industry_specialization,
                methodology_framework=config_schema.methodology_framework,
                enable_cross_agent_intelligence=config_schema.enable_cross_agent_intelligence,
                specialized_tools=config_schema.specialized_tools,
                compliance_requirements=config_schema.compliance_requirements,
                workflow_patterns=config_schema.workflow_patterns,
                performance_optimization=config_schema.performance_optimization
            )
            
            if success:
                # Update cache
                self._config_cache[persona_id] = config_schema
                self._cache_expiry[persona_id] = datetime.utcnow() + self._cache_ttl
                
                # Trigger event
                await self._trigger_event("configuration_created", {
                    "persona_id": persona_id,
                    "configuration": asdict(config_schema)
                })
                
                self.logger.info(f"Created persona configuration for {persona_id}")
                return True
            else:
                self.logger.error(f"Failed to save persona configuration for {persona_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error creating persona configuration for {persona_id}: {e}")
            return False
    
    async def get_persona_configuration(
        self,
        persona_id: str,
        use_cache: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        Get persona configuration.
        
        Args:
            persona_id: Persona identifier
            use_cache: Whether to use cached configuration
            
        Returns:
            Configuration data or None if not found
        """
        try:
            # Check cache first if enabled
            if use_cache and persona_id in self._config_cache:
                cache_expiry = self._cache_expiry.get(persona_id)
                if cache_expiry and datetime.utcnow() < cache_expiry:
                    config = self._config_cache[persona_id]
                    return asdict(config)
            
            # Load from database
            config_data = await self.db_manager.get_persona_configuration(persona_id)
            
            if config_data:
                # Create schema object
                config_schema = PersonaConfigurationSchema(**config_data["configuration"])
                
                # Update cache
                self._config_cache[persona_id] = config_schema
                self._cache_expiry[persona_id] = datetime.utcnow() + self._cache_ttl
                
                return asdict(config_schema)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting persona configuration for {persona_id}: {e}")
            return None
    
    async def update_persona_configuration(
        self,
        persona_id: str,
        updates: Dict[str, Any],
        validate: bool = True
    ) -> bool:
        """
        Update persona configuration.
        
        Args:
            persona_id: Persona identifier
            updates: Configuration updates
            validate: Whether to validate updated configuration
            
        Returns:
            True if updated successfully
        """
        try:
            self.logger.info(f"Updating persona configuration for {persona_id}")
            
            # Get current configuration
            current_config = await self.get_persona_configuration(persona_id, use_cache=False)
            if not current_config:
                self.logger.error(f"Configuration not found for persona {persona_id}")
                return False
            
            # Merge updates
            updated_config = {**current_config, **updates}
            updated_config["updated_at"] = datetime.utcnow()
            
            # Validate if requested
            if validate:
                validation_result = await self.validate_configuration(updated_config)
                if not validation_result["is_valid"]:
                    self.logger.error(f"Configuration validation failed: {validation_result['errors']}")
                    await self._trigger_event("validation_failed", {
                        "persona_id": persona_id,
                        "errors": validation_result["errors"]
                    })
                    return False
            
            # Create updated schema
            config_schema = PersonaConfigurationSchema(**updated_config)
            
            # Save to database
            success = await self.db_manager.save_persona_configuration(
                persona_id=persona_id,
                configuration=asdict(config_schema),
                industry_specialization=config_schema.industry_specialization,
                methodology_framework=config_schema.methodology_framework,
                enable_cross_agent_intelligence=config_schema.enable_cross_agent_intelligence,
                specialized_tools=config_schema.specialized_tools,
                compliance_requirements=config_schema.compliance_requirements,
                workflow_patterns=config_schema.workflow_patterns,
                performance_optimization=config_schema.performance_optimization
            )
            
            if success:
                # Update cache
                self._config_cache[persona_id] = config_schema
                self._cache_expiry[persona_id] = datetime.utcnow() + self._cache_ttl
                
                # Trigger event
                await self._trigger_event("configuration_updated", {
                    "persona_id": persona_id,
                    "configuration": asdict(config_schema),
                    "updates": updates
                })
                
                self.logger.info(f"Updated persona configuration for {persona_id}")
                return True
            else:
                self.logger.error(f"Failed to save updated configuration for {persona_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error updating persona configuration for {persona_id}: {e}")
            return False

    async def validate_configuration(
        self,
        configuration_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate persona configuration data.

        Args:
            configuration_data: Configuration data to validate

        Returns:
            Validation result with is_valid flag and errors list
        """
        try:
            errors = []
            warnings = []

            # Required fields validation
            required_fields = ["persona_id", "name", "description"]
            for field in required_fields:
                if field not in configuration_data or not configuration_data[field]:
                    errors.append(f"Required field '{field}' is missing or empty")

            # Persona ID validation
            if "persona_id" in configuration_data:
                persona_id = configuration_data["persona_id"]
                if not isinstance(persona_id, str) or len(persona_id) < 3:
                    errors.append("Persona ID must be a string with at least 3 characters")
                if not persona_id.replace("_", "").replace("-", "").isalnum():
                    errors.append("Persona ID can only contain alphanumeric characters, hyphens, and underscores")

            # Methodology framework validation
            if "methodology_framework" in configuration_data:
                valid_frameworks = [
                    "UNDERSTAND_ASSESS_EXECUTE_DELIVER",
                    "AGILE_ITERATIVE",
                    "WATERFALL_SEQUENTIAL",
                    "DESIGN_THINKING",
                    "LEAN_STARTUP"
                ]
                if configuration_data["methodology_framework"] not in valid_frameworks:
                    errors.append(f"Invalid methodology framework. Must be one of: {valid_frameworks}")

            # Industry specialization validation
            if "industry_specialization" in configuration_data:
                industry = configuration_data["industry_specialization"]
                if industry and not isinstance(industry, str):
                    errors.append("Industry specialization must be a string")

            # Tools validation
            if "specialized_tools" in configuration_data:
                tools = configuration_data["specialized_tools"]
                if not isinstance(tools, list):
                    errors.append("Specialized tools must be a list")
                else:
                    for tool in tools:
                        if not isinstance(tool, str):
                            errors.append("Each specialized tool must be a string")

            # Capabilities validation
            if "capabilities" in configuration_data:
                capabilities = configuration_data["capabilities"]
                if not isinstance(capabilities, list):
                    errors.append("Capabilities must be a list")
                else:
                    for capability in capabilities:
                        if not isinstance(capability, str):
                            errors.append("Each capability must be a string")

            # Pricing validation
            if "base_price" in configuration_data:
                price = configuration_data["base_price"]
                if not isinstance(price, (int, float)) or price < 0:
                    errors.append("Base price must be a non-negative number")

            # Performance optimization validation
            if "performance_optimization" in configuration_data:
                perf_opt = configuration_data["performance_optimization"]
                if not isinstance(perf_opt, dict):
                    errors.append("Performance optimization must be a dictionary")

            # Business logic validation
            if len(errors) == 0:
                # Check for conflicting configurations
                if (configuration_data.get("enable_cross_agent_intelligence", True) and
                    not configuration_data.get("enable_business_profile_context", True)):
                    warnings.append("Cross-agent intelligence works best with business profile context enabled")

                # Check tool compatibility
                tools = configuration_data.get("specialized_tools", [])
                if "data_analysis" in tools and "visualization" not in tools:
                    warnings.append("Data analysis tool works better with visualization tool")

            return {
                "is_valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "validation_timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error validating configuration: {e}")
            return {
                "is_valid": False,
                "errors": [f"Validation error: {str(e)}"],
                "warnings": [],
                "validation_timestamp": datetime.utcnow().isoformat()
            }

    def _load_validation_rules(self) -> Dict[str, Any]:
        """Load validation rules from configuration."""
        return {
            "min_persona_id_length": 3,
            "max_persona_id_length": 50,
            "max_name_length": 100,
            "max_description_length": 500,
            "max_tools_count": 20,
            "max_capabilities_count": 30,
            "min_price": 0.0,
            "max_price": 1000.0
        }

    def _load_industry_templates(self) -> Dict[str, Dict[str, Any]]:
        """Load industry-specific configuration templates."""
        return {
            "technology": {
                "specialized_tools": ["code_analysis", "api_integration", "data_processing"],
                "capabilities": ["software_development", "system_architecture", "technical_analysis"],
                "methodology_framework": "AGILE_ITERATIVE",
                "compliance_requirements": ["security_standards", "data_privacy"]
            },
            "healthcare": {
                "specialized_tools": ["medical_data_analysis", "compliance_checker", "research_tools"],
                "capabilities": ["medical_analysis", "regulatory_compliance", "patient_data_handling"],
                "methodology_framework": "UNDERSTAND_ASSESS_EXECUTE_DELIVER",
                "compliance_requirements": ["hipaa", "gdpr", "medical_ethics"]
            },
            "finance": {
                "specialized_tools": ["financial_analysis", "risk_assessment", "market_data"],
                "capabilities": ["financial_modeling", "risk_analysis", "market_research"],
                "methodology_framework": "WATERFALL_SEQUENTIAL",
                "compliance_requirements": ["sox", "basel_iii", "mifid_ii"]
            },
            "marketing": {
                "specialized_tools": ["content_generation", "social_media_analysis", "campaign_optimization"],
                "capabilities": ["content_creation", "audience_analysis", "campaign_management"],
                "methodology_framework": "DESIGN_THINKING",
                "compliance_requirements": ["gdpr", "ccpa", "advertising_standards"]
            }
        }

    async def get_industry_template(self, industry: str) -> Optional[Dict[str, Any]]:
        """
        Get industry-specific configuration template.

        Args:
            industry: Industry name

        Returns:
            Template configuration or None if not found
        """
        return self._industry_templates.get(industry.lower())

    async def _trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """
        Trigger event handlers.

        Args:
            event_type: Type of event
            event_data: Event data
        """
        try:
            handlers = self._event_handlers.get(event_type, [])
            for handler in handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(event_data)
                    else:
                        handler(event_data)
                except Exception as e:
                    self.logger.error(f"Error in event handler for {event_type}: {e}")

        except Exception as e:
            self.logger.error(f"Error triggering event {event_type}: {e}")

    def add_event_handler(self, event_type: str, handler: callable) -> None:
        """
        Add event handler.

        Args:
            event_type: Type of event
            handler: Handler function
        """
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)

    async def _sync_configuration_with_db(self, persona_id: str) -> None:
        """
        Synchronize cached configuration with database.

        Args:
            persona_id: Persona identifier
        """
        try:
            if persona_id in self._config_cache:
                config = self._config_cache[persona_id]
                await self.db_manager.save_persona_configuration(
                    persona_id=persona_id,
                    configuration=asdict(config),
                    industry_specialization=config.industry_specialization,
                    methodology_framework=config.methodology_framework,
                    enable_cross_agent_intelligence=config.enable_cross_agent_intelligence,
                    specialized_tools=config.specialized_tools,
                    compliance_requirements=config.compliance_requirements,
                    workflow_patterns=config.workflow_patterns,
                    performance_optimization=config.performance_optimization
                )

        except Exception as e:
            self.logger.error(f"Error syncing configuration for {persona_id}: {e}")

    async def cleanup(self) -> None:
        """Cleanup resources and stop background tasks."""
        try:
            # Cancel background tasks
            for task in self._background_tasks:
                if not task.done():
                    task.cancel()

            # Wait for tasks to complete
            if self._background_tasks:
                await asyncio.gather(*self._background_tasks, return_exceptions=True)

            # Clear cache
            self._config_cache.clear()
            self._cache_expiry.clear()

            self.logger.info("Persona configuration manager cleaned up successfully")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


# Global instance (lazy initialization)
_persona_config_manager = None

def get_persona_config_manager() -> PersonaConfigurationManager:
    """Get the global persona configuration manager instance (lazy initialization)."""
    global _persona_config_manager
    if _persona_config_manager is None:
        _persona_config_manager = PersonaConfigurationManager()
    return _persona_config_manager

# For backward compatibility
persona_config_manager = None
