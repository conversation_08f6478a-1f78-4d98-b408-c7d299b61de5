"""
Tool Execution Node for LangGraph-based Datagenius System.

This module provides tool execution capabilities that integrate
MCP tools into the LangGraph workflow system.
"""

import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
import uuid
import asyncio

from ..states.unified_state import (
    UnifiedDatageniusState,
    update_tool_status,
    ToolStatus
)

logger = logging.getLogger(__name__)


class ToolExecutionNode:
    """
    Node for executing tools within LangGraph workflows.
    
    This node handles MCP tool integration and execution,
    replacing the existing tool execution logic with
    LangGraph-native tool calling.
    """
    
    def __init__(
        self,
        tool_name: str,
        tool_function: Callable,
        tool_config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the tool execution node.
        
        Args:
            tool_name: Name of the tool
            tool_function: Function to execute the tool
            tool_config: Optional tool configuration
        """
        self.tool_name = tool_name
        self.tool_function = tool_function
        self.tool_config = tool_config or {}
        self.logger = logging.getLogger(f"{__name__}.{tool_name}")
        
        # Performance tracking
        self.execution_count = 0
        self.success_count = 0
        self.error_count = 0
        self.total_execution_time = 0.0
        
        self.logger.info(f"Initialized tool execution node: {tool_name}")
    
    async def execute(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Execute the tool with the given state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state with tool results
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"Executing tool: {self.tool_name}")
            
            # Update tool status to running
            state = update_tool_status(state, self.tool_name, ToolStatus.RUNNING)
            
            # Prepare tool arguments from state
            tool_args = await self._prepare_tool_arguments(state)
            
            # Execute the tool
            result = await self._execute_tool(tool_args)
            
            # Process tool result
            state = await self._process_tool_result(state, result)
            
            # Update tool status to completed
            state = update_tool_status(state, self.tool_name, ToolStatus.COMPLETED, result)
            
            # Update performance metrics
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_metrics(execution_time, success=True)
            
            self.logger.info(f"Tool {self.tool_name} completed successfully in {execution_time:.2f}s")
            
            return state
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_metrics(execution_time, success=False)
            
            # Update tool status to failed
            error_info = {
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "execution_time": execution_time
            }
            state = update_tool_status(state, self.tool_name, ToolStatus.FAILED, error_info)
            
            # Add error to state
            error_record = {
                "timestamp": datetime.now().isoformat(),
                "tool_name": self.tool_name,
                "error": str(e),
                "type": "tool_execution_error"
            }
            state["error_history"].append(error_record)
            
            self.logger.error(f"Error executing tool {self.tool_name}: {e}", exc_info=True)
            
            return state
    
    async def _prepare_tool_arguments(self, state: UnifiedDatageniusState) -> Dict[str, Any]:
        """
        Prepare arguments for tool execution from state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Tool arguments dictionary
        """
        # Base arguments from current message
        current_message = state.get("current_message", {})
        
        args = {
            "message": current_message.get("content", ""),
            "user_id": state["user_id"],
            "conversation_id": state["conversation_id"],
            "agent_id": state.get("current_agent", ""),
        }
        
        # Add context from state
        args["context"] = {
            "business_profile_id": state.get("business_profile_id"),
            "business_context": state.get("business_context", {}),
            "conversation_context": state.get("conversation_context", {}),
            "attached_files": state.get("attached_files", []),
            "data_sources": state.get("data_sources", []),
            "user_preferences": state.get("user_preferences", {}),
            "cross_agent_context": state.get("cross_agent_context", {})
        }
        
        # Add tool-specific configuration
        if self.tool_config:
            args["config"] = self.tool_config
        
        # Add any tool-specific arguments from state
        tool_specific_args = state.get("tool_arguments", {}).get(self.tool_name, {})
        args.update(tool_specific_args)
        
        return args
    
    async def _execute_tool(self, args: Dict[str, Any]) -> Any:
        """
        Execute the tool function with prepared arguments.
        
        Args:
            args: Tool arguments
            
        Returns:
            Tool execution result
        """
        # Check if tool function is async
        if asyncio.iscoroutinefunction(self.tool_function):
            return await self.tool_function(**args)
        else:
            # Run sync function in thread pool
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: self.tool_function(**args))
    
    async def _process_tool_result(
        self, 
        state: UnifiedDatageniusState, 
        result: Any
    ) -> UnifiedDatageniusState:
        """
        Process tool execution result and update state.
        
        Args:
            state: Current workflow state
            result: Tool execution result
            
        Returns:
            Updated workflow state
        """
        # Store result in state
        state["tool_results"][self.tool_name] = result
        
        # Process different types of results
        if isinstance(result, dict):
            # Handle structured results
            if "message" in result:
                # Tool returned a message response
                message = {
                    "id": str(uuid.uuid4()),
                    "content": result["message"],
                    "type": "tool",
                    "timestamp": datetime.now().isoformat(),
                    "tool_name": self.tool_name,
                    "metadata": result.get("metadata", {})
                }
                state["messages"].append(message)
            
            if "data" in result:
                # Tool returned data
                state["processed_data"][self.tool_name] = result["data"]
            
            if "insights" in result:
                # Tool generated insights
                for insight in result["insights"]:
                    insight_record = {
                        "id": str(uuid.uuid4()),
                        "type": f"tool_{self.tool_name}",
                        "content": insight,
                        "source_tool": self.tool_name,
                        "timestamp": datetime.now().isoformat(),
                        "business_profile_id": state.get("business_profile_id")
                    }
                    state["shared_insights"].append(insight_record)
            
            if "files" in result:
                # Tool generated files
                state["attached_files"].extend(result["files"])
        
        elif isinstance(result, str):
            # Simple string result - treat as message
            message = {
                "id": str(uuid.uuid4()),
                "content": result,
                "type": "tool",
                "timestamp": datetime.now().isoformat(),
                "tool_name": self.tool_name
            }
            state["messages"].append(message)
        
        # Update state metadata
        state["updated_at"] = datetime.now().isoformat()
        
        return state
    
    def _update_performance_metrics(self, execution_time: float, success: bool) -> None:
        """
        Update tool performance metrics.
        
        Args:
            execution_time: Time taken for execution
            success: Whether execution was successful
        """
        self.execution_count += 1
        self.total_execution_time += execution_time
        
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get tool performance metrics.
        
        Returns:
            Performance metrics dictionary
        """
        return {
            "tool_name": self.tool_name,
            "execution_count": self.execution_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_count / max(self.execution_count, 1),
            "average_execution_time": self.total_execution_time / max(self.execution_count, 1),
            "total_execution_time": self.total_execution_time
        }
    
    def get_tool_info(self) -> Dict[str, Any]:
        """
        Get tool information.
        
        Returns:
            Tool information dictionary
        """
        return {
            "name": self.tool_name,
            "config": self.tool_config,
            "metrics": self.get_performance_metrics()
        }


class MCPToolExecutionNode(ToolExecutionNode):
    """
    Specialized tool execution node for MCP tools.
    
    This node provides enhanced integration with the existing
    MCP tool system in Datagenius.
    """
    
    def __init__(
        self,
        tool_name: str,
        mcp_server,
        tool_config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the MCP tool execution node.
        
        Args:
            tool_name: Name of the MCP tool
            mcp_server: MCP server instance
            tool_config: Optional tool configuration
        """
        # Create tool function that calls MCP server
        async def mcp_tool_function(**kwargs):
            return await mcp_server.call_tool(tool_name, kwargs)
        
        super().__init__(tool_name, mcp_tool_function, tool_config)
        self.mcp_server = mcp_server
        self.logger.info(f"Initialized MCP tool execution node: {tool_name}")
    
    async def _prepare_tool_arguments(self, state: UnifiedDatageniusState) -> Dict[str, Any]:
        """
        Prepare MCP-specific tool arguments.
        
        Args:
            state: Current workflow state
            
        Returns:
            MCP tool arguments
        """
        # Get base arguments
        args = await super()._prepare_tool_arguments(state)
        
        # Add MCP-specific formatting
        mcp_args = {
            "arguments": {
                "message": args["message"],
                "context": args["context"],
                "user_id": args["user_id"],
                "conversation_id": args["conversation_id"]
            }
        }
        
        # Add any MCP-specific configuration
        if "mcp_config" in self.tool_config:
            mcp_args["arguments"].update(self.tool_config["mcp_config"])
        
        return mcp_args
    
    async def _process_tool_result(
        self, 
        state: UnifiedDatageniusState, 
        result: Any
    ) -> UnifiedDatageniusState:
        """
        Process MCP tool result.
        
        Args:
            state: Current workflow state
            result: MCP tool result
            
        Returns:
            Updated workflow state
        """
        # Handle MCP response format
        if isinstance(result, dict) and "content" in result:
            # Extract content from MCP response
            content = result["content"]
            if isinstance(content, list) and content:
                # Get first content item
                first_content = content[0]
                if isinstance(first_content, dict) and "text" in first_content:
                    processed_result = {
                        "message": first_content["text"],
                        "metadata": {
                            "mcp_tool": self.tool_name,
                            "raw_response": result
                        }
                    }
                    return await super()._process_tool_result(state, processed_result)
        
        # Fallback to standard processing
        return await super()._process_tool_result(state, result)
