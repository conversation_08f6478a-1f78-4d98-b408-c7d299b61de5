"""
Workflow State Validation System for LangGraph.

This module provides comprehensive state validation to prevent agents from executing
without producing meaningful state changes and ensures proper workflow termination.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime, timedelta
from enum import Enum

from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class ValidationResult(Enum):
    """Validation result types."""
    VALID = "valid"
    INVALID = "invalid"
    WARNING = "warning"
    TERMINATE = "terminate"


class StateValidationError(Exception):
    """Exception raised when state validation fails."""
    pass


class WorkflowStateValidator:
    """
    Comprehensive state validation system for workflow management.
    
    Validates:
    - State integrity and completeness
    - Agent execution progress
    - Infinite loop prevention
    - Termination conditions
    - Resource constraints
    """
    
    def __init__(self):
        """Initialize the state validator."""
        self.logger = logging.getLogger(__name__)
        
        # Validation metrics
        self.validation_metrics = {
            "validations_performed": 0,
            "validations_passed": 0,
            "validations_failed": 0,
            "warnings_issued": 0,
            "terminations_triggered": 0
        }
        
        # State change tracking
        self.state_history: Dict[str, List[Dict[str, Any]]] = {}
        self.execution_tracking: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info("WorkflowStateValidator initialized")
    
    def validate_state(
        self,
        state: UnifiedDatageniusState,
        agent_id: Optional[str] = None,
        template_config: Optional[Dict[str, Any]] = None
    ) -> Tuple[ValidationResult, List[str], Dict[str, Any]]:
        """
        Perform comprehensive state validation.
        
        Args:
            state: Current workflow state
            agent_id: ID of the agent being validated
            template_config: Workflow template configuration
            
        Returns:
            Tuple of (validation_result, issues, recommendations)
        """
        try:
            self.validation_metrics["validations_performed"] += 1
            
            issues = []
            recommendations = {}
            
            # Basic state integrity validation
            integrity_result, integrity_issues = self._validate_state_integrity(state)
            issues.extend(integrity_issues)
            
            # Agent execution validation
            if agent_id:
                execution_result, execution_issues = self._validate_agent_execution(state, agent_id)
                issues.extend(execution_issues)
            
            # Infinite loop detection
            loop_result, loop_issues = self._detect_infinite_loops(state, agent_id)
            issues.extend(loop_issues)
            
            # Termination condition validation
            termination_result, termination_issues = self._validate_termination_conditions(
                state, template_config
            )
            issues.extend(termination_issues)
            
            # Resource constraint validation
            resource_result, resource_issues = self._validate_resource_constraints(
                state, template_config
            )
            issues.extend(resource_issues)
            
            # Progress validation
            progress_result, progress_issues = self._validate_progress(state, agent_id)
            issues.extend(progress_issues)
            
            # Determine overall result
            results = [integrity_result, execution_result, loop_result, 
                      termination_result, resource_result, progress_result]
            
            if ValidationResult.TERMINATE in results:
                self.validation_metrics["terminations_triggered"] += 1
                return ValidationResult.TERMINATE, issues, recommendations
            elif ValidationResult.INVALID in results:
                self.validation_metrics["validations_failed"] += 1
                return ValidationResult.INVALID, issues, recommendations
            elif ValidationResult.WARNING in results:
                self.validation_metrics["warnings_issued"] += 1
                return ValidationResult.WARNING, issues, recommendations
            else:
                self.validation_metrics["validations_passed"] += 1
                return ValidationResult.VALID, issues, recommendations
                
        except Exception as e:
            self.logger.error(f"Error during state validation: {e}")
            self.validation_metrics["validations_failed"] += 1
            return ValidationResult.INVALID, [f"Validation error: {str(e)}"], {}
    
    def _validate_state_integrity(self, state: UnifiedDatageniusState) -> Tuple[ValidationResult, List[str]]:
        """Validate basic state integrity."""
        issues = []
        
        # Check required fields
        required_fields = ["user_id", "conversation_id", "messages", "workflow_id"]
        for field in required_fields:
            if field not in state or state[field] is None:
                issues.append(f"Missing required field: {field}")
        
        # Check state structure
        if not isinstance(state.get("messages", []), list):
            issues.append("Messages field must be a list")
        
        if not isinstance(state.get("agent_context", {}), dict):
            issues.append("Agent context must be a dictionary")
        
        # Check for empty critical fields
        if not state.get("messages"):
            issues.append("No messages in conversation")
        
        return ValidationResult.INVALID if issues else ValidationResult.VALID, issues
    
    def _validate_agent_execution(self, state: UnifiedDatageniusState, agent_id: str) -> Tuple[ValidationResult, List[str]]:
        """Validate agent execution state."""
        issues = []
        
        # Check execution metrics
        execution_metrics = state.get("execution_metrics", {})
        execution_count = execution_metrics.get("agent_execution_count", 0)
        
        # Check for excessive executions
        max_executions = 20  # Default limit
        if execution_count > max_executions:
            issues.append(f"Agent execution count ({execution_count}) exceeds maximum ({max_executions})")
            return ValidationResult.TERMINATE, issues
        
        # Check agent context
        agent_context = state.get("agent_context", {}).get(agent_id, {})
        
        # Check for processing errors
        if not agent_context.get("processing_successful", True):
            error = agent_context.get("error")
            if error:
                issues.append(f"Agent {agent_id} processing failed: {error}")
                return ValidationResult.WARNING, issues
        
        # Check for stale execution
        last_processed = agent_context.get("last_processed")
        if last_processed:
            try:
                last_time = datetime.fromisoformat(last_processed)
                if (datetime.now() - last_time).total_seconds() > 300:  # 5 minutes
                    issues.append(f"Agent {agent_id} execution appears stale")
                    return ValidationResult.WARNING, issues
            except ValueError:
                issues.append(f"Invalid timestamp format for agent {agent_id}")
        
        return ValidationResult.VALID, issues
    
    def _detect_infinite_loops(self, state: UnifiedDatageniusState, agent_id: Optional[str]) -> Tuple[ValidationResult, List[str]]:
        """Detect infinite loops in workflow execution."""
        issues = []
        
        # Check rapid execution patterns
        if agent_id:
            agent_context = state.get("agent_context", {}).get(agent_id, {})
            rapid_count = agent_context.get("rapid_execution_count", 0)
            
            if rapid_count >= 3:
                issues.append(f"Infinite loop detected for agent {agent_id}")
                return ValidationResult.TERMINATE, issues
        
        # Check overall execution patterns
        execution_metrics = state.get("execution_metrics", {})
        total_executions = execution_metrics.get("agent_execution_count", 0)
        
        # Track state changes
        workflow_id = state.get("workflow_id")
        if workflow_id:
            self._track_state_changes(workflow_id, state)
            
            # Check for repetitive state patterns
            if self._detect_repetitive_patterns(workflow_id):
                issues.append("Repetitive execution pattern detected")
                return ValidationResult.TERMINATE, issues
        
        return ValidationResult.VALID, issues
    
    def _validate_termination_conditions(
        self,
        state: UnifiedDatageniusState,
        template_config: Optional[Dict[str, Any]]
    ) -> Tuple[ValidationResult, List[str]]:
        """Validate workflow termination conditions."""
        issues = []
        
        # Check explicit termination signals
        if state.get("workflow_complete"):
            return ValidationResult.TERMINATE, ["Workflow marked as complete"]
        
        if state.get("next_action") == "END":
            return ValidationResult.TERMINATE, ["Explicit END action requested"]
        
        # Check template-based termination conditions
        if template_config:
            termination_conditions = template_config.get("termination_conditions", [])
            
            for condition in termination_conditions:
                if self._evaluate_condition(condition.get("condition", ""), state):
                    action = condition.get("action", "END")
                    if action == "END":
                        return ValidationResult.TERMINATE, [f"Template condition met: {condition.get('condition')}"]
        
        # Check for error conditions that should terminate
        if state.get("critical_error"):
            return ValidationResult.TERMINATE, ["Critical error detected"]
        
        return ValidationResult.VALID, issues
    
    def _validate_resource_constraints(
        self,
        state: UnifiedDatageniusState,
        template_config: Optional[Dict[str, Any]]
    ) -> Tuple[ValidationResult, List[str]]:
        """Validate resource constraints."""
        issues = []
        
        # Check execution time limits
        workflow_start = state.get("workflow_start_time")
        if workflow_start:
            try:
                start_time = datetime.fromisoformat(workflow_start)
                elapsed = (datetime.now() - start_time).total_seconds()
                
                # Default timeout
                max_timeout = 300  # 5 minutes
                
                # Template-specific timeout
                if template_config:
                    workflow_config = template_config.get("workflow_config", {})
                    max_timeout = workflow_config.get("execution_timeout", max_timeout)
                
                if elapsed > max_timeout:
                    issues.append(f"Workflow execution timeout ({elapsed:.1f}s > {max_timeout}s)")
                    return ValidationResult.TERMINATE, issues
                    
            except ValueError:
                issues.append("Invalid workflow start time format")
        
        # Check memory constraints (if available)
        memory_usage = state.get("resource_usage", {}).get("memory_mb", 0)
        if memory_usage > 1000:  # 1GB limit
            issues.append(f"High memory usage detected: {memory_usage}MB")
            return ValidationResult.WARNING, issues
        
        return ValidationResult.VALID, issues
    
    def _validate_progress(self, state: UnifiedDatageniusState, agent_id: Optional[str]) -> Tuple[ValidationResult, List[str]]:
        """Validate that meaningful progress is being made."""
        issues = []
        
        # Check for message generation
        messages = state.get("messages", [])
        if not messages:
            issues.append("No messages generated")
            return ValidationResult.WARNING, issues
        
        # Check for recent activity
        last_message = messages[-1] if messages else {}
        message_timestamp = last_message.get("timestamp")
        
        if message_timestamp:
            try:
                msg_time = datetime.fromisoformat(message_timestamp)
                if (datetime.now() - msg_time).total_seconds() > 60:  # 1 minute
                    issues.append("No recent message activity")
                    return ValidationResult.WARNING, issues
            except ValueError:
                issues.append("Invalid message timestamp format")
        
        # Check for state changes
        if agent_id:
            agent_context = state.get("agent_context", {}).get(agent_id, {})
            if not agent_context:
                issues.append(f"No context found for agent {agent_id}")
                return ValidationResult.WARNING, issues
        
        return ValidationResult.VALID, issues
    
    def _track_state_changes(self, workflow_id: str, state: UnifiedDatageniusState) -> None:
        """Track state changes for pattern detection."""
        if workflow_id not in self.state_history:
            self.state_history[workflow_id] = []
        
        # Create state snapshot
        snapshot = {
            "timestamp": datetime.now().isoformat(),
            "message_count": len(state.get("messages", [])),
            "current_agent": state.get("current_agent"),
            "execution_count": state.get("execution_metrics", {}).get("agent_execution_count", 0)
        }
        
        self.state_history[workflow_id].append(snapshot)
        
        # Keep only recent history (last 10 snapshots)
        if len(self.state_history[workflow_id]) > 10:
            self.state_history[workflow_id] = self.state_history[workflow_id][-10:]
    
    def _detect_repetitive_patterns(self, workflow_id: str) -> bool:
        """Detect repetitive execution patterns."""
        if workflow_id not in self.state_history:
            return False
        
        history = self.state_history[workflow_id]
        if len(history) < 4:
            return False
        
        # Check for identical consecutive states
        recent_states = history[-4:]
        
        # Compare execution counts
        execution_counts = [s.get("execution_count", 0) for s in recent_states]
        
        # If execution count isn't increasing, it might be a loop
        if len(set(execution_counts)) <= 2 and max(execution_counts) > 5:
            return True
        
        return False
    
    def _evaluate_condition(self, condition: str, state: UnifiedDatageniusState) -> bool:
        """Evaluate a termination condition string."""
        try:
            # Simple condition evaluation
            condition_lower = condition.lower()
            
            if "response_generated" in condition_lower:
                messages = state.get("messages", [])
                return any(msg.get("type") == "agent" for msg in messages)
            
            if "workflow_complete" in condition_lower:
                return state.get("workflow_complete", False)
            
            if "error" in condition_lower:
                return state.get("error") is not None
            
            if "timeout" in condition_lower:
                # This would need more sophisticated timeout checking
                return False
            
            return False
            
        except Exception as e:
            self.logger.debug(f"Error evaluating condition '{condition}': {e}")
            return False
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get validation metrics."""
        return self.validation_metrics.copy()
    
    def reset_metrics(self) -> None:
        """Reset validation metrics."""
        self.validation_metrics = {
            "validations_performed": 0,
            "validations_passed": 0,
            "validations_failed": 0,
            "warnings_issued": 0,
            "terminations_triggered": 0
        }
    
    def cleanup_history(self, max_age_hours: int = 24) -> None:
        """Clean up old state history."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        workflows_to_remove = []
        for workflow_id, history in self.state_history.items():
            # Remove old entries
            filtered_history = []
            for entry in history:
                try:
                    entry_time = datetime.fromisoformat(entry["timestamp"])
                    if entry_time > cutoff_time:
                        filtered_history.append(entry)
                except ValueError:
                    continue
            
            if filtered_history:
                self.state_history[workflow_id] = filtered_history
            else:
                workflows_to_remove.append(workflow_id)
        
        # Remove empty workflows
        for workflow_id in workflows_to_remove:
            del self.state_history[workflow_id]
        
        self.logger.debug(f"Cleaned up {len(workflows_to_remove)} old workflow histories")


# Global state validator instance
state_validator = WorkflowStateValidator()
