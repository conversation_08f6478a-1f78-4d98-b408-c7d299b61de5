"""
Event type definitions for LangGraph system.

This module defines standardized event types for different system components
including agent registration, workflow completion, capability updates, and
dashboard notifications.
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from .event_bus import LangGraphEvent, EventPriority


class AgentRegistrationEvent(LangGraphEvent):
    """Event fired when an agent is registered or updated."""
    
    def __init__(self, agent_id: str, capabilities: List[str], metadata: Dict[str, Any]):
        super().__init__(
            event_type="agent.registered",
            timestamp=datetime.now(),
            source="agent_registry",
            data={
                "agent_id": agent_id,
                "capabilities": capabilities,
                "metadata": metadata,
                "registration_time": datetime.now().isoformat()
            },
            priority=EventPriority.HIGH
        )


class AgentUnregistrationEvent(LangGraphEvent):
    """Event fired when an agent is unregistered."""
    
    def __init__(self, agent_id: str, reason: str = "manual"):
        super().__init__(
            event_type="agent.unregistered",
            timestamp=datetime.now(),
            source="agent_registry",
            data={
                "agent_id": agent_id,
                "reason": reason,
                "unregistration_time": datetime.now().isoformat()
            },
            priority=EventPriority.HIGH
        )


class WorkflowStartedEvent(LangGraphEvent):
    """Event fired when a workflow starts execution."""
    
    def __init__(self, workflow_id: str, workflow_type: str, user_id: str, 
                 agents_involved: List[str], estimated_duration: float = 30.0):
        super().__init__(
            event_type="workflow.started",
            timestamp=datetime.now(),
            source="workflow_manager",
            data={
                "workflow_id": workflow_id,
                "workflow_type": workflow_type,
                "user_id": user_id,
                "agents_involved": agents_involved,
                "estimated_duration": estimated_duration,
                "start_time": datetime.now().isoformat()
            },
            priority=EventPriority.MEDIUM
        )


class WorkflowCompletedEvent(LangGraphEvent):
    """Event fired when a workflow completes."""
    
    def __init__(self, workflow_id: str, execution_time: float, success: bool, 
                 results: Dict[str, Any], error_message: Optional[str] = None):
        super().__init__(
            event_type="workflow.completed",
            timestamp=datetime.now(),
            source="workflow_manager",
            data={
                "workflow_id": workflow_id,
                "execution_time": execution_time,
                "success": success,
                "results": results,
                "error_message": error_message,
                "completion_time": datetime.now().isoformat()
            },
            priority=EventPriority.MEDIUM
        )


class WorkflowFailedEvent(LangGraphEvent):
    """Event fired when a workflow fails."""
    
    def __init__(self, workflow_id: str, error_message: str, error_details: Dict[str, Any],
                 execution_time: float, recovery_possible: bool = True):
        super().__init__(
            event_type="workflow.failed",
            timestamp=datetime.now(),
            source="workflow_manager",
            data={
                "workflow_id": workflow_id,
                "error_message": error_message,
                "error_details": error_details,
                "execution_time": execution_time,
                "recovery_possible": recovery_possible,
                "failure_time": datetime.now().isoformat()
            },
            priority=EventPriority.HIGH
        )


class CapabilityUpdateEvent(LangGraphEvent):
    """Event fired when agent capabilities are updated."""
    
    def __init__(self, agent_id: str, old_capabilities: List[str], 
                 new_capabilities: List[str], update_reason: str = "manual"):
        changes = {
            "added": list(set(new_capabilities) - set(old_capabilities)),
            "removed": list(set(old_capabilities) - set(new_capabilities))
        }
        
        super().__init__(
            event_type="agent.capability_updated",
            timestamp=datetime.now(),
            source="capability_manager",
            data={
                "agent_id": agent_id,
                "old_capabilities": old_capabilities,
                "new_capabilities": new_capabilities,
                "changes": changes,
                "update_reason": update_reason,
                "update_time": datetime.now().isoformat()
            },
            priority=EventPriority.HIGH
        )


class DashboardUpdateEvent(LangGraphEvent):
    """Event fired when dashboard data needs updating."""
    
    def __init__(self, dashboard_id: str, widget_id: str, data: Dict[str, Any],
                 update_type: str = "data_refresh"):
        super().__init__(
            event_type="dashboard.update_required",
            timestamp=datetime.now(),
            source="workflow_result",
            data={
                "dashboard_id": dashboard_id,
                "widget_id": widget_id,
                "update_data": data,
                "update_type": update_type,
                "update_time": datetime.now().isoformat()
            },
            priority=EventPriority.MEDIUM
        )


class AgentPerformanceEvent(LangGraphEvent):
    """Event fired when agent performance metrics are updated."""
    
    def __init__(self, agent_id: str, performance_metrics: Dict[str, Any],
                 workflow_id: Optional[str] = None):
        super().__init__(
            event_type="agent.performance_updated",
            timestamp=datetime.now(),
            source="performance_monitor",
            data={
                "agent_id": agent_id,
                "performance_metrics": performance_metrics,
                "workflow_id": workflow_id,
                "measurement_time": datetime.now().isoformat()
            },
            priority=EventPriority.LOW
        )


class SystemHealthEvent(LangGraphEvent):
    """Event fired for system health monitoring."""
    
    def __init__(self, component: str, status: str, metrics: Dict[str, Any],
                 alert_level: str = "info"):
        priority_map = {
            "critical": EventPriority.CRITICAL,
            "warning": EventPriority.HIGH,
            "info": EventPriority.LOW
        }
        
        super().__init__(
            event_type="system.health_update",
            timestamp=datetime.now(),
            source="health_monitor",
            data={
                "component": component,
                "status": status,
                "metrics": metrics,
                "alert_level": alert_level,
                "check_time": datetime.now().isoformat()
            },
            priority=priority_map.get(alert_level, EventPriority.LOW)
        )


class UserActivityEvent(LangGraphEvent):
    """Event fired for user activity tracking."""
    
    def __init__(self, user_id: str, activity_type: str, activity_data: Dict[str, Any]):
        super().__init__(
            event_type="user.activity",
            timestamp=datetime.now(),
            source="user_activity_tracker",
            data={
                "user_id": user_id,
                "activity_type": activity_type,
                "activity_data": activity_data,
                "activity_time": datetime.now().isoformat()
            },
            priority=EventPriority.LOW
        )


class BusinessProfileUpdateEvent(LangGraphEvent):
    """Event fired when business profile is updated."""

    def __init__(self, profile_id: str, user_id: str, changes: Dict[str, Any],
                 update_source: str = "user"):
        super().__init__(
            event_type="business_profile.updated",
            timestamp=datetime.now(),
            source="business_profile_manager",
            data={
                "profile_id": profile_id,
                "user_id": user_id,
                "changes": changes,
                "update_source": update_source,
                "update_time": datetime.now().isoformat()
            },
            priority=EventPriority.MEDIUM
        )


class ToolExecutionStartedEvent(LangGraphEvent):
    """Event fired when a tool execution starts."""

    def __init__(self, tool_name: str, agent_type: str, workflow_id: str,
                 conversation_id: str, message_id: str, tool_args: Dict[str, Any]):
        super().__init__(
            event_type="tool.execution_started",
            timestamp=datetime.now(),
            source="mcp_tool_node",
            data={
                "tool_name": tool_name,
                "agent_type": agent_type,
                "workflow_id": workflow_id,
                "conversation_id": conversation_id,
                "message_id": message_id,
                "tool_args": tool_args,
                "start_time": datetime.now().isoformat()
            },
            priority=EventPriority.MEDIUM
        )


class ToolExecutionProgressEvent(LangGraphEvent):
    """Event fired during tool execution to show progress."""

    def __init__(self, tool_name: str, agent_type: str, workflow_id: str,
                 conversation_id: str, message_id: str, progress: float = None,
                 status_message: str = None):
        super().__init__(
            event_type="tool.execution_progress",
            timestamp=datetime.now(),
            source="mcp_tool_node",
            data={
                "tool_name": tool_name,
                "agent_type": agent_type,
                "workflow_id": workflow_id,
                "conversation_id": conversation_id,
                "message_id": message_id,
                "progress": progress,
                "status_message": status_message,
                "progress_time": datetime.now().isoformat()
            },
            priority=EventPriority.LOW
        )


class ToolExecutionCompletedEvent(LangGraphEvent):
    """Event fired when a tool execution completes successfully."""

    def __init__(self, tool_name: str, agent_type: str, workflow_id: str,
                 conversation_id: str, message_id: str, execution_time: float,
                 result_summary: Dict[str, Any] = None):
        super().__init__(
            event_type="tool.execution_completed",
            timestamp=datetime.now(),
            source="mcp_tool_node",
            data={
                "tool_name": tool_name,
                "agent_type": agent_type,
                "workflow_id": workflow_id,
                "conversation_id": conversation_id,
                "message_id": message_id,
                "execution_time": execution_time,
                "result_summary": result_summary or {},
                "completion_time": datetime.now().isoformat()
            },
            priority=EventPriority.MEDIUM
        )


class ToolExecutionFailedEvent(LangGraphEvent):
    """Event fired when a tool execution fails."""

    def __init__(self, tool_name: str, agent_type: str, workflow_id: str,
                 conversation_id: str, message_id: str, error_message: str,
                 execution_time: float = None, error_details: Dict[str, Any] = None):
        super().__init__(
            event_type="tool.execution_failed",
            timestamp=datetime.now(),
            source="mcp_tool_node",
            data={
                "tool_name": tool_name,
                "agent_type": agent_type,
                "workflow_id": workflow_id,
                "conversation_id": conversation_id,
                "message_id": message_id,
                "error_message": error_message,
                "execution_time": execution_time,
                "error_details": error_details or {},
                "failure_time": datetime.now().isoformat()
            },
            priority=EventPriority.HIGH
        )


# Export all event types for easy importing
__all__ = [
    "AgentRegistrationEvent",
    "AgentUnregistrationEvent",
    "WorkflowStartedEvent",
    "WorkflowCompletedEvent",
    "WorkflowFailedEvent",
    "CapabilityUpdateEvent",
    "DashboardUpdateEvent",
    "AgentPerformanceEvent",
    "SystemHealthEvent",
    "UserActivityEvent",
    "BusinessProfileUpdateEvent",
    "ToolExecutionStartedEvent",
    "ToolExecutionProgressEvent",
    "ToolExecutionCompletedEvent",
    "ToolExecutionFailedEvent"
]
