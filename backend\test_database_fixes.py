#!/usr/bin/env python3
"""
Test script to verify database connectivity and repository fixes.

This script tests:
1. Database connection
2. ProviderApiKeyRepository session attribute fix
3. DatabaseServiceError constructor fix
"""

import sys
import os
import logging

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test basic database connection."""
    try:
        from app.config import DATABASE_URL
        from app.repositories.database_service import get_database_service
        
        logger.info(f"Testing database connection to: {DATABASE_URL}")
        
        db_service = get_database_service()
        with db_service.get_session() as session:
            result = session.execute("SELECT 1 as test").fetchone()
            logger.info(f"✓ Database connection successful: {result}")
            return True
    except Exception as e:
        logger.error(f"✗ Database connection failed: {e}")
        return False

def test_provider_api_key_repository():
    """Test ProviderApiKeyRepository session attribute fix."""
    try:
        from app.repositories.database_service import get_database_service
        from app.repositories.provider_api_key_repository import ProviderApiKeyRepository
        
        logger.info("Testing ProviderApiKeyRepository session attribute...")
        
        db_service = get_database_service()
        with db_service.get_session() as session:
            repo = ProviderApiKeyRepository(session)
            
            # Test that both db and session attributes exist
            assert hasattr(repo, 'db'), "Repository missing 'db' attribute"
            assert hasattr(repo, 'session'), "Repository missing 'session' attribute"
            assert repo.db is repo.session, "db and session attributes should be the same"
            
            logger.info("✓ ProviderApiKeyRepository session attribute fix working")
            return True
    except Exception as e:
        logger.error(f"✗ ProviderApiKeyRepository test failed: {e}")
        return False

def test_database_service_error():
    """Test DatabaseServiceError constructor fix."""
    try:
        from app.repositories.database_service import DatabaseServiceError
        
        logger.info("Testing DatabaseServiceError constructor...")
        
        # Test creating error with operation parameter
        error = DatabaseServiceError("Test error", operation="test_operation")
        assert error.operation == "test_operation", "Operation attribute not set correctly"
        assert error.message == "Test error", "Message not set correctly"
        
        logger.info("✓ DatabaseServiceError constructor fix working")
        return True
    except Exception as e:
        logger.error(f"✗ DatabaseServiceError test failed: {e}")
        return False

def test_provider_api_key_methods():
    """Test ProviderApiKeyRepository methods that were failing."""
    try:
        from app.repositories.database_service import get_database_service
        from app.repositories.provider_api_key_repository import ProviderApiKeyRepository
        
        logger.info("Testing ProviderApiKeyRepository methods...")
        
        db_service = get_database_service()
        with db_service.get_session() as session:
            repo = ProviderApiKeyRepository(session)
            
            # Test get_user_provider_api_keys method (this was failing before)
            try:
                result = repo.get_user_provider_api_keys(user_id=1)
                logger.info(f"✓ get_user_provider_api_keys method working: {len(result)} keys found")
            except Exception as e:
                # This might fail if user doesn't exist, but should not fail due to session attribute
                if "session" in str(e).lower():
                    raise e
                logger.info(f"✓ get_user_provider_api_keys method working (no session error)")
            
            return True
    except Exception as e:
        logger.error(f"✗ ProviderApiKeyRepository methods test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting database fixes verification...")
    
    tests = [
        ("Database Connection", test_database_connection),
        ("ProviderApiKeyRepository Session Fix", test_provider_api_key_repository),
        ("DatabaseServiceError Constructor Fix", test_database_service_error),
        ("ProviderApiKeyRepository Methods", test_provider_api_key_methods),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("="*50)
    
    passed = 0
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        logger.info(f"{test_name}: {status}")
        if success:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.info("✓ All database fixes are working correctly!")
        return 0
    else:
        logger.error("✗ Some tests failed. Check the logs above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
