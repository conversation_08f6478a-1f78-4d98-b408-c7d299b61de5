"""
GDPR/CCPA Compliance Service for Phase 4 Privacy & Compliance.

This module provides comprehensive GDPR and CCPA compliance features including
data subject rights, consent management, and privacy impact assessments.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from enum import Enum
from dataclasses import dataclass, field
from sqlalchemy.orm import Session
from app.security.privacy_compliant_logging import privacy_logger, ConsentType
from app.security.data_encryption_service import encryption_service

logger = logging.getLogger(__name__)


class DataSubjectRight(str, Enum):
    """GDPR/CCPA data subject rights."""
    ACCESS = "access"  # Right to access personal data
    RECTIFICATION = "rectification"  # Right to correct personal data
    ERASURE = "erasure"  # Right to delete personal data
    PORTABILITY = "portability"  # Right to data portability
    RESTRICTION = "restriction"  # Right to restrict processing
    OBJECTION = "objection"  # Right to object to processing
    WITHDRAW_CONSENT = "withdraw_consent"  # Right to withdraw consent


class RequestStatus(str, Enum):
    """Status of data subject rights requests."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REJECTED = "rejected"
    EXPIRED = "expired"


@dataclass
class DataSubjectRequest:
    """Data subject rights request."""
    request_id: str
    user_id: str
    request_type: DataSubjectRight
    status: RequestStatus = RequestStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    description: Optional[str] = None
    verification_method: Optional[str] = None
    response_data: Optional[Dict[str, Any]] = None
    
    def is_expired(self) -> bool:
        """Check if request has expired (30 days for GDPR)."""
        return datetime.now() > self.created_at + timedelta(days=30)


@dataclass
class PrivacyImpactAssessment:
    """Privacy Impact Assessment record."""
    assessment_id: str
    title: str
    description: str
    data_types: List[str]
    processing_purposes: List[str]
    risk_level: str  # "low", "medium", "high"
    mitigation_measures: List[str]
    created_at: datetime = field(default_factory=datetime.now)
    reviewed_at: Optional[datetime] = None
    approved: bool = False


class GDPRCCPAComplianceService:
    """Comprehensive GDPR/CCPA compliance service."""
    
    def __init__(self):
        """Initialize the compliance service."""
        self.data_subject_requests: Dict[str, DataSubjectRequest] = {}
        self.privacy_assessments: Dict[str, PrivacyImpactAssessment] = {}
        self.data_processing_activities: List[Dict[str, Any]] = []
        
    def submit_data_subject_request(
        self,
        user_id: str,
        request_type: DataSubjectRight,
        description: Optional[str] = None,
        verification_method: str = "email"
    ) -> str:
        """
        Submit a data subject rights request.
        
        Args:
            user_id: User identifier
            request_type: Type of request
            description: Optional description
            verification_method: Method used to verify identity
            
        Returns:
            Request ID
        """
        request_id = f"dsr_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user_id}"
        
        request = DataSubjectRequest(
            request_id=request_id,
            user_id=user_id,
            request_type=request_type,
            description=description,
            verification_method=verification_method
        )
        
        self.data_subject_requests[request_id] = request
        
        # Log the request
        privacy_logger.log_privacy_event(
            event_type="data_subject_request_submitted",
            user_id=user_id,
            data={
                "request_id": request_id,
                "request_type": request_type.value,
                "verification_method": verification_method
            },
            log_type="audit"
        )
        
        logger.info(f"Data subject request submitted: {request_id} for user {user_id}")
        return request_id
    
    def process_access_request(self, request_id: str, db: Session) -> Dict[str, Any]:
        """
        Process a data access request (GDPR Article 15).
        
        Args:
            request_id: Request identifier
            db: Database session
            
        Returns:
            User's personal data
        """
        request = self.data_subject_requests.get(request_id)
        if not request or request.request_type != DataSubjectRight.ACCESS:
            raise ValueError("Invalid access request")
        
        user_id = request.user_id
        
        # Collect all user data
        user_data = {
            "user_id": user_id,
            "request_id": request_id,
            "data_collected_at": datetime.now().isoformat(),
            "data_categories": {}
        }
        
        # Get conversation data
        conversation_data = privacy_logger.get_user_data_summary(user_id)
        user_data["data_categories"]["conversations"] = conversation_data
        
        # Get business profile data (if exists)
        # This would integrate with your business profile service
        user_data["data_categories"]["business_profiles"] = self._get_user_business_profiles(user_id, db)
        
        # Get user preferences and settings
        user_data["data_categories"]["preferences"] = self._get_user_preferences(user_id, db)
        
        # Get consent records
        user_data["data_categories"]["consent_records"] = self._get_user_consent_records(user_id)
        
        # Update request status
        request.status = RequestStatus.COMPLETED
        request.completed_at = datetime.now()
        request.response_data = {"data_export_size": len(json.dumps(user_data))}
        
        # Log completion
        privacy_logger.log_privacy_event(
            event_type="data_access_request_completed",
            user_id=user_id,
            data={"request_id": request_id},
            log_type="audit"
        )
        
        return user_data
    
    def process_erasure_request(self, request_id: str, db: Session) -> Dict[str, Any]:
        """
        Process a data erasure request (GDPR Article 17 - Right to be Forgotten).
        
        Args:
            request_id: Request identifier
            db: Database session
            
        Returns:
            Summary of deleted data
        """
        request = self.data_subject_requests.get(request_id)
        if not request or request.request_type != DataSubjectRight.ERASURE:
            raise ValueError("Invalid erasure request")
        
        user_id = request.user_id
        deletion_summary = {"user_id": user_id, "deleted_data": {}}
        
        # Delete conversation data
        conversation_deletion = privacy_logger.delete_user_data(user_id)
        deletion_summary["deleted_data"]["conversations"] = conversation_deletion
        
        # Delete business profile data
        profile_deletion = self._delete_user_business_profiles(user_id, db)
        deletion_summary["deleted_data"]["business_profiles"] = profile_deletion
        
        # Delete user preferences
        preferences_deletion = self._delete_user_preferences(user_id, db)
        deletion_summary["deleted_data"]["preferences"] = preferences_deletion
        
        # Anonymize remaining data that cannot be deleted (legal requirements)
        anonymization_summary = self._anonymize_user_data(user_id, db)
        deletion_summary["anonymized_data"] = anonymization_summary
        
        # Update request status
        request.status = RequestStatus.COMPLETED
        request.completed_at = datetime.now()
        request.response_data = deletion_summary
        
        # Log completion
        privacy_logger.log_privacy_event(
            event_type="data_erasure_request_completed",
            data={
                "request_id": request_id,
                "deleted_user_id": user_id,
                "deletion_summary": deletion_summary
            },
            log_type="audit"
        )
        
        return deletion_summary
    
    def process_portability_request(self, request_id: str, db: Session) -> Dict[str, Any]:
        """
        Process a data portability request (GDPR Article 20).
        
        Args:
            request_id: Request identifier
            db: Database session
            
        Returns:
            User data in portable format
        """
        request = self.data_subject_requests.get(request_id)
        if not request or request.request_type != DataSubjectRight.PORTABILITY:
            raise ValueError("Invalid portability request")
        
        user_id = request.user_id
        
        # Get user data in structured format
        portable_data = {
            "user_id": user_id,
            "export_format": "JSON",
            "export_date": datetime.now().isoformat(),
            "data": {}
        }
        
        # Export conversation data
        portable_data["data"]["conversations"] = self._export_conversation_data(user_id)
        
        # Export business profile data
        portable_data["data"]["business_profiles"] = self._export_business_profile_data(user_id, db)
        
        # Export user preferences
        portable_data["data"]["preferences"] = self._export_user_preferences(user_id, db)
        
        # Update request status
        request.status = RequestStatus.COMPLETED
        request.completed_at = datetime.now()
        request.response_data = {"export_size": len(json.dumps(portable_data))}
        
        # Log completion
        privacy_logger.log_privacy_event(
            event_type="data_portability_request_completed",
            user_id=user_id,
            data={"request_id": request_id},
            log_type="audit"
        )
        
        return portable_data
    
    def create_privacy_impact_assessment(
        self,
        title: str,
        description: str,
        data_types: List[str],
        processing_purposes: List[str],
        risk_level: str = "medium"
    ) -> str:
        """
        Create a Privacy Impact Assessment.
        
        Args:
            title: Assessment title
            description: Assessment description
            data_types: Types of data being processed
            processing_purposes: Purposes for processing
            risk_level: Risk level assessment
            
        Returns:
            Assessment ID
        """
        assessment_id = f"pia_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Determine mitigation measures based on risk level
        mitigation_measures = self._get_mitigation_measures(risk_level, data_types)
        
        assessment = PrivacyImpactAssessment(
            assessment_id=assessment_id,
            title=title,
            description=description,
            data_types=data_types,
            processing_purposes=processing_purposes,
            risk_level=risk_level,
            mitigation_measures=mitigation_measures
        )
        
        self.privacy_assessments[assessment_id] = assessment
        
        # Log the assessment creation
        privacy_logger.log_privacy_event(
            event_type="privacy_impact_assessment_created",
            data={
                "assessment_id": assessment_id,
                "risk_level": risk_level,
                "data_types": data_types
            },
            log_type="audit"
        )
        
        return assessment_id
    
    def get_compliance_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive compliance report.
        
        Returns:
            Compliance report
        """
        return {
            "report_date": datetime.now().isoformat(),
            "data_subject_requests": {
                "total": len(self.data_subject_requests),
                "by_type": self._count_requests_by_type(),
                "by_status": self._count_requests_by_status(),
                "pending_overdue": self._count_overdue_requests()
            },
            "privacy_assessments": {
                "total": len(self.privacy_assessments),
                "by_risk_level": self._count_assessments_by_risk()
            },
            "consent_management": {
                "total_users_with_consent": len(privacy_logger.user_consents),
                "consent_types_distribution": self._get_consent_distribution()
            },
            "data_processing_activities": len(self.data_processing_activities)
        }
    
    def _get_user_business_profiles(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Get user's business profile data."""
        # This would integrate with your business profile service
        return {"message": "Business profile data would be retrieved here"}
    
    def _get_user_preferences(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Get user's preferences and settings."""
        # This would integrate with your user preferences service
        return {"message": "User preferences would be retrieved here"}
    
    def _get_user_consent_records(self, user_id: str) -> Dict[str, Any]:
        """Get user's consent records."""
        if user_id in privacy_logger.user_consents:
            consent = privacy_logger.user_consents[user_id]
            return {
                "consent_types": [ct.value for ct in consent.consent_types],
                "granted_at": consent.granted_at.isoformat(),
                "expires_at": consent.expires_at.isoformat() if consent.expires_at else None,
                "consent_version": consent.consent_version
            }
        return {"message": "No consent records found"}
    
    def _delete_user_business_profiles(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Delete user's business profile data."""
        # This would integrate with your business profile service
        return {"deleted_profiles": 0, "message": "Business profile deletion would be implemented here"}
    
    def _delete_user_preferences(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Delete user's preferences."""
        # This would integrate with your user preferences service
        return {"deleted_preferences": 0, "message": "User preferences deletion would be implemented here"}
    
    def _anonymize_user_data(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Anonymize user data that cannot be deleted."""
        # This would implement anonymization logic
        return {"anonymized_records": 0, "message": "Data anonymization would be implemented here"}
    
    def _export_conversation_data(self, user_id: str) -> Dict[str, Any]:
        """Export user's conversation data in portable format."""
        return privacy_logger.get_user_data_summary(user_id)
    
    def _export_business_profile_data(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Export user's business profile data."""
        return self._get_user_business_profiles(user_id, db)
    
    def _export_user_preferences(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Export user's preferences."""
        return self._get_user_preferences(user_id, db)
    
    def _get_mitigation_measures(self, risk_level: str, data_types: List[str]) -> List[str]:
        """Get appropriate mitigation measures based on risk level."""
        base_measures = ["Data encryption", "Access controls", "Audit logging"]
        
        if risk_level == "high":
            base_measures.extend([
                "Additional encryption layers",
                "Enhanced access controls",
                "Regular security audits",
                "Data minimization",
                "Pseudonymization"
            ])
        elif risk_level == "medium":
            base_measures.extend([
                "Regular access reviews",
                "Data retention policies"
            ])
        
        return base_measures
    
    def _count_requests_by_type(self) -> Dict[str, int]:
        """Count requests by type."""
        counts = {}
        for request in self.data_subject_requests.values():
            counts[request.request_type.value] = counts.get(request.request_type.value, 0) + 1
        return counts
    
    def _count_requests_by_status(self) -> Dict[str, int]:
        """Count requests by status."""
        counts = {}
        for request in self.data_subject_requests.values():
            counts[request.status.value] = counts.get(request.status.value, 0) + 1
        return counts
    
    def _count_overdue_requests(self) -> int:
        """Count overdue requests."""
        return sum(1 for request in self.data_subject_requests.values() if request.is_expired())
    
    def _count_assessments_by_risk(self) -> Dict[str, int]:
        """Count assessments by risk level."""
        counts = {}
        for assessment in self.privacy_assessments.values():
            counts[assessment.risk_level] = counts.get(assessment.risk_level, 0) + 1
        return counts
    
    def _get_consent_distribution(self) -> Dict[str, int]:
        """Get distribution of consent types."""
        distribution = {}
        for consent in privacy_logger.user_consents.values():
            for consent_type in consent.consent_types:
                distribution[consent_type.value] = distribution.get(consent_type.value, 0) + 1
        return distribution


# Global compliance service instance
compliance_service = GDPRCCPAComplianceService()
