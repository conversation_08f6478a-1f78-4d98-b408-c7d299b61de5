"""
Advanced Multi-Agent Collaboration System for LangGraph.

This module provides sophisticated collaboration patterns including:
- Dynamic team formation based on task requirements
- Skill-based agent matching and selection
- Collaborative learning and knowledge sharing
- Consensus building mechanisms for complex decisions
- Performance-driven collaboration optimization

Key Components:
- TeamFormationEngine: Dynamic team assembly
- SkillMatcher: Agent capability matching
- CollaborativeLearning: Knowledge sharing system
- ConsensusBuilder: Decision-making coordination
- CollaborationAnalytics: Performance tracking
"""

from .advanced_patterns import (
    TeamFormationEngine,
    SkillMatcher,
    CollaborativeLearning,
    ConsensusBuilder,
    CollaborationAnalytics,
    AgentSkill,
    TeamConfiguration,
    CollaborationMetrics
)

__version__ = "1.0.0"

__all__ = [
    "TeamFormationEngine",
    "SkillMatcher", 
    "CollaborativeLearning",
    "ConsensusBuilder",
    "CollaborationAnalytics",
    "AgentSkill",
    "TeamConfiguration",
    "CollaborationMetrics"
]
