"""
Test suite for Phase 4 optimization implementations.

This module tests the performance optimizations, async operations,
and memory management features implemented in Phase 4.
"""

import asyncio
import time
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the optimized components
try:
    from agents.langgraph.core.agent_discovery import AgentDiscoveryService
    from agents.langgraph.core.agent_factory import AgentNodeFactory
    from agents.langgraph.states.unified_state import (
        StateMemoryManager,
        optimize_state_transition,
        get_state_memory_stats,
        create_initial_state
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("Running basic tests without full imports...")


class TestAgentDiscoveryOptimization:
    """Test the optimized agent discovery system."""
    
    @pytest.fixture
    def discovery_service(self):
        """Create a discovery service for testing."""
        return AgentDiscoveryService()
    
    def test_indexed_discovery_performance(self, discovery_service):
        """Test that indexed discovery is faster than linear search."""
        # This test would require a larger agent registry to see meaningful differences
        # For now, we test that the indexed methods exist and work
        
        # Test that indexed lookup methods exist
        assert hasattr(discovery_service, '_get_agents_by_capability_indexed')
        assert hasattr(discovery_service, '_get_available_agents_indexed')
        assert hasattr(discovery_service, '_rebuild_indexes')
        
        # Test cache statistics
        stats = discovery_service.get_cache_stats()
        assert 'cache_size' in stats
        assert 'hit_rate_percent' in stats
        assert 'index_last_updated' in stats
    
    def test_cache_management(self, discovery_service):
        """Test cache size management and cleanup."""
        # Clear cache first
        discovery_service.clear_cache()
        
        # Verify cache is empty
        stats = discovery_service.get_cache_stats()
        assert stats['cache_size'] == 0
        assert stats['cache_hits'] == 0
        assert stats['cache_misses'] == 0
        
        # Test cache statistics tracking
        assert stats['hit_rate_percent'] == 0


class TestAsyncOptimizations:
    """Test async operation optimizations."""
    
    @pytest.fixture
    def agent_factory(self):
        """Create an agent factory for testing."""
        return AgentNodeFactory()
    
    @pytest.mark.asyncio
    async def test_async_initialization(self, agent_factory):
        """Test that async initialization works."""
        # Test that async_initialize method exists
        assert hasattr(agent_factory, 'async_initialize')
        
        # Test async initialization (this will load personas if available)
        start_time = time.time()
        await agent_factory.async_initialize()
        end_time = time.time()
        
        # Should complete within reasonable time
        assert end_time - start_time < 10.0  # 10 seconds max
    
    @pytest.mark.asyncio
    async def test_concurrent_file_loading(self, agent_factory):
        """Test that file loading methods exist for concurrent processing."""
        # Test that async loading methods exist
        assert hasattr(agent_factory, '_load_personas_async')
        assert hasattr(agent_factory, '_load_single_persona_async')
        assert hasattr(agent_factory, '_read_yaml_file')


class TestMemoryOptimizations:
    """Test memory usage optimizations."""
    
    @pytest.fixture
    def memory_manager(self):
        """Create a memory manager for testing."""
        return StateMemoryManager(max_history_size=100, cleanup_threshold_mb=10)
    
    @pytest.fixture
    def test_state(self):
        """Create a test state with some data."""
        state = create_initial_state()
        
        # Add some test data
        for i in range(150):  # More than max_history_size
            state["agent_history"].append(f"agent_{i}")
            state["conversation_history"].append({
                "id": f"msg_{i}",
                "content": f"Test message {i}",
                "timestamp": datetime.now().isoformat()
            })
        
        return state
    
    def test_memory_manager_initialization(self, memory_manager):
        """Test memory manager initialization."""
        assert memory_manager.max_history_size == 100
        assert memory_manager.cleanup_threshold_mb == 10
        assert memory_manager._cleanup_count == 0
    
    def test_memory_usage_tracking(self, memory_manager):
        """Test memory usage statistics."""
        stats = memory_manager.get_memory_usage()
        
        assert 'total_memory_mb' in stats
        assert 'active_states' in stats
        assert 'cleanup_count' in stats
        assert 'gc_stats' in stats
    
    def test_state_memory_optimization(self, memory_manager, test_state):
        """Test state memory optimization."""
        # Verify state has excess data
        assert len(test_state["agent_history"]) > memory_manager.max_history_size
        assert len(test_state["conversation_history"]) > memory_manager.max_history_size
        
        # Optimize the state
        optimized_state = memory_manager.optimize_state_memory(test_state)
        
        # Verify optimization worked
        assert len(optimized_state["agent_history"]) <= memory_manager.max_history_size
        assert len(optimized_state["conversation_history"]) <= memory_manager.max_history_size
        assert memory_manager._cleanup_count > 0
    
    def test_optimized_state_transition(self, test_state):
        """Test optimized state transition function."""
        # Test the optimized transition function
        optimized_state = optimize_state_transition(
            test_state, 
            "new_agent", 
            optimize_memory=True
        )
        
        # Verify transition worked
        assert optimized_state["current_agent"] == "new_agent"
        assert "new_agent" in optimized_state["active_agents"]
        
        # Verify memory optimization was applied
        assert len(optimized_state["agent_history"]) <= 1000  # Default max
    
    def test_state_memory_stats(self, test_state):
        """Test state memory statistics."""
        stats = get_state_memory_stats(test_state)
        
        assert 'state_size_bytes' in stats
        assert 'state_size_mb' in stats
        assert 'conversation_history_size' in stats
        assert 'agent_history_size' in stats
        assert 'memory_manager_stats' in stats
        
        # Verify stats are reasonable
        assert stats['state_size_bytes'] > 0
        assert stats['conversation_history_size'] == len(test_state["conversation_history"])


class TestIntegrationOptimizations:
    """Test integration of all optimizations."""
    
    @pytest.mark.asyncio
    async def test_full_optimization_workflow(self):
        """Test a complete workflow with all optimizations enabled."""
        # Initialize components
        discovery_service = AgentDiscoveryService()
        factory = AgentNodeFactory()
        memory_manager = StateMemoryManager()
        
        # Test async initialization
        await factory.async_initialize()
        
        # Test state creation and optimization
        state = create_initial_state()
        
        # Add some data to test memory optimization
        for i in range(50):
            state["conversation_history"].append({
                "id": f"msg_{i}",
                "content": f"Test message {i}",
                "timestamp": datetime.now().isoformat()
            })
        
        # Test optimized state transition
        optimized_state = optimize_state_transition(state, "test_agent")
        
        # Verify everything worked
        assert optimized_state["current_agent"] == "test_agent"
        
        # Test memory statistics
        stats = get_state_memory_stats(optimized_state)
        assert stats['state_size_bytes'] > 0
        
        # Test discovery cache
        cache_stats = discovery_service.get_cache_stats()
        assert 'cache_size' in cache_stats


if __name__ == "__main__":
    # Run basic tests
    print("Running Phase 4 optimization tests...")
    
    # Test memory manager
    manager = StateMemoryManager()
    print(f"Memory manager initialized: {manager.get_memory_usage()}")
    
    # Test state optimization
    state = create_initial_state()
    stats = get_state_memory_stats(state)
    print(f"Initial state memory: {stats['state_size_mb']:.2f} MB")
    
    print("Phase 4 optimization tests completed successfully!")
