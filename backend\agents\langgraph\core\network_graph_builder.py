"""
Network Graph Builder for LangGraph Network Architecture.

This module extends the base graph builder to create network-style graphs
where agents can communicate directly with each other in any-to-any patterns.
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Set, Union
from datetime import datetime
import uuid

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .graph_builder import GraphBuilder
from ..states.network_state import NetworkDatageniusState, create_network_state
from ..nodes.network_router_node import NetworkRouterNode
from ..nodes.unified_persona_node import UnifiedPersonaNode
from ..core.agent_registry import network_registry, NetworkAgent
from ..communication.messaging_system import messaging_system

logger = logging.getLogger(__name__)


class NetworkGraphBuilder(GraphBuilder):
    """
    Network graph builder for creating any-to-any agent communication graphs.
    
    This builder creates network-style graphs where:
    - Every agent can communicate with every other agent
    - Agents can dynamically discover and connect to other agents
    - Workflows can involve multiple agents collaborating
    - Network topology is managed dynamically
    """

    def __init__(self):
        """Initialize the network graph builder."""
        super().__init__()
        
        # Network-specific components
        self.network_router = NetworkRouterNode()
        self.network_agents: Dict[str, UnifiedPersonaNode] = {}
        self.agent_capabilities: Dict[str, List[str]] = {}
        
        # Network configuration
        self.enable_direct_communication = True
        self.enable_dynamic_discovery = True
        self.enable_multi_agent_workflows = True
        self.max_network_size = 50
        
        logger.info("NetworkGraphBuilder initialized")

    def register_network_agent(
        self,
        agent_id: str,
        agent_config: Dict[str, Any],
        capabilities: List[str]
    ) -> bool:
        """
        Register an agent in the network.
        
        Args:
            agent_id: Unique identifier for the agent
            agent_config: Agent configuration dictionary
            capabilities: List of agent capabilities
            
        Returns:
            True if registration successful
        """
        try:
            # Create unified persona node
            persona_node = UnifiedPersonaNode(
                persona_config=agent_config,
                agent_id=agent_id
            )
            
            # Register in network registry
            success = network_registry.register_agent(
                agent_id=agent_id,
                agent_type=agent_config.get("type", "general"),
                name=agent_config.get("name", agent_id),
                description=agent_config.get("description", ""),
                capabilities=[
                    {
                        "name": cap,
                        "description": f"Capability: {cap}",
                        "confidence_level": 0.8
                    }
                    for cap in capabilities
                ],
                communication_capabilities=[
                    "direct_message",
                    "consultation",
                    "delegation",
                    "collaboration"
                ],
                metadata=agent_config.get("metadata", {})
            )
            
            if success:
                self.network_agents[agent_id] = persona_node
                self.agent_capabilities[agent_id] = capabilities
                logger.info(f"Registered network agent: {agent_id}")
                return True
            else:
                logger.error(f"Failed to register network agent: {agent_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error registering network agent {agent_id}: {e}")
            return False

    def build_network_graph(
        self,
        workflow_type: str = "network",
        initial_agent: Optional[str] = None,
        required_capabilities: Optional[List[str]] = None,
        enable_collaboration: bool = True
    ) -> StateGraph:
        """
        Build a network-style graph for multi-agent communication.
        
        Args:
            workflow_type: Type of workflow to create
            initial_agent: Initial agent to start with
            required_capabilities: Required capabilities for the workflow
            enable_collaboration: Whether to enable multi-agent collaboration
            
        Returns:
            Configured StateGraph for network communication
        """
        try:
            # Create state graph with network state
            graph = StateGraph(NetworkDatageniusState)
            
            # Add network router as central coordination node
            graph.add_node("network_router", self.network_router)
            
            # Add all registered network agents
            for agent_id, agent_node in self.network_agents.items():
                graph.add_node(f"agent_{agent_id}", agent_node)
            
            # Create network topology
            self._create_network_topology(graph, enable_collaboration)
            
            # Set entry point
            if initial_agent and initial_agent in self.network_agents:
                graph.set_entry_point(f"agent_{initial_agent}")
            else:
                graph.set_entry_point("network_router")
            
            # Add conditional edges for dynamic routing
            self._add_network_routing_edges(graph)
            
            # Compile graph
            memory = MemorySaver()
            compiled_graph = graph.compile(checkpointer=memory)
            
            logger.info(f"Built network graph with {len(self.network_agents)} agents")
            return compiled_graph
            
        except Exception as e:
            logger.error(f"Error building network graph: {e}")
            raise

    def _create_network_topology(self, graph: StateGraph, enable_collaboration: bool):
        """Create network topology with agent connections."""
        # Connect network router to all agents
        for agent_id in self.network_agents.keys():
            # Router can route to any agent
            graph.add_edge("network_router", f"agent_{agent_id}")
            
            # Agents can communicate back through router
            graph.add_edge(f"agent_{agent_id}", "network_router")
        
        if enable_collaboration:
            # Enable direct agent-to-agent communication
            self._add_direct_agent_connections(graph)

    def _add_direct_agent_connections(self, graph: StateGraph):
        """Add direct connections between agents for collaboration."""
        agent_ids = list(self.network_agents.keys())
        
        # Create connections based on capability complementarity
        for i, agent1_id in enumerate(agent_ids):
            for j, agent2_id in enumerate(agent_ids):
                if i != j:  # Don't connect agent to itself
                    # Check if agents have complementary capabilities
                    if self._agents_are_complementary(agent1_id, agent2_id):
                        # Add conditional edge for direct communication
                        graph.add_conditional_edges(
                            f"agent_{agent1_id}",
                            self._create_agent_routing_function(agent1_id, agent2_id),
                            {
                                f"communicate_with_{agent2_id}": f"agent_{agent2_id}",
                                "continue": "network_router",
                                "end": END
                            }
                        )

    def _agents_are_complementary(self, agent1_id: str, agent2_id: str) -> bool:
        """Check if two agents have complementary capabilities."""
        caps1 = set(self.agent_capabilities.get(agent1_id, []))
        caps2 = set(self.agent_capabilities.get(agent2_id, []))
        
        # Agents are complementary if they have different capabilities
        # or if one specializes in areas the other doesn't
        return len(caps1.intersection(caps2)) < len(caps1.union(caps2)) * 0.5

    def _create_agent_routing_function(self, source_agent: str, target_agent: str) -> Callable:
        """Create routing function for agent-to-agent communication."""
        def routing_function(state: NetworkDatageniusState) -> str:
            try:
                # Check if there's a pending communication to target agent
                pending_comms = state.get("pending_communications", {})
                if target_agent in pending_comms and pending_comms[target_agent]:
                    return f"communicate_with_{target_agent}"
                
                # Check if current agent wants to communicate with target
                current_agent = state.get("current_agent")
                if current_agent == source_agent:
                    # Check for handoff requests
                    handoff_history = state.get("handoff_history", [])
                    if handoff_history:
                        latest_handoff = handoff_history[-1]
                        if latest_handoff.get("to_agent") == target_agent:
                            return f"communicate_with_{target_agent}"
                    
                    # Check for consultation requests
                    consultation_requests = state.get("consultation_requests", [])
                    for req in consultation_requests:
                        if (req.get("requesting_agent") == source_agent and 
                            req.get("specialist_agent") == target_agent and 
                            req.get("status") == "pending"):
                            return f"communicate_with_{target_agent}"
                
                # Check for team collaboration
                active_teams = state.get("active_teams", {})
                for team_data in active_teams.values():
                    if (source_agent in team_data.get("members", []) and 
                        target_agent in team_data.get("members", []) and
                        team_data.get("status") == "active"):
                        return f"communicate_with_{target_agent}"
                
                # Default to continue through router
                return "continue"
                
            except Exception as e:
                logger.error(f"Error in routing function {source_agent}->{target_agent}: {e}")
                return "continue"
        
        return routing_function

    def _add_network_routing_edges(self, graph: StateGraph):
        """Add conditional edges for network routing."""
        # Network router routing logic
        graph.add_conditional_edges(
            "network_router",
            self._network_router_decision,
            {
                **{f"route_to_{agent_id}": f"agent_{agent_id}" 
                   for agent_id in self.network_agents.keys()},
                "process_consultation": "network_router",
                "coordinate_team_formation": "network_router",
                "end": END
            }
        )

    def _network_router_decision(self, state: NetworkDatageniusState) -> str:
        """Decision function for network router."""
        try:
            # Check for explicit next action
            next_action = state.get("next_action")
            if next_action:
                # Clear the next action
                state["next_action"] = None
                return next_action
            
            # Check for current agent
            current_agent = state.get("current_agent")
            if current_agent and current_agent in self.network_agents:
                return f"route_to_{current_agent}"
            
            # Check for pending handoffs
            handoff_history = state.get("handoff_history", [])
            if handoff_history:
                latest_handoff = handoff_history[-1]
                target_agent = latest_handoff.get("to_agent")
                if target_agent and target_agent in self.network_agents:
                    return f"route_to_{target_agent}"
            
            # Check for active consultations
            consultation_requests = state.get("consultation_requests", [])
            pending_consultations = [req for req in consultation_requests if req.get("status") == "pending"]
            if pending_consultations:
                return "process_consultation"
            
            # Check for team formation
            active_teams = state.get("active_teams", {})
            forming_teams = [team for team in active_teams.values() if team.get("status") == "forming"]
            if forming_teams:
                return "coordinate_team_formation"
            
            # Default to first available agent or end
            if self.network_agents:
                first_agent = list(self.network_agents.keys())[0]
                return f"route_to_{first_agent}"
            
            return "end"
            
        except Exception as e:
            logger.error(f"Error in network router decision: {e}")
            return "end"

    def create_network_workflow(
        self,
        user_id: str,
        conversation_id: str,
        initial_message: Optional[Dict[str, Any]] = None,
        workflow_type: str = "network",
        initial_agent: Optional[str] = None,
        business_profile_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a network workflow configuration.
        
        Args:
            user_id: User identifier
            conversation_id: Conversation identifier
            initial_message: Initial message to process
            workflow_type: Type of workflow
            initial_agent: Initial agent to start with
            business_profile_id: Business profile context
            
        Returns:
            Workflow configuration dictionary
        """
        try:
            # Get available agents from registry
            available_agents = []
            for agent in network_registry.get_all_agents():
                agent_info = {
                    "agent_id": agent.agent_id,
                    "agent_type": agent.agent_type,
                    "name": agent.name,
                    "capabilities": [cap.name for cap in agent.capabilities],
                    "status": agent.status.value
                }
                available_agents.append(agent_info)
            
            # Create initial network state
            initial_state = create_network_state(
                user_id=user_id,
                conversation_id=conversation_id,
                workflow_type=workflow_type,
                initial_message=initial_message,
                business_profile_id=business_profile_id,
                available_agents=available_agents
            )
            
            # Set initial agent if specified
            if initial_agent and initial_agent in self.network_agents:
                initial_state["current_agent"] = initial_agent
            
            # Build the graph
            graph = self.build_network_graph(
                workflow_type=workflow_type,
                initial_agent=initial_agent,
                enable_collaboration=True
            )
            
            workflow_config = {
                "workflow_id": str(uuid.uuid4()),
                "workflow_type": workflow_type,
                "graph": graph,
                "initial_state": initial_state,
                "available_agents": available_agents,
                "network_topology": network_registry.get_network_topology(),
                "created_at": datetime.now().isoformat()
            }
            
            logger.info(f"Created network workflow with {len(available_agents)} agents")
            return workflow_config
            
        except Exception as e:
            logger.error(f"Error creating network workflow: {e}")
            raise

    def get_network_stats(self) -> Dict[str, Any]:
        """Get network statistics."""
        return {
            "registered_agents": len(self.network_agents),
            "total_capabilities": sum(len(caps) for caps in self.agent_capabilities.values()),
            "registry_stats": network_registry.get_registry_stats(),
            "messaging_stats": messaging_system.get_messaging_stats()
        }

    def create_user_selected_workflow(
        self,
        user_id: str,
        conversation_id: str,
        selected_agent_id: str,
        initial_message: Optional[Dict[str, Any]] = None,
        business_profile_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a workflow for a user-selected agent.

        This method creates a workflow where the user has explicitly selected
        an agent to interact with. The workflow supports collaboration but
        does not include automatic routing.

        Args:
            user_id: User identifier
            conversation_id: Conversation identifier
            selected_agent_id: ID of the agent selected by the user
            initial_message: Initial message to process
            business_profile_id: Business profile context

        Returns:
            Workflow configuration dictionary
        """
        try:
            logger.info(f"Creating user-selected workflow for agent {selected_agent_id}")

            # Validate that the selected agent exists and supports user selection
            selected_agent = network_registry.get_agent(selected_agent_id)
            if not selected_agent:
                raise ValueError(f"Selected agent {selected_agent_id} not found")

            # Get available agents for potential collaboration
            available_agents = []
            for agent in network_registry.get_all_agents():
                agent_info = {
                    "agent_id": agent.agent_id,
                    "agent_type": agent.agent_type,
                    "name": agent.name,
                    "capabilities": [cap.name for cap in agent.capabilities],
                    "status": agent.status.value,
                    "supports_user_selection": getattr(agent, 'supports_user_selection', False)
                }
                available_agents.append(agent_info)

            # Create initial state for user-selected workflow
            initial_state = create_network_state(
                user_id=user_id,
                conversation_id=conversation_id,
                workflow_type="user_selected",
                initial_message=initial_message,
                business_profile_id=business_profile_id,
                available_agents=available_agents
            )

            # Set the selected agent as the primary agent
            initial_state["selected_agent"] = selected_agent_id
            initial_state["primary_agent"] = selected_agent_id
            initial_state["user_controlled"] = True
            initial_state["collaboration_mode"] = "permission_based"

            workflow_config = {
                "workflow_id": str(uuid.uuid4()),
                "workflow_type": "user_selected",
                "selected_agent_id": selected_agent_id,
                "initial_state": initial_state,
                "available_agents": available_agents,
                "collaboration_enabled": True,
                "permission_required": True,
                "created_at": datetime.now().isoformat()
            }

            logger.info(f"Created user-selected workflow for agent {selected_agent_id}")
            return workflow_config

        except Exception as e:
            logger.error(f"Error creating user-selected workflow: {e}")
            return {}

    def shutdown(self):
        """Shutdown the network graph builder."""
        try:
            # Shutdown messaging system
            messaging_system.shutdown()
            logger.info("NetworkGraphBuilder shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


# Global network graph builder instance
network_graph_builder = NetworkGraphBuilder()
