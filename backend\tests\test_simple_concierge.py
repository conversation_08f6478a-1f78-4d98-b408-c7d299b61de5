#!/usr/bin/env python3
"""
Test script to verify the simple concierge agent works.
"""

import sys
import os
import asyncio
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_simple_concierge():
    """Test the simple concierge agent."""
    try:
        logger.info("🧪 Testing simple concierge agent...")
        
        # Test 1: Create simple concierge agent directly
        logger.info("🤖 Test 1: Creating simple concierge agent...")
        from agents.langgraph.agents.simple_concierge_agent import SimpleConciergeAgent
        
        agent = SimpleConciergeAgent({'agent_id': 'concierge'})
        logger.info("✅ Simple concierge agent created successfully")
        
        # Test 2: Test basic message processing
        logger.info("💬 Test 2: Testing basic message processing...")
        response = await agent.process_message(
            message="Hello, what tools are available?",
            user_id="test_user",
            conversation_id="test_conv",
            context={
                "conversation_history": [],
                "business_profile": {},
                "is_initial_greeting": False
            }
        )
        
        logger.info(f"✅ Response received: {response.get('message', '')[:100]}...")
        logger.info(f"✅ Metadata: {response.get('metadata', {})}")
        
        # Check if it's NOT using fallback mode
        metadata = response.get('metadata', {})
        if metadata.get('fallback_mode'):
            logger.warning("⚠️ Agent is still using fallback mode")
        else:
            logger.info("✅ Agent is NOT in fallback mode")
        
        # Test 3: Test greeting generation
        logger.info("👋 Test 3: Testing greeting generation...")
        greeting_response = await agent.process_message(
            message="generate_greeting",
            user_id="test_user",
            conversation_id="test_conv",
            context={
                "is_initial_greeting": True,
                "user_name": "Test User"
            }
        )
        
        logger.info(f"✅ Greeting response: {greeting_response.get('message', '')[:100]}...")
        
        # Test 4: Test agent factory integration
        logger.info("🏭 Test 4: Testing agent factory integration...")
        from agents.langgraph.core.agent_factory import agent_factory
        
        # Load registry (method name might be different)
        try:
            if hasattr(agent_factory, 'load_agent_registry'):
                agent_factory.load_agent_registry()
            elif hasattr(agent_factory, 'load_registry'):
                agent_factory.load_registry()
            logger.info("✅ Agent registry loaded")
        except Exception as e:
            logger.warning(f"Registry loading method not found: {e}")
            logger.info("✅ Proceeding without explicit registry loading")
        
        # Create concierge node
        concierge_node = agent_factory.create_agent_node('concierge')
        if concierge_node and concierge_node.agent_instance:
            logger.info("✅ Concierge node created with agent instance")
            
            # Check if it's the right type
            agent_type = type(concierge_node.agent_instance).__name__
            logger.info(f"✅ Agent instance type: {agent_type}")
            
            if "SimpleConciergeAgent" in agent_type:
                logger.info("✅ Using SimpleConciergeAgent (correct!)")
            elif "Fallback" in agent_type:
                logger.warning("⚠️ Still using fallback agent")
            else:
                logger.info(f"✅ Using agent type: {agent_type}")
                
        else:
            logger.error("❌ Failed to create concierge node or agent instance")
        
        logger.info("🎉 All tests completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_simple_concierge())
    if success:
        print("\n✅ Simple concierge agent is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Simple concierge agent has issues!")
        sys.exit(1)
