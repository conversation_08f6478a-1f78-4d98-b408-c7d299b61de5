# Environment Variables Template for Secure Secret Injection
# Copy this file to .env and fill in the actual values

# JWT Security
JWT_SECRET_KEY=<GENERATE_STRONG_SECRET_64_CHARS>
JWT_SECRET_KEY_DEVELOPMENT=<GENERATE_STRONG_SECRET_64_CHARS>
JWT_SECRET_KEY_STAGING=<GENERATE_STRONG_SECRET_64_CHARS>
JWT_SECRET_KEY_PRODUCTION=<GENERATE_STRONG_SECRET_64_CHARS>

# Database
DATABASE_URL=<DATABASE_CONNECTION_STRING>

# Redis
REDIS_URL=<REDIS_CONNECTION_STRING>

# Environment
ENVIRONMENT=<development|staging|production>

# Optional Secrets
WEBHOOK_SECRET_KEY=<WEBHOOK_SECRET>
SMTP_PASSWORD=<SMTP_PASSWORD>
GOOGLE_CLIENT_SECRET=<GOOGLE_CLIENT_SECRET>
ENCRYPTION_KEY=<ENCRYPTION_KEY>

# Security Configuration
SECRET_MANAGER_KEY=<SECRET_MANAGER_ENCRYPTION_KEY>
DATA_ENCRYPTION_KEY=<DATA_ENCRYPTION_KEY>
FILE_ENCRYPTION_KEY=<FILE_ENCRYPTION_KEY>

# External Service API Keys
OPENAI_API_KEY=<OPENAI_API_KEY>
ANTHROPIC_API_KEY=<ANTHROPIC_API_KEY>
GOOGLE_API_KEY=<GOOGLE_API_KEY>

# Database Credentials (if not using connection string)
DB_HOST=<DATABASE_HOST>
DB_PORT=<DATABASE_PORT>
DB_NAME=<DATABASE_NAME>
DB_USER=<DATABASE_USER>
DB_PASSWORD=<DATABASE_PASSWORD>

# Redis Credentials (if not using connection string)
REDIS_HOST=<REDIS_HOST>
REDIS_PORT=<REDIS_PORT>
REDIS_PASSWORD=<REDIS_PASSWORD>

# Email Configuration
SMTP_HOST=<SMTP_HOST>
SMTP_PORT=<SMTP_PORT>
SMTP_USER=<SMTP_USER>
SMTP_PASSWORD=<SMTP_PASSWORD>
SMTP_USE_TLS=<true|false>

# OAuth Configuration
GOOGLE_CLIENT_ID=<GOOGLE_CLIENT_ID>
GOOGLE_CLIENT_SECRET=<GOOGLE_CLIENT_SECRET>
GITHUB_CLIENT_ID=<GITHUB_CLIENT_ID>
GITHUB_CLIENT_SECRET=<GITHUB_CLIENT_SECRET>

# Webhook Configuration
WEBHOOK_SECRET_KEY=<WEBHOOK_SECRET_KEY>
WEBHOOK_URL=<WEBHOOK_URL>

# Monitoring and Logging
LOG_LEVEL=<DEBUG|INFO|WARNING|ERROR>
SENTRY_DSN=<SENTRY_DSN>
MONITORING_API_KEY=<MONITORING_API_KEY>

# Security Settings
CORS_ALLOWED_ORIGINS=<COMMA_SEPARATED_ORIGINS>
RATE_LIMIT_ENABLED=<true|false>
RATE_LIMIT_REQUESTS_PER_MINUTE=<NUMBER>

# Development Settings (only for development environment)
DEBUG=<true|false>
DEVELOPMENT_MODE=<true|false>
ENABLE_DEBUG_TOOLBAR=<true|false>

# Testing Settings (only for testing environment)
TEST_DATABASE_URL=<TEST_DATABASE_CONNECTION_STRING>
TEST_REDIS_URL=<TEST_REDIS_CONNECTION_STRING>
TESTING_MODE=<true|false>

# Production Settings (only for production environment)
PRODUCTION_MODE=<true|false>
ENABLE_HTTPS_REDIRECT=<true|false>
SECURE_COOKIES=<true|false>
SESSION_COOKIE_SECURE=<true|false>
CSRF_COOKIE_SECURE=<true|false>

# Backup and Recovery
BACKUP_ENCRYPTION_KEY=<BACKUP_ENCRYPTION_KEY>
BACKUP_STORAGE_URL=<BACKUP_STORAGE_URL>
BACKUP_RETENTION_DAYS=<NUMBER>

# Performance Settings
CACHE_TTL_SECONDS=<NUMBER>
MAX_CONNECTIONS=<NUMBER>
CONNECTION_TIMEOUT_SECONDS=<NUMBER>
QUERY_TIMEOUT_SECONDS=<NUMBER>

# Feature Flags
ENABLE_ADVANCED_SECURITY=<true|false>
ENABLE_PLUGIN_SANDBOXING=<true|false>
ENABLE_SECRET_ROTATION=<true|false>
ENABLE_AUDIT_LOGGING=<true|false>

# Instructions for generating secure secrets:
# 1. Use a cryptographically secure random generator
# 2. Minimum 32 characters for JWT secrets
# 3. Include uppercase, lowercase, numbers, and special characters
# 4. Avoid dictionary words and common patterns
# 5. Use different secrets for each environment
# 6. Never commit actual secrets to version control
# 7. Rotate secrets regularly according to security policy

# Example commands for generating secure secrets:
# Linux/Mac: openssl rand -base64 32
# Python: python -c "import secrets; print(secrets.token_urlsafe(32))"
# Node.js: node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
