"""
LangGraph WebSocket Manager for Real-time Communication.

This module provides WebSocket functionality that integrates with
LangGraph workflows for real-time messaging and progress updates.
"""

import logging
import json
import asyncio
from typing import Dict, List, Set, Optional, Any
from datetime import datetime
import uuid

from fastapi import WebSocket, WebSocketDisconnect, HTTPException
from sqlalchemy.orm import Session

from ..core.workflow_manager import WorkflowManager
from ..core.agent_factory import agent_factory
from ....app.database import get_db, create_message, get_conversation, update_conversation
from ....app.auth import get_current_user_from_token
from ....app.utils.json_utils import sanitize_metadata

logger = logging.getLogger(__name__)


class ConnectionManager:
    """
    Manages WebSocket connections for LangGraph workflows.
    
    This manager handles connection lifecycle, message routing,
    and real-time workflow progress updates.
    """
    
    def __init__(self):
        """Initialize the connection manager."""
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[int, Set[str]] = {}
        self.conversation_connections: Dict[str, Set[str]] = {}
        self.workflow_connections: Dict[str, Set[str]] = {}
        self.logger = logging.getLogger(__name__)
    
    async def connect(
        self, 
        websocket: WebSocket, 
        connection_id: str, 
        user_id: int, 
        conversation_id: str
    ) -> None:
        """
        Accept a new WebSocket connection.
        
        Args:
            websocket: WebSocket connection
            connection_id: Unique connection identifier
            user_id: User identifier
            conversation_id: Conversation identifier
        """
        await websocket.accept()
        
        # Store connection
        self.active_connections[connection_id] = websocket
        
        # Track user connections
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(connection_id)
        
        # Track conversation connections
        if conversation_id not in self.conversation_connections:
            self.conversation_connections[conversation_id] = set()
        self.conversation_connections[conversation_id].add(connection_id)
        
        self.logger.info(f"WebSocket connected: {connection_id} for user {user_id}, conversation {conversation_id}")
    
    def disconnect(self, connection_id: str, user_id: int, conversation_id: str) -> None:
        """
        Remove a WebSocket connection.
        
        Args:
            connection_id: Connection identifier
            user_id: User identifier
            conversation_id: Conversation identifier
        """
        # Remove from active connections
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        # Remove from user connections
        if user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        # Remove from conversation connections
        if conversation_id in self.conversation_connections:
            self.conversation_connections[conversation_id].discard(connection_id)
            if not self.conversation_connections[conversation_id]:
                del self.conversation_connections[conversation_id]
        
        # Remove from workflow connections
        for workflow_id in list(self.workflow_connections.keys()):
            self.workflow_connections[workflow_id].discard(connection_id)
            if not self.workflow_connections[workflow_id]:
                del self.workflow_connections[workflow_id]
        
        self.logger.info(f"WebSocket disconnected: {connection_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], connection_id: str) -> None:
        """
        Send a message to a specific connection.
        
        Args:
            message: Message to send
            connection_id: Target connection identifier
        """
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                self.logger.error(f"Error sending message to {connection_id}: {e}")
                # Remove broken connection
                if connection_id in self.active_connections:
                    del self.active_connections[connection_id]
    
    async def broadcast_to_conversation(self, message: Dict[str, Any], conversation_id: str) -> None:
        """
        Broadcast a message to all connections in a conversation.
        
        Args:
            message: Message to broadcast
            conversation_id: Conversation identifier
        """
        if conversation_id in self.conversation_connections:
            connections = self.conversation_connections[conversation_id].copy()
            for connection_id in connections:
                await self.send_personal_message(message, connection_id)
    
    async def broadcast_workflow_update(self, update: Dict[str, Any], workflow_id: str) -> None:
        """
        Broadcast workflow progress update to relevant connections.
        
        Args:
            update: Update message
            workflow_id: Workflow identifier
        """
        if workflow_id in self.workflow_connections:
            connections = self.workflow_connections[workflow_id].copy()
            for connection_id in connections:
                await self.send_personal_message(update, connection_id)
    
    def subscribe_to_workflow(self, connection_id: str, workflow_id: str) -> None:
        """
        Subscribe a connection to workflow updates.
        
        Args:
            connection_id: Connection identifier
            workflow_id: Workflow identifier
        """
        if workflow_id not in self.workflow_connections:
            self.workflow_connections[workflow_id] = set()
        self.workflow_connections[workflow_id].add(connection_id)
    
    async def send_typing_indicator(self, conversation_id: str, is_typing: bool) -> None:
        """
        Send typing indicator to conversation.
        
        Args:
            conversation_id: Conversation identifier
            is_typing: Whether AI is typing
        """
        message = {
            "type": "typing_indicator",
            "is_typing": is_typing,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_conversation(message, conversation_id)


class LangGraphWebSocketManager:
    """
    WebSocket manager that integrates with LangGraph workflows.
    
    This manager provides real-time communication capabilities
    for LangGraph-based agent interactions.
    """
    
    def __init__(self):
        """Initialize the LangGraph WebSocket manager."""
        self.connection_manager = ConnectionManager()
        self.workflow_manager = WorkflowManager()
        self.logger = logging.getLogger(__name__)
        
        # Initialize workflow manager
        self._initialize_workflow_manager()
    
    def _initialize_workflow_manager(self) -> None:
        """Initialize workflow manager with agents."""
        try:
            agents = agent_factory.create_all_agents()
            for agent_id, agent_node in agents.items():
                self.workflow_manager.register_agent_node(agent_id, agent_node)
            
            self.logger.info("WebSocket workflow manager initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing WebSocket workflow manager: {e}")
    
    async def handle_websocket_connection(
        self,
        websocket: WebSocket,
        conversation_id: str,
        token: str,
        db: Session
    ) -> None:
        """
        Handle a WebSocket connection for real-time chat.
        
        Args:
            websocket: WebSocket connection
            conversation_id: Conversation identifier
            token: Authentication token
            db: Database session
        """
        connection_id = str(uuid.uuid4())
        current_user = None
        
        try:
            # Authenticate user
            current_user = await get_current_user_from_token(token, db)
            if not current_user:
                await websocket.close(code=4001, reason="Authentication failed")
                return
            
            # Validate conversation access
            conversation = get_conversation(db, conversation_id)
            if not conversation or conversation.user_id != current_user.id:
                await websocket.close(code=4003, reason="Access denied")
                return
            
            # Accept connection
            await self.connection_manager.connect(
                websocket, connection_id, current_user.id, conversation_id
            )
            
            # Send connection confirmation
            await self.connection_manager.send_personal_message({
                "type": "connection_established",
                "connection_id": connection_id,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat()
            }, connection_id)
            
            # Handle messages
            while True:
                try:
                    # Receive message
                    data = await websocket.receive_text()
                    message_data = json.loads(data)
                    
                    # Process message
                    await self._process_websocket_message(
                        message_data, connection_id, current_user, conversation_id, db
                    )
                    
                except WebSocketDisconnect:
                    break
                except json.JSONDecodeError:
                    await self.connection_manager.send_personal_message({
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.now().isoformat()
                    }, connection_id)
                except Exception as e:
                    self.logger.error(f"Error processing WebSocket message: {e}")
                    await self.connection_manager.send_personal_message({
                        "type": "error",
                        "message": "Error processing message",
                        "timestamp": datetime.now().isoformat()
                    }, connection_id)
        
        except Exception as e:
            self.logger.error(f"WebSocket connection error: {e}")
        
        finally:
            # Clean up connection
            if current_user:
                self.connection_manager.disconnect(connection_id, current_user.id, conversation_id)
    
    async def _process_websocket_message(
        self,
        message_data: Dict[str, Any],
        connection_id: str,
        current_user: Any,
        conversation_id: str,
        db: Session
    ) -> None:
        """
        Process a WebSocket message through LangGraph workflow.
        
        Args:
            message_data: Message data from client
            connection_id: Connection identifier
            current_user: Current user
            conversation_id: Conversation identifier
            db: Database session
        """
        try:
            message_type = message_data.get("type", "message")
            
            if message_type == "message":
                await self._handle_chat_message(
                    message_data, connection_id, current_user, conversation_id, db
                )
            elif message_type == "workflow_subscribe":
                workflow_id = message_data.get("workflow_id")
                if workflow_id:
                    self.connection_manager.subscribe_to_workflow(connection_id, workflow_id)
            elif message_type == "ping":
                await self.connection_manager.send_personal_message({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }, connection_id)
            else:
                await self.connection_manager.send_personal_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}",
                    "timestamp": datetime.now().isoformat()
                }, connection_id)
        
        except Exception as e:
            self.logger.error(f"Error processing WebSocket message: {e}")
            await self.connection_manager.send_personal_message({
                "type": "error",
                "message": "Error processing message",
                "timestamp": datetime.now().isoformat()
            }, connection_id)
    
    async def _handle_chat_message(
        self,
        message_data: Dict[str, Any],
        connection_id: str,
        current_user: Any,
        conversation_id: str,
        db: Session
    ) -> None:
        """
        Handle a chat message through LangGraph workflow.
        
        Args:
            message_data: Message data
            connection_id: Connection identifier
            current_user: Current user
            conversation_id: Conversation identifier
            db: Database session
        """
        try:
            message_content = message_data.get("message", "")
            if not message_content.strip():
                return
            
            # Send typing indicator
            await self.connection_manager.send_typing_indicator(conversation_id, True)
            
            # Create user message in database
            user_message_id = str(uuid.uuid4())
            user_db_message = create_message(
                db=db,
                message_id=user_message_id,
                conversation_id=conversation_id,
                sender="user",
                content=message_content,
                metadata=message_data.get("metadata", {})
            )
            
            # Broadcast user message
            await self.connection_manager.broadcast_to_conversation({
                "type": "user_message",
                "message": {
                    "id": user_db_message.id,
                    "content": user_db_message.content,
                    "sender": "user",
                    "timestamp": user_db_message.created_at.isoformat()
                }
            }, conversation_id)
            
            # Create and execute workflow
            workflow_id = await self.workflow_manager.create_workflow(
                user_id=current_user.id,
                conversation_id=conversation_id,
                message=message_content,
                context=message_data.get("context", {}),
                workflow_type=message_data.get("workflow_type", "default")
            )
            
            # Subscribe connection to workflow updates
            self.connection_manager.subscribe_to_workflow(connection_id, workflow_id)
            
            # Execute workflow with streaming
            await self._stream_workflow_execution(workflow_id, conversation_id, db)
            
        except Exception as e:
            self.logger.error(f"Error handling chat message: {e}")
            await self.connection_manager.send_typing_indicator(conversation_id, False)
            await self.connection_manager.broadcast_to_conversation({
                "type": "error",
                "message": "Error processing your message",
                "timestamp": datetime.now().isoformat()
            }, conversation_id)
    
    async def _stream_workflow_execution(
        self,
        workflow_id: str,
        conversation_id: str,
        db: Session
    ) -> None:
        """
        Stream workflow execution progress via WebSocket.
        
        Args:
            workflow_id: Workflow identifier
            conversation_id: Conversation identifier
            db: Database session
        """
        try:
            # Send workflow started notification
            await self.connection_manager.broadcast_workflow_update({
                "type": "workflow_started",
                "workflow_id": workflow_id,
                "timestamp": datetime.now().isoformat()
            }, workflow_id)
            
            # Execute workflow
            result = await self.workflow_manager.execute_workflow(workflow_id)
            
            # Turn off typing indicator
            await self.connection_manager.send_typing_indicator(conversation_id, False)
            
            # Process result
            if result.get("status") == "completed":
                result_data = result.get("result", {})
                messages = result_data.get("messages", [])
                
                # Find AI response
                ai_response = "I apologize, but I couldn't generate a response."
                for message in messages:
                    if message.get("type") == "agent":
                        ai_response = message.get("content", ai_response)
                        break
                
                # Create AI message in database
                ai_message_id = str(uuid.uuid4())
                ai_metadata = {
                    "workflow_id": workflow_id,
                    "agent_id": result_data.get("current_agent"),
                    "execution_time": result.get("execution_time")
                }
                
                ai_db_message = create_message(
                    db=db,
                    message_id=ai_message_id,
                    conversation_id=conversation_id,
                    sender="ai",
                    content=ai_response,
                    metadata=sanitize_metadata(ai_metadata)
                )
                
                # Broadcast AI response
                await self.connection_manager.broadcast_to_conversation({
                    "type": "ai_message",
                    "message": {
                        "id": ai_db_message.id,
                        "content": ai_db_message.content,
                        "sender": "ai",
                        "timestamp": ai_db_message.created_at.isoformat(),
                        "metadata": ai_metadata
                    }
                }, conversation_id)
                
                # Update conversation timestamp
                update_conversation(db, conversation_id)
            
            else:
                # Handle workflow failure
                await self.connection_manager.broadcast_to_conversation({
                    "type": "error",
                    "message": "I encountered an issue processing your request.",
                    "timestamp": datetime.now().isoformat()
                }, conversation_id)
            
            # Send workflow completed notification
            await self.connection_manager.broadcast_workflow_update({
                "type": "workflow_completed",
                "workflow_id": workflow_id,
                "status": result.get("status"),
                "timestamp": datetime.now().isoformat()
            }, workflow_id)
            
        except Exception as e:
            self.logger.error(f"Error streaming workflow execution: {e}")
            await self.connection_manager.send_typing_indicator(conversation_id, False)
            await self.connection_manager.broadcast_to_conversation({
                "type": "error",
                "message": "Error processing your request",
                "timestamp": datetime.now().isoformat()
            }, conversation_id)


# Global WebSocket manager instance
websocket_manager = LangGraphWebSocketManager()
