{"default_permissions": ["read_data", "process_data", "generate_response", "access_tools"], "blocked_imports": ["os", "sys", "subprocess", "eval", "exec", "compile", "__import__", "importlib", "pickle", "marshal", "shelve", "dill", "socket", "urllib", "requests", "http", "ftplib", "smtplib", "telnetlib", "webbrowser", "ctypes", "multiprocessing", "threading", "asyncio.subprocess", "shutil", "tempfile", "glob", "pathlib.Path.write_text", "pathlib.Path.write_bytes", "builtins.open", "io.open"], "max_execution_time": 30, "max_memory_mb": 256, "require_approval": true, "audit_all_operations": true, "sandbox_configuration": {"enable_network_isolation": true, "enable_filesystem_isolation": true, "enable_process_isolation": true, "allowed_network_hosts": [], "allowed_file_paths": ["/tmp/plugin_workspace", "/var/log/plugin_audit"], "blocked_file_paths": ["/etc", "/usr", "/bin", "/sbin", "/root", "/home", "/var/lib", "/var/run"]}, "permission_levels": {"basic": {"description": "Basic data processing permissions", "permissions": ["read_data", "process_data", "generate_response"], "max_execution_time": 15, "max_memory_mb": 128}, "standard": {"description": "Standard plugin permissions with tool access", "permissions": ["read_data", "process_data", "generate_response", "access_tools"], "max_execution_time": 30, "max_memory_mb": 256}, "advanced": {"description": "Advanced permissions with limited file access", "permissions": ["read_data", "process_data", "generate_response", "access_tools", "limited_file_access"], "max_execution_time": 60, "max_memory_mb": 512, "require_manual_approval": true}, "admin": {"description": "Administrative permissions - requires special approval", "permissions": ["read_data", "process_data", "generate_response", "access_tools", "file_access", "admin_access"], "max_execution_time": 120, "max_memory_mb": 1024, "require_manual_approval": true, "require_security_review": true}}, "security_policies": {"input_validation": {"enabled": true, "max_input_size_mb": 10, "allowed_input_types": ["text/plain", "application/json", "text/csv", "application/xml"], "blocked_patterns": ["<script", "javascript:", "data:text/html", "eval(", "exec(", "__import__"]}, "output_sanitization": {"enabled": true, "remove_sensitive_data": true, "sanitize_html": true, "sanitize_javascript": true, "max_output_size_mb": 50}, "rate_limiting": {"enabled": true, "requests_per_minute": 60, "burst_size": 10, "cooldown_seconds": 5}, "audit_logging": {"enabled": true, "log_all_operations": true, "log_input_data": false, "log_output_data": false, "log_security_events": true, "log_permission_checks": true, "retention_days": 90}}}