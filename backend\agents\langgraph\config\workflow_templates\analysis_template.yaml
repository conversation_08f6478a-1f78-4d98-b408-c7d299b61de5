# Analysis Agent Workflow Template
# Optimized for data analysis, visualization, and statistical workflows

template_id: "analysis_workflow"
template_name: "Analysis Agent Workflow"
version: "1.0.0"
description: "Comprehensive workflow template for analysis agent with data processing and visualization capabilities"
agent_type: "analysis"

# Template metadata
metadata:
  author: "Datagenius Team"
  created_date: "2024-01-26"
  last_modified: "2024-01-26"
  tags: ["analysis", "data", "visualization", "statistics", "complex"]
  complexity: "high"
  estimated_duration: "30-120 seconds"

# Workflow configuration
workflow_config:
  # Execution settings
  max_execution_steps: 10  # More steps for complex analysis
  execution_timeout: 120   # 2 minutes for data processing
  enable_parallel_execution: true  # Enable parallel tool execution
  
  # Loop prevention
  max_agent_executions: 5  # Allow more iterations for analysis
  min_execution_interval: 2.0  # 2 seconds between executions
  enable_infinite_loop_detection: true
  
  # Termination conditions
  auto_terminate_on_response: false  # May need multiple iterations
  require_explicit_continuation: true
  enable_workflow_completion_signals: true

# Node configuration
nodes:
  # Entry point - analysis agent
  entry:
    type: "agent"
    agent_id: "analysis"
    config:
      max_retries: 2
      timeout: 60
      enable_fallback: true
      enable_data_validation: true
    
  # Data preprocessing node
  data_preprocessing:
    type: "tool_execution"
    tools: ["data_validator", "data_cleaner", "format_converter"]
    config:
      parallel_execution: true
      timeout: 30
      required_for_analysis: true
    
  # Analysis execution node
  analysis_execution:
    type: "tool_execution"
    tools: ["statistical_analyzer", "data_analyzer", "pattern_detector"]
    config:
      parallel_execution: false  # Sequential for accuracy
      timeout: 45
      enable_caching: true
    
  # Visualization node
  visualization:
    type: "tool_execution"
    tools: ["chart_generator", "dashboard_creator", "report_builder"]
    config:
      parallel_execution: true
      timeout: 30
      optional: true
    
  # Quality assurance node
  quality_check:
    type: "validation"
    config:
      validate_results: true
      check_data_integrity: true
      verify_calculations: true
    
  # Routing node
  routing:
    type: "routing"
    config:
      enable_intelligent_routing: true
      enable_collaboration: true
      max_routing_attempts: 3

# Edge configuration (routing patterns)
edges:
  # Start -> Analysis Agent
  - from: "START"
    to: "analysis"
    condition: "always"
    priority: 1
    
  # Analysis -> Data Preprocessing
  - from: "analysis"
    to: "data_preprocessing"
    condition: "data_available AND requires_preprocessing"
    priority: 1
    
  # Analysis -> Analysis Execution (direct)
  - from: "analysis"
    to: "analysis_execution"
    condition: "data_ready AND NOT requires_preprocessing"
    priority: 1
    
  # Data Preprocessing -> Analysis Execution
  - from: "data_preprocessing"
    to: "analysis_execution"
    condition: "preprocessing_complete"
    priority: 1
    
  # Analysis Execution -> Quality Check
  - from: "analysis_execution"
    to: "quality_check"
    condition: "analysis_complete"
    priority: 1
    
  # Quality Check -> Visualization
  - from: "quality_check"
    to: "visualization"
    condition: "quality_passed AND visualization_requested"
    priority: 1
    
  # Quality Check -> Analysis Agent (for response)
  - from: "quality_check"
    to: "analysis"
    condition: "quality_passed AND NOT visualization_requested"
    priority: 1
    
  # Visualization -> Analysis Agent (for final response)
  - from: "visualization"
    to: "analysis"
    condition: "visualization_complete"
    priority: 1
    
  # Analysis Agent -> END
  - from: "analysis"
    to: "END"
    condition: "workflow_complete OR final_response_generated"
    priority: 1
    
  # Error handling edges
  - from: "quality_check"
    to: "analysis_execution"
    condition: "quality_failed AND retry_count < max_retries"
    priority: 2
    
  # Emergency termination
  - from: "*"
    to: "END"
    condition: "critical_error OR timeout OR max_executions_reached"
    priority: 3

# Termination conditions
termination_conditions:
  # Successful completion
  - condition: "analysis_complete AND quality_passed AND response_generated"
    action: "END"
    priority: 1
    
  # Workflow completion signal
  - condition: "workflow_complete"
    action: "END"
    priority: 1
    
  # Data-related termination
  - condition: "no_data_available OR data_invalid"
    action: "END"
    priority: 2
    
  # Error conditions
  - condition: "critical_analysis_error OR data_corruption"
    action: "END"
    priority: 2
    
  # Resource limits
  - condition: "memory_limit_exceeded OR processing_timeout"
    action: "END"
    priority: 3
    
  # Loop prevention
  - condition: "infinite_loop_detected OR max_iterations_reached"
    action: "END"
    priority: 3

# State management
state_management:
  # Required state fields
  required_fields:
    - "user_id"
    - "conversation_id"
    - "messages"
    - "data_context"
    
  # Analysis-specific state
  analysis_state:
    - "data_sources"
    - "analysis_type"
    - "processing_status"
    - "results"
    - "visualizations"
    
  # State validation rules
  validation_rules:
    - field: "data_context"
      rule: "not_empty"
      error_action: "request_data"
      
    - field: "analysis_type"
      rule: "valid_analysis_type"
      error_action: "default_to_descriptive"
  
  # State persistence
  persist_intermediate_results: true
  enable_state_checkpoints: true
  cleanup_on_completion: false  # Keep results for reference

# Performance optimization
performance:
  # Caching
  enable_result_caching: true
  cache_duration: 1800  # 30 minutes
  cache_intermediate_results: true
  
  # Resource limits
  memory_limit_mb: 500  # Higher for data processing
  cpu_time_limit_seconds: 90
  max_data_size_mb: 100
  
  # Parallel processing
  max_parallel_tools: 3
  enable_async_processing: true
  
  # Monitoring
  enable_performance_tracking: true
  track_processing_times: true
  track_memory_usage: true
  track_data_throughput: true

# Error handling
error_handling:
  # Retry configuration
  max_retries: 3
  retry_delay_seconds: 2.0
  exponential_backoff: true
  
  # Fallback strategies
  fallback_strategies:
    - trigger: "data_processing_error"
      action: "use_simplified_analysis"
      
    - trigger: "visualization_error"
      action: "return_text_results"
      
    - trigger: "memory_error"
      action: "reduce_data_size"
      
    - trigger: "timeout"
      action: "return_partial_results"
  
  # Error responses
  data_error_response: "I encountered an issue with the data. Please check the data format and try again."
  processing_error_response: "The analysis couldn't be completed due to processing constraints. Try with a smaller dataset."
  timeout_response: "The analysis is taking longer than expected. I'll provide partial results."

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable: true
    required: true
    use_industry_context: true
    
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable: true
    coordinate_with: ["marketing", "visualization"]
    
  # Data sources
  data_sources:
    enable: true
    supported_formats: ["csv", "xlsx", "json", "parquet"]
    max_file_size: "100MB"
    
  # MCP tools
  mcp_tools:
    enable: true
    max_tools: 5
    timeout: 45

# Monitoring and metrics
monitoring:
  # Metrics to collect
  metrics:
    - "analysis_accuracy"
    - "processing_time"
    - "data_quality_score"
    - "visualization_success_rate"
    - "user_satisfaction"
    - "resource_utilization"
    
  # Performance thresholds
  thresholds:
    processing_time: 90.0
    memory_usage: 400.0
    error_rate: 0.05
    
  # Alerts
  alerts:
    - metric: "processing_time"
      threshold: 90.0
      action: "optimize_processing"
      
    - metric: "error_rate"
      threshold: 0.05
      action: "investigate_errors"

# Template validation
validation:
  # Required components
  required_components:
    - "analysis_agent"
    - "data_processing_tools"
    - "quality_validation"
    
  # Optional components
  optional_components:
    - "visualization_tools"
    - "statistical_tools"
    - "machine_learning_tools"
    
  # Validation rules
  rules:
    - rule: "max_execution_steps >= 5"
      error: "Analysis workflows need sufficient steps for complex processing"
      
    - rule: "execution_timeout >= 60"
      error: "Analysis workflows need adequate time for data processing"
