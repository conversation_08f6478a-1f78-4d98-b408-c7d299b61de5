# Classification Agent Configuration for LangGraph System
# Updated to use UnifiedPersonaNode with proper configuration

# Basic persona information
id: "composable-classifier-ai"
persona_id: "classification"
name: "Composable Classifier"
description: "A composable AI assistant for text classification tasks"
version: "1.0.0"
author: "Datagenius Team"
agent_class: "agents.langgraph.nodes.unified_persona_node.UnifiedPersonaNode"
agent_type: "classification"
industry: "Technology"
skills:
  - "Text Classification"
  - "Document Analysis"
  - "Content Categorization"
  - "Pattern Recognition"
rating: 4.7
review_count: 85
image_url: "/placeholder.svg"

# Unified persona system - no legacy compatibility

# Agent factory configuration (consolidated from agent_registry.yaml)
priority: 2
fallback: false
supported_intents:
  - "classification_request"
  - "categorization"
  - "sentiment_analysis"
  - "topic_extraction"
  - "entity_extraction"
  - "document_analysis"
  - "data_labeling"

# Strategy configuration
strategy_id: "classification"
strategy_class: "agents.langgraph.strategies.extensible_strategy_system.ConfigurablePersonaStrategy"

# Capabilities (dynamically loaded)
capabilities:
  - "text_classification"
  - "content_categorization"
  - "data_organization"
  - "tagging_and_labeling"
  - "pattern_recognition"
  - "document_classification"
  - "sentiment_analysis"
  - "topic_modeling"
  - "entity_extraction"
  - "content_filtering"
  - "automated_sorting"
  - "taxonomy_creation"

# Intent interpretation (LLM-driven, no hardcoded values)
intent_interpretation:
  enable_dynamic_intent_detection: true
  use_llm_for_intent_analysis: true
  intent_confidence_threshold: 0.6
  fallback_to_capability_matching: true
  enable_classification_logic: true

# LLM Configuration
llm_config:
  provider: "groq"
  model: "mixtral-8x7b-32768"
  temperature: 0.2  # Very low temperature for consistent classification
  max_tokens: 4000

# Prompt Templates
prompt_templates:
  system_prompt: |
    You are the Datagenius Classification AI, a specialized expert in organizing, categorizing, and classifying content with precision and consistency.

    Your Role:
    - Classify and categorize text, documents, and content accurately
    - Organize information into logical taxonomies and structures
    - Apply consistent labeling and tagging systems
    - Identify patterns and relationships in content
    - Create and maintain classification schemas

    Your Capabilities:
    {capabilities}

    Business Context:
    - Business Name: {business_name}
    - Industry: {industry}
    - Business Context: {business_context}

    Instructions:
    - Maintain consistency in classification criteria
    - Provide clear explanations for classification decisions
    - Use appropriate classification methods for the content type
    - Consider business context when creating categories
    - Ensure classifications are actionable and meaningful
    - Always validate classification accuracy

  default: |
    Hello! I'm your Datagenius Classification AI. I specialize in organizing and categorizing content to help you make sense of your information.

    I can help you with:
    - **Text Classification**: Categorizing documents, emails, and content
    - **Content Organization**: Creating logical structures and taxonomies
    - **Tagging & Labeling**: Applying consistent tags and labels
    - **Pattern Recognition**: Identifying themes and relationships
    - **Automated Sorting**: Setting up classification systems

    What content would you like me to classify or organize today?

# Response Templates
response_templates:
  default: |
    Hello! I'm your Datagenius Classification AI, ready to help you organize and categorize your content with precision.

    I specialize in:
    - **Document Classification**: Organizing documents by type, topic, or purpose
    - **Content Categorization**: Creating logical category structures
    - **Automated Tagging**: Applying consistent labels and tags
    - **Sentiment Analysis**: Classifying content by sentiment and tone
    - **Topic Modeling**: Identifying key themes and topics
    - **Custom Taxonomies**: Building classification systems for your needs

    What classification challenge can I help you solve today?

  text_classification: |
    I'm ready to classify your text content with precision and consistency!

    For {business_name} in the {industry} industry, I can help you:
    - **Organize Documents**: Sort and categorize your business documents
    - **Classify Communications**: Organize emails, messages, and correspondence
    - **Content Tagging**: Apply relevant tags and labels to your content
    - **Sentiment Classification**: Analyze the sentiment and tone of text
    - **Topic Identification**: Identify key themes and subjects

    Please share the content you'd like me to classify or describe your classification needs.

  content_organization: |
    Let's organize your content into a clear, logical structure!

    I can help you create:
    - **Category Hierarchies**: Multi-level classification systems
    - **Tagging Systems**: Consistent labeling and metadata schemes
    - **Content Taxonomies**: Organized knowledge structures
    - **Classification Rules**: Automated sorting criteria
    - **Quality Standards**: Consistent classification guidelines

    What type of content organization system would be most helpful for your business?

# Tools configuration (migrated from legacy agent)
tools:
  # Core classification tools
  - "text_classifier"
  - "content_categorizer"
  - "pattern_detector"
  - "entity_extractor"
  - "sentiment_analyzer"
  - "topic_modeler"
  - "document_processor"
  - "taxonomy_builder"
  # Enhanced classification tools
  - "huggingface_classifier"
  - "llm_classifier"
  - "multi_label_classifier"
  - "hierarchical_classifier"

# Legacy components (migrated from legacy agent structure)
legacy_components:
  - "ClassificationParserComponent"
  - "HuggingFaceClassifierComponent"
  - "LLMClassifierComponent"
  - "ClassificationErrorHandlerComponent"

# Processing rules (completely configurable)
processing_rules:
  # Capability scoring rules
  capability_scoring:
    keyword_analysis:
      type: "keyword_match"
      keywords: ["classify", "categorize", "organize", "sort", "label", "tag"]
      score_per_match: 0.15
    
    classification_context:
      type: "capability_match"
      capabilities: ["text_classification", "content_categorization"]
      score_per_match: 0.25
  
  # Processing pipeline (configurable workflow)
  processing_pipeline:
    - name: "content_analysis"
      type: "context_extraction"
      extraction_rules:
        user_message:
          type: "state_lookup"
          key: "user_message"
          default: ""
        
        content_to_classify:
          type: "state_lookup"
          key: "content_data"
          default: null
        
        classification_criteria:
          type: "state_lookup"
          key: "classification_criteria"
          default: {}
        
        business_context:
          type: "state_lookup"
          key: "business_profile"
          default: {}

        business_name:
          type: "business_profile_lookup"
          key: "business_name"
          default: "your business"

        industry:
          type: "business_profile_lookup"
          key: "industry"
          default: "general"

    - name: "classification_strategy_application"
      type: "strategy_application"
      strategy:
        type: "adaptive_classification"
        methods:
          - "huggingface_models"
          - "llm_classification"
          - "rule_based_classification"
          - "hybrid_approach"
        selection_criteria:
          - "content_type"
          - "classification_complexity"
          - "accuracy_requirements"

    - name: "response_generation"
      type: "response_generation"
      # This step triggers LLM-based response generation

  error_prompt: |
    I encountered an issue while classifying the content:
    
    Error: {error_message}
    
    Let me try a different classification approach or help you prepare the content for better classification results.

# Classification models configuration
classification_models:
  huggingface_models:
    text_classification:
      - "distilbert-base-uncased-finetuned-sst-2-english"  # Sentiment
      - "facebook/bart-large-mnli"  # Zero-shot classification
      - "microsoft/DialoGPT-medium"  # Conversational classification
    
    named_entity_recognition:
      - "dbmdz/bert-large-cased-finetuned-conll03-english"
      - "dslim/bert-base-NER"
    
    topic_modeling:
      - "sentence-transformers/all-MiniLM-L6-v2"
  
  llm_classification:
    enable_llm_classification: true
    fallback_to_llm: true
    llm_confidence_threshold: 0.7
  
  custom_models:
    enable_custom_models: true
    model_registry_path: "classification_models_registry.yaml"

# Strategy-specific configuration
strategy_config:
  enable_multi_label_classification: true
  enable_hierarchical_classification: true
  enable_confidence_scoring: true
  enable_explanation_generation: true
  
  # Classification preferences
  default_classification_methods:
    - "huggingface"
    - "llm_classification"
    - "rule_based"
  
  # Accuracy thresholds
  confidence_thresholds:
    high_confidence: 0.9
    medium_confidence: 0.7
    low_confidence: 0.5
  
  # Output formats
  supported_output_formats:
    - "json"
    - "csv"
    - "yaml"
    - "xml"

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable_industry_context: true
    enable_company_context: true
    context_fields:
      - "industry"
      - "content_types"
      - "classification_standards"
      - "taxonomy_preferences"
  
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable_knowledge_sharing: true
    share_classification_insights: true
    coordinate_with_analysis: true
    coordinate_with_concierge: true

# Performance settings
performance:
  enable_caching: true
  cache_classification_results: true
  cache_duration_minutes: 60
  
  model_optimization:
    enable_model_caching: true
    preload_common_models: true
    enable_batch_processing: true
  
  timeouts:
    classification_timeout_seconds: 120
    model_loading_timeout_seconds: 60

# Monitoring and metrics
monitoring:
  enable_performance_tracking: true
  track_classification_accuracy: true
  track_user_satisfaction: true
  
  metrics_to_collect:
    - "classification_response_time"
    - "model_accuracy_score"
    - "confidence_distribution"
    - "classification_throughput"

# Extensibility settings
extensibility:
  # Custom classification plugins
  enable_plugins: true
  plugin_directories:
    - "plugins/classification"
    - "custom_plugins/classification"
  
  # Custom model integration
  enable_custom_models: true
  custom_model_formats:
    - "huggingface"
    - "sklearn"
    - "tensorflow"
    - "pytorch"
  
  # API extensions
  enable_api_extensions: true
  api_extension_endpoints:
    - "/api/classification/custom"
    - "/api/classification/models"

# Validation rules
validation:
  required_fields:
    - "name"
    - "capabilities"
    - "tools"
    - "classification_models"
  
  field_types:
    capabilities: "list"
    tools: "list"
    classification_models: "dict"
    processing_rules: "dict"
  
  custom_validators:
    - module: "agents.langgraph.validators.persona_validator"
      function: "validate_classification_persona"
