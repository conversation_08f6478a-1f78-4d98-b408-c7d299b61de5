{"enabled": true, "rotation_interval_days": 90, "auto_rotate_jwt_secrets": true, "notify_on_rotation": true, "backup_old_secrets": true, "validate_new_secrets": true, "secrets_to_rotate": ["JWT_SECRET_KEY", "WEBHOOK_SECRET_KEY", "ENCRYPTION_KEY"], "rotation_schedules": {"JWT_SECRET_KEY": {"interval_days": 90, "auto_rotate": true, "environments": ["development", "staging", "production"], "notification_days_before": 7, "backup_old_key": true, "validate_new_key": true}, "WEBHOOK_SECRET_KEY": {"interval_days": 60, "auto_rotate": true, "environments": ["staging", "production"], "notification_days_before": 5, "backup_old_key": true, "validate_new_key": true}, "ENCRYPTION_KEY": {"interval_days": 365, "auto_rotate": false, "environments": ["production"], "notification_days_before": 30, "backup_old_key": true, "validate_new_key": true, "require_manual_approval": true}, "DATABASE_PASSWORD": {"interval_days": 180, "auto_rotate": false, "environments": ["staging", "production"], "notification_days_before": 14, "backup_old_key": false, "validate_new_key": true, "require_manual_approval": true, "coordinate_with_database": true}, "API_KEYS": {"interval_days": 120, "auto_rotate": false, "environments": ["staging", "production"], "notification_days_before": 10, "backup_old_key": true, "validate_new_key": true, "require_manual_approval": true, "coordinate_with_external_services": true}}, "rotation_policies": {"automatic_rotation": {"enabled": true, "max_auto_rotations_per_day": 3, "require_health_check": true, "rollback_on_failure": true, "notification_required": true}, "manual_rotation": {"enabled": true, "require_approval": true, "require_reason": true, "audit_all_rotations": true, "validate_before_rotation": true}, "emergency_rotation": {"enabled": true, "bypass_approval": true, "immediate_notification": true, "audit_emergency_rotations": true, "require_post_rotation_review": true}}, "notification_settings": {"enabled": true, "channels": ["log_file", "system_notification"], "notification_types": {"rotation_scheduled": {"enabled": true, "advance_notice_days": 7}, "rotation_completed": {"enabled": true, "include_summary": true}, "rotation_failed": {"enabled": true, "immediate_alert": true}, "rotation_overdue": {"enabled": true, "escalate_after_days": 3}}}, "backup_settings": {"enabled": true, "backup_location": "backend/security/secret_backups", "encrypt_backups": true, "backup_retention_days": 365, "max_backup_versions": 5, "verify_backup_integrity": true}, "validation_settings": {"enabled": true, "validate_strength": true, "validate_uniqueness": true, "validate_format": true, "test_new_secrets": true, "rollback_on_validation_failure": true}, "security_measures": {"encrypt_rotation_logs": true, "audit_all_rotations": true, "require_secure_channels": true, "validate_rotation_permissions": true, "monitor_rotation_patterns": true}, "environment_specific_policies": {"development": {"rotation_enabled": true, "auto_rotation_enabled": true, "notification_required": false, "backup_required": false, "validation_level": "basic"}, "staging": {"rotation_enabled": true, "auto_rotation_enabled": true, "notification_required": true, "backup_required": true, "validation_level": "standard"}, "production": {"rotation_enabled": true, "auto_rotation_enabled": false, "notification_required": true, "backup_required": true, "validation_level": "strict", "require_manual_approval": true}, "testing": {"rotation_enabled": false, "auto_rotation_enabled": false, "notification_required": false, "backup_required": false, "validation_level": "none"}}, "monitoring": {"enabled": true, "track_rotation_success_rate": true, "track_rotation_duration": true, "alert_on_rotation_failures": true, "alert_on_overdue_rotations": true, "generate_rotation_reports": true}}