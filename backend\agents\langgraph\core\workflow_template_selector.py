"""
Workflow Template Selection System for LangGraph.

This module provides dynamic template selection based on agent capabilities,
conversation context, and workflow requirements with comprehensive fallback mechanisms.
"""

import logging
import yaml
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from datetime import datetime
import re

from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class WorkflowTemplateSelector:
    """
    Dynamic workflow template selection system.
    
    Selects optimal workflow templates based on:
    - Agent type and capabilities
    - Conversation context and complexity
    - User preferences and history
    - System performance metrics
    """
    
    def __init__(self, template_directory: Optional[str] = None):
        """
        Initialize the template selector.
        
        Args:
            template_directory: Path to workflow template configurations
        """
        self.logger = logging.getLogger(__name__)
        
        # Set template directory
        if template_directory:
            self.template_directory = Path(template_directory)
        else:
            # Default to config/workflow_templates
            current_dir = Path(__file__).parent
            self.template_directory = current_dir.parent / "config" / "workflow_templates"
        
        # Template cache
        self.template_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_timestamp: Optional[datetime] = None
        self.cache_ttl_seconds = 300  # 5 minutes
        
        # Selection metrics
        self.selection_metrics = {
            "templates_loaded": 0,
            "selections_made": 0,
            "fallbacks_used": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        # Load templates
        self._load_templates()
        
        self.logger.info(f"WorkflowTemplateSelector initialized with {len(self.template_cache)} templates")
    
    def _load_templates(self) -> None:
        """Load workflow templates from configuration files."""
        try:
            if not self.template_directory.exists():
                self.logger.warning(f"Template directory not found: {self.template_directory}")
                return
            
            template_count = 0
            for template_file in self.template_directory.glob("*.yaml"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        template_config = yaml.safe_load(f)
                    
                    template_id = template_config.get("template_id")
                    if not template_id:
                        self.logger.warning(f"Template file {template_file} missing template_id")
                        continue
                    
                    # Add file metadata
                    template_config["_file_path"] = str(template_file)
                    template_config["_loaded_at"] = datetime.now().isoformat()
                    
                    self.template_cache[template_id] = template_config
                    template_count += 1
                    
                    self.logger.debug(f"Loaded template: {template_id} from {template_file.name}")
                    
                except Exception as e:
                    self.logger.error(f"Error loading template {template_file}: {e}")
            
            self.cache_timestamp = datetime.now()
            self.selection_metrics["templates_loaded"] = template_count
            
            self.logger.info(f"Loaded {template_count} workflow templates")
            
        except Exception as e:
            self.logger.error(f"Error loading templates: {e}")
    
    def select_template(
        self,
        agent_type: str,
        state: UnifiedDatageniusState,
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[Optional[Dict[str, Any]], float]:
        """
        Select the optimal workflow template for the given context.
        
        Args:
            agent_type: Type of agent (concierge, analysis, marketing, classification)
            state: Current workflow state
            context: Additional context information
            
        Returns:
            Tuple of (selected_template, confidence_score)
        """
        try:
            self.selection_metrics["selections_made"] += 1
            
            # Refresh cache if needed
            self._refresh_cache_if_needed()
            
            # Get candidate templates
            candidates = self._get_candidate_templates(agent_type)
            
            if not candidates:
                self.logger.warning(f"No templates found for agent type: {agent_type}")
                return self._get_fallback_template(agent_type), 0.5
            
            # Score candidates
            scored_candidates = []
            for template in candidates:
                score = self._score_template(template, agent_type, state, context)
                scored_candidates.append((template, score))
                self.logger.debug(f"Template {template['template_id']} scored: {score:.3f}")
            
            # Sort by score (highest first)
            scored_candidates.sort(key=lambda x: x[1], reverse=True)
            
            # Select best template
            best_template, best_score = scored_candidates[0]
            
            self.logger.info(f"Selected template '{best_template['template_id']}' with score {best_score:.3f}")
            
            return best_template, best_score
            
        except Exception as e:
            self.logger.error(f"Error selecting template: {e}")
            self.selection_metrics["fallbacks_used"] += 1
            return self._get_fallback_template(agent_type), 0.3
    
    def _get_candidate_templates(self, agent_type: str) -> List[Dict[str, Any]]:
        """Get candidate templates for the given agent type."""
        candidates = []
        
        for template_id, template in self.template_cache.items():
            # Primary match: exact agent type
            if template.get("agent_type") == agent_type:
                candidates.append(template)
                continue
            
            # Secondary match: compatible agent types
            compatible_types = template.get("metadata", {}).get("compatible_agent_types", [])
            if agent_type in compatible_types:
                candidates.append(template)
                continue
            
            # Tertiary match: generic templates
            if template.get("agent_type") == "generic" or template.get("template_id") == "default_workflow":
                candidates.append(template)
        
        return candidates
    
    def _score_template(
        self,
        template: Dict[str, Any],
        agent_type: str,
        state: UnifiedDatageniusState,
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Score a template based on how well it matches the current context.
        
        Returns:
            Score between 0.0 and 1.0 (higher is better)
        """
        score = 0.0
        context = context or {}
        
        # Base score for agent type match
        if template.get("agent_type") == agent_type:
            score += 0.4
        elif agent_type in template.get("metadata", {}).get("compatible_agent_types", []):
            score += 0.2
        
        # Complexity matching
        complexity_score = self._score_complexity_match(template, state, context)
        score += complexity_score * 0.2
        
        # Context relevance
        context_score = self._score_context_relevance(template, state, context)
        score += context_score * 0.2
        
        # Performance considerations
        performance_score = self._score_performance_suitability(template, state, context)
        score += performance_score * 0.1
        
        # User preferences (if available)
        preference_score = self._score_user_preferences(template, state, context)
        score += preference_score * 0.1
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _score_complexity_match(
        self,
        template: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> float:
        """Score how well template complexity matches the task complexity."""
        try:
            template_complexity = template.get("metadata", {}).get("complexity", "medium")
            
            # Determine task complexity from context
            task_complexity = self._determine_task_complexity(state, context)
            
            # Score based on match
            if template_complexity == task_complexity:
                return 1.0
            elif abs(self._complexity_to_number(template_complexity) - self._complexity_to_number(task_complexity)) == 1:
                return 0.7
            else:
                return 0.3
                
        except Exception as e:
            self.logger.debug(f"Error scoring complexity match: {e}")
            return 0.5
    
    def _determine_task_complexity(self, state: UnifiedDatageniusState, context: Dict[str, Any]) -> str:
        """Determine task complexity from state and context."""
        complexity_indicators = {
            "low": ["hello", "hi", "help", "what", "simple", "quick"],
            "medium": ["analyze", "create", "generate", "plan", "strategy"],
            "high": ["comprehensive", "detailed", "complex", "advanced", "multiple", "integrate"]
        }
        
        # Get message content
        messages = state.get("messages", [])
        if messages:
            last_message = messages[-1].get("content", "").lower()
            
            # Count indicators
            scores = {}
            for complexity, indicators in complexity_indicators.items():
                score = sum(1 for indicator in indicators if indicator in last_message)
                scores[complexity] = score
            
            # Return highest scoring complexity
            if scores:
                return max(scores, key=scores.get)
        
        # Check context for complexity hints
        if context.get("data_sources") or context.get("multiple_agents_needed"):
            return "high"
        elif context.get("content_creation") or context.get("analysis_required"):
            return "medium"
        
        return "low"  # Default
    
    def _complexity_to_number(self, complexity: str) -> int:
        """Convert complexity string to number for comparison."""
        mapping = {"low": 1, "medium": 2, "high": 3}
        return mapping.get(complexity, 2)
    
    def _score_context_relevance(
        self,
        template: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> float:
        """Score how relevant the template is to the current context."""
        try:
            template_tags = template.get("metadata", {}).get("tags", [])
            
            # Extract context keywords
            context_keywords = self._extract_context_keywords(state, context)
            
            if not context_keywords or not template_tags:
                return 0.5
            
            # Calculate overlap
            overlap = len(set(template_tags) & set(context_keywords))
            total_tags = len(template_tags)
            
            return overlap / total_tags if total_tags > 0 else 0.5
            
        except Exception as e:
            self.logger.debug(f"Error scoring context relevance: {e}")
            return 0.5
    
    def _extract_context_keywords(self, state: UnifiedDatageniusState, context: Dict[str, Any]) -> List[str]:
        """Extract relevant keywords from state and context."""
        keywords = []
        
        # From messages
        messages = state.get("messages", [])
        if messages:
            last_message = messages[-1].get("content", "").lower()
            # Simple keyword extraction
            words = re.findall(r'\b\w+\b', last_message)
            keywords.extend([w for w in words if len(w) > 3])
        
        # From context
        if context.get("task_type"):
            keywords.append(context["task_type"])
        
        if context.get("business_context", {}).get("industry"):
            keywords.append(context["business_context"]["industry"])
        
        return keywords[:10]  # Limit to top 10
    
    def _score_performance_suitability(
        self,
        template: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> float:
        """Score template based on performance requirements."""
        try:
            # Get template performance characteristics
            template_timeout = template.get("workflow_config", {}).get("execution_timeout", 60)
            template_steps = template.get("workflow_config", {}).get("max_execution_steps", 5)
            
            # Determine performance requirements
            urgency = context.get("urgency", "normal")
            
            # Score based on urgency
            if urgency == "high":
                # Prefer faster templates
                if template_timeout <= 30 and template_steps <= 5:
                    return 1.0
                elif template_timeout <= 60 and template_steps <= 8:
                    return 0.7
                else:
                    return 0.3
            elif urgency == "low":
                # Can use more comprehensive templates
                return 0.8
            else:
                # Normal urgency
                return 0.6
                
        except Exception as e:
            self.logger.debug(f"Error scoring performance suitability: {e}")
            return 0.5
    
    def _score_user_preferences(
        self,
        template: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> float:
        """Score template based on user preferences."""
        try:
            # This could be enhanced with actual user preference data
            user_preferences = context.get("user_preferences", {})
            
            if not user_preferences:
                return 0.5
            
            # Simple preference matching
            preferred_complexity = user_preferences.get("preferred_complexity")
            template_complexity = template.get("metadata", {}).get("complexity")
            
            if preferred_complexity and template_complexity == preferred_complexity:
                return 1.0
            
            return 0.5
            
        except Exception as e:
            self.logger.debug(f"Error scoring user preferences: {e}")
            return 0.5
    
    def _get_fallback_template(self, agent_type: str) -> Dict[str, Any]:
        """Get a fallback template when no suitable template is found."""
        self.logger.info(f"Using fallback template for agent type: {agent_type}")
        
        # Return a minimal template configuration
        return {
            "template_id": f"fallback_{agent_type}",
            "template_name": f"Fallback {agent_type.title()} Template",
            "version": "1.0.0",
            "agent_type": agent_type,
            "workflow_config": {
                "max_execution_steps": 3,
                "execution_timeout": 30,
                "enable_parallel_execution": False,
                "max_agent_executions": 2,
                "min_execution_interval": 1.0,
                "auto_terminate_on_response": True
            },
            "termination_conditions": [
                {
                    "condition": "response_generated OR workflow_complete",
                    "action": "END",
                    "priority": 1
                }
            ],
            "metadata": {
                "complexity": "low",
                "is_fallback": True
            }
        }
    
    def _refresh_cache_if_needed(self) -> None:
        """Refresh template cache if TTL has expired."""
        if (self.cache_timestamp is None or 
            (datetime.now() - self.cache_timestamp).total_seconds() > self.cache_ttl_seconds):
            
            self.logger.debug("Refreshing template cache")
            self._load_templates()
            self.selection_metrics["cache_misses"] += 1
        else:
            self.selection_metrics["cache_hits"] += 1
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """Get list of all available templates."""
        self._refresh_cache_if_needed()
        return list(self.template_cache.values())
    
    def get_template_by_id(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific template by ID."""
        self._refresh_cache_if_needed()
        return self.template_cache.get(template_id)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get selection metrics."""
        return self.selection_metrics.copy()


# Global template selector instance
template_selector = WorkflowTemplateSelector()
