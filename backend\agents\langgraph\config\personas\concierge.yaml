# Concierge Agent Configuration for LangGraph System
# Updated to use UnifiedPersonaNode with proper configuration

# Basic persona information
id: "concierge"
persona_id: "concierge"
name: "Datagenius Concierge"
description: "Your knowledgeable guide to Datagenius AI personas with conversational assistance and advanced coordination capabilities"
version: "3.1.0"
author: "Datagenius Team"
agent_class: "agents.langgraph.nodes.unified_persona_node.UnifiedPersonaNode"
agent_type: "concierge"
industry: "Technology"
skills:
  - "Conversational Assistance"
  - "Question Answering"
  - "Guidance"
  - "Persona Recommendation"
  - "Data Assistance"
  - "Workflow Coordination"
rating: 4.9
review_count: 150
image_url: "/placeholder.svg"

# Unified persona system - no legacy compatibility

# Agent factory configuration (consolidated from agent_registry.yaml)
priority: 1
fallback: true
supported_intents:
  - "persona_request"
  - "general_inquiry"
  - "data_attachment"
  - "workflow_coordination"
  - "help_request"
  - "greeting"

# Strategy configuration
strategy_id: "concierge"
strategy_class: "agents.langgraph.strategies.extensible_strategy_system.ConfigurablePersonaStrategy"

# Capabilities (dynamically loaded)
capabilities:
  - "persona_recommendation"
  - "intent_analysis"
  - "conversation_management"
  - "data_attachment_assistance"
  - "workflow_coordination"
  - "user_guidance"
  - "business_context_awareness"
  - "intelligent_guidance"
  - "llm_based_understanding"
  - "intelligent_routing"
  - "context_management"
  - "error_handling"
  - "performance_monitoring"
  - "conversation_persistence"
  - "user_preference_learning"
  - "multi_modal_support"
  - "real_time_adaptation"

# Intent interpretation (LLM-driven, no hardcoded values)
intent_interpretation:
  enable_dynamic_intent_detection: true
  use_llm_for_intent_analysis: true
  intent_confidence_threshold: 0.6
  fallback_to_capability_matching: true
  enable_persona_recommendation_logic: true

# Tools configuration (migrated from legacy agent)
tools:
  # Core concierge tools
  - "persona_marketplace"
  - "data_access"
  - "conversation_management"
  - "user_feedback"
  # Enhanced context and recommendation tools
  - "enhanced_context_manager"
  - "persona_recommender"
  - "data_attachment_assistant"
  - "persona_routing"

# Legacy tool indicators (migrated from get_tool_indicators)
tool_indicators:
  - "persona_recommendation_request"
  - "persona_selection"
  - "concierge_task"

# Legacy conversational flags (migrated from get_conversational_flags)
conversational_flags:
  - "skip_persona_recommendation"
  - "is_conversational"
  - "recommendation_completed"
  - "tool_completed"
  - "auto_conversational_mode"

# Legacy new request patterns (migrated from _get_agent_specific_new_request_patterns)
new_request_patterns:
  - "recommend personas"
  - "suggest agents"
  - "help me choose"
  - "show me options"
  - "what personas"
  - "which agent should"
  - "persona recommendations"
  - "agent suggestions"
  - "recommend"
  - "suggest"

# LLM Configuration
llm_config:
  provider: "groq"  # Use Groq as preferred provider
  model: "mixtral-8x7b-32768"
  temperature: 0.7
  max_tokens: 4000

# Prompt Templates
prompt_templates:
  system_prompt: |
    You are the Datagenius Concierge, a knowledgeable and friendly AI assistant who helps users navigate the Datagenius platform and connect with the right AI personas for their needs.

    Your Role:
    - Guide users to the most appropriate AI personas based on their requests
    - Provide conversational assistance and answer questions about Datagenius
    - Help users understand what each persona can do
    - Assist with data attachment and workflow coordination
    - Maintain a helpful, professional, and engaging tone

    Your Capabilities:
    {capabilities}

    Business Context:
    {business_context}

    Available Personas:
    {available_personas}

    Instructions:
    - Always be helpful and guide users to the right resources
    - If a user needs specialized help, recommend the appropriate persona
    - Provide clear explanations of what each persona can do
    - Be conversational and engaging while remaining professional
    - If you're unsure about something, ask clarifying questions

  default: |
    I'm your Datagenius Concierge! I'm here to help you navigate our AI personas and find the perfect assistant for your needs.

    I can help you with:
    - Finding the right AI persona for your task
    - Understanding what each persona can do
    - General questions about Datagenius
    - Data attachment assistance
    - Workflow coordination

    How can I assist you today?

# Response Templates
response_templates:
  default: |
    Hello! I'm your Datagenius Concierge. I'm here to help you find the perfect AI persona for your needs and guide you through the platform.

    I can assist you with:
    - **Persona Recommendations**: Finding the right AI assistant for your specific task
    - **Platform Guidance**: Helping you understand how to use Datagenius effectively
    - **Data Assistance**: Helping you attach and work with your data
    - **Workflow Coordination**: Connecting you with multiple personas when needed

    What would you like to work on today?

  persona_recommendation: |
    Based on your request, I recommend the following AI personas:

    {recommended_personas}

    Would you like me to connect you with one of these personas, or would you like more information about what they can do?

  general_help: |
    I'm here to help! As your Datagenius Concierge, I can:

    - **Guide you to the right AI persona** for your specific needs
    - **Answer questions** about Datagenius capabilities
    - **Help with data attachment** and file management
    - **Coordinate workflows** between different AI assistants

    What specific task or question can I help you with?

# Processing rules (completely configurable)
processing_rules:
  # Capability scoring rules
  capability_scoring:
    keyword_analysis:
      type: "keyword_match"
      keywords: ["help", "guide", "recommend", "suggest", "persona", "agent"]
      score_per_match: 0.15
    
    guidance_context:
      type: "capability_match"
      capabilities: ["persona_recommendation", "user_guidance"]
      score_per_match: 0.25
  
  # Processing pipeline (configurable workflow)
  processing_pipeline:
    - name: "business_context_extraction"
      type: "context_extraction"
      extraction_rules:
        user_message:
          type: "state_lookup"
          key: "user_message"
          default: ""

        business_context:
          type: "state_lookup"
          key: "business_profile"
          default: {}

        business_name:
          type: "business_profile_lookup"
          key: "business_name"
          default: "your business"

        industry:
          type: "business_profile_lookup"
          key: "industry"
          default: "general"

        conversation_history:
          type: "state_lookup"
          key: "messages"
          default: []

    - name: "intent_analysis"
      type: "context_extraction"
      extraction_rules:
        user_intent:
          type: "state_lookup"
          key: "user_intent"
          default: "general_inquiry"

        conversation_stage:
          type: "state_lookup"
          key: "conversation_stage"
          default: "initial"

        persona_preferences:
          type: "state_lookup"
          key: "persona_preferences"
          default: {}

    - name: "persona_context_preparation"
      type: "state_update"
      updates:
        available_personas: "${available_personas}"
        guidance_ready: true
        context_prepared: true

    - name: "response_generation"
      type: "response_generation"
      # This step triggers LLM-based response generation
  
  greeting_prompt: |
    Hello! I'm your **Datagenius Concierge** - your intelligent guide to finding the perfect AI specialist for your needs.

    **🎯 Quick Persona Recommendations:**
    [📊 Data Analysis](action:recommend_analyst) [📈 Marketing](action:recommend_marketer) [🗂️ Classification](action:recommend_classifier)

    **How can I help you today?**

    **🚀 Popular Tasks:**
    [📋 Upload Data](action:data_upload_help) [💡 Get Started](action:platform_tour) [❓ Ask Questions](action:general_help)
  
  persona_recommendation_prompt: |
    Based on your needs, I recommend connecting with the **{recommended_persona}**.

    **Why this persona is perfect for you:**
    {recommendation_reasons}

    **What they can help with:**
    {persona_capabilities}

    Would you like me to connect you with the {recommended_persona} now, or would you prefer to explore other options?
  
  error_prompt: |
    I apologize, but I encountered an issue while trying to help you:
    
    Error: {error_message}
    
    Let me try a different approach or connect you with a specialized persona who can better assist with your request.

# Components configuration (migrated from legacy YAML configs)
components:
  - type: "enhanced_context_manager"
    name: "EnhancedContextManager"
    config:
      context_ttl: 3600
      max_history_size: 15
      sync_interval: 300
      enable_entity_tracking: true
      enable_context_versioning: true
      enable_compression: true

  - type: "persona_recommender"
    name: "PersonaRecommender"
    config:
      recommendation_threshold: 0.7
      max_recommendations: 5
      consider_user_history: true
      enable_confidence_boosting: true
      cache_ttl: 300

  - type: "data_attachment_assistant"
    name: "DataAttachmentAssistant"
    config:
      supported_file_types: ["csv", "xlsx", "pdf", "docx", "txt", "json"]
      max_file_size: "100MB"
      enable_preview: true
      auto_detect_format: true

  - type: "persona_routing"
    name: "PersonaRouter"
    config:
      routing_threshold: 0.7
      enable_intelligent_routing: true
      fallback_persona: "concierge"

  - type: "mcp_server"
    name: "MCPServer"
    config:
      enable_auto_tools: true
      tool_selection_threshold: 0.5
      max_tool_calls: 10
      tool_timeout: 30
      available_tools:
        - "persona_marketplace"
        - "data_access"
        - "conversation_management"
        - "user_feedback"

# Persona descriptions for recommendations (migrated from legacy configs)
persona_descriptions:
  analysis:
    name: "Composable Analyst"
    description: "Expert in data analysis, visualization, and statistical insights"
    capabilities: ["data_analysis", "visualization", "statistical_analysis", "reporting"]
    keywords: ["data", "analysis", "chart", "graph", "statistics", "visualization"]
    priority: 1
    
  marketing:
    name: "Composable Marketer"
    description: "Marketing specialist for strategy, content creation, and campaigns"
    capabilities: ["marketing_strategy", "content_creation", "campaign_planning", "social_media"]
    keywords: ["marketing", "campaign", "content", "brand", "promotion", "strategy"]
    priority: 2
    
  classification:
    name: "Classification Specialist"
    description: "Expert in organizing, categorizing, and classifying content"
    capabilities: ["text_classification", "content_organization", "data_categorization"]
    keywords: ["classify", "organize", "categorize", "sort", "label", "tag"]
    priority: 3

# Strategy-specific configuration
strategy_config:
  enable_intelligent_routing: true
  enable_context_awareness: true
  enable_learning_from_interactions: true
  
  # Recommendation preferences
  recommendation_strategy: "intent_based"
  confidence_threshold: 0.7
  max_recommendations_per_request: 3
  
  # Conversation management
  conversation_memory_duration: 3600  # 1 hour
  enable_conversation_continuity: true
  track_user_preferences: true

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable_industry_context: true
    enable_company_context: true
    context_fields:
      - "industry"
      - "company_size"
      - "business_goals"
      - "user_preferences"
  
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable_knowledge_sharing: true
    coordinate_with_all_personas: true
    share_user_context: true
    enable_handoff_coordination: true

# Performance settings
performance:
  enable_caching: true
  cache_recommendations: true
  cache_duration_minutes: 30
  
  response_optimization:
    enable_fast_recommendations: true
    preload_common_personas: true
  
  timeouts:
    recommendation_timeout_seconds: 10
    persona_connection_timeout_seconds: 30

# Monitoring and metrics
monitoring:
  enable_performance_tracking: true
  track_recommendation_accuracy: true
  track_user_satisfaction: true
  
  metrics_to_collect:
    - "recommendation_response_time"
    - "persona_connection_success_rate"
    - "user_satisfaction_score"
    - "conversation_completion_rate"
