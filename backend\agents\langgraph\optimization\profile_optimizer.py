"""
Business Profile Workflow Optimizer.

This module provides intelligent workflow optimization based on business profiles,
industry patterns, and user behavior to enhance AI agent performance and relevance.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

from ..states.agent_state import DatageniusAgentState
from ..events.event_bus import event_bus, LangGraphEvent, EventPriority

logger = logging.getLogger(__name__)


class OptimizationStrategy(Enum):
    """Optimization strategies for workflows."""
    INDUSTRY_SPECIFIC = "industry_specific"
    BUSINESS_SIZE_BASED = "business_size_based"
    GOAL_ORIENTED = "goal_oriented"
    PERFORMANCE_DRIVEN = "performance_driven"
    USER_BEHAVIOR = "user_behavior"
    HYBRID = "hybrid"


class BusinessType(Enum):
    """Business types for optimization."""
    STARTUP = "startup"
    SMB = "smb"
    ENTERPRISE = "enterprise"
    NONPROFIT = "nonprofit"
    ECOMMERCE = "ecommerce"
    SAAS = "saas"
    CONSULTING = "consulting"
    MANUFACTURING = "manufacturing"
    RETAIL = "retail"
    HEALTHCARE = "healthcare"
    EDUCATION = "education"
    FINANCE = "finance"


@dataclass
class BusinessProfile:
    """Business profile data for optimization."""
    profile_id: str
    name: str
    industry: str
    business_type: BusinessType
    business_size: str
    target_audience: str
    products_services: str
    marketing_goals: str
    context_metadata: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    user_preferences: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OptimizationResult:
    """Result of workflow optimization."""
    original_workflow: str
    optimized_workflow: str
    optimization_strategy: OptimizationStrategy
    confidence_score: float
    expected_improvement: float
    optimization_params: Dict[str, Any]
    reasoning: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)


class BusinessProfileWorkflowOptimizer:
    """
    Intelligent workflow optimizer based on business profiles.
    
    This optimizer analyzes business profiles and optimizes workflows to provide
    more relevant, industry-specific, and goal-oriented AI agent interactions.
    """
    
    def __init__(self):
        self.industry_patterns: Dict[str, Dict[str, Any]] = {}
        self.business_type_patterns: Dict[BusinessType, Dict[str, Any]] = {}
        self.optimization_history: List[OptimizationResult] = []
        self.performance_cache: Dict[str, Dict[str, float]] = {}
        
        # Load default optimization patterns
        self._load_default_patterns()
        
        # Subscribe to events
        event_bus.subscribe("business_profile.updated", self._handle_profile_update)
        event_bus.subscribe("workflow.completed", self._handle_workflow_completion)
        
        logger.info("Business Profile Workflow Optimizer initialized")
    
    async def optimize_workflow(
        self, 
        workflow_template: str,
        business_profile: BusinessProfile,
        user_query: str,
        strategy: OptimizationStrategy = OptimizationStrategy.HYBRID
    ) -> OptimizationResult:
        """
        Optimize workflow based on business profile and strategy.
        
        Args:
            workflow_template: Original workflow template name
            business_profile: Business profile data
            user_query: User's query/request
            strategy: Optimization strategy to use
            
        Returns:
            OptimizationResult: Optimization results and recommendations
        """
        try:
            logger.info(f"Optimizing workflow {workflow_template} for profile {business_profile.profile_id}")
            
            # Analyze business context
            context_analysis = self._analyze_business_context(business_profile, user_query)
            
            # Apply optimization strategy
            optimization_params = await self._apply_optimization_strategy(
                workflow_template,
                business_profile,
                context_analysis,
                strategy
            )
            
            # Generate optimized workflow
            optimized_workflow = self._generate_optimized_workflow(
                workflow_template,
                optimization_params
            )
            
            # Calculate confidence and expected improvement
            confidence_score = self._calculate_confidence_score(
                business_profile,
                optimization_params
            )
            
            expected_improvement = self._estimate_improvement(
                workflow_template,
                optimization_params,
                business_profile
            )
            
            # Create optimization result
            result = OptimizationResult(
                original_workflow=workflow_template,
                optimized_workflow=optimized_workflow,
                optimization_strategy=strategy,
                confidence_score=confidence_score,
                expected_improvement=expected_improvement,
                optimization_params=optimization_params,
                reasoning=self._generate_reasoning(optimization_params),
                metadata={
                    "profile_id": business_profile.profile_id,
                    "industry": business_profile.industry,
                    "business_type": business_profile.business_type.value,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            # Store optimization history
            self.optimization_history.append(result)
            
            # Publish optimization event
            await event_bus.publish(LangGraphEvent(
                event_type="workflow.optimized",
                timestamp=datetime.now(),
                source="profile_optimizer",
                data={
                    "profile_id": business_profile.profile_id,
                    "workflow": workflow_template,
                    "strategy": strategy.value,
                    "confidence": confidence_score,
                    "improvement": expected_improvement
                },
                priority=EventPriority.MEDIUM
            ))
            
            logger.info(f"Workflow optimization completed with {confidence_score:.2f} confidence")
            return result
            
        except Exception as e:
            logger.error(f"Error optimizing workflow: {e}")
            # Return fallback result
            return OptimizationResult(
                original_workflow=workflow_template,
                optimized_workflow=workflow_template,
                optimization_strategy=strategy,
                confidence_score=0.0,
                expected_improvement=0.0,
                optimization_params={},
                reasoning=["Optimization failed, using original workflow"],
                metadata={"error": str(e)}
            )
    
    async def get_industry_recommendations(
        self, 
        industry: str,
        business_type: BusinessType
    ) -> Dict[str, Any]:
        """
        Get workflow recommendations for specific industry and business type.
        
        Args:
            industry: Industry sector
            business_type: Type of business
            
        Returns:
            Dict containing recommendations
        """
        try:
            industry_patterns = self.industry_patterns.get(industry, {})
            business_patterns = self.business_type_patterns.get(business_type, {})
            
            # Combine patterns
            recommendations = {
                "preferred_workflows": [],
                "optimization_params": {},
                "industry_insights": [],
                "business_type_insights": []
            }
            
            # Industry-specific recommendations
            if industry_patterns:
                recommendations["preferred_workflows"].extend(
                    industry_patterns.get("preferred_workflows", [])
                )
                recommendations["optimization_params"].update(
                    industry_patterns.get("optimization_params", {})
                )
                recommendations["industry_insights"] = industry_patterns.get("insights", [])
            
            # Business type recommendations
            if business_patterns:
                recommendations["preferred_workflows"].extend(
                    business_patterns.get("preferred_workflows", [])
                )
                recommendations["optimization_params"].update(
                    business_patterns.get("optimization_params", {})
                )
                recommendations["business_type_insights"] = business_patterns.get("insights", [])
            
            # Remove duplicates
            recommendations["preferred_workflows"] = list(set(recommendations["preferred_workflows"]))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting industry recommendations: {e}")
            return {
                "preferred_workflows": [],
                "optimization_params": {},
                "industry_insights": [],
                "business_type_insights": []
            }
    
    async def analyze_profile_performance(
        self, 
        profile_id: str,
        time_window: timedelta = timedelta(days=30)
    ) -> Dict[str, Any]:
        """
        Analyze workflow performance for a specific business profile.
        
        Args:
            profile_id: Business profile identifier
            time_window: Time window for analysis
            
        Returns:
            Dict containing performance analysis
        """
        try:
            # Get performance data from cache
            profile_performance = self.performance_cache.get(profile_id, {})
            
            # Filter by time window
            cutoff_time = datetime.now() - time_window
            recent_optimizations = [
                opt for opt in self.optimization_history
                if (opt.metadata.get("profile_id") == profile_id and
                    datetime.fromisoformat(opt.metadata.get("timestamp", "")) > cutoff_time)
            ]
            
            # Calculate metrics
            total_optimizations = len(recent_optimizations)
            avg_confidence = sum(opt.confidence_score for opt in recent_optimizations) / max(total_optimizations, 1)
            avg_improvement = sum(opt.expected_improvement for opt in recent_optimizations) / max(total_optimizations, 1)
            
            # Most used strategies
            strategy_counts = {}
            for opt in recent_optimizations:
                strategy = opt.optimization_strategy.value
                strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
            
            most_used_strategy = max(strategy_counts.items(), key=lambda x: x[1])[0] if strategy_counts else None
            
            # Performance trends
            performance_trend = self._calculate_performance_trend(recent_optimizations)
            
            return {
                "profile_id": profile_id,
                "time_window_days": time_window.days,
                "total_optimizations": total_optimizations,
                "average_confidence": avg_confidence,
                "average_improvement": avg_improvement,
                "most_used_strategy": most_used_strategy,
                "strategy_distribution": strategy_counts,
                "performance_trend": performance_trend,
                "recommendations": self._generate_performance_recommendations(
                    recent_optimizations, 
                    profile_performance
                )
            }
            
        except Exception as e:
            logger.error(f"Error analyzing profile performance: {e}")
            return {
                "profile_id": profile_id,
                "error": str(e)
            }
    
    def _analyze_business_context(
        self, 
        business_profile: BusinessProfile, 
        user_query: str
    ) -> Dict[str, Any]:
        """Analyze business context for optimization."""
        context = {
            "industry_focus": self._extract_industry_keywords(business_profile.industry),
            "business_goals": self._extract_goals(business_profile.marketing_goals),
            "target_audience": self._analyze_audience(business_profile.target_audience),
            "query_intent": self._analyze_query_intent(user_query),
            "business_maturity": self._assess_business_maturity(business_profile),
            "complexity_level": self._assess_complexity_needs(business_profile, user_query)
        }
        
        return context
    
    async def _apply_optimization_strategy(
        self,
        workflow_template: str,
        business_profile: BusinessProfile,
        context_analysis: Dict[str, Any],
        strategy: OptimizationStrategy
    ) -> Dict[str, Any]:
        """Apply specific optimization strategy."""
        optimization_params = {}
        
        if strategy == OptimizationStrategy.INDUSTRY_SPECIFIC:
            optimization_params.update(self._apply_industry_optimization(
                business_profile.industry, context_analysis
            ))
        
        elif strategy == OptimizationStrategy.BUSINESS_SIZE_BASED:
            optimization_params.update(self._apply_business_size_optimization(
                business_profile.business_size, context_analysis
            ))
        
        elif strategy == OptimizationStrategy.GOAL_ORIENTED:
            optimization_params.update(self._apply_goal_optimization(
                business_profile.marketing_goals, context_analysis
            ))
        
        elif strategy == OptimizationStrategy.PERFORMANCE_DRIVEN:
            optimization_params.update(await self._apply_performance_optimization(
                business_profile.profile_id, context_analysis
            ))
        
        elif strategy == OptimizationStrategy.USER_BEHAVIOR:
            optimization_params.update(self._apply_behavior_optimization(
                business_profile.user_preferences, context_analysis
            ))
        
        elif strategy == OptimizationStrategy.HYBRID:
            # Combine multiple strategies
            optimization_params.update(self._apply_industry_optimization(
                business_profile.industry, context_analysis
            ))
            optimization_params.update(self._apply_goal_optimization(
                business_profile.marketing_goals, context_analysis
            ))
            optimization_params.update(await self._apply_performance_optimization(
                business_profile.profile_id, context_analysis
            ))
        
        return optimization_params
    
    def _generate_optimized_workflow(
        self, 
        original_workflow: str, 
        optimization_params: Dict[str, Any]
    ) -> str:
        """Generate optimized workflow name/identifier."""
        # For now, append optimization suffix
        # In a real implementation, this would create a new workflow configuration
        optimizations = []
        
        if optimization_params.get("industry_focus"):
            optimizations.append("industry")
        if optimization_params.get("goal_oriented"):
            optimizations.append("goal")
        if optimization_params.get("performance_tuned"):
            optimizations.append("perf")
        
        if optimizations:
            return f"{original_workflow}_optimized_{'_'.join(optimizations)}"
        else:
            return original_workflow
    
    def _calculate_confidence_score(
        self, 
        business_profile: BusinessProfile, 
        optimization_params: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for optimization."""
        confidence = 0.5  # Base confidence
        
        # Increase confidence based on available data
        if business_profile.industry:
            confidence += 0.1
        if business_profile.marketing_goals:
            confidence += 0.1
        if business_profile.target_audience:
            confidence += 0.1
        if optimization_params:
            confidence += 0.2
        
        # Adjust based on historical performance
        profile_history = [
            opt for opt in self.optimization_history
            if opt.metadata.get("profile_id") == business_profile.profile_id
        ]
        
        if profile_history:
            avg_past_confidence = sum(opt.confidence_score for opt in profile_history) / len(profile_history)
            confidence = (confidence + avg_past_confidence) / 2
        
        return min(confidence, 1.0)
    
    def _estimate_improvement(
        self,
        workflow_template: str,
        optimization_params: Dict[str, Any],
        business_profile: BusinessProfile
    ) -> float:
        """Estimate expected improvement percentage."""
        base_improvement = 0.1  # 10% base improvement
        
        # Increase based on optimization parameters
        param_count = len(optimization_params)
        improvement = base_improvement + (param_count * 0.05)
        
        # Adjust based on business profile completeness
        profile_completeness = self._calculate_profile_completeness(business_profile)
        improvement *= profile_completeness
        
        return min(improvement, 0.5)  # Cap at 50% improvement
    
    def _generate_reasoning(self, optimization_params: Dict[str, Any]) -> List[str]:
        """Generate human-readable reasoning for optimization."""
        reasoning = []
        
        if optimization_params.get("industry_focus"):
            reasoning.append("Applied industry-specific optimizations based on sector patterns")
        
        if optimization_params.get("goal_oriented"):
            reasoning.append("Aligned workflow with stated business goals")
        
        if optimization_params.get("performance_tuned"):
            reasoning.append("Applied performance optimizations based on historical data")
        
        if optimization_params.get("audience_targeted"):
            reasoning.append("Customized approach for target audience characteristics")
        
        if not reasoning:
            reasoning.append("Applied general optimization patterns")
        
        return reasoning

    def _load_default_patterns(self):
        """Load default optimization patterns for industries and business types."""
        # Industry patterns
        self.industry_patterns = {
            "technology": {
                "preferred_workflows": ["analysis_template", "dashboard_generation"],
                "optimization_params": {
                    "data_focus": True,
                    "technical_depth": "high",
                    "innovation_emphasis": True
                },
                "insights": [
                    "Tech companies benefit from data-driven insights",
                    "Innovation metrics are crucial for growth tracking"
                ]
            },
            "healthcare": {
                "preferred_workflows": ["compliance_analysis", "patient_insights"],
                "optimization_params": {
                    "compliance_focus": True,
                    "privacy_emphasis": True,
                    "regulatory_awareness": True
                },
                "insights": [
                    "Healthcare requires strict compliance considerations",
                    "Patient privacy is paramount in all analyses"
                ]
            },
            "finance": {
                "preferred_workflows": ["risk_analysis", "financial_dashboard"],
                "optimization_params": {
                    "risk_assessment": True,
                    "regulatory_compliance": True,
                    "precision_focus": True
                },
                "insights": [
                    "Financial services require precise risk assessment",
                    "Regulatory compliance is critical for operations"
                ]
            },
            "retail": {
                "preferred_workflows": ["customer_analysis", "sales_dashboard"],
                "optimization_params": {
                    "customer_focus": True,
                    "seasonal_awareness": True,
                    "inventory_optimization": True
                },
                "insights": [
                    "Retail success depends on customer behavior understanding",
                    "Seasonal trends significantly impact performance"
                ]
            },
            "manufacturing": {
                "preferred_workflows": ["efficiency_analysis", "supply_chain_dashboard"],
                "optimization_params": {
                    "efficiency_focus": True,
                    "quality_emphasis": True,
                    "supply_chain_awareness": True
                },
                "insights": [
                    "Manufacturing efficiency drives profitability",
                    "Supply chain optimization is crucial for operations"
                ]
            }
        }

        # Business type patterns
        self.business_type_patterns = {
            BusinessType.STARTUP: {
                "preferred_workflows": ["growth_analysis", "market_validation"],
                "optimization_params": {
                    "growth_focus": True,
                    "agility_emphasis": True,
                    "resource_efficiency": True
                },
                "insights": [
                    "Startups need rapid growth insights",
                    "Resource efficiency is critical for survival"
                ]
            },
            BusinessType.ENTERPRISE: {
                "preferred_workflows": ["comprehensive_analysis", "executive_dashboard"],
                "optimization_params": {
                    "comprehensive_scope": True,
                    "strategic_focus": True,
                    "scalability_emphasis": True
                },
                "insights": [
                    "Enterprises require comprehensive strategic insights",
                    "Scalability considerations are paramount"
                ]
            },
            BusinessType.SMB: {
                "preferred_workflows": ["practical_analysis", "operational_dashboard"],
                "optimization_params": {
                    "practical_focus": True,
                    "cost_efficiency": True,
                    "actionable_insights": True
                },
                "insights": [
                    "SMBs need practical, actionable insights",
                    "Cost efficiency is a primary concern"
                ]
            }
        }

    def _extract_industry_keywords(self, industry: str) -> List[str]:
        """Extract relevant keywords from industry."""
        industry_keywords = {
            "technology": ["innovation", "digital", "software", "data", "automation"],
            "healthcare": ["patient", "medical", "compliance", "safety", "treatment"],
            "finance": ["risk", "investment", "regulatory", "capital", "returns"],
            "retail": ["customer", "sales", "inventory", "seasonal", "market"],
            "manufacturing": ["efficiency", "quality", "production", "supply", "cost"]
        }

        return industry_keywords.get(industry.lower(), [])

    def _extract_goals(self, marketing_goals: str) -> List[str]:
        """Extract key goals from marketing goals text."""
        if not marketing_goals:
            return []

        goal_keywords = [
            "growth", "revenue", "customers", "brand", "market share",
            "engagement", "conversion", "retention", "awareness", "leads"
        ]

        goals = []
        marketing_goals_lower = marketing_goals.lower()

        for keyword in goal_keywords:
            if keyword in marketing_goals_lower:
                goals.append(keyword)

        return goals

    def _analyze_audience(self, target_audience: str) -> Dict[str, Any]:
        """Analyze target audience characteristics."""
        if not target_audience:
            return {}

        audience_lower = target_audience.lower()

        analysis = {
            "b2b": any(term in audience_lower for term in ["business", "enterprise", "company", "b2b"]),
            "b2c": any(term in audience_lower for term in ["consumer", "individual", "personal", "b2c"]),
            "technical": any(term in audience_lower for term in ["developer", "technical", "engineer", "IT"]),
            "executive": any(term in audience_lower for term in ["executive", "manager", "director", "CEO"]),
            "young": any(term in audience_lower for term in ["young", "millennial", "gen z", "student"]),
            "professional": any(term in audience_lower for term in ["professional", "corporate", "business"])
        }

        return analysis

    def _analyze_query_intent(self, user_query: str) -> Dict[str, Any]:
        """Analyze user query intent."""
        if not user_query:
            return {}

        query_lower = user_query.lower()

        intent = {
            "analytical": any(term in query_lower for term in ["analyze", "data", "metrics", "performance"]),
            "strategic": any(term in query_lower for term in ["strategy", "plan", "roadmap", "future"]),
            "operational": any(term in query_lower for term in ["process", "workflow", "efficiency", "operations"]),
            "financial": any(term in query_lower for term in ["revenue", "cost", "profit", "budget", "ROI"]),
            "marketing": any(term in query_lower for term in ["marketing", "campaign", "brand", "customer"]),
            "urgent": any(term in query_lower for term in ["urgent", "immediate", "asap", "quickly"])
        }

        return intent

    def _assess_business_maturity(self, business_profile: BusinessProfile) -> str:
        """Assess business maturity level."""
        if business_profile.business_type == BusinessType.STARTUP:
            return "early"
        elif business_profile.business_type == BusinessType.ENTERPRISE:
            return "mature"
        elif business_profile.business_size in ["small", "medium"]:
            return "growing"
        else:
            return "established"

    def _assess_complexity_needs(self, business_profile: BusinessProfile, user_query: str) -> str:
        """Assess complexity needs based on profile and query."""
        complexity_score = 0

        # Business type complexity
        if business_profile.business_type == BusinessType.ENTERPRISE:
            complexity_score += 2
        elif business_profile.business_type == BusinessType.SMB:
            complexity_score += 1

        # Query complexity
        query_lower = user_query.lower() if user_query else ""
        complex_terms = ["comprehensive", "detailed", "advanced", "complex", "strategic"]
        simple_terms = ["simple", "basic", "quick", "overview", "summary"]

        if any(term in query_lower for term in complex_terms):
            complexity_score += 2
        elif any(term in query_lower for term in simple_terms):
            complexity_score -= 1

        if complexity_score >= 3:
            return "high"
        elif complexity_score >= 1:
            return "medium"
        else:
            return "low"

    def _apply_industry_optimization(self, industry: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply industry-specific optimizations."""
        industry_pattern = self.industry_patterns.get(industry.lower(), {})
        optimization_params = industry_pattern.get("optimization_params", {}).copy()

        # Add industry focus flag
        optimization_params["industry_focus"] = True
        optimization_params["industry"] = industry

        return optimization_params

    def _apply_business_size_optimization(self, business_size: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply business size-based optimizations."""
        optimization_params = {}

        if business_size in ["small", "startup"]:
            optimization_params.update({
                "resource_efficiency": True,
                "quick_insights": True,
                "cost_awareness": True
            })
        elif business_size in ["large", "enterprise"]:
            optimization_params.update({
                "comprehensive_analysis": True,
                "strategic_depth": True,
                "scalability_focus": True
            })
        else:  # medium
            optimization_params.update({
                "balanced_approach": True,
                "growth_focus": True,
                "practical_insights": True
            })

        return optimization_params

    def _apply_goal_optimization(self, marketing_goals: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply goal-oriented optimizations."""
        goals = self._extract_goals(marketing_goals)
        optimization_params = {"goal_oriented": True}

        if "growth" in goals:
            optimization_params["growth_metrics"] = True
        if "revenue" in goals:
            optimization_params["revenue_focus"] = True
        if "customers" in goals:
            optimization_params["customer_analysis"] = True
        if "brand" in goals:
            optimization_params["brand_metrics"] = True

        return optimization_params

    async def _apply_performance_optimization(self, profile_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply performance-driven optimizations."""
        optimization_params = {"performance_tuned": True}

        # Get historical performance data
        profile_performance = self.performance_cache.get(profile_id, {})

        if profile_performance:
            # Apply optimizations based on past performance
            if profile_performance.get("avg_execution_time", 0) > 30:
                optimization_params["speed_optimization"] = True
            if profile_performance.get("error_rate", 0) > 0.1:
                optimization_params["reliability_focus"] = True
            if profile_performance.get("user_satisfaction", 0) < 0.8:
                optimization_params["user_experience_focus"] = True

        return optimization_params

    def _apply_behavior_optimization(self, user_preferences: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply user behavior-based optimizations."""
        optimization_params = {"behavior_optimized": True}

        if user_preferences.get("prefers_visual", False):
            optimization_params["visual_emphasis"] = True
        if user_preferences.get("prefers_detailed", False):
            optimization_params["detailed_analysis"] = True
        if user_preferences.get("prefers_quick", False):
            optimization_params["quick_insights"] = True

        return optimization_params

    def _calculate_performance_trend(self, optimizations: List[OptimizationResult]) -> str:
        """Calculate performance trend from optimization history."""
        if len(optimizations) < 2:
            return "insufficient_data"

        # Sort by timestamp
        sorted_opts = sorted(optimizations, key=lambda x: x.metadata.get("timestamp", ""))

        # Calculate trend in confidence scores
        recent_confidence = sum(opt.confidence_score for opt in sorted_opts[-5:]) / min(len(sorted_opts), 5)
        older_confidence = sum(opt.confidence_score for opt in sorted_opts[:5]) / min(len(sorted_opts), 5)

        if recent_confidence > older_confidence + 0.1:
            return "improving"
        elif recent_confidence < older_confidence - 0.1:
            return "declining"
        else:
            return "stable"

    def _generate_performance_recommendations(
        self,
        optimizations: List[OptimizationResult],
        profile_performance: Dict[str, float]
    ) -> List[str]:
        """Generate performance improvement recommendations."""
        recommendations = []

        if not optimizations:
            recommendations.append("Start using workflow optimizations to improve performance")
            return recommendations

        avg_confidence = sum(opt.confidence_score for opt in optimizations) / len(optimizations)

        if avg_confidence < 0.6:
            recommendations.append("Consider providing more detailed business profile information")

        if profile_performance.get("error_rate", 0) > 0.1:
            recommendations.append("Review workflow configurations to reduce error rates")

        # Strategy recommendations
        strategy_counts = {}
        for opt in optimizations:
            strategy = opt.optimization_strategy.value
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

        if len(strategy_counts) == 1:
            recommendations.append("Try different optimization strategies for better results")

        return recommendations

    def _calculate_profile_completeness(self, business_profile: BusinessProfile) -> float:
        """Calculate how complete a business profile is."""
        completeness = 0.0
        total_fields = 7

        if business_profile.name:
            completeness += 1
        if business_profile.industry:
            completeness += 1
        if business_profile.business_type:
            completeness += 1
        if business_profile.business_size:
            completeness += 1
        if business_profile.target_audience:
            completeness += 1
        if business_profile.products_services:
            completeness += 1
        if business_profile.marketing_goals:
            completeness += 1

        return completeness / total_fields

    async def _handle_profile_update(self, event: LangGraphEvent):
        """Handle business profile update events."""
        data = event.data
        profile_id = data.get("profile_id")

        if profile_id:
            # Clear cached performance data for updated profile
            self.performance_cache.pop(profile_id, None)
            logger.info(f"Cleared performance cache for updated profile {profile_id}")

    async def _handle_workflow_completion(self, event: LangGraphEvent):
        """Handle workflow completion events for performance tracking."""
        data = event.data
        profile_id = data.get("profile_id")
        execution_time = data.get("execution_time", 0)
        success = data.get("success", False)

        if profile_id:
            # Update performance cache
            if profile_id not in self.performance_cache:
                self.performance_cache[profile_id] = {
                    "total_executions": 0,
                    "successful_executions": 0,
                    "total_execution_time": 0,
                    "error_count": 0
                }

            perf = self.performance_cache[profile_id]
            perf["total_executions"] += 1
            perf["total_execution_time"] += execution_time

            if success:
                perf["successful_executions"] += 1
            else:
                perf["error_count"] += 1

            # Calculate derived metrics
            perf["avg_execution_time"] = perf["total_execution_time"] / perf["total_executions"]
            perf["success_rate"] = perf["successful_executions"] / perf["total_executions"]
            perf["error_rate"] = perf["error_count"] / perf["total_executions"]

    async def get_optimization_insights(self, profile_id: str) -> Dict[str, Any]:
        """Get optimization insights for a business profile."""
        try:
            # Get profile optimizations
            profile_optimizations = [
                opt for opt in self.optimization_history
                if opt.metadata.get("profile_id") == profile_id
            ]

            if not profile_optimizations:
                return {
                    "profile_id": profile_id,
                    "insights": ["No optimization history available"],
                    "recommendations": ["Start using workflow optimizations"]
                }

            # Analyze patterns
            most_successful_strategy = self._find_most_successful_strategy(profile_optimizations)
            optimization_trends = self._analyze_optimization_trends(profile_optimizations)
            improvement_opportunities = self._identify_improvement_opportunities(profile_optimizations)

            return {
                "profile_id": profile_id,
                "total_optimizations": len(profile_optimizations),
                "most_successful_strategy": most_successful_strategy,
                "optimization_trends": optimization_trends,
                "improvement_opportunities": improvement_opportunities,
                "insights": self._generate_optimization_insights(profile_optimizations),
                "recommendations": self._generate_optimization_recommendations(profile_optimizations)
            }

        except Exception as e:
            logger.error(f"Error getting optimization insights: {e}")
            return {
                "profile_id": profile_id,
                "error": str(e)
            }

    def _find_most_successful_strategy(self, optimizations: List[OptimizationResult]) -> Optional[str]:
        """Find the most successful optimization strategy."""
        if not optimizations:
            return None

        strategy_performance = {}

        for opt in optimizations:
            strategy = opt.optimization_strategy.value
            if strategy not in strategy_performance:
                strategy_performance[strategy] = {
                    "total_confidence": 0,
                    "total_improvement": 0,
                    "count": 0
                }

            perf = strategy_performance[strategy]
            perf["total_confidence"] += opt.confidence_score
            perf["total_improvement"] += opt.expected_improvement
            perf["count"] += 1

        # Calculate average performance for each strategy
        best_strategy = None
        best_score = 0

        for strategy, perf in strategy_performance.items():
            avg_confidence = perf["total_confidence"] / perf["count"]
            avg_improvement = perf["total_improvement"] / perf["count"]
            combined_score = (avg_confidence + avg_improvement) / 2

            if combined_score > best_score:
                best_score = combined_score
                best_strategy = strategy

        return best_strategy

    def _analyze_optimization_trends(self, optimizations: List[OptimizationResult]) -> Dict[str, Any]:
        """Analyze optimization trends over time."""
        if len(optimizations) < 2:
            return {"trend": "insufficient_data"}

        # Sort by timestamp
        sorted_opts = sorted(optimizations, key=lambda x: x.metadata.get("timestamp", ""))

        # Analyze confidence trend
        confidence_trend = self._calculate_performance_trend(sorted_opts)

        # Analyze improvement trend
        recent_improvements = [opt.expected_improvement for opt in sorted_opts[-5:]]
        older_improvements = [opt.expected_improvement for opt in sorted_opts[:5]]

        avg_recent = sum(recent_improvements) / len(recent_improvements)
        avg_older = sum(older_improvements) / len(older_improvements)

        if avg_recent > avg_older + 0.05:
            improvement_trend = "improving"
        elif avg_recent < avg_older - 0.05:
            improvement_trend = "declining"
        else:
            improvement_trend = "stable"

        return {
            "confidence_trend": confidence_trend,
            "improvement_trend": improvement_trend,
            "total_optimizations": len(optimizations),
            "recent_avg_confidence": sum(opt.confidence_score for opt in sorted_opts[-5:]) / min(len(sorted_opts), 5),
            "recent_avg_improvement": avg_recent
        }

    def _identify_improvement_opportunities(self, optimizations: List[OptimizationResult]) -> List[str]:
        """Identify opportunities for improvement."""
        opportunities = []

        if not optimizations:
            return ["Start using workflow optimizations"]

        avg_confidence = sum(opt.confidence_score for opt in optimizations) / len(optimizations)
        avg_improvement = sum(opt.expected_improvement for opt in optimizations) / len(optimizations)

        if avg_confidence < 0.7:
            opportunities.append("Improve business profile completeness for better optimization confidence")

        if avg_improvement < 0.2:
            opportunities.append("Explore different optimization strategies for higher impact")

        # Check strategy diversity
        strategies_used = set(opt.optimization_strategy.value for opt in optimizations)
        if len(strategies_used) < 3:
            opportunities.append("Try more diverse optimization strategies")

        return opportunities

    def _generate_optimization_insights(self, optimizations: List[OptimizationResult]) -> List[str]:
        """Generate insights from optimization history."""
        insights = []

        if not optimizations:
            return ["No optimization data available"]

        # Strategy insights
        strategy_counts = {}
        for opt in optimizations:
            strategy = opt.optimization_strategy.value
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

        most_used = max(strategy_counts.items(), key=lambda x: x[1])
        insights.append(f"Most frequently used strategy: {most_used[0]} ({most_used[1]} times)")

        # Performance insights
        avg_confidence = sum(opt.confidence_score for opt in optimizations) / len(optimizations)
        insights.append(f"Average optimization confidence: {avg_confidence:.1%}")

        avg_improvement = sum(opt.expected_improvement for opt in optimizations) / len(optimizations)
        insights.append(f"Average expected improvement: {avg_improvement:.1%}")

        return insights

    def _generate_optimization_recommendations(self, optimizations: List[OptimizationResult]) -> List[str]:
        """Generate recommendations based on optimization history."""
        recommendations = []

        if not optimizations:
            return ["Start using workflow optimizations to improve AI agent performance"]

        # Find best performing strategy
        best_strategy = self._find_most_successful_strategy(optimizations)
        if best_strategy:
            recommendations.append(f"Continue using {best_strategy} strategy for best results")

        # Check for underused strategies
        all_strategies = set(strategy.value for strategy in OptimizationStrategy)
        used_strategies = set(opt.optimization_strategy.value for opt in optimizations)
        unused_strategies = all_strategies - used_strategies

        if unused_strategies:
            recommendations.append(f"Try these unused strategies: {', '.join(list(unused_strategies)[:2])}")

        # Performance-based recommendations
        avg_confidence = sum(opt.confidence_score for opt in optimizations) / len(optimizations)
        if avg_confidence < 0.6:
            recommendations.append("Provide more detailed business information to improve optimization accuracy")

        return recommendations
