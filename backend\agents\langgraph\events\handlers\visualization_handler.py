"""
Visualization Event Handler.

This module handles events related to workflow visualization and monitoring,
integrating the visualization system with the event-driven architecture.
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

from ..event_bus import LangGraphEvent
from ..types import WorkflowCompletedEvent, DashboardUpdateEvent
from ...visualization.workflow_visualizer import WorkflowVisualizer
from ...monitoring.metrics import WorkflowMetrics

logger = logging.getLogger(__name__)


class VisualizationEventHandler:
    """
    Event handler for workflow visualization and monitoring.
    
    This handler processes workflow events and generates appropriate
    visualizations and dashboard updates.
    """

    def __init__(self, metrics_system: Optional[WorkflowMetrics] = None):
        """
        Initialize the visualization event handler.
        
        Args:
            metrics_system: Optional metrics system for performance tracking
        """
        self.visualizer = WorkflowVisualizer()
        self.metrics_system = metrics_system or WorkflowMetrics()
        self.logger = logging.getLogger(__name__)
        
        # Cache for generated visualizations
        self.visualization_cache: Dict[str, Dict[str, Any]] = {}
        
        # Performance tracking
        self.processed_events = 0
        self.processing_errors = 0

    async def handle_workflow_completed(self, event: LangGraphEvent):
        """
        Handle workflow completion events by generating visualizations.
        
        Args:
            event: Workflow completion event
        """
        try:
            workflow_id = event.data.get("workflow_id")
            if not workflow_id:
                self.logger.warning("Workflow completion event missing workflow_id")
                return

            execution_time = event.data.get("execution_time", 0.0)
            success = event.data.get("success", False)
            results = event.data.get("results", {})

            # Generate execution timeline if timeline data is available
            timeline_data = results.get("execution_timeline", [])
            timeline_diagram = None
            if timeline_data:
                timeline_diagram = self.visualizer.generate_execution_timeline(
                    timeline_data,
                    title=f"Workflow {workflow_id} Execution Timeline"
                )

            # Generate sequence diagram for agent interactions
            sequence_diagram = None
            if timeline_data:
                interactions = self.visualizer._extract_agent_interactions(timeline_data)
                if interactions:
                    sequence_diagram = self.visualizer.generate_sequence_diagram(
                        interactions,
                        title=f"Agent Interactions - {workflow_id}"
                    )

            # Generate workflow structure diagram if available
            workflow_definition = results.get("workflow_definition")
            structure_diagram = None
            if workflow_definition:
                execution_data = self._extract_execution_status(timeline_data)
                structure_diagram = self.visualizer.generate_mermaid_flowchart(
                    workflow_definition,
                    execution_data,
                    title=f"Workflow {workflow_id} Structure"
                )

            # Cache visualizations
            self.visualization_cache[workflow_id] = {
                "timeline": timeline_diagram,
                "sequence": sequence_diagram,
                "structure": structure_diagram,
                "execution_time": execution_time,
                "success": success,
                "generated_at": datetime.now().isoformat(),
                "event_timestamp": event.timestamp.isoformat()
            }

            # Record metrics
            if self.metrics_system:
                await self._record_workflow_metrics(workflow_id, execution_time, success, results)

            # Generate dashboard update events
            await self._generate_dashboard_updates(workflow_id, event)

            self.processed_events += 1
            self.logger.info(f"Generated visualizations for workflow {workflow_id}")

        except Exception as e:
            self.processing_errors += 1
            self.logger.error(f"Error handling workflow completion event: {e}")

    async def handle_dashboard_update_request(self, event: LangGraphEvent):
        """
        Handle dashboard update requests.
        
        Args:
            event: Dashboard update request event
        """
        try:
            dashboard_id = event.data.get("dashboard_id")
            widget_id = event.data.get("widget_id")
            
            if not dashboard_id or not widget_id:
                self.logger.warning("Dashboard update event missing required IDs")
                return

            update_data = event.data.get("update_data", {})
            
            # Process different types of dashboard updates
            if "workflow_id" in update_data:
                workflow_id = update_data["workflow_id"]
                
                # Get cached visualizations for the workflow
                if workflow_id in self.visualization_cache:
                    cached_viz = self.visualization_cache[workflow_id]
                    
                    # Update the dashboard widget with cached visualization
                    await self._update_dashboard_widget(
                        dashboard_id, 
                        widget_id, 
                        cached_viz,
                        update_data
                    )
                else:
                    self.logger.warning(f"No cached visualization found for workflow {workflow_id}")

            self.logger.debug(f"Processed dashboard update for {dashboard_id}/{widget_id}")

        except Exception as e:
            self.processing_errors += 1
            self.logger.error(f"Error handling dashboard update request: {e}")

    async def handle_agent_registration(self, event: LangGraphEvent):
        """
        Handle agent registration events for capability visualization.
        
        Args:
            event: Agent registration event
        """
        try:
            agent_id = event.data.get("agent_id")
            capabilities = event.data.get("capabilities", [])
            
            if not agent_id:
                self.logger.warning("Agent registration event missing agent_id")
                return

            # Generate capability visualization
            capability_diagram = self._generate_capability_diagram(agent_id, capabilities)
            
            # Cache capability visualization
            cache_key = f"agent_capabilities_{agent_id}"
            self.visualization_cache[cache_key] = {
                "diagram": capability_diagram,
                "agent_id": agent_id,
                "capabilities": capabilities,
                "generated_at": datetime.now().isoformat()
            }

            self.logger.debug(f"Generated capability visualization for agent {agent_id}")

        except Exception as e:
            self.processing_errors += 1
            self.logger.error(f"Error handling agent registration event: {e}")

    def _extract_execution_status(self, timeline_data: list) -> Dict[str, Dict[str, Any]]:
        """Extract execution status for workflow nodes."""
        execution_status = {}
        
        for event in timeline_data:
            agent_id = event.get("agent_id")
            status = event.get("status", "pending")
            
            if agent_id:
                execution_status[agent_id] = {"status": status}
        
        return execution_status

    async def _record_workflow_metrics(
        self, 
        workflow_id: str, 
        execution_time: float, 
        success: bool, 
        results: Dict[str, Any]
    ):
        """Record workflow metrics for performance tracking."""
        try:
            success_rate = 1.0 if success else 0.0
            
            # Extract additional metrics from results
            timeline_data = results.get("execution_timeline", [])
            agent_transitions = len(set(event.get("agent_id") for event in timeline_data)) - 1
            tool_executions = len([event for event in timeline_data if "tool" in event.get("task_name", "").lower()])
            
            # Calculate quality score based on execution efficiency
            quality_score = self._calculate_quality_score(execution_time, success, timeline_data)
            
            await self.metrics_system.record_workflow_metrics(
                workflow_id=workflow_id,
                execution_time_ms=execution_time * 1000,
                success_rate=success_rate,
                quality_score=quality_score,
                agent_transitions=max(0, agent_transitions),
                tool_executions=tool_executions,
                error_count=0 if success else 1
            )

        except Exception as e:
            self.logger.error(f"Error recording workflow metrics: {e}")

    def _calculate_quality_score(
        self, 
        execution_time: float, 
        success: bool, 
        timeline_data: list
    ) -> float:
        """Calculate a quality score for the workflow execution."""
        if not success:
            return 0.3  # Base score for failed workflows
        
        # Base score for successful workflows
        base_score = 0.7
        
        # Time efficiency bonus (faster is better, up to 30 seconds baseline)
        time_bonus = max(0, 0.2 * (1 - min(execution_time / 30.0, 1.0)))
        
        # Consistency bonus (fewer agent transitions generally better)
        agent_count = len(set(event.get("agent_id") for event in timeline_data))
        consistency_bonus = max(0, 0.1 * (1 - min(agent_count / 5.0, 1.0)))
        
        return min(1.0, base_score + time_bonus + consistency_bonus)

    async def _generate_dashboard_updates(self, workflow_id: str, original_event: LangGraphEvent):
        """Generate dashboard update events for workflow visualizations."""
        try:
            from ..event_bus import event_bus
            
            # Get cached visualizations
            if workflow_id not in self.visualization_cache:
                return
            
            cached_viz = self.visualization_cache[workflow_id]
            
            # Generate update events for different dashboard widgets
            dashboard_updates = [
                {
                    "dashboard_id": "main-dashboard",
                    "widget_id": "workflow-timeline-widget",
                    "data": {
                        "diagram_type": "timeline",
                        "diagram_content": cached_viz.get("timeline"),
                        "workflow_id": workflow_id
                    }
                },
                {
                    "dashboard_id": "main-dashboard", 
                    "widget_id": "agent-sequence-widget",
                    "data": {
                        "diagram_type": "sequence",
                        "diagram_content": cached_viz.get("sequence"),
                        "workflow_id": workflow_id
                    }
                },
                {
                    "dashboard_id": "performance-dashboard",
                    "widget_id": "workflow-structure-widget", 
                    "data": {
                        "diagram_type": "flowchart",
                        "diagram_content": cached_viz.get("structure"),
                        "workflow_id": workflow_id,
                        "execution_time": cached_viz.get("execution_time"),
                        "success": cached_viz.get("success")
                    }
                }
            ]
            
            # Publish dashboard update events
            for update in dashboard_updates:
                if update["data"]["diagram_content"]:  # Only update if diagram was generated
                    dashboard_event = DashboardUpdateEvent(
                        dashboard_id=update["dashboard_id"],
                        widget_id=update["widget_id"],
                        data=update["data"]
                    )
                    await event_bus.publish(dashboard_event)

        except Exception as e:
            self.logger.error(f"Error generating dashboard updates: {e}")

    async def _update_dashboard_widget(
        self, 
        dashboard_id: str, 
        widget_id: str, 
        visualization_data: Dict[str, Any],
        update_data: Dict[str, Any]
    ):
        """Update a specific dashboard widget with visualization data."""
        try:
            # This would integrate with the actual dashboard system
            # For now, we log the update
            self.logger.info(
                f"Updating dashboard widget {dashboard_id}/{widget_id} "
                f"with visualization data for workflow {update_data.get('workflow_id')}"
            )
            
            # In a real implementation, this would:
            # 1. Connect to the dashboard service
            # 2. Update the widget with the visualization data
            # 3. Trigger real-time updates to connected clients

        except Exception as e:
            self.logger.error(f"Error updating dashboard widget: {e}")

    def _generate_capability_diagram(self, agent_id: str, capabilities: list) -> str:
        """Generate a diagram showing agent capabilities."""
        try:
            # Create a simple flowchart showing agent capabilities
            mermaid_lines = [
                "---",
                f"title: {agent_id} Capabilities",
                "---",
                "flowchart TD"
            ]
            
            # Add agent node
            mermaid_lines.append(f'    {agent_id}["{agent_id}"]')
            
            # Add capability nodes
            for i, capability in enumerate(capabilities):
                cap_id = f"cap{i}"
                mermaid_lines.append(f'    {cap_id}["{capability}"]')
                mermaid_lines.append(f'    {agent_id} --> {cap_id}')
            
            # Add styling
            mermaid_lines.append(f'    style {agent_id} fill:#4CAF50')
            for i in range(len(capabilities)):
                mermaid_lines.append(f'    style cap{i} fill:#2196F3')
            
            return "\n".join(mermaid_lines)

        except Exception as e:
            self.logger.error(f"Error generating capability diagram: {e}")
            return f"flowchart TD\n    {agent_id}[\"Error generating diagram\"]"

    def get_cached_visualization(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get cached visualization for a workflow."""
        return self.visualization_cache.get(workflow_id)

    def clear_cache(self, max_age_hours: int = 24):
        """Clear old visualizations from cache."""
        try:
            current_time = datetime.now()
            expired_keys = []
            
            for key, data in self.visualization_cache.items():
                generated_at = datetime.fromisoformat(data["generated_at"])
                age_hours = (current_time - generated_at).total_seconds() / 3600
                
                if age_hours > max_age_hours:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.visualization_cache[key]
            
            if expired_keys:
                self.logger.info(f"Cleared {len(expired_keys)} expired visualizations from cache")

        except Exception as e:
            self.logger.error(f"Error clearing visualization cache: {e}")

    def get_handler_metrics(self) -> Dict[str, Any]:
        """Get metrics about the event handler performance."""
        return {
            "processed_events": self.processed_events,
            "processing_errors": self.processing_errors,
            "cached_visualizations": len(self.visualization_cache),
            "error_rate": self.processing_errors / max(self.processed_events, 1),
            "cache_keys": list(self.visualization_cache.keys())
        }
