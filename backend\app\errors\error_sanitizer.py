"""
Error Sanitizer for Security.

Provides security-aware error sanitization to prevent sensitive information
leakage in error messages and responses.
"""

import re
import logging
from typing import Dict, Any, List, Pattern, Optional, Set
from copy import deepcopy

from .base_errors import DatageniusError
from .correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class ErrorSanitizer:
    """
    Security-aware error sanitizer.
    
    Removes or masks sensitive information from error messages and details
    to prevent information disclosure vulnerabilities.
    """
    
    def __init__(self):
        self.logger = get_correlation_logger(__name__)
        
        # Sensitive patterns to remove or mask
        self._sensitive_patterns: List[Pattern] = [
            # Database connection strings
            re.compile(r'postgresql://[^:]+:[^@]+@[^/]+/\w+', re.IGNORECASE),
            re.compile(r'mysql://[^:]+:[^@]+@[^/]+/\w+', re.IGNORECASE),
            re.compile(r'mongodb://[^:]+:[^@]+@[^/]+/\w+', re.IGNORECASE),
            
            # API keys and tokens
            re.compile(r'api[_-]?key["\s]*[:=]["\s]*[a-zA-Z0-9_-]{20,}', re.IGNORECASE),
            re.compile(r'token["\s]*[:=]["\s]*[a-zA-Z0-9_.-]{20,}', re.IGNORECASE),
            re.compile(r'bearer\s+[a-zA-Z0-9_.-]{20,}', re.IGNORECASE),
            
            # Passwords
            re.compile(r'password["\s]*[:=]["\s]*[^\s"]+', re.IGNORECASE),
            re.compile(r'passwd["\s]*[:=]["\s]*[^\s"]+', re.IGNORECASE),
            re.compile(r'pwd["\s]*[:=]["\s]*[^\s"]+', re.IGNORECASE),
            
            # Secret keys
            re.compile(r'secret[_-]?key["\s]*[:=]["\s]*[^\s"]+', re.IGNORECASE),
            re.compile(r'private[_-]?key["\s]*[:=]["\s]*[^\s"]+', re.IGNORECASE),
            
            # Email addresses (partial masking)
            re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            
            # Phone numbers
            re.compile(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'),
            re.compile(r'\b\+\d{1,3}[-.\s]?\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{3,4}\b'),
            
            # Credit card numbers
            re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'),
            
            # Social Security Numbers
            re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
            
            # IP addresses (internal networks)
            re.compile(r'\b192\.168\.\d{1,3}\.\d{1,3}\b'),
            re.compile(r'\b10\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'),
            re.compile(r'\b172\.(1[6-9]|2[0-9]|3[0-1])\.\d{1,3}\.\d{1,3}\b'),
            
            # File paths (system paths)
            re.compile(r'[C-Z]:\\[^"\s]+', re.IGNORECASE),
            re.compile(r'/(?:home|root|usr|etc|var)/[^"\s]+'),
            
            # SQL injection patterns
            re.compile(r'(union|select|insert|update|delete|drop|create|alter)\s+[^"\s]+', re.IGNORECASE),
        ]
        
        # Sensitive field names to mask
        self._sensitive_fields: Set[str] = {
            'password', 'passwd', 'pwd', 'secret', 'token', 'key', 'api_key',
            'private_key', 'public_key', 'jwt_secret', 'database_password',
            'redis_password', 'smtp_password', 'ldap_password', 'oauth_secret',
            'client_secret', 'webhook_secret', 'encryption_key', 'signing_key',
            'access_token', 'refresh_token', 'session_token', 'csrf_token',
            'credit_card', 'ssn', 'social_security', 'bank_account', 'routing_number'
        }
        
        # Generic error messages for different categories
        self._generic_messages = {
            'database': 'Database operation failed',
            'authentication': 'Authentication failed',
            'authorization': 'Access denied',
            'validation': 'Input validation failed',
            'network': 'Network communication error',
            'external_service': 'External service error',
            'configuration': 'System configuration error',
            'internal': 'Internal server error occurred'
        }
    
    def sanitize_error(self, error: DatageniusError) -> DatageniusError:
        """
        Sanitize a DatageniusError for client consumption.
        
        Args:
            error: Original error to sanitize
            
        Returns:
            Sanitized copy of the error
        """
        try:
            # Create a deep copy to avoid modifying the original
            sanitized_error = deepcopy(error)
            
            # Sanitize the user message
            sanitized_error.user_message = self._sanitize_message(error.user_message)
            
            # For high/critical severity errors, use generic messages
            if error.severity.value in ['high', 'critical']:
                category_key = error.category.value.replace('_', '')
                sanitized_error.user_message = self._generic_messages.get(
                    category_key, 
                    self._generic_messages['internal']
                )
            
            # Sanitize details
            sanitized_error.details = self._sanitize_details(error.details)
            
            # Remove sensitive context information
            sanitized_error.context = self._sanitize_context(error.context)
            
            # Remove stack trace and cause for client responses
            sanitized_error.stack_trace = None
            sanitized_error.cause = None
            
            # Keep the internal message for logging but don't expose it
            # (The error handler will use this appropriately)
            
            return sanitized_error
            
        except Exception as e:
            self.logger.error(f"Error sanitizing error: {e}")
            # Return a generic error if sanitization fails
            return DatageniusError(
                message="An error occurred",
                user_message="An internal error occurred. Please try again later.",
                correlation_id=error.correlation_id if hasattr(error, 'correlation_id') else None
            )
    
    def _sanitize_message(self, message: str) -> str:
        """Sanitize an error message."""
        if not message:
            return message
        
        sanitized = message
        
        # Apply pattern-based sanitization
        for pattern in self._sensitive_patterns:
            sanitized = pattern.sub('[REDACTED]', sanitized)
        
        # Additional specific sanitizations
        sanitized = self._mask_email_addresses(sanitized)
        sanitized = self._mask_file_paths(sanitized)
        
        return sanitized
    
    def _sanitize_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize error details dictionary."""
        if not details:
            return {}
        
        sanitized_details = {}
        
        for key, value in details.items():
            # Check if field name is sensitive
            if any(sensitive in key.lower() for sensitive in self._sensitive_fields):
                sanitized_details[key] = '[REDACTED]'
            elif isinstance(value, str):
                sanitized_details[key] = self._sanitize_message(value)
            elif isinstance(value, dict):
                sanitized_details[key] = self._sanitize_details(value)
            elif isinstance(value, list):
                sanitized_details[key] = self._sanitize_list(value)
            else:
                # For other types, include as-is but be cautious
                if not self._is_potentially_sensitive_value(value):
                    sanitized_details[key] = value
                else:
                    sanitized_details[key] = '[REDACTED]'
        
        return sanitized_details
    
    def _sanitize_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize context information."""
        if not context:
            return {}
        
        # Only include safe context fields
        safe_fields = {
            'correlation_id', 'request_id', 'timestamp', 'operation',
            'method', 'status_code', 'user_agent'
        }
        
        sanitized_context = {}
        for key, value in context.items():
            if key in safe_fields:
                if isinstance(value, str):
                    sanitized_context[key] = self._sanitize_message(value)
                else:
                    sanitized_context[key] = value
        
        return sanitized_context
    
    def _sanitize_list(self, items: List[Any]) -> List[Any]:
        """Sanitize a list of items."""
        sanitized_items = []
        
        for item in items:
            if isinstance(item, str):
                sanitized_items.append(self._sanitize_message(item))
            elif isinstance(item, dict):
                sanitized_items.append(self._sanitize_details(item))
            elif isinstance(item, list):
                sanitized_items.append(self._sanitize_list(item))
            else:
                if not self._is_potentially_sensitive_value(item):
                    sanitized_items.append(item)
                else:
                    sanitized_items.append('[REDACTED]')
        
        return sanitized_items
    
    def _mask_email_addresses(self, text: str) -> str:
        """Mask email addresses partially."""
        def mask_email(match):
            email = match.group(0)
            if '@' in email:
                local, domain = email.split('@', 1)
                if len(local) > 2:
                    masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
                else:
                    masked_local = '*' * len(local)
                return f"{masked_local}@{domain}"
            return '[EMAIL]'
        
        return re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', mask_email, text)
    
    def _mask_file_paths(self, text: str) -> str:
        """Mask sensitive file paths."""
        # Mask Windows paths
        text = re.sub(r'[C-Z]:\\[^"\s]+', '[PATH]', text, flags=re.IGNORECASE)
        
        # Mask Unix paths (but keep common safe paths)
        safe_prefixes = ['/tmp', '/var/tmp', '/usr/local', '/opt']
        
        def mask_unix_path(match):
            path = match.group(0)
            if any(path.startswith(prefix) for prefix in safe_prefixes):
                return path
            return '[PATH]'
        
        text = re.sub(r'/(?:home|root|usr|etc|var)/[^"\s]+', mask_unix_path, text)
        
        return text
    
    def _is_potentially_sensitive_value(self, value: Any) -> bool:
        """Check if a value might be sensitive."""
        if isinstance(value, str):
            # Check for patterns that might indicate sensitive data
            if len(value) > 20 and re.match(r'^[a-zA-Z0-9+/=]+$', value):
                return True  # Might be base64 encoded
            
            if re.match(r'^[a-fA-F0-9]{32,}$', value):
                return True  # Might be a hash or token
            
            # Check for sensitive patterns
            for pattern in self._sensitive_patterns:
                if pattern.search(value):
                    return True
        
        return False
    
    def add_sensitive_pattern(self, pattern: str, flags: int = 0):
        """
        Add a custom sensitive pattern.
        
        Args:
            pattern: Regular expression pattern
            flags: Regex flags
        """
        try:
            compiled_pattern = re.compile(pattern, flags)
            self._sensitive_patterns.append(compiled_pattern)
            self.logger.info(f"Added sensitive pattern: {pattern}")
        except re.error as e:
            self.logger.error(f"Invalid regex pattern '{pattern}': {e}")
    
    def add_sensitive_field(self, field_name: str):
        """
        Add a custom sensitive field name.
        
        Args:
            field_name: Field name to treat as sensitive
        """
        self._sensitive_fields.add(field_name.lower())
        self.logger.info(f"Added sensitive field: {field_name}")
    
    def remove_sensitive_field(self, field_name: str):
        """
        Remove a sensitive field name.
        
        Args:
            field_name: Field name to remove from sensitive list
        """
        self._sensitive_fields.discard(field_name.lower())
        self.logger.info(f"Removed sensitive field: {field_name}")
    
    def get_sensitive_fields(self) -> Set[str]:
        """Get the current set of sensitive field names."""
        return self._sensitive_fields.copy()
    
    def test_sanitization(self, test_string: str) -> str:
        """
        Test sanitization on a string (for debugging).
        
        Args:
            test_string: String to test sanitization on
            
        Returns:
            Sanitized string
        """
        return self._sanitize_message(test_string)
