"""
Event system integrations with existing Datagenius components.

This module provides integration layers between the event system and
existing components like the agent registry and intelligent router.
"""

from .registry_integration import EventDrivenAgentRegistry
from .router_integration import EventDrivenIntelligentRouter

__all__ = [
    "EventDrivenAgentRegistry",
    "EventDrivenIntelligentRouter"
]
