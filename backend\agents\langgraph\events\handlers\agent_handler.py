"""
Agent event handler for LangGraph system.

This module handles agent-related events including registration, capability updates,
and performance monitoring.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
from ..event_bus import LangGraphEvent

logger = logging.getLogger(__name__)


class AgentEventHandler:
    """Handler for agent-related events."""
    
    def __init__(self):
        self.processed_events = 0
        self.agent_registry_cache = {}
        self.capability_history = {}
        self.performance_history = {}
    
    async def handle_agent_registration(self, event: LangGraphEvent):
        """Handle agent registration event."""
        try:
            data = event.data
            agent_id = data.get("agent_id")
            capabilities = data.get("capabilities", [])
            metadata = data.get("metadata", {})
            
            logger.info(f"Processing agent registration for {agent_id}")
            
            # Update local cache
            self.agent_registry_cache[agent_id] = {
                "capabilities": capabilities,
                "metadata": metadata,
                "registration_time": data.get("registration_time"),
                "last_updated": datetime.now().isoformat()
            }
            
            # Initialize capability history
            if agent_id not in self.capability_history:
                self.capability_history[agent_id] = []
            
            self.capability_history[agent_id].append({
                "capabilities": capabilities,
                "timestamp": event.timestamp.isoformat(),
                "event_type": "registration"
            })
            
            # Initialize performance tracking
            if agent_id not in self.performance_history:
                self.performance_history[agent_id] = {
                    "metrics": [],
                    "average_response_time": 0.0,
                    "success_rate": 1.0,
                    "total_requests": 0
                }
            
            self.processed_events += 1
            logger.debug(f"Successfully processed agent registration for {agent_id}")
            
        except Exception as e:
            logger.error(f"Error handling agent registration event: {e}")
            raise
    
    async def handle_agent_unregistration(self, event: LangGraphEvent):
        """Handle agent unregistration event."""
        try:
            data = event.data
            agent_id = data.get("agent_id")
            reason = data.get("reason", "unknown")
            
            logger.info(f"Processing agent unregistration for {agent_id}, reason: {reason}")
            
            # Remove from cache but keep history
            if agent_id in self.agent_registry_cache:
                del self.agent_registry_cache[agent_id]
            
            # Add unregistration to capability history
            if agent_id in self.capability_history:
                self.capability_history[agent_id].append({
                    "capabilities": [],
                    "timestamp": event.timestamp.isoformat(),
                    "event_type": "unregistration",
                    "reason": reason
                })
            
            self.processed_events += 1
            logger.debug(f"Successfully processed agent unregistration for {agent_id}")
            
        except Exception as e:
            logger.error(f"Error handling agent unregistration event: {e}")
            raise
    
    async def handle_capability_update(self, event: LangGraphEvent):
        """Handle capability update event."""
        try:
            data = event.data
            agent_id = data.get("agent_id")
            old_capabilities = data.get("old_capabilities", [])
            new_capabilities = data.get("new_capabilities", [])
            changes = data.get("changes", {})
            update_reason = data.get("update_reason", "unknown")
            
            logger.info(f"Processing capability update for {agent_id}")
            logger.debug(f"Changes: {changes}")
            
            # Update cache
            if agent_id in self.agent_registry_cache:
                self.agent_registry_cache[agent_id]["capabilities"] = new_capabilities
                self.agent_registry_cache[agent_id]["last_updated"] = datetime.now().isoformat()
            
            # Update capability history
            if agent_id not in self.capability_history:
                self.capability_history[agent_id] = []
            
            self.capability_history[agent_id].append({
                "old_capabilities": old_capabilities,
                "new_capabilities": new_capabilities,
                "changes": changes,
                "timestamp": event.timestamp.isoformat(),
                "event_type": "capability_update",
                "reason": update_reason
            })
            
            # Trigger capability analysis if significant changes
            if len(changes.get("added", [])) > 0 or len(changes.get("removed", [])) > 0:
                await self._analyze_capability_changes(agent_id, changes)
            
            self.processed_events += 1
            logger.debug(f"Successfully processed capability update for {agent_id}")
            
        except Exception as e:
            logger.error(f"Error handling capability update event: {e}")
            raise
    
    async def handle_performance_update(self, event: LangGraphEvent):
        """Handle agent performance update event."""
        try:
            data = event.data
            agent_id = data.get("agent_id")
            performance_metrics = data.get("performance_metrics", {})
            workflow_id = data.get("workflow_id")
            
            logger.debug(f"Processing performance update for {agent_id}")
            
            # Update performance history
            if agent_id not in self.performance_history:
                self.performance_history[agent_id] = {
                    "metrics": [],
                    "average_response_time": 0.0,
                    "success_rate": 1.0,
                    "total_requests": 0
                }
            
            perf_data = self.performance_history[agent_id]
            perf_data["metrics"].append({
                "metrics": performance_metrics,
                "workflow_id": workflow_id,
                "timestamp": event.timestamp.isoformat()
            })
            
            # Keep only last 100 performance records
            if len(perf_data["metrics"]) > 100:
                perf_data["metrics"] = perf_data["metrics"][-100:]
            
            # Update aggregated metrics
            await self._update_aggregated_performance(agent_id, performance_metrics)
            
            self.processed_events += 1
            
        except Exception as e:
            logger.error(f"Error handling performance update event: {e}")
            raise
    
    async def _analyze_capability_changes(self, agent_id: str, changes: Dict[str, Any]):
        """Analyze capability changes and trigger notifications if needed."""
        try:
            added = changes.get("added", [])
            removed = changes.get("removed", [])
            
            # Log significant changes
            if added:
                logger.info(f"Agent {agent_id} gained capabilities: {added}")
            if removed:
                logger.warning(f"Agent {agent_id} lost capabilities: {removed}")
            
            # Could trigger additional events or notifications here
            # For example, notify routing system of capability changes
            
        except Exception as e:
            logger.error(f"Error analyzing capability changes for {agent_id}: {e}")
    
    async def _update_aggregated_performance(self, agent_id: str, metrics: Dict[str, Any]):
        """Update aggregated performance metrics."""
        try:
            perf_data = self.performance_history[agent_id]
            
            # Update total requests
            perf_data["total_requests"] += 1
            
            # Update average response time
            response_time = metrics.get("response_time", 0.0)
            if response_time > 0:
                current_avg = perf_data["average_response_time"]
                total_requests = perf_data["total_requests"]
                perf_data["average_response_time"] = (
                    (current_avg * (total_requests - 1) + response_time) / total_requests
                )
            
            # Update success rate
            success = metrics.get("success", True)
            current_success_rate = perf_data["success_rate"]
            total_requests = perf_data["total_requests"]
            success_value = 1.0 if success else 0.0
            perf_data["success_rate"] = (
                (current_success_rate * (total_requests - 1) + success_value) / total_requests
            )
            
        except Exception as e:
            logger.error(f"Error updating aggregated performance for {agent_id}: {e}")
    
    def get_agent_info(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get cached agent information."""
        return self.agent_registry_cache.get(agent_id)
    
    def get_capability_history(self, agent_id: str) -> Optional[list]:
        """Get capability history for an agent."""
        return self.capability_history.get(agent_id)
    
    def get_performance_summary(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get performance summary for an agent."""
        return self.performance_history.get(agent_id)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get handler metrics."""
        return {
            "processed_events": self.processed_events,
            "cached_agents": len(self.agent_registry_cache),
            "agents_with_capability_history": len(self.capability_history),
            "agents_with_performance_data": len(self.performance_history)
        }
